import { StructureValue, type StructureType } from "@packages/shared";

export const formatNumber = (num: number) => {
	return new Intl.NumberFormat("zh-CN", {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(num);
};

export const formatIntNumber = (num: number) => {
	return new Intl.NumberFormat("zh-CN", {
		minimumFractionDigits: 0,
		maximumFractionDigits: 0,
	}).format(num);
};

export const formatDate = (date: string | Date) => {
	return new Date(date).toLocaleString("zh-CN", {
		year: "numeric",
		month: "2-digit",
		day: "2-digit",
		hour: "2-digit",
		minute: "2-digit",
	});
};

export const formatTime = (date: string | Date) => {
	return new Date(date).toLocaleTimeString("zh-CN", {
		hour: "2-digit",
		minute: "2-digit",
		second: "2-digit",
	});
};

export const formatDateDay = (date: string | Date) => {
	const dateObj = typeof date === "string" ? new Date(date) : date;
	return dateObj.toLocaleDateString("zh-CN", {
		year: "numeric",
		month: "2-digit",
		day: "2-digit",
	});
};

export const formatMoney = (amount: number) => {
	return new Intl.NumberFormat("en-US", {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(amount);
};

export const formatPrice = formatMoney;

export const formatStructure = (structure: StructureType): string => {
	return StructureValue[structure] || structure;
};
