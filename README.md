# INK Trading Platform

A comprehensive trading platform with client portal, admin panel, and backend services.

## Project Structure

```
ink-dev/
├── packages/
│ ├── client/ # Client Portal (Vue 3 + TypeScript + Vite)
│ ├── admin/ # Admin Panel (Vue 3 + TypeScript + Vite)
│ ├── server/ # Backend Services (Node.js + Express + TypeScript)
│ └── shared/ # Shared Types and Utilities
```

## Quick Start

```bash
# Install dependencies
pnpm install

# Start development servers
pnpm dev

# Build all packages
pnpm build
```

## Package Details

### Client Portal (`packages/client`)
- Customer-facing trading interface
- Built with Vue 3, TypeScript, and Vite
- Supports real-time trading and account management

### Admin Panel (`packages/admin`)
- Administrative management system
- Built with Vue 3, TypeScript, and Vite
- Features user management, trade monitoring, and system settings

### Backend (`packages/server`)
- RESTful API server
- WebSocket server for real-time updates
- Database and cache management
- Authentication and authorization

### Shared (`packages/shared`)
- Common types and interfaces
- Shared utilities and constants
- Cross-package business logic

## Development

Each package can be developed independently:

```bash
# Client Portal
pnpm --filter @packages/client dev

# Admin Panel
pnpm --filter @packages/admin dev

# Backend Server
pnpm --filter @packages/server dev
```

## Documentation
- [API Documentation](./docs/api.md)
- [Development Guide](./docs/development.md)
- [Deployment Guide](./docs/deployment.md)

## License
Private