import type { Job } from "bullmq";
import {
	createWorker,
	channelLockTasksQueue,
	addRepeatedJob,
} from "./index.js";
import logger from "@/utils/logger.js";
import * as ChannelFund from "@/models/channelFund.js";
import { isTradingPlatform } from "@/config/configManager.js";

// 定义通道锁定相关的作业类型
export const CHANNEL_LOCK_JOBS = {
	CHECK_AND_LOCK_NEGATIVE_BALANCES: "channel-check-lock-negative-balances",
};

// 处理通道锁定相关的作业
async function processChannelLockJob(job: Job) {
	const { name } = job;

	logger.info(`Processing channel lock job: ${name}`);

	try {
		switch (name) {
			case CHANNEL_LOCK_JOBS.CHECK_AND_LOCK_NEGATIVE_BALANCES:
				await checkAndLockNegativeBalances();
				break;
			default:
				logger.warn(`Unknown channel lock job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process channel lock job: ${name}`);
		throw error;
	}
}

// 检查通道负余额并锁定
async function checkAndLockNegativeBalances(): Promise<void> {
	try {
		logger.info("开始检查通道负余额并锁定");
		const result = await ChannelFund.checkAndLockNegativeBalances();
		logger.info(
			{
				locked: result.locked,
				notLocked: result.notLocked,
			},
			"通道余额检查完成",
		);

		// 如果有被锁定的通道，记录警告
		if (result.locked.length > 0) {
			logger.warn(
				`${result.locked.length}个通道因负余额被锁定: ${result.locked.join(", ")}`,
			);
		}
	} catch (error) {
		logger.error(error, "执行通道余额检查任务失败");
	}
}

// 创建Worker实例
export const channelLockWorker = createWorker(
	channelLockTasksQueue,
	processChannelLockJob,
);

// 初始化定时作业调度
export async function initializeChannelLockJobs() {
	try {
		// 交易台才需要设置资金锁定相关任务
		if (!isTradingPlatform()) {
			logger.info("非交易台，跳过通道锁定定时任务");
			return;
		}

		// 每日零点检查通道余额，如有负值则锁定
		await addRepeatedJob(
			channelLockTasksQueue,
			CHANNEL_LOCK_JOBS.CHECK_AND_LOCK_NEGATIVE_BALANCES,
			{},
			"0 0 * * *",
		);

		logger.info("通道锁定任务调度成功");
	} catch (error) {
		logger.error(error, "Failed to schedule channel lock jobs");
		throw error;
	}
}

// 用于应用程序启动时初始化所有通道锁定任务
export async function initializeOnStartup() {
	await initializeChannelLockJobs();
	logger.info("Channel lock worker initialized successfully");
}
