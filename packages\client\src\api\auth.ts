import { request } from "./request";
import http from "../plugins/axios";
import type {
	BaseAuthResponse,
	LoginRequest,
	RegisterRequest,
	VerifyRegisterRequest,
	ResetPasswordRequest,
	VerifyResetPasswordRequest,
} from "@packages/shared";

// 认证相关 API
export const authApi = {
	login: (data: LoginRequest) =>
		http.post<BaseAuthResponse>("/auth/login", data),

	refreshToken: () => http.post<BaseAuthResponse>("/auth/refresh-token"),

	register: (data: RegisterRequest) => request.post("/auth/register", data),

	verifyRegistration: (data: VerifyRegisterRequest) =>
		request.post("/auth/verify-registration", data),

	sendResetCode: (data: ResetPasswordRequest) =>
		request.post("/auth/send-reset-code", data),

	verifyResetPassword: (data: VerifyResetPasswordRequest) =>
		request.post("/auth/verify-reset-password", data),

	logout: () => request.post("/auth/logout"),

	changePassword: (data: {
		old_password: string;
		new_password: string;
	}) => request.post("/auth/change-password", data),
} as const;
