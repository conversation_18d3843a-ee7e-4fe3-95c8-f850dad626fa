import * as ExecuteOrderService from "./executeOrder.js";
import logger from "@/utils/logger.js";
import { notifyUser } from "@/utils/notify.js";
import { cleanupLimitOrder } from "@/queue/limitOrderWorker.js";
import { cleanupVwapOrder } from "@/queue/vwapOrderWorker.js";
import * as PendingOrder from "@/models/trade/pendingOrder.js";
import {
	OrderStatus,
	TradeDirection,
	OrderType,
	WebSocketMessageType,
} from "@packages/shared";
import type {
	OrderData,
	PendingOrderData,
	BuyingOrderData,
	SellingOrderData,
	BuyRequest,
	SellRequest,
} from "@packages/shared";

async function confirmBuyOrder(
	pendingOrder: BuyingOrderData,
	options?: {
		vwapPrice?: number;
		orderType?: OrderType;
	},
) {
	const buyRequest: BuyRequest = {
		type: OrderType.MARKET,
		direction: TradeDirection.BUY,
		user_id: pendingOrder.user_id,
		ts_code: pendingOrder.ts_code,
		scale: pendingOrder.scale,
		term: pendingOrder.term,
		quote: pendingOrder.quote,
		structure: pendingOrder.structure,
		quote_provider: pendingOrder.quote_provider || "INK",
		quote_diff: pendingOrder.quote_diff || 0,
	};

	return ExecuteOrderService.executeOrder(buyRequest, options);
}

async function confirmSellOrder(
	pendingOrder: SellingOrderData,
	options?: {
		vwapPrice?: number;
		orderType?: OrderType;
	},
) {
	const sellRequest: SellRequest = {
		type: OrderType.MARKET,
		direction: TradeDirection.SELL,
		user_id: pendingOrder.user_id,
		ts_code: pendingOrder.ts_code,
		scale: pendingOrder.scale,
		trade_no: pendingOrder.trade_no,
		quote_provider: pendingOrder.quote_provider,
		quote_diff: pendingOrder.quote_diff,
	};

	return ExecuteOrderService.executeOrder(sellRequest, options);
}

export async function executeLimit(
	pendingOrder: PendingOrderData,
	options?: {
		orderType?: OrderType;
	},
): Promise<OrderData> {
	try {
		const order = [OrderStatus.LIMIT_BUYING, OrderStatus.VWAP_BUYING].includes(
			pendingOrder.status,
		)
			? await confirmBuyOrder(pendingOrder as BuyingOrderData, options)
			: await confirmSellOrder(pendingOrder as SellingOrderData, options);

		await cancelPendingOrder(pendingOrder);

		return order;
	} catch (error) {
		// 确保在错误发生时清理相关挂单
		try {
			await cancelPendingOrder(pendingOrder);
		} catch (cleanupError) {
			logger.error(
				cleanupError,
				`Error during cleanup after order execution failure: ${pendingOrder.pending_id}`,
			);
		}
		// 重新抛出原始错误以便策略层处理
		throw error;
	}
}

export async function executeVwap(
	pendingOrder: PendingOrderData,
	vwapPrice: number,
): Promise<OrderData> {
	try {
		const order =
			pendingOrder.status === OrderStatus.VWAP_BUYING
				? await confirmBuyOrder(pendingOrder as BuyingOrderData, {
						vwapPrice,
						orderType: OrderType.VWAP,
					})
				: await confirmSellOrder(pendingOrder as SellingOrderData, {
						vwapPrice,
						orderType: OrderType.VWAP,
					});

		await cancelPendingOrder(pendingOrder);

		return order;
	} catch (error) {
		// 确保在错误发生时清理相关挂单
		try {
			await cancelPendingOrder(pendingOrder);
		} catch (cleanupError) {
			logger.error(
				cleanupError,
				`Error during cleanup after VWAP order execution failure: ${pendingOrder.pending_id}`,
			);
		}
		// 重新抛出原始错误以便策略层处理
		throw error;
	}
}

export async function cancelPendingOrder(
	pendingOrder: PendingOrderData,
): Promise<void> {
	try {
		// 停止限价策略监控
		if (
			[OrderStatus.LIMIT_BUYING, OrderStatus.LIMIT_SELLING].includes(
				pendingOrder.status,
			)
		) {
			await cleanupLimitOrder(pendingOrder.pending_id);
		}

		// 停止 VWAP 策略监控
		if (
			[OrderStatus.VWAP_BUYING, OrderStatus.VWAP_SELLING].includes(
				pendingOrder.status,
			)
		) {
			await cleanupVwapOrder(pendingOrder.pending_id);
		}

		// Manual orders don't have any strategy to clean up

		// Crucial step in a distributed queue system (BullMQ):
		// Delete from DB *after* queue/cache cleanup and *before* notifying user to ensure data consistency.
		await PendingOrder.close(pendingOrder.pending_id);
		await PendingOrder.deletePendingOrder(pendingOrder.pending_id);
		logger.info(
			`Pending order ${pendingOrder.pending_id} database entry removed.`,
		);

		// Update pending order after successful cancel pending order
		notifyUser(pendingOrder.user_id, {
			type: WebSocketMessageType.ORDER_UPDATE,
		});

		logger.info(
			`Pending order ${pendingOrder.pending_id} cancelled successfully`,
		);
	} catch (error) {
		logger.error(
			error,
			`Failed to cancel pending order ${pendingOrder.pending_id}`,
		);
		throw error;
	}
}
