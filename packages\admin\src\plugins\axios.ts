import axios from "axios";
import type { AxiosRequestConfig } from "axios";
import { useAuthStore } from "@/stores/auth";
import { ElMessage } from "element-plus";

// 创建实例
const http = axios.create({
	baseURL: "/api",
	withCredentials: true,
});

// 更新请求头中的 token
export function updateAuthHeader(authHeader: string | null) {
	if (authHeader) {
		http.defaults.headers.common.authorization = authHeader;
	} else {
		http.defaults.headers.common.authorization = undefined;
	}
}

// Axios 检测到名为 XSRF-TOKEN 的 cookie 存在，会自动添加到请求头

// 请求队列
let isRefreshing = false;
let failedQueue: Array<{
	onSuccess: (value?: unknown) => void;
	onFailure: (reason?: unknown) => void;
}> = [];

// 在重试请求前更新认证头
const retryRequest = (originalRequest: AxiosRequestConfig) => {
	if (!originalRequest.headers) {
		originalRequest.headers = {};
	}
	originalRequest.headers.authorization =
		http.defaults.headers.common.authorization;
	return http(originalRequest);
};

// 注册响应拦截器
http.interceptors.response.use(
	(response) => response,
	async (error) => {
		const { status } = error.response || {};
		const originalRequest = error.config;
		const auth = useAuthStore();

		// 处理 401
		if (status === 401 && !originalRequest._retry) {
			// 避免刷新 token 请求进入重试逻辑
			if (originalRequest.url === "/auth/admin-refresh-token") {
				return Promise.reject(error);
			}

			if (isRefreshing) {
				return new Promise(() => {
					failedQueue.push({
						onSuccess: () => retryRequest(originalRequest),
						onFailure: () => undefined,
					});
				});
			}

			isRefreshing = true;
			originalRequest._retry = true;

			try {
				await auth.refreshToken();
				for (const prom of failedQueue) prom.onSuccess();
				failedQueue = [];
				return retryRequest(originalRequest);
			} catch {
				for (const prom of failedQueue) prom.onFailure();
				failedQueue = [];
				ElMessage.error("认证已过期，请重新登录");
				auth.logout();
				return;
			} finally {
				isRefreshing = false;
			}
		}

		// CSRF token 错误处理
		if (
			status === 403 &&
			error.response?.data?.error?.includes("CSRF") &&
			!originalRequest._csrfRetry
		) {
			originalRequest._csrfRetry = true;
			return http(originalRequest);
		}

		return Promise.reject(error);
	},
);

export default http;
