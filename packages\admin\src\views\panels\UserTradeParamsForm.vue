<template>
  <el-dialog v-model="isDialogVisible" :title="`配置用户 ${userData?.user_id} (${userData?.name || userData?.email}) 的交易参数`"
    :width="dialogWidth" top="10vh" :close-on-click-modal="false">
    <div class="user-trade-params-form">
      <el-form :model="editableParams" label-width="180px" label-position="top">

        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>自定义盈利分成</span>
            </div>
          </template>
          <el-form-item label="盈利分成比例 (%)">
            <el-input-number v-model="editableParams.custom_profit_sharing_percentage" :min="0" :max="100" :step="1"
              :precision="0" placeholder="留空使用平台默认" style="width: 200px;" />
          </el-form-item>
        </el-card>

        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              自定义报价加价
              <span class="card-header-suffix"> (￥/每单)</span>
            </div>
          </template>
          <div v-if="platformQuoteProviders.length > 0 && editableParams.custom_quote_diffs">
            <el-row :gutter="20">
              <el-col :span="isMobile ? 24 : 12" v-for="provider in platformQuoteProviders" :key="provider.key">
                <el-form-item class="inline-form-item">
                  <span class="inline-form-item-label">{{ provider.name }} :</span>
                  <el-input-number v-model="editableParams.custom_quote_diffs[provider.key]" placeholder="留空使用平台默认"
                    :min="0" :step="100" :precision="0" controls-position="right" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div v-else-if="!platformQuoteProviders.length">
            <p>平台未配置报价提供商，无法设置自定义加价。</p>
          </div>
          <div v-else>
            <p>正在加载报价提供商...</p>
          </div>
        </el-card>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="isDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存修改</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import { basicApi, configApi as newConfigApi } from "@/api"; // 使用新的 configApi
import type { UserInfo, UserData, PlatformConfig } from "@packages/shared";
import { useSiteConfigStore } from "@/stores/siteConfig";

const props = defineProps<{
  visible: boolean;
  userId?: number;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "saved"): void; // 通知父组件保存成功，可以刷新列表
}>();

const siteConfigStore = useSiteConfigStore();
const loading = ref(false);
const saving = ref(false);
const userData = ref<UserInfo | null>(null); // 存储从API获取的原始用户信息
const editableParams = ref<
  Pick<UserInfo, "custom_profit_sharing_percentage" | "custom_quote_diffs">
>({
  custom_profit_sharing_percentage: undefined, // 初始化为 undefined 以便 placeholder 生效
  custom_quote_diffs: {},
});

const platformConfigGlobal = ref<PlatformConfig | null>(null);
const platformQuoteProviders = computed(() => {
  if (!platformConfigGlobal.value?.quote_providers) return [];
  const providers = Object.entries(platformConfigGlobal.value.quote_providers)
    .map(([key, value]) => ({
      key,
      name: key === 'INK' ? siteConfigStore.shortName() : value.display_name, // 对 INK 使用站点简称
      enabled: value.enabled,
    }));

  // 将 INK 和 HAIYING 放在最前面
  return providers.sort((a, b) => {
    if (a.key === "INK") return -1;
    if (b.key === "INK") return 1;
    if (a.key === "HAIYING") return -1;
    if (b.key === "HAIYING") return 1;
    return a.name.localeCompare(b.name); // 其他按显示名排序
  });
});

const isMobile = ref(window.innerWidth < 768);
const dialogWidth = computed(() => (isMobile.value ? "100%" : "70%"));

window.addEventListener("resize", () => {
  isMobile.value = window.innerWidth < 768;
});

const isDialogVisible = computed({
  get: () => props.visible && !loading.value,
  set: (value) => emit("update:visible", value),
});

const loadInitialData = async (userId: number) => {
  loading.value = true;
  try {
    const [user, pConfig] = await Promise.all([
      basicApi.getUserById(userId), // API 获取用户信息
      newConfigApi.getPlatformConfig(), // API 获取平台配置 (如果尚未全局加载)
    ]);
    userData.value = user;
    platformConfigGlobal.value = pConfig;

    if (user) {
      editableParams.value = {
        custom_profit_sharing_percentage:
          user.custom_profit_sharing_percentage ?? undefined,
        custom_quote_diffs: { ...(user.custom_quote_diffs || {}) }, // 创建副本进行编辑
      };
    } else {
      ElMessage.error("无法加载用户信息");
      isDialogVisible.value = false;
    }
  } catch (error) {
    console.error("Failed to load user trade params data:", error);
    ElMessage.error("加载用户交易参数失败");
    isDialogVisible.value = false;
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.userId,
  (newId) => {
    if (newId && props.visible) {
      loadInitialData(newId);
    }
  },
);

watch(
  () => props.visible,
  (isVisible) => {
    if (isVisible && props.userId) {
      loadInitialData(props.userId);
    }
  },
);

const handleSave = async () => {
  if (!userData.value?.user_id) return;
  saving.value = true;

  // 准备要发送的数据：只包含实际修改的、非空的、或显式设为 null 的值
  const paramsToUpdate: Pick<
    UserData,
    "custom_profit_sharing_percentage" | "custom_quote_diffs"
  > = {};

  // 处理百分比
  if (editableParams.value.custom_profit_sharing_percentage !== undefined) {
    paramsToUpdate.custom_profit_sharing_percentage =
      editableParams.value.custom_profit_sharing_percentage;
  } else if (
    userData.value?.custom_profit_sharing_percentage !== undefined &&
    userData.value?.custom_profit_sharing_percentage !== null
  ) {
    // 如果用户清空了输入框 (变为 undefined)，我们发送 null 来清除数据库中的值
    paramsToUpdate.custom_profit_sharing_percentage = null;
  }

  // 处理加价
  const cleanedDiffs: Record<string, number> = {};
  let diffsHaveValues = false;
  if (editableParams.value.custom_quote_diffs) {
    for (const key in editableParams.value.custom_quote_diffs) {
      const val = editableParams.value.custom_quote_diffs[key];
      const numVal = Number(val); // 尝试转换为数字
      if (val !== undefined && val !== null && !Number.isNaN(numVal)) {
        // 仅包含有效数字
        cleanedDiffs[key] = Number(val);
        diffsHaveValues = true;
      }
    }
  }
  // 如果 cleanedDiffs 有内容，则发送它；否则发送 null 来清空数据库中的 custom_quote_diffs
  if (diffsHaveValues) {
    paramsToUpdate.custom_quote_diffs = cleanedDiffs;
  } else if (
    userData.value?.custom_quote_diffs !== undefined &&
    userData.value?.custom_quote_diffs !== null
  ) {
    paramsToUpdate.custom_quote_diffs = null;
  }

  try {
    await newConfigApi.updateUserTradeParams(
      userData.value.user_id,
      paramsToUpdate,
    );
    ElMessage.success("用户交易参数保存成功");
    emit("saved");
    isDialogVisible.value = false;
  } catch (error) {
    console.error("Failed to save user trade params:", error);
    ElMessage.error("保存失败，请检查网络或联系管理员");
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.config-card:not(:last-child) {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
}

.card-header-suffix {
  color: var(--el-text-color-secondary);
}

.dialog-footer {
  text-align: right;
}

/* 新增样式用于内联表单项 */
.inline-form-item :deep(.el-form-item__content) {
  display: flex;
  align-items: center;
  /* 移除默认的 margin-left，避免影响布局 */
  margin-left: 0 !important;
}

/* 调整 label 样式，使其在内容区域内 */
.inline-form-item-label {
  flex-shrink: 0;
  /* 防止 label 被压缩 */
  margin-right: 10px;
  /* label 和输入框之间的距离 */
  /* 根据需要调整 label 的宽度或最小宽度 */
  min-width: 44px;
  font-weight: bold;
  color: var(--el-text-color-regular);
}

/* 调整输入框容器的 flex 属性 */
.inline-form-item :deep(.el-input-number) {
  flex-grow: 1;
  /* 让输入框填充剩余空间 */
}
</style>