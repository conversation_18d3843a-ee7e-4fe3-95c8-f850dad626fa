import { eventBus } from "@/utils/eventBus";
import {
	OrderType,
	TradeDirection,
	WebSocketMessageType,
	type WebSocketMessage,
} from "@packages/shared";
import { ElMessage } from "element-plus";
import { systemStatusManager } from "@/core/systemStatusManager.js";
import { useAuthStore } from "@/stores/auth";

class WebSocketManager {
	private ws: WebSocket | null = null;
	private static instance: WebSocketManager;
	private reconnectAttempts = 0;
	private readonly MAX_RECONNECT_ATTEMPTS = 5;
	private currentUserId: string | null = null;
	private reconnectTimer: NodeJS.Timeout | null = null;
	private isManuallyDisconnected = false;

	private constructor() {}

	static getInstance(): WebSocketManager {
		if (!WebSocketManager.instance) {
			WebSocketManager.instance = new WebSocketManager();
		}
		return WebSocketManager.instance;
	}

	connect(userId: string) {
		if (
			this.ws?.readyState === WebSocket.OPEN &&
			this.currentUserId === userId &&
			!this.isManuallyDisconnected
		) {
			return;
		}

		this.cleanup();

		this.currentUserId = userId;
		this.isManuallyDisconnected = false;
		this.createConnection();
	}

	private createConnection() {
		if (!this.currentUserId || this.isManuallyDisconnected) {
			return;
		}

		const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
		const wsUrl = `${protocol}//${window.location.host}/ws?userId=${this.currentUserId}`;

		try {
			this.ws = new WebSocket(wsUrl);
			this.setupEventHandlers();
		} catch (error) {
			console.error("Failed to create WebSocket connection:", error);
			this.scheduleReconnect();
		}
	}

	private setupEventHandlers() {
		if (!this.ws) return;

		this.ws.onopen = () => {
			console.log("WebSocket connected successfully");
			this.reconnectAttempts = 0;

			if (this.reconnectTimer) {
				clearTimeout(this.reconnectTimer);
				this.reconnectTimer = null;
			}
		};

		this.ws.onmessage = (event) => {
			try {
				const message: WebSocketMessage = JSON.parse(event.data);
				this.handleMessage(message);
			} catch (error) {
				console.error("Failed to parse WebSocket message:", error);
			}
		};

		this.ws.onerror = (error) => {
			console.error("WebSocket error:", error);
		};

		this.ws.onclose = (event) => {
			console.log("WebSocket connection closed:", event.code, event.reason);
			this.ws = null;

			if (!this.isManuallyDisconnected) {
				this.scheduleReconnect();
			}
		};
	}

	private handleMessage(message: WebSocketMessage) {
		const auth = useAuthStore();

		switch (message.type) {
			case WebSocketMessageType.PING:
				this.sendPong();
				break;
			case WebSocketMessageType.PONG:
				break;
			case WebSocketMessageType.NOTIFICATION_UPDATE:
				eventBus.emit("notification-updated");
				break;
			case WebSocketMessageType.PAGE_RELOAD:
				window.location.reload();
				break;
			case WebSocketMessageType.ORDER_UPDATE:
				eventBus.emit("order-updated");
				if (message.data?.orderType) {
					const orderType =
						message.data.orderType === OrderType.LIMIT ? "限价" : "均价";
					const direction =
						message.data.direction === TradeDirection.BUY ? "下单" : "结算";
					ElMessage.success(`成功执行${orderType}${direction}`);
				}
				break;
			case WebSocketMessageType.AUTH_UPDATE:
				auth
					.refreshToken()
					.then(() => window.location.reload())
					.catch(() => {
						ElMessage.error("认证已过期，请重新登录");
						auth.logout();
					});
				break;
			case WebSocketMessageType.SYSTEM_AVAILABLE_UPDATE:
				systemStatusManager.checkSystemStatus();
				break;
			default:
				console.warn("Unknown WebSocket message type:", message.type);
		}
	}

	private sendPong() {
		if (this.ws?.readyState === WebSocket.OPEN) {
			try {
				this.ws.send(JSON.stringify({ type: WebSocketMessageType.PONG }));
			} catch (error) {
				console.error("Failed to send pong:", error);
			}
		}
	}

	private scheduleReconnect() {
		if (this.reconnectTimer || this.isManuallyDisconnected) {
			return;
		}

		if (this.reconnectAttempts >= this.MAX_RECONNECT_ATTEMPTS) {
			ElMessage.error("网络连接已断开，请刷新页面重试");
			return;
		}

		this.reconnectAttempts++;
		const delay = Math.min(1000 * 2 ** this.reconnectAttempts, 30000);

		console.log(
			`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`,
		);

		this.reconnectTimer = setTimeout(() => {
			this.reconnectTimer = null;
			this.createConnection();
		}, delay);
	}

	private cleanup() {
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer);
			this.reconnectTimer = null;
		}

		if (this.ws) {
			this.ws.close();
			this.ws = null;
		}
	}

	disconnect() {
		this.isManuallyDisconnected = true;
		this.cleanup();
		this.currentUserId = null;
		this.reconnectAttempts = 0;
	}

	isConnected(): boolean {
		return this.ws?.readyState === WebSocket.OPEN;
	}
}

export default WebSocketManager.getInstance();
