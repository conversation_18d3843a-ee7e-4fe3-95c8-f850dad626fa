import type { Prisma } from "@prisma/client";
import * as User from "@/models/user.js";
import * as Fund from "@/models/fund.js";
import { AppError } from "@/core/appError.js";
import { withTransaction } from "@/core/dbTxnManager.js";
import { exchangeRateService } from "@/utils/exchangeRate.js";
import bcrypt from "bcryptjs";
import EmailService from "@/utils/email.js";
import { appRedis } from "@/lib/redis.js";
import * as SharedConfigService from "@/services/admin/sharedConfigService.js";
import * as NotifService from "@/services/notifService.js";
import { APP_CONFIG, isChannel } from "@/config/configManager.js";
import type { TransactionData } from "@packages/shared";
import { TransactionType, Currency, NotificationType } from "@packages/shared";

const MAX_PAYMENT_PASSWORD_ATTEMPTS = 6;
const PAYMENT_PASSWORD_LOCK_TIME = 24 * 60 * 60; // 24小时（秒）
const PAYMENT_PASSWORD_ATTEMPTS_PREFIX = "payment_password:attempts:";

export async function checkBalance(
	user_id: number,
	amount: number,
	currency: Currency,
): Promise<boolean> {
	const balance = await User.getBalance(user_id, currency);
	return balance >= amount;
}

export async function handleTransaction(
	{
		user_id,
		amount,
		type,
		currency,
		trade_no,
	}: Omit<TransactionData, "txn_id" | "created_at" | "signed_amount"> & {
		amount: number;
	},
	client?: Prisma.TransactionClient,
): Promise<void> {
	const handleFn = async (tx: Prisma.TransactionClient) => {
		const signedAmount =
			type === TransactionType.WITHDRAW || type === TransactionType.BUY
				? -amount
				: amount;

		switch (type) {
			case TransactionType.WITHDRAW:
				if (!(await checkBalance(user_id, amount, currency))) {
					throw AppError.create("INSUFFICIENT_BALANCE", "Insufficient balance");
				}
				await Fund.record(
					{
						user_id,
						signed_amount: signedAmount,
						type,
						currency,
						trade_no,
					},
					tx,
				);
				break;

			case TransactionType.BUY:
				if (!(await checkBalance(user_id, amount, Currency.CNY))) {
					throw AppError.create("INSUFFICIENT_BALANCE", "Insufficient balance");
				}
				await Fund.record(
					{
						user_id,
						signed_amount: signedAmount,
						type,
						currency: Currency.CNY,
						trade_no,
					},
					tx,
				);
				break;

			case TransactionType.DEPOSIT:
			case TransactionType.SELL:
			case TransactionType.PLATFORM_DEPOSIT:
				await Fund.record(
					{
						user_id,
						signed_amount: signedAmount,
						type,
						currency,
						trade_no,
					},
					tx,
				);
				break;

			// Exchange transactions are handled separately in exchangeCurrency function
		}
	};
	return client ? handleFn(client) : withTransaction(handleFn);
}

export async function getHistory(
	page: number,
	pageSize: number,
	isDescending: boolean,
	filters?: {
		user_id?: number;
		types?: TransactionType[];
		startDate?: string;
		endDate?: string;
	},
): Promise<{
	items: TransactionData[];
	total: number;
}> {
	const offset = (page - 1) * pageSize;

	// 获取总数和分页数据
	const [total, items] = await Promise.all([
		Fund.countTransactions(filters?.user_id, filters),
		Fund.getTransactions(
			offset,
			pageSize,
			isDescending,
			filters?.user_id,
			filters,
		),
	]);

	return { items, total };
}

export async function getExchangeRate(
	fromCurrency: Currency,
	toCurrency: Currency,
): Promise<number> {
	if (fromCurrency === toCurrency) {
		throw AppError.create(
			"INVALID_EXCHANGE_CURRENCIES",
			"Cannot exchange to same currency",
		);
	}

	const rates = await exchangeRateService.getCurrentRates();
	// CNY -> HKD,USD
	if (fromCurrency === Currency.CNY) {
		return toCurrency === Currency.HKD ? 1 / rates.HKD_CNY : 1 / rates.USD_CNY;
	}
	// HKD,USD -> CNY
	if (toCurrency === Currency.CNY) {
		return fromCurrency === Currency.HKD ? rates.HKD_CNY : rates.USD_CNY;
	}
	// HKD -> USD
	if (fromCurrency === Currency.HKD && toCurrency === Currency.USD) {
		return rates.HKD_CNY / rates.USD_CNY;
	}
	// USD -> HKD
	return rates.USD_CNY / rates.HKD_CNY;
}

export async function exchangeCurrency(
	user_id: number,
	fromCurrency: Currency,
	toCurrency: Currency,
	amount: number,
): Promise<void> {
	// 验证参数
	if (amount <= 0) {
		throw AppError.create(
			"EXCHANGE_AMOUNT_TOO_SMALL",
			"Exchange amount must be positive",
		);
	}

	// 检查余额是否充足
	const hasBalance = await checkBalance(user_id, amount, fromCurrency);
	if (!hasBalance) {
		throw AppError.create("INSUFFICIENT_BALANCE", "Insufficient balance");
	}

	// 获取当前汇率
	const rate = await getExchangeRate(fromCurrency, toCurrency);

	// 计算最终兑换所得
	const exchangedAmount = amount * rate;

	// 在事务中创建交易记录
	await withTransaction(async (tx) => {
		// 扣除原始货币
		await Fund.record(
			{
				user_id,
				signed_amount: -amount,
				type: TransactionType.EXCHANGE,
				currency: fromCurrency,
			},
			tx,
		);

		// 增加目标货币
		await Fund.record(
			{
				user_id,
				signed_amount: exchangedAmount,
				type: TransactionType.EXCHANGE,
				currency: toCurrency,
			},
			tx,
		);
	});
}

function maskPhoneNumber(phone: string): string {
	return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
}

export async function transfer(
	senderId: number,
	receiverName: string,
	receiverPhone: string,
	amount: number,
	paymentPassword: string,
): Promise<void> {
	// 1. 如果是通道，检查转账功能是否开启
	if (isChannel()) {
		const sharedConfig = await SharedConfigService.getSharedConfig();
		if (!sharedConfig?.channel_management?.enable_transfer_auth) {
			throw AppError.create(
				"TRANSFER_DISABLED",
				"User transfer is disabled by transfer auth",
			);
		}
	}

	// 2. 检查用户是否有转账权限
	const sender = await User.findById(senderId);
	if (!sender?.can_transfer) {
		throw AppError.create("FORBIDDEN", "No transfer permission");
	}

	// 验证支付密码
	await verifyPaymentPassword(senderId, paymentPassword);

	// Validate amount
	if (amount <= 0) {
		throw AppError.create(
			"INVALID_TRANSFER_AMOUNT",
			"Transfer amount must be positive",
		);
	}

	// Find receiver by phone and name
	const receiver = await User.findByPhoneNumber(receiverPhone);
	if (!receiver || receiver.name !== receiverName) {
		throw AppError.create("INVALID_RECEIVER_INFO", "Receiver info error");
	}

	// Execute transfer in a transaction
	await withTransaction(async (tx) => {
		// Deduct from sender
		await Fund.record(
			{
				user_id: senderId,
				signed_amount: -amount,
				type: TransactionType.TRANSFER,
				currency: Currency.CNY,
			},
			tx,
		);

		// Add to receiver
		await Fund.record(
			{
				user_id: receiver.user_id,
				signed_amount: amount,
				type: TransactionType.TRANSFER,
				currency: Currency.CNY,
			},
			tx,
		);

		// 发送系统内通知给转出方
		await NotifService.sendNotification(
			senderId,
			{
				title: "转账成功",
				content: `您已成功向 ${receiverName} 转账 ${amount} CNY`,
				type: NotificationType.ACCOUNT,
				metadata: {
					type: "fund_transfer",
					receiver_name: receiverName,
					receiver_phone: receiverPhone,
					formattedAmount: `-${amount} CNY`,
					operation: TransactionType.TRANSFER,
				},
			},
			tx,
		);

		// 发送系统内通知给接收方
		await NotifService.sendNotification(
			receiver.user_id,
			{
				title: "收到转账",
				content: `您收到来自 ${sender.name} 的转账 ${amount} CNY`,
				type: NotificationType.ACCOUNT,
				metadata: {
					type: "fund_transfer",
					sender_name: sender.name,
					sender_phone: maskPhoneNumber(sender.phone_number),
					formattedAmount: `+${amount} CNY`,
					operation: TransactionType.TRANSFER,
				},
			},
			tx,
		);
	});

	// 发送通知
	Promise.allSettled([
		EmailService.sendEmail(sender.email, "TRANSFER_OUT", { amount }),
		EmailService.sendEmail(receiver.email, "TRANSFER_IN", { amount }),
	]);
}

// 验证支付密码
export async function verifyPaymentPassword(
	user_id: number,
	password: string,
): Promise<void> {
	const attemptsKey = `${PAYMENT_PASSWORD_ATTEMPTS_PREFIX}${user_id}`;

	// 获取错误记录列表
	const attempts = await appRedis.lrange(attemptsKey, 0, -1);

	if (attempts.length >= MAX_PAYMENT_PASSWORD_ATTEMPTS) {
		// 计算最早错误到现在的剩余锁定时间
		const now = Date.now();
		const firstAttemptTime = Number(attempts[attempts.length - 1]); // 最早的错误记录
		const timeUntilUnlock = Math.ceil(
			(firstAttemptTime + PAYMENT_PASSWORD_LOCK_TIME * 1000 - now) / 1000 / 60,
		);
		throw AppError.create(
			"PAYMENT_PASSWORD_LOCKED",
			`错误次数过多，请等待${timeUntilUnlock}分钟后重试`,
		);
	}

	const passwordHash = await User.getPaymentPasswordHash(user_id);
	if (!passwordHash) {
		throw AppError.create("PAYMENT_PASSWORD_NOT_SET", "请先设置支付密码");
	}

	const isValid = await bcrypt.compare(password, passwordHash);
	if (!isValid) {
		// 添加新的错误记录
		await appRedis
			.multi()
			.lpush(attemptsKey, Date.now())
			.expire(attemptsKey, PAYMENT_PASSWORD_LOCK_TIME) // 设置过期时间
			.exec();

		const remainingAttempts =
			MAX_PAYMENT_PASSWORD_ATTEMPTS - (attempts.length + 1);
		throw AppError.create(
			"INVALID_PAYMENT_PASSWORD",
			`密码错误，还剩${remainingAttempts}次机会`,
		);
	}

	// 密码正确，清除错误记录
	await appRedis.del(attemptsKey);
}

// 检查是否设置了支付密码
export async function hasPaymentPassword(user_id: number): Promise<boolean> {
	const passwordHash = await User.getPaymentPasswordHash(user_id);
	return passwordHash != null;
}

// 设置支付密码
export async function setPaymentPassword(
	user_id: number,
	password: string,
): Promise<void> {
	// 检查是否已设置
	const hasPassword = await hasPaymentPassword(user_id);
	if (hasPassword) {
		throw AppError.create("PAYMENT_PASSWORD_EXISTS", "支付密码已设置");
	}

	// 验证密码格式（6位数字）
	if (!/^\d{6}$/.test(password)) {
		throw AppError.create("INVALID_PASSWORD_FORMAT", "支付密码必须是6位数字");
	}

	// 加密并保存
	const password_hash = await bcrypt.hash(password, 10);
	await User.setPaymentPassword(user_id, password_hash);

	// 发送通知
	const user = await User.findById(user_id);
	if (user?.email) {
		EmailService.sendEmail(user.email, "PAYMENT_PASSWORD_SET", {});
	}
}

/**
 * 解除支付密码锁定
 */
export async function unfreezePayment(user_id: number): Promise<void> {
	const attemptsKey = `${PAYMENT_PASSWORD_ATTEMPTS_PREFIX}${user_id}`;
	await appRedis.del(attemptsKey);
}

// 清空支付密码
export async function clearPaymentPassword(user_id: number): Promise<void> {
	// 清空支付密码
	await User.removePaymentPassword(user_id);

	// 清除错误记录
	const attemptsKey = `${PAYMENT_PASSWORD_ATTEMPTS_PREFIX}${user_id}`;
	await appRedis.del(attemptsKey);

	// 发送通知
	const user = await User.findById(user_id);
	if (user?.email) {
		EmailService.sendEmail(user.email, "PAYMENT_PASSWORD_REMOVED", {});
	}
}

export async function getAdminHistory(
	page: number,
	pageSize: number,
	isDescending: boolean,
	filters?: {
		types?: TransactionType[];
		startDate?: string;
		endDate?: string;
	},
): Promise<{
	items: TransactionData[];
	total: number;
}> {
	const offset = (page - 1) * pageSize;

	// 获取总数和分页数据
	const [total, items] = await Promise.all([
		Fund.countTransactions(undefined, filters),
		Fund.getTransactions(offset, pageSize, isDescending, undefined, filters),
	]);

	return { items, total };
}

// 获取所有资金记录，返回文本格式
// 格式：txn_id~user_id~signed_amount~type~currency~trade_no~created_at~app_id;...
export async function getAllTextByApi(): Promise<string> {
	const transactions = await Fund.getAllByApi();

	return transactions
		.map((transaction) => {
			// 构建数据字符串
			const values = [
				transaction.txn_id?.toString() || "",
				transaction.user_id?.toString() || "",
				transaction.signed_amount?.toString() || "0",
				transaction.type || "",
				transaction.currency || "",
				transaction.trade_no || "",
				transaction.created_at?.toISOString().split("T")[0] || "",
				APP_CONFIG.tradingPlatformId || APP_CONFIG.channelId || "",
			].join("~");

			return values;
		})
		.join(";\n");
}
