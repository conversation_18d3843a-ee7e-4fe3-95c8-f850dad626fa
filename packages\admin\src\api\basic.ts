import { request } from "./request";
import type {
	UserQueryOptions,
	OrderQueryOptions,
	PendingOrderQueryOptions,
} from "./types";
import type {
	DashboardStats,
	UserInfo,
	OrderData,
	PositionData,
	InquiryData,
	PendingOrderData,
} from "@packages/shared";

// 基础数据 API
export const basicApi = {
	getDashboardStats: () =>
		request.get<DashboardStats>("/admin/basic/dashboard-stats"),

	getProviderDiscounts: () =>
		request.get<Record<string, number>>("/admin/basic/provider-discounts"),

	// User API
	getUsers: (options?: UserQueryOptions) =>
		request.get<{
			items: UserInfo[];
			total: number;
		}>("/admin/basic/users", {
			params: {
				page: options?.page,
				pageSize: options?.pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
				...options?.filters,
			},
		}),

	getUserById: (id: number) =>
		request.get<UserInfo>(`/admin/basic/user/user_id/${id}`),

	getUserByPhone: (phone: string) =>
		request.get<UserInfo>(`/admin/basic/user/phone_number/${phone}`),

	getUserByEmail: (email: string) =>
		request.get<UserInfo>(`/admin/basic/user/email/${email}`),

	getUserByUsername: (username: string) =>
		request.get<UserInfo>(`/admin/basic/user/username/${username}`),

	// Order and Position API
	getOrders: (page: number, pageSize: number, options?: OrderQueryOptions) =>
		request.get<{
			items: OrderData[];
			total: number;
		}>("/admin/basic/orders", {
			params: {
				page,
				pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
				status: options?.status,
				startDate: options?.startDate,
				endDate: options?.endDate,
			},
		}),

	getUserOrders: (
		userId: number,
		page: number,
		pageSize: number,
		options?: OrderQueryOptions,
	) =>
		request.get<{
			items: OrderData[];
			total: number;
		}>(`/admin/basic/orders/${userId}`, {
			params: {
				page,
				pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
				status: options?.status,
				startDate: options?.startDate,
				endDate: options?.endDate,
			},
		}),

	getPositions: (page: number, pageSize: number) =>
		request.get<{
			items: PositionData[];
			total: number;
		}>("/admin/basic/positions", { params: { page, pageSize } }),

	getUserPositions: (userId: number, page: number, pageSize: number) =>
		request.get<{
			items: PositionData[];
			total: number;
		}>(`/admin/basic/positions/${userId}`, { params: { page, pageSize } }),

	getInquiries: (page: number, pageSize: number, options?: OrderQueryOptions) =>
		request.get<{
			items: InquiryData[];
			total: number;
		}>("/admin/basic/inquiries", {
			params: {
				page,
				pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
			},
		}),

	getUserInquiries: (
		userId: number,
		page: number,
		pageSize: number,
		options?: OrderQueryOptions,
	) =>
		request.get<{
			items: InquiryData[];
			total: number;
		}>(`/admin/basic/inquiries/${userId}`, {
			params: {
				page,
				pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
			},
		}),

	getPendingOrders: (
		page: number,
		pageSize: number,
		options?: PendingOrderQueryOptions,
	) =>
		request.get<{
			items: PendingOrderData[];
			total: number;
		}>("/admin/basic/list/pending", {
			params: {
				page,
				pageSize,
				types: options?.types?.join(","),
				ts_code: options?.ts_code,
				user_id: options?.user_id,
			},
		}),
} as const;
