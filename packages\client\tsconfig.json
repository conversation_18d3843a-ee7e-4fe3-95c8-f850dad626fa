{
	"compilerOptions": {
		"lib": [
			"DOM",
			"ES2020"
		],
		"jsx": "preserve",
		"target": "ES2020",
		"noEmit": true,
		"skipLibCheck": true,
		"jsxImportSource": "vue",
		"useDefineForClassFields": true,
		/* modules */
		"module": "ESNext",
		"isolatedModules": true,
		"resolveJsonModule": true,
		"moduleResolution": "Bundler",
		"allowImportingTsExtensions": true,
		/* type checking */
		"strict": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		/* 增量编译 */
		// "incremental": true,
		/* 强制模块检测 */
		// "moduleDetection": "force",
		/* 禁止switch穿透 */
		// "noFallthroughCasesInSwitch": true,
		/* Path alias */
		"baseUrl": ".",
		"paths": {
			"@/*": [
				"./src/*"
			],
			"@packages/shared": [
				"../shared/src"
			]
		}
	},
	"include": [
		"src/**/*"
	],
	"exclude": [
		"node_modules",
		"dist"
	],
	"references": [
		{
			"path": "../shared"
		}
	]
}