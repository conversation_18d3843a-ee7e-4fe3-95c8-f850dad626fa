import type { Job } from "bullmq";
import { createWorker, expiryTasksQueue, addRepeatedJob } from "./index.js";
import logger from "@/utils/logger.js";
import {
	isMarketDay,
	getNextTradingDay,
} from "@/financeUtils/marketTimeManager.js";
import * as Position from "@/models/position.js";
import * as TradeService from "@/services/trade/tradeService.js";
import * as PendingOrder from "@/models/trade/pendingOrder.js";
import { cancelPendingOrder } from "@/services/trade/confirmOrder.js";
import EmailService from "@/utils/email.js";
import * as User from "@/models/user.js";
import * as NotifService from "@/services/notifService.js";
import { NotificationType, OrderType, TradeDirection } from "@packages/shared";
import prisma from "@/lib/prisma.js";
import { formatDate } from "@/utils/format.js";
import { calculateExpiryDate } from "@packages/shared";
import { v4 as uuidv4 } from "uuid";

// 定义到期处理相关的作业类型
export const EXPIRY_JOBS = {
	PROCESS_PENDING_ORDERS: "expiry-process-pending-orders",
	PROCESS_EXPIRY_POSITIONS: "expiry-process-expiry-positions",
	SEND_EXPIRY_REMINDER: "expiry-send-reminder",
};

// 创建随机延迟函数，用于防止短时间内发送大量通知
async function randomDelay(): Promise<void> {
	const delay = Math.floor(Math.random() * 9) + 1; // 1-10秒随机延迟
	return new Promise((resolve) => setTimeout(resolve, delay * 1000));
}

// 消息通知队列，用于邮件发送
interface EmailNotification {
	email: string;
	templateType: "EXPIRY_REMINDER" | "EXPIRY_NOTIFICATION";
	params: {
		userName: string;
		uid: string;
		count: number;
		date?: string;
	};
}

// 处理到期相关的作业
async function processExpiryJob(job: Job) {
	const { name } = job;

	logger.info(`Processing expiry job: ${name}`);

	try {
		switch (name) {
			case EXPIRY_JOBS.PROCESS_PENDING_ORDERS:
				await processPendingOrders();
				break;
			case EXPIRY_JOBS.PROCESS_EXPIRY_POSITIONS:
				await processExpiryPositions();
				break;
			case EXPIRY_JOBS.SEND_EXPIRY_REMINDER:
				await sendExpiryReminder();
				break;
			default:
				logger.warn(`Unknown expiry job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process expiry job: ${name}`);
		throw error;
	}
}

// 处理挂单订单，确保在结算到期前清理所有挂单
async function processPendingOrders(): Promise<void> {
	try {
		const pendingOrders = await PendingOrder.findAllPending();
		for (const order of pendingOrders) {
			await cancelPendingOrder(order);
		}

		await prisma.$queryRaw`ALTER SEQUENCE pending_orders_pending_id_seq RESTART WITH 1;`;
		logger.info(
			`Processed ${pendingOrders.length} pending orders before expiry settlement`,
		);
	} catch (error) {
		logger.error(error, "Failed to process pending orders");
		throw error;
	}
}

// 发送邮件通知，带随机延迟
async function sendEmailsWithDelay(
	notifications: EmailNotification[],
): Promise<void> {
	for (const notification of notifications) {
		try {
			// 发送邮件前添加随机延迟，防止短时间内发送过多邮件
			await randomDelay();

			await EmailService.sendEmail(
				notification.email,
				notification.templateType,
				notification.params,
			);

			logger.info(
				`Email sent to ${notification.email} with template ${notification.templateType}`,
			);
		} catch (error) {
			logger.error(
				error,
				`Failed to send email to ${notification.email} with template ${notification.templateType}`,
			);
		}
	}
}

// 处理到期持仓
async function processExpiryPositions(): Promise<void> {
	try {
		// 检查是否为交易日，如果非交易日则跳过
		if (!(await isMarketDay())) {
			logger.info("Not a market day, skipping expiry position processing");
			return;
		}

		const startTime = Date.now();
		const positions = await Position.getAll();

		const today = new Date();
		const expiryPositions = positions.filter((position) => {
			const createdAt = new Date(position.created_at);
			const expiryDate = calculateExpiryDate(createdAt, position.term);
			// 只比较日期部分
			today.setHours(0, 0, 0, 0);
			expiryDate.setHours(0, 0, 0, 0);
			return expiryDate <= today;
		});

		// 按用户ID统计合约数
		const userPositionCounts = new Map<number, number>();

		// 执行市价卖出
		for (const position of expiryPositions) {
			try {
				await TradeService.placeOrder(
					{
						type: OrderType.MARKET,
						direction: TradeDirection.SELL,
						user_id: position.user_id,
						ts_code: position.ts_code,
						trade_no: position.trade_no,
						scale: position.scale,
						quote_provider: position.quote_provider,
						quote_diff: position.quote_diff,
					},
					{ isExpiry: true },
				);

				// 更新用户的合约计数
				userPositionCounts.set(
					position.user_id,
					(userPositionCounts.get(position.user_id) || 0) + 1,
				);

				logger.info(
					`Successfully placed expiry sell order for position ${position.trade_no}`,
				);
			} catch (error) {
				logger.error(
					error,
					`Failed to process expiry position ${position.trade_no}`,
				);
			}
		}

		// 收集需要发送的邮件通知
		const emailNotifications: EmailNotification[] = [];

		// 为每个用户发送合并后的通知
		for (const [userId, count] of userPositionCounts.entries()) {
			// 发送应用内通知
			await NotifService.sendNotification(userId, {
				title: "到期通知",
				content:
					count > 1
						? `您有${count}笔持仓已到期，请注意查看。`
						: "您的持仓已到期，请注意查看。",
				type: NotificationType.ORDER,
				metadata: {
					type: "expiry",
				},
			});

			// 准备邮件通知
			const user = await User.findById(userId);
			if (user) {
				// 生成随机UID，避免邮件被视为重复内容
				const uid = uuidv4().replace(/-/g, "").substring(0, 24);
				// 使用用户真实姓名或默认称呼
				const userName = user.name || "用户";

				// 收集邮件信息，稍后批量发送
				emailNotifications.push({
					email: user.email,
					templateType: "EXPIRY_NOTIFICATION",
					params: {
						userName,
						uid,
						count,
					},
				});
			}
		}

		// 处理完所有到期持仓后，开始延迟发送邮件通知
		if (emailNotifications.length > 0) {
			// 异步发送邮件通知，不阻塞主流程
			void sendEmailsWithDelay(emailNotifications);
		}

		const duration = Date.now() - startTime;
		logger.info(
			`Expiry position processing completed in ${duration}ms, processed ${expiryPositions.length} positions`,
		);
	} catch (error) {
		logger.error(error, "Error during expiry position processing");
		throw error;
	}
}

// 发送到期提醒
async function sendExpiryReminder(): Promise<void> {
	try {
		// 检查是否为交易日，如果非交易日则跳过
		if (!(await isMarketDay())) {
			logger.info("Not a market day, skipping expiry reminder");
			return;
		}

		const positions = await Position.getAll();
		const nextTradingDay = await getNextTradingDay();

		// 按用户ID和到期日期分组统计合约数
		const userExpiryCountMap = new Map<number, Map<string, number>>();

		// 统计到期合约
		for (const position of positions) {
			const expiryDate = new Date(position.expiry_date);
			if (expiryDate.toDateString() === nextTradingDay.toDateString()) {
				const formattedDate = formatDate(expiryDate);

				// 确保用户的映射存在
				if (!userExpiryCountMap.has(position.user_id)) {
					userExpiryCountMap.set(position.user_id, new Map<string, number>());
				}

				// 获取用户的日期-计数映射
				const dateCountMap = userExpiryCountMap.get(position.user_id);

				// 更新计数
				if (dateCountMap) {
					dateCountMap.set(
						formattedDate,
						(dateCountMap.get(formattedDate) || 0) + 1,
					);
				}
			}
		}

		// 收集需要发送的邮件通知
		const emailNotifications: EmailNotification[] = [];

		// 为每个用户发送合并通知
		for (const [userId, dateCountMap] of userExpiryCountMap.entries()) {
			const user = await User.findById(userId);
			if (!user) continue;

			// 使用用户真实姓名或默认称呼
			const userName = user.name || "用户";

			// 为每个到期日期发送通知
			for (const [date, count] of dateCountMap.entries()) {
				// 发送应用内通知
				await NotifService.sendNotification(userId, {
					title: "到期日提醒",
					content:
						count > 1
							? `您有${count}笔持仓将于${date}到期，请注意查看。`
							: `您有一笔持仓将于${date}到期，请注意查看。`,
					type: NotificationType.ORDER,
					metadata: {
						type: "expiry",
					},
				});

				// 生成随机UID，避免邮件被视为重复内容
				const uid = uuidv4().replace(/-/g, "").substring(0, 24);

				// 收集邮件信息，稍后批量发送
				emailNotifications.push({
					email: user.email,
					templateType: "EXPIRY_REMINDER",
					params: {
						date,
						userName,
						uid,
						count,
					},
				});
			}
		}

		// 处理完所有即将到期持仓后，开始延迟发送邮件通知
		if (emailNotifications.length > 0) {
			// 异步发送邮件通知，不阻塞主流程
			void sendEmailsWithDelay(emailNotifications);
		}

		logger.info("Expiry reminders sent successfully");
	} catch (error) {
		logger.error(error, "Failed to send expiry reminders");
		throw error;
	}
}

// 创建Worker实例
export const expiryWorker = createWorker(expiryTasksQueue, processExpiryJob);

// 初始化定时作业调度
export async function initializeExpiryJobs() {
	try {
		// 在每个交易日14:58处理挂单（避免到期结算遇到挂单待沽）
		await addRepeatedJob(
			expiryTasksQueue,
			EXPIRY_JOBS.PROCESS_PENDING_ORDERS,
			{},
			"58 14 * * *",
		);

		// 在每个交易日15:00执行到期持仓结算
		await addRepeatedJob(
			expiryTasksQueue,
			EXPIRY_JOBS.PROCESS_EXPIRY_POSITIONS,
			{},
			"0 15 * * *",
		);

		// 在每个交易日15:00发送到期提醒
		await addRepeatedJob(
			expiryTasksQueue,
			EXPIRY_JOBS.SEND_EXPIRY_REMINDER,
			{},
			"0 15 * * *",
		);

		logger.info("Expiry jobs scheduled successfully");
	} catch (error) {
		logger.error(error, "Failed to schedule expiry jobs");
		throw error;
	}
}

// 用于应用程序启动时初始化所有到期任务
export async function initializeOnStartup() {
	await initializeExpiryJobs();
	logger.info("Expiry worker initialized successfully");
}
