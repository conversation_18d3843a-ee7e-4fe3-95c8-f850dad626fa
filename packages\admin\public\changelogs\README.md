# 更新日志维护说明

## 文件组织

- 每年创建一个独立的Markdown文件，文件名格式：`YYYY.md`（如：`2025.md`）
- 文件按年份降序排列，最新年份的更新会优先显示

## 文件格式

每个年份文件应遵循以下格式：

```markdown
# YYYY年更新日志

## MM月

- 更新内容1
- 更新内容2
- 更新内容3

## MM月

- 更新内容1
- 更新内容2
```

## 格式规范

1. **标题格式**：
   - 文件标题：`# YYYY年更新日志`
   - 月份标题：`## MM月` （两位数字，如：`## 05月`）

2. **更新内容**：
   - 每条更新使用 `- ` 开头（注意空格）
   - 描述简洁明了，一行为一个功能点
   - 按重要性排序，重要更新在前

3. **月份排序**：
   - 文件内月份按时间降序排列（最新月份在前）
   - 例如：05月 → 04月 → 03月

## 添加新的更新日志

1. **当年新增月份**：
   - 在对应年份文件的最顶部添加新月份
   - 格式：`## MM月`

2. **新年度**：
   - 创建新的年份文件（如：`2026.md`）
   - 从 `## 01月` 开始

## 示例

参考 `2025.md` 和 `2024.md` 文件查看具体格式示例。 