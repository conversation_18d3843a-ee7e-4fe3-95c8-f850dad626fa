<template>
	<el-dialog v-model="isDialogVisible" title="用户资料更新" :width="dialogWidth" top="10vh" :close-on-click-modal="true"
		class="user-profile-dialog">
		<div v-if="userData" class="user-profile-form">
			<el-form :model="userData" label-width="120px">
				<el-descriptions :column="isMobile ? 1 : 2" border>
					<el-descriptions-item label="用户ID">{{ userData.user_id }}</el-descriptions-item>
					<el-descriptions-item label="邮箱">{{ userData.email }}</el-descriptions-item>
					<el-descriptions-item label="姓名">{{ userData.name }}</el-descriptions-item>
					<el-descriptions-item label="状态">
						<el-tag :type="userData.is_qualified ? 'success' : 'info'">
							{{ userData.is_qualified ? '已认证' : '未认证' }}
						</el-tag>
					</el-descriptions-item>
				</el-descriptions>

				<el-form-item label="手机号">
					<el-input v-model="contactValue" placeholder="请输入手机号" />
				</el-form-item>
				<el-form-item label="证件号码">
					<el-input v-model="userData.id_number" />
				</el-form-item>
				<el-form-item label="银行名称">
					<el-input v-model="userData.bank_name" />
				</el-form-item>
				<el-form-item label="银行编号">
					<el-input v-model="userData.bank_code" />
				</el-form-item>
				<el-form-item label="银行账号">
					<el-input v-model="userData.bank_account" />
				</el-form-item>

				<div class="balance-section">
					<h4>账户余额</h4>
					<div class="balance-tags">
						<el-tag>人民币: {{ formatMoney(userData.balance_cny) }}</el-tag>
						<el-tag type="success">港币: {{ formatMoney(userData.balance_hkd) }}</el-tag>
						<el-tag type="warning">美元: {{ formatMoney(userData.balance_usd) }}</el-tag>
					</div>
				</div>

				<div class="dates-section">
					<p>创建时间: {{ formatDate(userData.created_at) }}</p>
					<p>更新时间: {{ formatDate(userData.updated_at) }}</p>
				</div>
			</el-form>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="isDialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSave">保存修改</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { qualifyApi, basicApi } from "@/api";
import type { UserInfo } from "@packages/shared";
import { formatMoney, formatDate } from "@/utils/format";

const props = defineProps<{
	visible: boolean;
	userId?: number;
}>();

const emit = defineEmits<{
	(e: "update:visible", value: boolean): void;
	(e: "saved"): void;
}>();

const loading = ref(false);
const userData = ref<UserInfo | null>(null);

// 移动端检测
const isMobile = ref(window.innerWidth < 768);
const dialogWidth = computed(() => (isMobile.value ? "100%" : "70%"));

// 监听窗口大小变化
window.addEventListener("resize", () => {
	isMobile.value = window.innerWidth < 768;
});

const isDialogVisible = computed({
	get: () => props.visible && !loading.value,
	set: (value) => {
		emit("update:visible", value);
	},
});

// 计算属性用于处理联系方式字段
const contactValue = computed({
	get: () => {
		if (!userData.value) return "";
		return userData.value.phone_number || "";
	},
	set: (value: string) => {
		if (!userData.value) return;
		userData.value.phone_number = value;
	},
});

watch(
	() => props.userId,
	(newId) => {
		if (newId) {
			loadUserData(newId);
		}
	},
);

// 加载用户数据
const loadUserData = async (userId: number) => {
	loading.value = true;
	try {
		const data = await basicApi.getUserById(userId);
		userData.value = data;
	} catch (error) {
		console.error("Failed to load user details:", error);
		ElMessage.error("获取用户信息失败");
	} finally {
		loading.value = false;
	}
};

const fieldLabels: Record<string, string> = {
	phone_number: "手机号",
	id_number: "证件号码",
	bank_name: "银行名称",
	bank_code: "银行编号",
	bank_account: "银行账号",
};

// 验证用户数据
const validateUserData = () => {
	if (!userData.value) return false;

	if (contactValue.value) {
		// 手机号格式验证
		const phonePattern = /^1[3-9]\d{9}$/;
		if (!phonePattern.test(contactValue.value)) {
			ElMessage.warning("请输入有效的手机号");
			return false;
		}
	}

	return true;
};

const handleSave = async () => {
	if (!userData.value || !validateUserData()) return;

	try {
		const originalData = await basicApi.getUserById(userData.value.user_id);

		// 添加空值检查
		if (!originalData) {
			ElMessage.error("无法获取原始用户数据");
			return;
		}

		const changes = getChangedFields(originalData);

		if (Object.keys(changes).length === 0) {
			ElMessage.info("未检测到修改");
			return;
		}

		const confirmed = await ElMessageBox.confirm(
			generateChangeMessage(changes),
			"确认修改",
			{
				confirmButtonText: "保存",
				cancelButtonText: "取消",
				dangerouslyUseHTMLString: true,
				type: "warning",
			},
		);

		if (!confirmed) return;

		if (!userData.value) return;

		// 提交数据
		await qualifyApi.updateUserProfile(userData.value);

		ElMessage.success("用户资料更新成功");
		isDialogVisible.value = false;
		emit("saved");
	} catch (error) {
		console.error(error);
		ElMessage.error("用户资料更新失败");
	}
};

const getChangedFields = (original: UserInfo) => {
	if (!userData.value) return {};

	const changes: Record<
		string,
		{ old: string | number | null; new: string | number | null }
	> = {};
	const fieldsToCheck = [
		"phone_number",
		"id_number",
		"bank_name",
		"bank_code",
		"bank_account",
	] as const;

	for (const field of fieldsToCheck) {
		if (userData.value[field] !== original[field]) {
			changes[field] = {
				old: original[field] ?? null,
				new: userData.value[field] ?? null,
			};
		}
	}
	return changes;
};

const generateChangeMessage = (
	changes: Record<
		string,
		{ old: string | number | null; new: string | number | null }
	>,
) => {
	let message = '<div class="change-summary">';
	message += "<h4>以下字段将被更新：</h4>";
	message += "<ul>";

	for (const [field, values] of Object.entries(changes)) {
		message += `
      <li>
        <strong>${fieldLabels[field]}:</strong><br>
        <span class="old-value">原值: ${values.old || "(空)"}</span><br>
        <span class="new-value">新值: ${values.new || "(空)"}</span>
      </li>
    `;
	}

	message += "</ul></div>";
	return message;
};
</script>

<style scoped>
.user-profile-form {
	max-width: 800px;
	margin: 0 auto;
}

:deep(.el-form-item:nth-of-type(2)) {
	margin-top: 20px;
}

.balance-section {
	margin-top: 20px;
}

.balance-tags {
	display: flex;
	gap: 10px;
	margin-top: 10px;
	flex-wrap: wrap;
}

.dates-section {
	margin-top: 20px;
	color: var(--el-text-color-secondary);
	font-size: 14px;
}

.dates-section p {
	margin: 5px 0;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
}

:deep(.change-summary) {
	text-align: left;
}

:deep(.change-summary ul) {
	list-style: none;
	padding: 0;
}

:deep(.change-summary li) {
	margin: 10px 0;
	padding: 5px 0;
	border-bottom: 1px solid #eee;
}

:deep(.old-value) {
	color: #f56c6c;
}

:deep(.new-value) {
	color: #67c23a;
}

@media (max-width: 767px) {
	.balance-tags {
		flex-direction: column;
	}
}
</style>