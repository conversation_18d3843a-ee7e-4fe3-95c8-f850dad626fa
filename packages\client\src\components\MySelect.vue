<template>
  <div class="my-select-wrapper" role="combobox" :aria-expanded="isOpen ? 'true' : 'false'" :aria-haspopup="'listbox'"
    :aria-controls="`${id}-listbox`" v-click-outside="closeDropdown">
    <div class="selected-option" @click="focusInput">
      <div class="selected-tags">
        <span v-for="value in modelValue" :key="value" class="selected-tag">
          {{ getOptionLabel(value) }}
          <button class="remove-tag" @click.stop="removeValue(value)" tabindex="-1">
            <SvgIcon name="cross-small-rs-flaticon" />
          </button>
        </span>
      </div>
      <input ref="inputRef" :id="id" link v-model="searchText" @focus="handleFocus" @input="handleInput"
        @compositionstart="handleCompositionStart" @compositionend="handleCompositionEnd" @keydown="handleKeyDown"
        :placeholder="modelValue.length ? '' : placeholder" :aria-autocomplete="'list'" :aria-controls="`${id}-listbox`"
        :aria-activedescendant="highlightedOptionId" :aria-label="placeholder || 'Select an option'" />
    </div>
    <Transition name="options">
      <ul v-if="isOpen" :id="`${id}-listbox`" class="options" role="listbox" :aria-labelledby="id"
        @scroll="handleScroll">
        <li v-if="loading && filteredOptions.length === 0" class="loading-item">加载中...</li>
        <template v-else>
          <li v-if="filteredOptions.length === 0" class="empty-item">{{ searchText ? "无结果" : "无选项" }}</li>
          <li v-for="(option, index) in filteredOptions" :key="option.value" @click.stop="selectOption(option)"
            :id="`${id}-option-${index}`" :class="{
              selected: isSelected(option),
              highlighted: highlightedIndex === index
            }" :data-type="option.type" role="option" :aria-selected="isSelected(option)">
            {{ option.label }}
          </li>
          <li v-if="loading && hasMoreOptions" class="loading-more">加载中...</li>
        </template>
      </ul>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeUnmount, watch, nextTick } from "vue";
import { vClickOutside } from "@/utils/clickOutside";
import SvgIcon from "./SvgIcon.vue";

interface Option {
  value: string;
  label: string;
  type?: string;
}

const props = defineProps<{
  id: string;
  modelValue: string[];
  options: Option[];
  placeholder?: string;
  required?: boolean;
  loading?: boolean;
  pageSize?: number;
}>();

const emit = defineEmits(["update:modelValue", "search"]);

const isOpen = ref(false);
const searchText = ref("");
const currentOffset = ref(0);
const inputRef = ref<HTMLInputElement>();

// 添加防抖变量
let loadMoreTimer: number | null = null;

// 提取过滤逻辑为独立的计算属性
const filteredAllOptions = computed(() => {
  return props.options.filter(
    (option) =>
      option.label.toLowerCase().includes(searchText.value.toLowerCase()) &&
      !props.modelValue.includes(option.value),
  );
});

// 使用 filteredAllOptions 计算分页后的选项
const filteredOptions = computed(() => {
  if (props.pageSize) {
    return filteredAllOptions.value.slice(
      0,
      currentOffset.value + props.pageSize,
    );
  }
  return filteredAllOptions.value;
});

const getOptionLabel = (value: string) => {
  return props.options.find((opt) => opt.value === value)?.label || value;
};

const handleFocus = () => {
  isOpen.value = true;
};

const isComposing = ref(false);

const handleCompositionStart = () => {
  isComposing.value = true;
};

const handleCompositionEnd = (e: CompositionEvent) => {
  isComposing.value = false;
  // 在组合输入结束时触发搜索
  emit("search", (e.target as HTMLInputElement).value);
};

const handleInput = (e: Event) => {
  // 只在非组合输入状态下触发搜索
  if (!isComposing.value) {
    emit("search", (e.target as HTMLInputElement).value);
  }
};

const focusInput = () => {
  inputRef.value?.focus();
};

const closeDropdown = () => {
  isOpen.value = false;
  highlightedIndex.value = -1;
  // 只在有选中值时清空搜索文本
  if (props.modelValue.length > 0) {
    searchText.value = "";
  }
};

const selectOption = (option: Option) => {
  const newValue = [...props.modelValue];
  if (!newValue.includes(option.value)) {
    newValue.push(option.value);
    emit("update:modelValue", newValue);
  }
  searchText.value = "";
  inputRef.value?.focus();
};

const removeValue = (value: string) => {
  const newValue = props.modelValue.filter((v) => v !== value);
  emit("update:modelValue", newValue);
};

const isSelected = computed(() => (option: Option) => {
  return props.modelValue.includes(option.value);
});

// 修改 hasMoreOptions 的计算逻辑
const hasMoreOptions = computed(() => {
  if (!props.pageSize) {
    return false;
  }
  return filteredAllOptions.value.length > currentOffset.value + props.pageSize;
});

// 当搜索文本改变时重置 offset
watch(searchText, () => {
  currentOffset.value = 0;
});

const handleScroll = (e: Event) => {
  const target = e.target as HTMLElement;
  const { scrollTop, scrollHeight, clientHeight } = target;

  // 清除之前的定时器
  if (loadMoreTimer) {
    clearTimeout(loadMoreTimer);
  }

  // 使用 setTimeout 防抖
  loadMoreTimer = window.setTimeout(() => {
    if (
      scrollHeight - scrollTop - clientHeight < 20 &&
      !props.loading &&
      hasMoreOptions.value
    ) {
      currentOffset.value += props.pageSize || 0;
    }
  }, 100);
};

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  if (loadMoreTimer) {
    clearTimeout(loadMoreTimer);
  }
});

const highlightedIndex = ref(-1);
const highlightedOptionId = computed(() => {
  return highlightedIndex.value >= 0
    ? `${props.id}-option-${highlightedIndex.value}`
    : undefined;
});

const handleKeyDown = (e: KeyboardEvent) => {
  if (!isOpen.value && e.key !== "Enter" && e.key !== " ") {
    // 如果按下 Tab 键且下拉框未打开，让事件继续冒泡
    if (e.key === "Tab") {
      return;
    }
    return;
  }

  switch (e.key) {
    case "ArrowDown":
      e.preventDefault();
      if (!isOpen.value) {
        isOpen.value = true;
      } else {
        highlightedIndex.value = Math.min(
          highlightedIndex.value + 1,
          filteredOptions.value.length - 1,
        );
        if (highlightedIndex.value === -1 && filteredOptions.value.length > 0) {
          highlightedIndex.value = 0;
        }
        scrollToHighlighted();
      }
      break;

    case "ArrowUp":
      e.preventDefault();
      if (isOpen.value) {
        highlightedIndex.value = Math.max(highlightedIndex.value - 1, 0);
        scrollToHighlighted();
      }
      break;

    case "Enter":
      e.preventDefault();
      if (isOpen.value && highlightedIndex.value >= 0) {
        selectOption(filteredOptions.value[highlightedIndex.value]);
      } else if (!isOpen.value) {
        isOpen.value = true;
      }
      break;

    case "Escape":
      e.preventDefault();
      closeDropdown();
      break;

    case "Tab":
      if (isOpen.value) {
        e.preventDefault();
        closeDropdown();
      }
      break;
  }
};

const scrollToHighlighted = () => {
  nextTick(() => {
    const highlightedEl = document.getElementById(
      `${props.id}-option-${highlightedIndex.value}`,
    );
    if (highlightedEl) {
      highlightedEl.scrollIntoView({ block: "nearest" });
    }
  });
};

// 监听搜索文本变化，重置高亮索引
watch(searchText, () => {
  highlightedIndex.value = -1;
  currentOffset.value = 0;
});
</script>

<style scoped>
.my-select-wrapper {
  position: relative;
  min-width: 200px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 选择框样式 */
.selected-option {
  min-width: 206px;
  max-width: 320px;
  min-height: 32px;
  padding: 4px 12px;
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
  border-radius: var(--el-border-radius-base);
  display: flex;
  gap: 0;
  align-items: center;
  background-color: var(--el-fill-color-light);
  cursor: text;
  flex-wrap: wrap;
  transition: all 0.2s ease;
}

.selected-option:hover:not(:focus-within) {
  box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
}

.selected-option:focus-within {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.selected-tag {
  background-color: var(--el-color-primary-light-8);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.remove-tag {
  border: none;
  background: none;
  padding: 0;
  font-size: 14px;
  line-height: 1;
  cursor: pointer;
  fill: var(--el-text-color-primary);
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  opacity: 0.6;
  transition: all 0.2s ease;
}

.remove-tag .svg-icon {
  width: 14px;
  height: 14px;
}

.remove-tag:hover {
  opacity: 1;
  background-color: var(--el-color-primary-light-5);
  fill: var(--el-color-error);
}

input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 60px;
  background: transparent;
  padding: 4px;
  height: 24px;
}

.options {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  right: 0;
  background-color: var(--el-fill-color-light);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  list-style: none;
  margin: 0;
  padding: 6px 0;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(var(--shadow-color), 0.15);
  width: 200px;

  /* 添加过渡效果 */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
  opacity: 1;
  transform: translateY(0);
}

/* 添加 v-if 的进入和离开动画 */
.options-enter-from,
.options-leave-to {
  opacity: 0;
  transform: translateY(-8px);
}

.options-enter-active,
.options-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.options li {
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.options li:hover {
  background-color: var(--el-color-primary-light-8);
}

.options li.selected {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

/* 滚动条 */
.options {
  scrollbar-color: rgba(var(--shadow-color), 0.5) transparent;
}

.options::-webkit-scrollbar {
  width: 4px;
}

.options::-webkit-scrollbar-track {
  background: transparent;
}

.options::-webkit-scrollbar-thumb {
  background-color: rgba(var(--shadow-color), 0.2);
  border-radius: 2px;
}

.options::-webkit-scrollbar-thumb:hover {
  background-color: rgba(var(--shadow-color), 0.3);
}

/* 隐藏滚动条但保留功能 */
.options {
  overflow-y: auto;
}

/* 在悬停时显示滚动条 */
.options:hover::-webkit-scrollbar-thumb {
  background-color: rgba(var(--shadow-color), 0.2);
}

.options:not(:hover)::-webkit-scrollbar-thumb {
  background-color: transparent;
}

.options:not(:hover)::-webkit-scrollbar {
  width: 6px;
}

.loading-item,
.empty-item,
.loading-more {
  text-align: center;
  padding: 12px;
  color: var(--color-text-secondary);
  font-size: 13px;
  cursor: default;
}

/* 确保空状态不会有hover效果 */
.empty-item:hover {
  background-color: transparent;
}

.loading-more {
  text-align: center;
  padding: 8px;
  color: var(--color-text-secondary);
  font-size: 12px;
  cursor: default;
}

.loading-more:hover {
  background-color: transparent;
}

.options li.highlighted {
  background-color: var(--el-color-primary-light-8);
}

.options li.selected.highlighted {
  background-color: var(--el-color-primary-light-3);
}
</style>
