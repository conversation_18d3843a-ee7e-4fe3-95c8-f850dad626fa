<template>
  <div class="svg-icon" :class="{ active }" v-html="svgContent" />
</template>

<script setup lang="ts">
import { ref, watchEffect } from "vue";

interface Props {
	name: string;
	active?: boolean;
	activeName?: string;
}

const props = defineProps<Props>();

const svgContent = ref("");

watchEffect(async () => {
	let svg: { default: string };
	const iconName =
		props.active && props.activeName ? props.activeName : props.name;

	svg = await import(`../assets/icons/${iconName}.svg`);

	const parser = new DOMParser();
	const svgDoc = parser.parseFromString(svg.default, "image/svg+xml");
	const svgElement = svgDoc.documentElement;
	svgContent.value = new XMLSerializer().serializeToString(svgElement);
});
</script>

<style scoped>
.svg-icon {
  display: inline-flex;
  vertical-align: middle;
}

.svg-icon :deep(svg) {
  width: 100%;
  height: 100%;
  fill: currentColor;
}

.svg-icon :deep(svg:not(.exception-stroke-width)) {
  stroke-width: 0;
}
</style>
