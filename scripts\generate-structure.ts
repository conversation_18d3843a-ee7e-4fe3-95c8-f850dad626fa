import dirTree from "directory-tree";
import { writeFileSync } from "node:fs";

const tree = dirTree(".", {
	exclude: [
		// 构建和缓存文件
		/node_modules/,
		/dist/,
		/\.git/,
		/coverage/,
		/\.cache/,
		/\.output/,
		/build/,

		// 类型声明文件
		/\.d\.ts$/,
		/\.d\.ts\.map$/,
		/types\/(?!index\.ts|env\.ts|api\.ts|common\.ts)/, // 保留关键类型定义文件

		// 临时文件和日志
		/\.log$/,
		/logs\//,
		/tmp\//,

		// IDE 和编辑器配置
		/\.idea/,
		/\.vscode/,

		// 构建产物
		/\.tsbuildinfo$/,

		// 资源文件
		/\.(png|jpe?g|gif|svg|woff2?|ttf|eot)$/,

		// 配置文件
		/\.env.*/,
		/\.eslintrc.*/,
		/\.prettierrc.*/,
		/\.browserslistrc/,

		// 锁文件
		/pnpm-lock\.yaml/,
		/package-lock\.json/,
		/yarn\.lock/,
	],
	normalizePath: true,
	attributes: ["type", "extension"],
});

const formatTree = (
	node: dirTree.DirectoryTree,
	prefix = "",
	level = 0,
): string => {
	// 跳过空目录
	if (node.children?.length === 0) return "";

	const isLast = prefix.includes("└─");
	const indent = prefix.replace(/[└├]─\s/g, isLast ? "   " : "│  ");

	// 添加目录描述
	const description = getDirectoryDescription(node.name, node.path);
	let output = `${prefix}${node.name}${description ? `# ${description}` : ""}\n`;

	if (node.children) {
		const sortedChildren = node.children
			.sort((a, b) => {
				// 优先显示目录
				if (a.type !== b.type) return a.type === "directory" ? -1 : 1;
				// 关键目录排在前面
				const priority = getPriority(a.name);
				const otherPriority = getPriority(b.name);
				if (priority !== otherPriority) return priority - otherPriority;
				// 按字母排序
				return a.name.localeCompare(b.name);
			})
			// 过滤掉不需要显示的文件
			.filter((child) => shouldShowFile(child.name, level));

		sortedChildren.forEach((child, index) => {
			const isLastChild = index === sortedChildren.length - 1;
			const newPrefix = indent + (isLastChild ? "└─ " : "├─ ");
			output += formatTree(child, newPrefix, level + 1);
		});
	}

	return output;
};

function getDirectoryDescription(name: string, path: string): string {
	const descriptions: Record<string, string> = {
		src: "源代码目录",
		shared: "共享代码",
		types: "类型定义",
		components: "可复用组件",
		utils: "工具函数",
		services: "业务服务",
		models: "数据模型",
		routes: "路由定义",
		controllers: "控制器",
		middlewares: "中间件",
		config: "配置文件",
		tests: "测试文件",
		views: "页面组件",
		assets: "静态资源",
	};
	return descriptions[name] || "";
}

function getPriority(name: string): number {
	const priorities: Record<string, number> = {
		src: 100,
		shared: 90,
		types: 80,
		components: 70,
		services: 60,
		models: 50,
		routes: 40,
		controllers: 30,
		config: 20,
	};
	return priorities[name] || 0;
}

function shouldShowFile(name: string, level: number): boolean {
	// 根级别显示所有主要目录
	if (level === 0) return true;

	// 忽略特定文件
	const ignoreFiles = [
		"README.md",
		"LICENSE",
		"CHANGELOG.md",
		"vite.config.ts",
		"tsconfig.json",
	];

	return !ignoreFiles.includes(name);
}

const content = `# Project Structure

\`\`\`
${formatTree(tree)}
\`\`\`

## Key Directories

### packages/client
用户前端应用，包含用户界面和交互逻辑。

### packages/admin
管理后台界面，用于系统管理和监控。

### packages/server
后端服务实现，处理业务逻辑和数据存储。

### packages/shared
共享代码，包含类型定义、工具函数和常量。

## Development

\`\`\`bash
# 安装依赖
pnpm install

# 启动开发服务
pnpm dev

# 构建项目
pnpm build

# 运行测试
pnpm test
\`\`\`
`;

writeFileSync("STRUCTURE.md", content);
console.log("Successfully generated STRUCTURE.md");
