import { request } from "./request";
import type {
	QualificationAuditData,
	UserInfo,
	AuditStatus,
} from "@packages/shared";
import type { QueryOptions } from "./types";

// 资质审核相关 API
export const qualifyApi = {
	getQualificationsAudits: (options?: QueryOptions) =>
		request.get<{ items: QualificationAuditData[]; total: number }>(
			"/admin/qualify/audits",
			{
				params: {
					status: options?.filters?.status,
					user_id: options?.filters?.user_id,
					page: options?.page,
					pageSize: options?.pageSize,
					sortBy: options?.sortBy,
					sortOrder: options?.sortOrder,
				},
			},
		),

	processQualificationAudit: (
		auditId: number,
		status: AuditStatus,
		comment?: string,
	) =>
		request.post("/admin/qualify/audits", {
			audit_id: auditId,
			status,
			comment,
		}),

	updateUserProfile: (user: Omit<UserInfo, "email">) =>
		request.post("/admin/qualify/user-profile", user),

	// 获取用户签名数据
	getUserSignature: (userId: number) =>
		request.get<string>(`/admin/qualify/signature/${userId}`),

	// 获取用户上传的证件文件
	downloadQualificationDocuments: (
		documents: Array<{ uid: string; name: string }>,
	) =>
		request.post<Blob>(
			"/admin/qualify/download-documents",
			{ documents },
			{ responseType: "blob" },
		),
} as const;
