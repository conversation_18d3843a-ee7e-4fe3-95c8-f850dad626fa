/**
 * 计算给定日期后指定期限的到期日
 * @param startDate 起始日期
 * @param term 期限 (1, 2, 3 代表月数; 14 代表 14 天)
 * @returns 到期日
 */
export function calculateExpiryDate(startDate: Date, term: number): Date {
	const result = new Date(startDate);

	if (term === 14) {
		// 如果期限是 14，则增加 14 天
		result.setDate(result.getDate() + 14);
		// setDate 会自动处理月末溢出
	} else if ([1, 2, 3].includes(term)) {
		// 如果期限是 1, 2, 或 3，则增加相应的月数
		result.setMonth(result.getMonth() + term);

		// 处理月末日期的特殊情况
		// 例如：1月31日 + 1个月，应该返回2月28日/29日
		const originalDate = startDate.getDate();
		if (result.getDate() !== originalDate) {
			// 如果日期不一致，说明发生了月末溢出，将日期设置为上个月的最后一天
			result.setDate(0);
		}
	} else {
		// 处理无效的 term 值，抛出错误
		throw new Error(`Invalid term: ${term}`);
	}

	return result;
}
