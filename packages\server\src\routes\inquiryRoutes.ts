import { Router } from "express";
import * as inquiryService from "@/services/inquiryService.js";
import { wrapUserRoute } from "@/utils/routeWrapper.js";
import type { BatchQuoteRequest } from "@packages/shared";
import { batchQuoteRequestSchema } from "@packages/shared";
import { validateSchema } from "@/middlewares/validateSchema.js";
import logger from "@/utils/logger.js";

interface SearchQuery {
	query: string;
	offset: string;
}

const router = Router();

/**
 * Search stocks route: GET /api/inquiry/search
 */
router.get(
	"/search",
	wrapUserRoute<SearchQuery>(async (req, res) => {
		const { query, offset } = req.query;
		const result = await inquiryService.searchStocks(
			query as string,
			Number.parseInt(offset as string) || 0,
		);
		res.status(200).json(result);
	}),
);

/**
 * Process quote request route: POST /api/inquiry/quotes
 */
router.post(
	"/quotes",
	validateSchema(batchQuoteRequestSchema),
	wrapUserRoute<BatchQuoteRequest>(async (req, res) => {
		const startTime = performance.now();

		// 记录请求信息
		logger.info(
			`Processing batch quotes request with ${req.body.quotes.length} quotes`,
		);

		const result = await inquiryService.processBatchQuotes({
			quotes: req.body.quotes,
			user_id: req.jwt.user_id,
		});

		logger.info(
			`PERF: Total batch quotes processing took ${performance.now() - startTime}ms`,
		);
		res.status(200).json(result);
	}),
);

/**
 * Get user inquiries route: GET /api/inquiry/list
 */
router.get(
	"/list",
	wrapUserRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const isDescending = req.query.isDescending !== "false";
		const { ts_codes } = req.query;

		const result = await inquiryService.getUserInquiries(
			req.jwt.user_id,
			page,
			pageSize,
			isDescending,
			{
				ts_codes: ts_codes ? (ts_codes as string).split(",") : undefined,
			},
		);
		res.status(200).json(result);
	}),
);

export default router;
