import { request } from "./request";
import type {
	BuyRequest,
	SellRequest,
	OrderResponse,
	OrderData,
	SettledOrderData,
	BuyingOrderData,
	SellingOrderData,
	UserInfo,
} from "@packages/shared";

// 交易相关 API
export const tradeApi = {
	createOrder: (data: BuyRequest) =>
		request.post<OrderResponse>("/trade/order", data),

	settleOrder: (data: SellRequest) =>
		request.post<OrderResponse>("/trade/settle", data),

	getOrderHistory: (
		page: number,
		pageSize: number,
		isDescending: boolean,
		filters?: { ts_codes?: string[]; startDate?: string; endDate?: string },
	) =>
		request.get<{ items: OrderData[]; total: number; ts_codes: string[] }>(
			"/trade/history/order",
			{
				params: {
					page,
					pageSize,
					isDescending,
					ts_codes: filters?.ts_codes?.join(","),
					startDate: filters?.startDate,
					endDate: filters?.endDate,
				},
			},
		),

	getSettleHistory: (
		page: number,
		pageSize: number,
		isDescending: boolean,
		filters?: { ts_codes?: string[]; startDate?: string; endDate?: string },
	) =>
		request.get<{
			items: SettledOrderData[];
			total: number;
			ts_codes: string[];
		}>("/trade/history/settle", {
			params: {
				page,
				pageSize,
				isDescending,
				ts_codes: filters?.ts_codes?.join(","),
				startDate: filters?.startDate,
				endDate: filters?.endDate,
			},
		}),

	getPendingBuyHistory: () =>
		request.get<BuyingOrderData[]>("/trade/list/buying"),

	getPendingSellHistory: () =>
		request.get<SellingOrderData[]>("/trade/list/selling"),

	getPendingOrders: (trade_no: string) =>
		request.get<SellingOrderData[]>("/trade/pending", {
			params: { trade_no },
		}),

	cancelPendingOrder: (data: { pending_id: number }) =>
		request.post("/trade/pending/cancel", data),

	getUserInfo: () => request.get<UserInfo>("/trade/user"),
} as const;
