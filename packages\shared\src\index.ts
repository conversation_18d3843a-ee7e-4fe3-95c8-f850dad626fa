// 错误
export * from "./errorCore.js";
// 类型
export * from "./types/auth.js";
export * from "./types/order.js";
export * from "./types/fund.js";
export * from "./types/admin.js";
export * from "./types/config.js";
export * from "./types/inquiry.js";
export * from "./types/user.js";
export * from "./types/position.js";
export * from "./types/audit.js";
export * from "./types/notification.js";
export * from "./types/websocket.js";
export * from "./types/channel.js";
export * from "./types/bankAccount.js";
export * from "./types/siteConfig.js";
export * from "./types/manualOrder.js";

// 验证
export * from "./schemas/inquiry.js";

// 工具
export * from "./utils/date.js";

// 可用性
export * from "./availability.js";

// 应用版本
export enum AppType {
	TRADING_PLATFORM = "trading_platform", // 交易台版本
	CHANNEL = "channel", // 通道版本基础类型
}

// 通用响应类型
export interface ApiResponse<T> {
	code: number;
	data: T;
	message: string;
}

export interface HealthCheckResponse {
	status: string;
	timestamp: string;
	uptime: number;
	environment: string;
	system_enabled: boolean;
	position_entry_enabled: boolean;
	inquiry_enabled: boolean;
}

export interface ExchangeRateResult {
	HKD_CNY: number;
	USD_CNY: number;
}

// System Health Check Types
export enum HealthCheckStatus {
	SUCCESS = "success",
	FAILURE = "failure",
	PARTIAL = "partial",
	RUNNING = "running",
}

export interface SystemHealthCheckData {
	check_id?: number;
	timestamp?: Date;
	test_type: string;
	status: string;
	details?: Record<string, unknown>;
	execution_time?: number;
	error_message?: string;
}

export enum HealthCheckTestType {
	INQUIRY = "inquiry_process",
	ORDER_CREATION = "order_creation",
	FULL_FLOW = "full_business_flow",
	DATABASE = "database_connectivity",
	REDIS = "redis_connectivity",
	SYSTEM_INIT = "system_initialization",
}
