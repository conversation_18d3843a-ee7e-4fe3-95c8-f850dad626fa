import axios from "axios";
import { ENV } from "@/config/configManager.js";
import logger from "@/utils/logger.js";

async function sleep(ms: number): Promise<void> {
	return new Promise((resolve) => setTimeout(resolve, ms));
}

async function fetchStockList(): Promise<{ ts_code: string }[]> {
	const response = await axios.post("http://api.tushare.pro", {
		api_name: "stock_basic",
		token: ENV.TUSHARE_TOKEN,
		params: { list_status: "L" },
		fields: "ts_code",
	});

	if (!response?.data?.data?.items) {
		throw new Error("Invalid stock list response");
	}

	return response.data.data.items.map((item: [string]) => ({
		ts_code: item[0],
	}));
}

async function fetchCurrentPrices(
	ts_codes: string[],
): Promise<Map<string, number>> {
	try {
		const s_queries = ts_codes
			.map((code) => `s_${code.toLowerCase().split(".").reverse().join("")}`)
			.join(",");

		const response = await axios.get(`https://qt.gtimg.cn/q=${s_queries}`);

		if (!response.data) {
			throw new Error("Current day data not found");
		}

		const stocksData = response.data.trim().split(";").filter(Boolean);
		logger.info(
			`Batch request size: ${ts_codes.length}, response items: ${stocksData.length}`,
		);

		if (stocksData.length !== ts_codes.length) {
			logger.warn(
				{ ts_codes, stocksData },
				`Mismatch between request (${ts_codes.length}) and response (${stocksData.length}) size`,
			);
		}

		const resultMap = new Map<string, number>();

		stocksData.forEach((stockData: string, index: number) => {
			const data = stockData.split("~");
			resultMap.set(ts_codes[index], Number.parseFloat(data[3]));
		});

		return resultMap;
	} catch (error) {
		logger.error(error, "Error fetching current prices");
		throw error;
	}
}

async function testBatchSize(
	stocks: string[],
	batchSize: number,
	intervalMs: number,
): Promise<{ success: boolean; time: number }> {
	try {
		const startTime = Date.now();
		const batches = Math.ceil(stocks.length / batchSize);

		for (let i = 0; i < stocks.length; i += batchSize) {
			const batchStocks = stocks.slice(i, i + batchSize);
			logger.info(
				`Starting batch ${Math.floor(i / batchSize) + 1}/${batches} with ${batchStocks.length} stocks`,
			);
			await fetchCurrentPrices(batchStocks);

			if (i + batchSize < stocks.length) {
				await sleep(intervalMs);
			}
		}

		const duration = Date.now() - startTime;
		return { success: true, time: duration };
	} catch (error) {
		logger.error(error, `Test failed for batch size ${batchSize}`);
		return { success: false, time: 0 };
	}
}

async function runTests() {
	try {
		const stocks = await fetchStockList();
		const stockCodes = stocks.map((stock) => stock.ts_code);

		// 测试不同的批量大小
		const batchSizes = [20, 50, 100, 200];
		// 测试不同的请求间隔（毫秒）
		const intervals = [0];

		logger.info(`Total stocks to test: ${stockCodes.length}`);

		for (const batchSize of batchSizes) {
			for (const interval of intervals) {
				logger.info(
					`Testing batch size: ${batchSize}, interval: ${interval}ms`,
				);

				const result = await testBatchSize(stockCodes, batchSize, interval);

				if (result.success) {
					logger.info(
						`✅ Batch size ${batchSize} with ${interval}ms interval: ` +
							`${result.time}ms total, ` +
							`${result.time / Math.ceil(stockCodes.length / batchSize)}ms per batch`,
					);
				} else {
					logger.error(
						`❌ Failed with batch size ${batchSize} and interval ${interval}ms`,
					);
				}

				// 在不同批量大小的测试之间添加较长的间隔
				await sleep(2000);
			}
		}
	} catch (error) {
		logger.error(error, "Test failed");
	}
}

// 运行测试
runTests().then(() => {
	logger.info("All tests completed");
	process.exit(0);
});
