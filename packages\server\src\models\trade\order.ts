import prisma from "@/lib/prisma.js";
import type { Prisma, order_status } from "@prisma/client";
import { AppError } from "@/core/appError.js";
import { withTransaction } from "@/core/dbTxnManager.js";
import type {
	OrderData,
	OrderModification,
	OrderModificationData,
	OrderModificationType,
	StructureType,
} from "@packages/shared";
import { OrderStatus } from "@packages/shared";
import { calcBuyAmount } from "@/financeUtils/calculator.js";

export interface PrismaOrderData {
	trade_no: string;
	user_id: number;
	ts_code: string;
	entry_price: Prisma.Decimal;
	exercise_price: Prisma.Decimal;
	settle_price: Prisma.Decimal;
	scale: number;
	total_scale: number;
	term: number;
	quote: Prisma.Decimal;
	status: order_status;
	created_at: Date | null;
	closed_at: Date | null;
	is_split: boolean;
	structure: string;
	expiry_date: Date | null;
	expiry_date_confirmed: boolean | null;
	quote_provider?: string | null;
	quote_diff?: Prisma.Decimal | null;
}

export function transformOrderData(o: PrismaOrderData): OrderData {
	return {
		...o,
		entry_price: Number(o.entry_price),
		exercise_price: Number(o.exercise_price),
		settle_price: Number(o.settle_price),
		quote: Number(o.quote),
		status: o.status as OrderStatus,
		created_at: o.created_at?.toISOString() || "",
		closed_at: o.closed_at?.toISOString() || "",
		structure: o.structure as StructureType,
		expiry_date: o.expiry_date?.toISOString() || "",
		expiry_date_confirmed: o.expiry_date_confirmed || false,
		quote_provider: o.quote_provider ?? "INK",
		quote_diff: o.quote_diff ? Number(o.quote_diff) : 0,
	};
}

async function generateTradeNo(tx: Prisma.TransactionClient): Promise<string> {
	const date = new Date();
	const dateStr =
		date.getFullYear().toString() +
		(date.getMonth() + 1).toString().padStart(2, "0") +
		date.getDate().toString().padStart(2, "0");

	const sequence = await tx.order_sequences.upsert({
		where: { date_str: dateStr },
		create: { date_str: dateStr, last_sequence: 1 },
		update: { last_sequence: { increment: 1 } },
	});

	if (sequence.last_sequence >= 9999) {
		throw AppError.create("BAD_REQUEST", "Daily order sequence limit exceeded");
	}
	return `TR${dateStr}${sequence.last_sequence.toString().padStart(4, "0")}`;
}

async function generateSplitTradeNo(
	originalTradeNo: string,
	tx: Prisma.TransactionClient,
): Promise<string> {
	const lastSplitOrder = await tx.orders.findFirst({
		where: {
			trade_no: { startsWith: `${originalTradeNo}_` },
		},
		orderBy: { trade_no: "desc" },
	});

	if (!lastSplitOrder) {
		return `${originalTradeNo}_01`;
	}

	const lastSuffix = Number.parseInt(lastSplitOrder.trade_no.split("_")[1]);
	return `${originalTradeNo}_${(lastSuffix + 1).toString().padStart(2, "0")}`;
}

export async function create(
	data: Omit<OrderData, "trade_no" | "created_at"> & {
		trade_no?: string;
		created_at?: string;
		closed_at?: string;
		is_split?: boolean;
		// 这些字段会从请求中传入，但不应该保存到数据库
		type?: string;
		direction?: string;
		inquiry_id?: number;
	},
	client?: Prisma.TransactionClient,
): Promise<OrderData> {
	const createFn = async (tx: Prisma.TransactionClient) => {
		const trade_no =
			data.is_split && data.trade_no
				? await generateSplitTradeNo(data.trade_no, tx)
				: await generateTradeNo(tx);

		// 过滤掉不需要的字段
		const { type, direction, inquiry_id, ...orderData } = data;

		const order = await tx.orders.create({
			data: {
				...orderData,
				trade_no, // 覆盖传入的 trade_no
				expiry_date: new Date(orderData.expiry_date),
				created_at: orderData.created_at
					? new Date(orderData.created_at)
					: undefined,
				closed_at: orderData.closed_at
					? new Date(orderData.closed_at)
					: undefined,
			},
		});

		return transformOrderData(order);
	};
	return client ? createFn(client) : withTransaction(createFn);
}

export async function update(
	tradeNo: string,
	updates: Partial<OrderData>,
	client?: Prisma.TransactionClient,
): Promise<OrderData> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		// Only allow specific fields to be updated
		const allowedFields = [
			// Fields from OrderManagementView
			"entry_price",
			"quote",
			"scale",
			"term",
			"structure",
			// Additional allowed fields
			"expiry_date",
			"expiry_date_confirmed",
			"status",
			"settle_price",
			// 分红调整开仓执行价是独立函数 updatePricesByStock
		];

		// Filter updates to only include allowed fields
		const filteredUpdates: Record<string, unknown> = {};
		for (const key of Object.keys(updates)) {
			if (allowedFields.includes(key)) {
				filteredUpdates[key] = updates[key as keyof OrderData];
			}
		}

		const updateData: Prisma.ordersUpdateInput = {
			...filteredUpdates,
			closed_at: updates.status === OrderStatus.SOLD ? new Date() : undefined,
		};

		// 如果修改了开仓价或结构，需要重新计算执行价
		if (updates.entry_price !== undefined || updates.structure !== undefined) {
			const order = await tx.orders.findUnique({
				where: { trade_no: tradeNo },
				select: { entry_price: true, structure: true },
			});
			if (!order) {
				throw AppError.create("NOT_FOUND", `Order not found for ${tradeNo}`);
			}

			updateData.exercise_price =
				((updates.entry_price ?? Number(order.entry_price)) *
					Number.parseInt(updates.structure ?? order.structure)) /
				100;
		}

		const order = await tx.orders.update({
			where: { trade_no: tradeNo },
			data: updateData,
		});

		return transformOrderData(order);
	};
	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function findByTradeNo(
	trade_no: string,
	client?: Prisma.TransactionClient,
): Promise<OrderData & { trade_no: string }> {
	const findFn = async (tx: Prisma.TransactionClient) => {
		const order = await tx.orders.findUnique({
			where: { trade_no },
		});

		if (!order) {
			throw AppError.create("NOT_FOUND", `Order not found for ${trade_no}`);
		}

		return transformOrderData(order);
	};
	return client ? findFn(client) : withTransaction(findFn);
}

function adjustDateRange(filters?: {
	startDate?: string;
	endDate?: string;
}): { startDate?: Date; endDate?: Date } {
	if (!filters) return {};

	return {
		startDate: filters.startDate ? new Date(filters.startDate) : undefined,
		// 如果有结束日期，加一天
		endDate: filters.endDate
			? new Date(new Date(filters.endDate).getTime() + 86400000)
			: undefined,
	};
}

export async function getOrderHistory(
	user_id: number,
	offset: number,
	limit: number,
	isDescending: boolean,
	filters?: {
		ts_codes?: string[];
		startDate?: string;
		endDate?: string;
	},
): Promise<{ items: OrderData[]; total: number }> {
	const { startDate, endDate } = adjustDateRange(filters);

	const [orders, total] = await Promise.all([
		prisma.orders.findMany({
			where: {
				user_id,
				...(filters?.ts_codes?.length
					? { ts_code: { in: filters.ts_codes } }
					: {}),
				created_at: { gte: startDate, lt: endDate },
			},
			orderBy: { created_at: isDescending ? "desc" : "asc" },
			skip: offset,
			take: limit,
		}),
		prisma.orders.count({
			where: {
				user_id,
				...(filters?.ts_codes?.length
					? { ts_code: { in: filters.ts_codes } }
					: {}),
				created_at: { gte: startDate, lt: endDate },
			},
		}),
	]);

	return {
		items: orders.map(transformOrderData),
		total,
	};
}

export async function getSettleHistory(
	user_id: number,
	offset: number,
	limit: number,
	isDescending: boolean,
	filters?: {
		ts_codes?: string[];
		startDate?: string;
		endDate?: string;
	},
): Promise<{ items: OrderData[]; total: number }> {
	const { startDate, endDate } = adjustDateRange(filters);

	const [orders, total] = await Promise.all([
		prisma.orders.findMany({
			where: {
				user_id,
				status: OrderStatus.SOLD,
				...(filters?.ts_codes?.length
					? { ts_code: { in: filters.ts_codes } }
					: {}),
				closed_at: { gte: startDate, lt: endDate },
			},
			orderBy: { closed_at: isDescending ? "desc" : "asc" },
			skip: offset,
			take: limit,
		}),
		prisma.orders.count({
			where: {
				user_id,
				status: OrderStatus.SOLD,
				...(filters?.ts_codes?.length
					? { ts_code: { in: filters.ts_codes } }
					: {}),
				closed_at: {
					gte: startDate,
					lt: endDate,
				},
			},
		}),
	]);

	return {
		items: orders.map(transformOrderData),
		total,
	};
}

export async function countOrderHistory(
	user_id: number,
	filters?: {
		ts_codes?: string[];
		startDate?: string;
		endDate?: string;
	},
): Promise<number> {
	const { startDate, endDate } = adjustDateRange(filters);

	return prisma.orders.count({
		where: {
			user_id,
			ts_code: filters?.ts_codes?.length ? { in: filters.ts_codes } : undefined,
			created_at: { gte: startDate, lt: endDate },
		},
	});
}

export async function countSettleHistory(
	user_id: number,
	filters?: {
		ts_codes?: string[];
		startDate?: string;
		endDate?: string;
	},
): Promise<number> {
	const { startDate, endDate } = adjustDateRange(filters);

	return prisma.orders.count({
		where: {
			user_id,
			status: OrderStatus.SOLD,
			ts_code: filters?.ts_codes?.length ? { in: filters.ts_codes } : undefined,
			closed_at: { gte: startDate, lt: endDate },
		},
	});
}

export async function countByDateRange(
	start: Date,
	end: Date,
): Promise<number> {
	return prisma.orders.count({
		where: {
			created_at: { gte: start, lt: end },
		},
	});
}

export async function getVolumeByDateRange(
	start: Date,
	end: Date,
): Promise<number> {
	const orders = await prisma.orders.findMany({
		where: {
			created_at: { gte: start, lt: end },
		},
		select: {
			scale: true,
			quote: true,
		},
	});

	// 对每个订单使用 calcBuyAmount 计算交易额并求和
	return orders.reduce((sum, order) => {
		return sum + calcBuyAmount(order.scale, Number(order.quote));
	}, 0);
}

export async function getTotalVolume(): Promise<number> {
	const orders = await prisma.orders.findMany({
		select: {
			scale: true,
			quote: true,
		},
	});

	// 对每个订单使用 calcBuyAmount 计算交易额并求和
	return orders.reduce((sum, order) => {
		return sum + calcBuyAmount(order.scale, Number(order.quote));
	}, 0);
}

interface OrderQueryOptions {
	page?: number;
	pageSize?: number;
	sortBy?: string;
	sortOrder?: "ASC" | "DESC";
	filters?: {
		user_id?: number;
		ts_codes?: string[];
		startDate?: string;
		endDate?: string;
		status?: OrderStatus;
		// 可以根据需要添加更多过滤条件
	};
}

export async function getAll(
	options: OrderQueryOptions = {},
): Promise<{ total: number; items: OrderData[] }> {
	const {
		page = 1,
		pageSize = 10,
		sortBy = "created_at",
		sortOrder = "DESC",
		filters = {},
	} = options;
	const { startDate, endDate } = adjustDateRange(filters);

	const where: Prisma.ordersWhereInput = {
		user_id: filters.user_id,
		ts_code: filters.ts_codes?.length ? { in: filters.ts_codes } : undefined,
		created_at: { gte: startDate, lt: endDate },
		status: filters.status,
	};

	const [total, items] = await Promise.all([
		prisma.orders.count({ where }),
		prisma.orders.findMany({
			where,
			orderBy: [{ [sortBy]: sortOrder.toLowerCase() }, { created_at: "desc" }],
			skip: (page - 1) * pageSize,
			take: pageSize,
		}),
	]);

	return {
		total,
		items: items.map(transformOrderData),
	};
}

export async function updatePricesByStock(
	ts_code: string,
	ratio: number,
	client?: Prisma.TransactionClient,
): Promise<void> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		await tx.orders.updateMany({
			where: {
				ts_code,
				status: {
					not: OrderStatus.SOLD,
				},
			},
			data: {
				entry_price: {
					multiply: ratio,
				},
				exercise_price: {
					multiply: ratio,
				},
			},
		});
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function getAllOrderTsCodes(user_id: number): Promise<string[]> {
	const orders = await prisma.orders.findMany({
		where: { user_id },
		select: { ts_code: true },
		distinct: ["ts_code"],
		orderBy: { ts_code: "asc" },
	});

	return orders.map((order) => order.ts_code);
}

export async function getAllSettleTsCodes(user_id: number): Promise<string[]> {
	const orders = await prisma.orders.findMany({
		where: {
			user_id,
			status: OrderStatus.SOLD,
		},
		select: { ts_code: true },
		distinct: ["ts_code"],
		orderBy: { ts_code: "asc" },
	});

	return orders.map((order) => order.ts_code);
}

export async function findUnconfirmedExpiryDates(): Promise<OrderData[]> {
	const orders = await prisma.orders.findMany({
		where: {
			expiry_date_confirmed: false,
		},
	});

	return orders.map(transformOrderData);
}

export async function getModifications(
	page: number,
	pageSize: number,
	filters?: {
		trade_no?: string;
	},
): Promise<{ items: OrderModification[]; total: number }> {
	const offset = (page - 1) * pageSize;

	// 构建查询条件
	const where = filters?.trade_no ? { trade_no: filters.trade_no } : undefined;

	// 获取总数
	const total = await prisma.order_modifications.count({
		where,
	});

	// 获取分页数据
	const modifications = await prisma.order_modifications.findMany({
		where,
		skip: offset,
		take: pageSize,
		orderBy: {
			created_at: "desc", // 按创建时间倒序排列
		},
	});

	// 转换数据格式
	const items = modifications.map((mod) => ({
		id: mod.id,
		trade_no: mod.trade_no,
		user_id: mod.user_id,
		type: mod.type as OrderModificationType,
		data: mod.data as unknown as OrderModificationData,
		comment: mod.comment || undefined,
		created_at: mod.created_at?.toISOString() || "",
		admin_id: mod.admin_id ?? undefined,
	}));

	return {
		items,
		total,
	};
}

export async function createModification(
	data: {
		trade_no: string;
		user_id: number;
		type: OrderModificationType;
		data: OrderModificationData;
		comment: string;
		admin_id: number;
	},
	client?: Prisma.TransactionClient,
): Promise<OrderModification> {
	const createFn = async (tx: Prisma.TransactionClient) => {
		const modification = await tx.order_modifications.create({
			data: {
				trade_no: data.trade_no,
				user_id: data.user_id,
				type: data.type,
				data: data.data as unknown as Prisma.InputJsonValue,
				comment: data.comment,
				admin_id: data.admin_id,
				created_at: new Date(),
			},
		});

		return {
			id: modification.id,
			trade_no: modification.trade_no,
			user_id: modification.user_id,
			type: modification.type as OrderModificationType,
			data: modification.data as unknown as OrderModificationData,
			comment: modification.comment || undefined,
			created_at: modification.created_at?.toISOString() || "",
			admin_id: modification.admin_id ?? undefined,
		};
	};

	return client ? createFn(client) : withTransaction(createFn);
}

export async function getTotalCount(): Promise<number> {
	return await prisma.orders.count();
}

export async function getCompletedCount(): Promise<number> {
	return await prisma.orders.count({
		where: {
			status: OrderStatus.SOLD,
		},
	});
}
