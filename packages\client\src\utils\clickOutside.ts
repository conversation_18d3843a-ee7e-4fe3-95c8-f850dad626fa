declare global {
  interface HTMLElement {
    clickOutsideEvent?: (event: Event) => void
  }
}

export const vClickOutside = {
  mounted(el: HTMLElement, binding: { value: (event: Event) => void }) {
    el.clickOutsideEvent = (event: Event) => {
      // 检查点击是否发生在设置按钮上
      const settingsButton = document.querySelector('.settings-button')
      if (
        !(el === event.target || el.contains(event.target as Node)) &&
        event.target !== settingsButton &&
        !settingsButton?.contains(event.target as Node)
      ) {
        binding.value(event)
      }
    }
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el: HTMLElement) {
    if (el.clickOutsideEvent) {
      document.removeEventListener('click', el.clickOutsideEvent)
    }
  },
}
