import cron from "node-cron";
import logger from "@/utils/logger.js";
import {
	isMarketDay,
	checkUpcomingHoliday,
} from "@/financeUtils/marketTimeManager.js";
import { getAdjFactorChanges } from "@/financeUtils/adjFactorManager.js";
import * as Position from "@/models/position.js";
import * as Order from "@/models/trade/order.js";
import { withTransaction } from "@/core/dbTxnManager.js";
import type { Prisma } from "@prisma/client";
import EmailService from "@/utils/email.js";
import { formatDate } from "@/utils/format.js";

export class AdjFactorCron {
	private static instance: AdjFactorCron;

	// typescript will auto privatize constructor

	public static getInstance(): AdjFactorCron {
		if (!AdjFactorCron.instance) {
			AdjFactorCron.instance = new AdjFactorCron();
		}
		return AdjFactorCron.instance;
	}

	public start(): void {
		try {
			// 每天早上9点15分执行（盘前获取 gtimg <9:10AM update> 的当日数据）
			cron.schedule(
				"15 9 * * *",
				async () => {
					await this.processAdjFactorChanges();
					await this.holidayReminder();
				},
				{
					timezone: "Asia/Shanghai",
				},
			);

			logger.info("Adjustment factor processing cron job started");
		} catch (error) {
			logger.error(
				error,
				"Failed to start adjustment factor processing cron job",
			);
			throw error;
		}
	}

	private async processAdjFactorChanges(): Promise<void> {
		try {
			// 检查是否为交易日
			if (!(await isMarketDay())) {
				logger.info("Not a market day, skipping adjustment factor processing");
				return;
			}

			const startTime = Date.now();
			const changes = await getAdjFactorChanges();

			if (!changes.length) {
				logger.info("No adjustment factor changes found");
				return;
			}

			// 处理每个发生变化的股票
			for (const change of changes) {
				try {
					await this.updatePrices(change.ts_code, change.factor);
					logger.info(
						`Successfully processed adjustment factor change for ${change.ts_code}`,
					);
				} catch (error) {
					logger.error(
						error,
						`Failed to process adjustment factor change for ${change.ts_code}`,
					);
				}
			}

			const duration = Date.now() - startTime;
			logger.info(
				`Adjustment factor processing completed in ${duration}ms, processed ${changes.length} stocks`,
			);
		} catch (error) {
			logger.error(error, "Error during adjustment factor processing");
		}
	}

	private async updatePrices(ts_code: string, factor: number): Promise<void> {
		const updateFn = async (tx: Prisma.TransactionClient) => {
			// 使用模型方法更新价格
			await Promise.all([
				Position.updatePricesByStock(ts_code, factor, tx),
				Order.updatePricesByStock(ts_code, factor, tx),
			]);

			logger.info(`Updated prices for ${ts_code} with factor ${factor}`);
		};

		await withTransaction(updateFn);
	}

	private async holidayReminder(): Promise<void> {
		// 检查是否为交易日，如果非交易日则跳过
		if (!(await isMarketDay())) {
			logger.info("Not a market day, skipping holiday reminder");
			return;
		}

		const holiday = await checkUpcomingHoliday();
		if (holiday) {
			EmailService.sendToAllUsers("HOLIDAY_NOTIFICATION", {
				date1: formatDate(holiday.start),
				date2: formatDate(holiday.end),
			});
		}
	}
}

// 导出单例实例
export const adjFactorCron = AdjFactorCron.getInstance();
