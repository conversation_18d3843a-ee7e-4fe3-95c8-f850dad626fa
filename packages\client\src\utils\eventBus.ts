type Events = {
	"order-updated": { source: "position" | "other" } | undefined;
	"notification-updated": undefined;
};

type EventCallback<T> = (payload: T) => void;

class EventBus {
	private events: Map<
		keyof Events,
		Array<EventCallback<Events[keyof Events]>>
	> = new Map();

	constructor() {
		this.events = new Map();
	}

	on<K extends keyof Events>(event: K, callback: EventCallback<Events[K]>) {
		if (!this.events.has(event)) {
			this.events.set(event, []);
		}
		const callbacks = this.events.get(event) as EventCallback<
			Events[keyof Events]
		>[];
		callbacks.push(callback as EventCallback<Events[keyof Events]>);
		this.events.set(event, callbacks);
	}

	off<K extends keyof Events>(event: K, callback: EventCallback<Events[K]>) {
		const callbacks = this.events.get(event) || [];
		const index = callbacks.indexOf(
			callback as EventCallback<Events[keyof Events]>,
		);
		if (index !== -1) {
			callbacks.splice(index, 1);
		}
		if (callbacks.length === 0) {
			this.events.delete(event);
		}
	}

	emit<K extends keyof Events>(event: K, payload?: Events[K]) {
		const callbacks = this.events.get(event) || [];
		for (const callback of callbacks) {
			callback(payload);
		}
	}
}

export const eventBus = new EventBus();
