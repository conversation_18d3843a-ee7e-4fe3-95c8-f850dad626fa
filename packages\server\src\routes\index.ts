import { ENV } from "@/config/configManager.js";
import { Router } from "express";
import { csrfProtection } from "@/middlewares/csrf.js";
import {
	authenticateToken,
	authenticateAdmin,
	authenticateTokenWithoutQualification,
} from "@/middlewares/jwtAuth.js";
import {
	checkSystemAvailable,
	checkPositionEntryAvailable,
	checkInquiryAvailable,
} from "@/middlewares/systemAvailability.js";
import * as ConfigService from "@/services/admin/configService.js";
import type { HealthCheckResponse } from "@packages/shared";
import { tradePaths, positionPaths, inquiryPaths } from "@packages/shared";
import logger from "@/utils/logger.js";

import authRouter from "./authRoutes.js";
import fundRouter from "./fundRoutes.js";
import inquiryRouter from "./inquiryRoutes.js";
import tradeRouter from "./tradeRoutes.js";
import positionRouter from "./positionRoutes.js";
import auditRouter from "./auditRoutes.js";
import basicRouter from "./basicRoutes.js";
import notifRouter from "./notifRoutes.js";
import referenceRouter from "./referenceRoutes.js";
import adminRouter from "./admin/index.js";
import adminSiteConfigRoutes from "./adminSiteConfigRoutes.js";
import siteConfigRouter from "./siteConfigRoutes.js";
import fileRouter from "./fileRoutes.js";
import openRouter from "./openRoutes.js";
import sharedConfigRouter from "./sharedConfigRoutes.js";
import manualOrderRouter from "./manualOrderRoutes.js";
import publicRouter from "./publicRoutes.js";

const router = Router();

// 添加路由日志中间件
router.use((req, _res, next) => {
	logger.info(
		{
			method: req.method,
			path: req.originalUrl, // originalUrl 包含完整路径，包括查询参数
		},
		"Route accessed",
	);
	next();
});

// 健康检查路由
router.get("/health", async (_, res) => {
	const systemStatus = await ConfigService.getSystemStatus();
	res.status(200).json({
		status: "ok",
		timestamp: new Date().toISOString(),
		uptime: process.uptime(),
		environment: ENV.NODE_ENV || "development",
		system_enabled: systemStatus.SYSTEM_ENABLED,
		position_entry_enabled: systemStatus.POSITION_ENTRY_ENABLED,
		inquiry_enabled: systemStatus.INQUIRY_ENABLED,
	} as HealthCheckResponse);
});

// 认证路由 - 部分端点需要 CSRF 保护
router.use(
	"/auth",
	(req, res, next) => {
		["/logout", "/refresh-token", "/change-password"].includes(req.path)
			? csrfProtection(req, res, next)
			: next();
	},
	authRouter,
);

// 应用系统可用性中间件到交易路由
router.use(tradePaths, checkSystemAvailable);
// 应用持仓准入控制中间件到买入路由
router.use(positionPaths, checkPositionEntryAvailable);
// 应用询价可用性中间件到询价路由
router.use(inquiryPaths, checkInquiryAvailable);

// 用户相关路由 - 需要 CSRF 和 Token 认证
router.use("/fund", csrfProtection, authenticateToken, fundRouter);
router.use("/inquiry", csrfProtection, authenticateToken, inquiryRouter);
router.use("/trade", csrfProtection, authenticateToken, tradeRouter);
router.use("/position", csrfProtection, authenticateToken, positionRouter);
router.use("/audit", csrfProtection, auditRouter); // 因资质审核移到内部细分
router.use("/basic", csrfProtection, authenticateToken, basicRouter);
router.use(
	"/manual-order",
	csrfProtection,
	authenticateToken,
	manualOrderRouter,
);

// 通知相关路由 - 需要 CSRF 和 Token 认证，不检查资质
router.use(
	"/notifications",
	csrfProtection,
	authenticateTokenWithoutQualification,
	notifRouter,
);

// 参考数据路由 - 不需要认证
router.use("/reference", csrfProtection, referenceRouter);

// 管理员路由 - 需要 CSRF 和管理员认证
router.use("/admin", csrfProtection, authenticateAdmin, adminRouter);

// 管理端网站配置路由，只有上传需要认证和权限，内部单独处理
router.use("/admin-site-config", adminSiteConfigRoutes);

// 客户端网站配置路由 - 不需要认证
router.use("/site-config", siteConfigRouter);

// 共享配置路由 - 不需要认证
router.use("/shared-config", sharedConfigRouter);

// 文件相关路由 - 需要 CSRF 和 Token 认证
router.use("/file", csrfProtection, fileRouter);

// 开放路由（带 API Token） - 不需要认证
router.use("/open", openRouter);

// 公共路由 - 不需要认证
router.use("/public", publicRouter);

export default router;
