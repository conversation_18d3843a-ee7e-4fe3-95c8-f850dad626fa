import { request } from "./request";
import type {
	Balances,
	TransactionType,
	TransactionData,
	ExchangeRateResult,
	TransferData,
	ExchangeCurrencyData,
} from "@packages/shared";

// 资金相关 API
export const fundApi = {
	getBalances: () => request.get<Balances>("/fund/balances"),

	getTransactionHistory: (
		page: number,
		pageSize: number,
		isDescending: boolean,
		filters?: {
			types?: TransactionType[];
			startDate?: string;
			endDate?: string;
		},
	) =>
		request.get<{ items: TransactionData[]; total: number }>("/fund/history", {
			params: {
				page,
				pageSize,
				isDescending,
				types: filters?.types?.join(","),
				startDate: filters?.startDate,
				endDate: filters?.endDate,
			},
		}),

	exchangeCurrency: (data: ExchangeCurrencyData) =>
		request.post("/fund/exchange", data),

	getExchangeRates: () => request.get<ExchangeRateResult>("/fund/rates"),
} as const;

// 支付相关 API
export const paymentApi = {
	checkPaymentPassword: () =>
		request.get<{ hasPassword: boolean }>("/fund/has-password"),

	setPaymentPassword: (password: string) =>
		request.post("/fund/set-password", { password }),

	transfer: (data: TransferData) => request.post("/fund/transfer", data),
} as const;
