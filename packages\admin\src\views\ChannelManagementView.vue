<template>
  <div class="channel-management-view view">
    <div class="header-container">
      <h2>通道管理</h2>
      <div class="controls">
        <el-button type="primary" :loading="saving" @click="saveSettings">
          保存配置
        </el-button>
      </div>
    </div>

    <!-- 通道功能管理 -->
    <el-card class="config-card">
      <div class="config-section">
        <h3 class="section-title">通道功能管理</h3>
        <p class="config-description">管理通道端可使用的功能，更改将影响所有通道。</p>

        <div class="config-items">
          <div class="config-item">
            <div class="config-row">
              <span class="config-label">转账授权：</span>
              <el-switch v-model="transferAuthEnabled" />
            </div>
            <div class="config-description">关闭后，通道端后台将无法使用转账授权功能，并禁用转账。</div>
          </div>

          <div class="config-item" v-if="false">
            <div class="config-row">
              <span class="config-label">一键下单：</span>
              <el-switch v-model="externalOrderEnabled" />
            </div>
            <div class="config-description">关闭后，交易台和通道的前台用户对外部报价将无法进行下单。</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { SharedConfig } from "@packages/shared";
import { useSharedConfigStore } from "@/stores/sharedConfig";

const sharedConfigStore = useSharedConfigStore();
const saving = ref(false);

// 转账授权状态的计算属性
const transferAuthEnabled = computed({
  get: () => sharedConfigStore.config?.channel_management?.enable_transfer_auth ?? true,
  set: (value: boolean) => {
    if (sharedConfigStore.config) {
      if (!sharedConfigStore.config.channel_management) {
        sharedConfigStore.config.channel_management = {};
      }
      sharedConfigStore.config.channel_management.enable_transfer_auth = value;
    }
  }
});

// 外部报价下单状态的计算属性
const externalOrderEnabled = computed({
  get: () => sharedConfigStore.config?.channel_management?.enable_external_order ?? true,
  set: (value: boolean) => {
    if (sharedConfigStore.config) {
      if (!sharedConfigStore.config.channel_management) {
        sharedConfigStore.config.channel_management = {};
      }
      sharedConfigStore.config.channel_management.enable_external_order = value;
    }
  }
});

// 保存配置
const saveSettings = async () => {
  saving.value = true;
  try {
    // 获取当前完整配置
    const currentConfig = sharedConfigStore.config;
    if (!currentConfig) {
      ElMessage.error("配置未加载，请刷新页面重试");
      return;
    }

    const updateData: SharedConfig = {
      ...currentConfig,
      channel_management: {
        ...currentConfig.channel_management,
        enable_transfer_auth: transferAuthEnabled.value,
        enable_external_order: externalOrderEnabled.value
      }
    };

    const result = await sharedConfigStore.updateSharedConfig(updateData);
    if (result) {
      ElMessage.success("通道管理配置保存成功");
    } else {
      ElMessage.warning("保存成功，但服务器无响应");
    }
  } catch (error) {
    console.error("Failed to save channel management settings:", error);
    ElMessage.error("保存配置失败");
  } finally {
    saving.value = false;
  }
};

// 组件挂载时确保配置已加载
onMounted(() => {
  if (!sharedConfigStore.config) {
    sharedConfigStore.loadSharedConfig();
  }
});
</script>

<style scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

h2 {
  margin: 0;
}

.section-title {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  border-left: 3px solid var(--el-color-primary);
  padding-left: 10px;
}

.config-section {
  margin-bottom: 10px;
}

.config-description {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 20px;
}

.config-items {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-label {
  width: 120px;
  flex-shrink: 0;
}

.config-item .config-description {
  margin-bottom: 0;
  margin-left: 0;
  padding-left: 132px;
  /* 对齐到开关位置 */
}

@media (max-width: 768px) {
  .config-label {
    width: 100px;
  }

  .config-item .config-description {
    padding-left: 112px;
  }
}
</style>