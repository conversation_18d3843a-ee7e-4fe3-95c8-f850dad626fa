import { getChildLogger } from "@/utils/logger.js";
import { redisManager } from "./redisManager.js";
import { retryHandler } from "./retryHandler.js";
import { lockManager } from "./distributedLockManager.js";

const logger = getChildLogger("MetricsCollector");

/**
 * 指标数据接口
 *
 * 定义系统监控的完整指标结构，包含：
 * - 作业执行指标
 * - 系统资源指标
 * - 缓存性能指标
 * - 分布式锁指标
 */
interface Metrics {
	timestamp: number; // 指标收集时间戳
	jobMetrics: {
		// 作业执行指标
		total: number; // 总作业数
		successful: number; // 成功作业数
		failed: number; // 失败作业数
		avgDuration: number; // 平均执行时长（毫秒）
	};
	systemMetrics: {
		// 系统资源指标
		memoryUsage: NodeJS.MemoryUsage; // 内存使用情况
		uptime: number; // 运行时间（秒）
		activeConnections: number; // 活跃连接数
	};
	cacheMetrics: {
		// 缓存性能指标
		hitRate: number; // 缓存命中率
		swingDataSize: number; // 波动数据大小
		quoteCacheCount: number; // 报价缓存数量
		redisLatency: number; // Redis延迟（毫秒）
	};
	lockMetrics: {
		// 分布式锁指标
		activeLocks: number; // 活跃锁数量
		lockAcquisitionRate: number; // 锁获取频率（次/秒）
	};
}

/**
 * 作业执行统计接口
 *
 * 记录单个作业的完整执行信息
 */
interface JobStats {
	jobName: string; // 作业名称
	startTime: number; // 开始时间戳
	endTime?: number; // 结束时间戳
	success?: boolean; // 是否成功
	error?: string; // 错误信息
	duration?: number; // 执行时长（毫秒）
}

/**
 * 指标收集器
 *
 * 核心功能：
 * - 收集系统性能指标、作业执行状态、缓存命中率等
 * - 提供实时监控数据和历史趋势分析
 * - 支持健康状态评估和异常告警
 * - 导出多种格式的监控数据
 *
 * 设计特点：
 * - 低开销：异步收集，不影响主业务流程
 * - 自动清理：定期清理过期数据，防止内存泄漏
 * - 多维度：从作业、系统、缓存、锁等多个维度监控
 * - 可扩展：支持自定义指标和导出格式
 */
export class MetricsCollector {
	private jobStats: JobStats[] = [];
	private cacheHits = 0; // 缓存命中次数
	private cacheMisses = 0; // 缓存未命中次数
	private lockAcquisitions = 0; // 锁获取次数
	private lockAcquisitionStartTime = Date.now(); // 锁获取统计开始时间

	private readonly METRICS_RETENTION_TIME = 24 * 60 * 60 * 1000; // 24小时数据保留期
	private readonly CLEANUP_INTERVAL = 60 * 60 * 1000; // 1小时清理一次

	constructor() {
		// 定期清理旧数据，防止内存泄漏
		setInterval(() => {
			this.cleanupOldMetrics();
		}, this.CLEANUP_INTERVAL);
	}

	/**
	 * 记录作业开始
	 *
	 * 为每个作业生成唯一ID，记录开始时间
	 *
	 * @param jobName 作业名称
	 * @returns 作业唯一ID
	 */
	recordJobStart(jobName: string): string {
		const jobId = `${jobName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

		this.jobStats.push({
			jobName,
			startTime: Date.now(),
		});

		return jobId;
	}

	/**
	 * 记录作业完成
	 *
	 * 更新作业执行结果，计算执行时长
	 * 支持成功和失败两种状态
	 *
	 * @param jobName 作业名称
	 * @param success 是否成功
	 * @param error 错误信息（失败时）
	 */
	recordJobComplete(jobName: string, success: boolean, error?: string): void {
		// 查找最近的未完成作业记录
		const jobStat = this.jobStats
			.slice()
			.reverse()
			.find((stat) => stat.jobName === jobName && !stat.endTime);

		if (jobStat) {
			jobStat.endTime = Date.now();
			jobStat.success = success;
			jobStat.error = error;
			jobStat.duration = jobStat.endTime - jobStat.startTime;
		}

		logger.debug(
			`Job ${jobName} completed: ${success ? "success" : "failed"}`,
			{
				duration: jobStat?.duration,
				error,
			},
		);
	}

	/**
	 * 记录缓存命中
	 *
	 * 用于计算缓存命中率，评估缓存效果
	 */
	recordCacheHit(): void {
		this.cacheHits++;
	}

	/**
	 * 记录缓存未命中
	 *
	 * 与缓存命中配合，计算整体缓存性能
	 */
	recordCacheMiss(): void {
		this.cacheMisses++;
	}

	/**
	 * 记录锁获取
	 *
	 * 统计分布式锁的使用频率
	 */
	recordLockAcquisition(): void {
		this.lockAcquisitions++;
	}

	/**
	 * 获取当前指标
	 *
	 * 实时计算并返回所有监控指标：
	 * - 作业执行统计：成功率、平均时长等
	 * - 系统资源状态：内存、运行时间等
	 * - 缓存性能：命中率、数据量等
	 * - 锁使用情况：活跃锁数、获取频率等
	 *
	 * @returns 完整的指标数据
	 */
	async getCurrentMetrics(): Promise<Metrics> {
		const now = Date.now();
		// 筛选最近24小时内的已完成作业
		const recentJobs = this.jobStats.filter(
			(job) => now - job.startTime < this.METRICS_RETENTION_TIME && job.endTime,
		);

		// 计算作业指标
		const successful = recentJobs.filter((job) => job.success).length;
		const failed = recentJobs.filter((job) => job.success === false).length;
		const totalDuration = recentJobs.reduce(
			(sum, job) => sum + (job.duration || 0),
			0,
		);
		const avgDuration =
			recentJobs.length > 0 ? totalDuration / recentJobs.length : 0;

		// 计算缓存指标
		const totalCacheRequests = this.cacheHits + this.cacheMisses;
		const hitRate =
			totalCacheRequests > 0 ? this.cacheHits / totalCacheRequests : 0;

		// 获取Redis健康状态和缓存统计
		const redisHealth = await redisManager.healthCheck();
		const cacheStats = await redisManager.getCacheStats();

		// 计算锁指标
		const timeElapsed = (now - this.lockAcquisitionStartTime) / 1000; // 转换为秒
		const lockAcquisitionRate =
			timeElapsed > 0 ? this.lockAcquisitions / timeElapsed : 0;

		return {
			timestamp: now,
			jobMetrics: {
				total: recentJobs.length,
				successful,
				failed,
				avgDuration,
			},
			systemMetrics: {
				memoryUsage: process.memoryUsage(),
				uptime: process.uptime(),
				activeConnections: 0, // 可以从网络模块获取
			},
			cacheMetrics: {
				hitRate,
				swingDataSize: 0, // 可以计算swing data的大小
				quoteCacheCount: cacheStats.quoteCacheCount,
				redisLatency: redisHealth.latency,
			},
			lockMetrics: {
				activeLocks: lockManager.getActiveLockCount(),
				lockAcquisitionRate,
			},
		};
	}

	/**
	 * 获取作业执行历史
	 *
	 * 提供作业执行的历史记录，支持：
	 * - 按作业名称过滤
	 * - 按时间倒序排列
	 * - 限制返回数量
	 *
	 * @param jobName 作业名称（可选）
	 * @param limit 返回数量限制
	 * @returns 作业执行历史列表
	 */
	getJobHistory(jobName?: string, limit = 100): JobStats[] {
		let jobs = this.jobStats.filter((job) => job.endTime);

		if (jobName) {
			jobs = jobs.filter((job) => job.jobName === jobName);
		}

		return jobs
			.sort((a, b) => (b.endTime || 0) - (a.endTime || 0))
			.slice(0, limit);
	}

	/**
	 * 获取错误统计
	 *
	 * 分析指定时间窗口内的错误情况：
	 * - 总错误数量
	 * - 按作业分组的错误统计
	 * - 最近的错误详情
	 *
	 * @param timeWindow 时间窗口（毫秒），默认1小时
	 * @returns 错误统计信息
	 */
	getErrorStats(timeWindow = 60 * 60 * 1000): {
		totalErrors: number;
		errorsByJob: Record<string, number>;
		recentErrors: JobStats[];
	} {
		const now = Date.now();
		const recentErrors = this.jobStats.filter(
			(job) =>
				job.endTime && job.success === false && now - job.endTime < timeWindow,
		);

		// 按作业名称统计错误数量
		const errorsByJob: Record<string, number> = {};
		for (const job of recentErrors) {
			errorsByJob[job.jobName] = (errorsByJob[job.jobName] || 0) + 1;
		}

		return {
			totalErrors: recentErrors.length,
			errorsByJob,
			recentErrors: recentErrors.slice(0, 10), // 最近10个错误
		};
	}

	/**
	 * 生成健康报告
	 *
	 * 综合分析系统健康状况：
	 * - 整体健康等级：healthy/warning/critical
	 * - 详细指标数据
	 * - 优化建议
	 *
	 * 评估维度：
	 * - 错误率：作业失败比例
	 * - 响应延迟：Redis等外部服务延迟
	 * - 资源使用：内存使用情况
	 * - 缓存效率：缓存命中率
	 * - 服务可用性：断路器状态
	 *
	 * @returns 健康报告
	 */
	async generateHealthReport(): Promise<{
		overall: "healthy" | "warning" | "critical";
		details: Record<string, unknown>;
		recommendations: string[];
	}> {
		const metrics = await this.getCurrentMetrics();
		const errorStats = this.getErrorStats();
		const circuitBreakerStatus = retryHandler.getCircuitBreakerStatus();

		const recommendations: string[] = [];
		let overall: "healthy" | "warning" | "critical" = "healthy";

		// 检查错误率
		const errorRate =
			metrics.jobMetrics.total > 0
				? metrics.jobMetrics.failed / metrics.jobMetrics.total
				: 0;

		if (errorRate > 0.1) {
			// 10%以上错误率
			overall = errorRate > 0.3 ? "critical" : "warning";
			recommendations.push(
				`作业错误率过高: ${(errorRate * 100).toFixed(1)}%，建议检查配置和网络连接`,
			);
		}

		// 检查Redis延迟
		if (metrics.cacheMetrics.redisLatency > 1000) {
			overall =
				metrics.cacheMetrics.redisLatency > 5000 ? "critical" : "warning";
			recommendations.push(
				`Redis延迟过高: ${metrics.cacheMetrics.redisLatency}ms，建议检查网络和Redis服务器状态`,
			);
		}

		// 检查内存使用
		const memoryUsageMB =
			metrics.systemMetrics.memoryUsage.heapUsed / 1024 / 1024;
		if (memoryUsageMB > 500) {
			// 500MB
			overall = memoryUsageMB > 1000 ? "critical" : "warning";
			recommendations.push(
				`内存使用过高: ${memoryUsageMB.toFixed(1)}MB，建议重启进程或优化代码`,
			);
		}

		// 检查缓存命中率
		if (
			metrics.cacheMetrics.hitRate < 0.8 &&
			this.cacheHits + this.cacheMisses > 100
		) {
			overall = overall === "healthy" ? "warning" : overall;
			recommendations.push(
				`缓存命中率偏低: ${(metrics.cacheMetrics.hitRate * 100).toFixed(1)}%，建议优化缓存策略`,
			);
		}

		// 检查断路器状态
		const openCircuitBreakers = Object.entries(circuitBreakerStatus).filter(
			([, status]) => status.state === "OPEN",
		);

		if (openCircuitBreakers.length > 0) {
			overall = "warning";
			recommendations.push(
				`有${openCircuitBreakers.length}个断路器处于打开状态，部分功能可能不可用`,
			);
		}

		return {
			overall,
			details: {
				metrics,
				errorStats,
				circuitBreakerStatus,
			},
			recommendations,
		};
	}

	/**
	 * 清理旧指标数据
	 *
	 * 定期清理超过保留期的数据，防止内存泄漏
	 * 保留期默认为24小时
	 */
	private cleanupOldMetrics(): void {
		const now = Date.now();
		const beforeCount = this.jobStats.length;

		this.jobStats = this.jobStats.filter(
			(job) => now - job.startTime < this.METRICS_RETENTION_TIME,
		);

		const cleanedCount = beforeCount - this.jobStats.length;
		if (cleanedCount > 0) {
			logger.debug(`Cleaned up ${cleanedCount} old job stats`);
		}
	}

	/**
	 * 导出指标到外部系统
	 *
	 * 支持多种导出格式：
	 * - JSON: 结构化数据，便于程序处理
	 * - Prometheus: 时序数据库格式，便于监控系统集成
	 *
	 * @param format 导出格式
	 * @returns 格式化的指标数据
	 */
	async exportMetrics(format: "json" | "prometheus" = "json"): Promise<string> {
		const metrics = await this.getCurrentMetrics();

		if (format === "json") {
			return JSON.stringify(metrics, null, 2);
		}

		// Prometheus格式 (简化版)
		if (format === "prometheus") {
			return [
				`ink_sync_jobs_total ${metrics.jobMetrics.total}`,
				`ink_sync_jobs_successful ${metrics.jobMetrics.successful}`,
				`ink_sync_jobs_failed ${metrics.jobMetrics.failed}`,
				`ink_sync_job_duration_avg ${metrics.jobMetrics.avgDuration}`,
				`ink_sync_cache_hit_rate ${metrics.cacheMetrics.hitRate}`,
				`ink_sync_redis_latency ${metrics.cacheMetrics.redisLatency}`,
				`ink_sync_active_locks ${metrics.lockMetrics.activeLocks}`,
				`ink_sync_memory_heap_used ${metrics.systemMetrics.memoryUsage.heapUsed}`,
			].join("\n");
		}

		throw new Error(`Unsupported format: ${format}`);
	}

	/**
	 * 重置统计数据
	 *
	 * 清空所有统计数据，重新开始计数
	 * 通常用于测试或系统重启后的数据重置
	 */
	reset(): void {
		this.jobStats = [];
		this.cacheHits = 0;
		this.cacheMisses = 0;
		this.lockAcquisitions = 0;
		this.lockAcquisitionStartTime = Date.now();
		logger.info("Metrics collector reset");
	}
}

// 导出单例实例
export const metricsCollector = new MetricsCollector();

// 导出getCurrentMetrics函数供外部使用
export const getCurrentMetrics = () => metricsCollector.getCurrentMetrics();
