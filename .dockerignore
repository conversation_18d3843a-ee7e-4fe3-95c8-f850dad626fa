# Git
.git
.gitignore

# Node
node_modules
# pnpm-lock.yaml  <-- 注释掉或删除这一行
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json
pids
*.pid
*.seed
*.log
*.log.*.[0-9]
*.log.[0-9]
*.log.*.gz
*.log.gz

# Build artifacts
dist
build
.next
.nuxt
*.tsbuildinfo

# OS generated files
.DS_Store
Thumbs.db

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.*
!packages/server/.env.example # 如果有示例env文件，可以取消注释此行以包含它

# Logs directory (created by ecosystem.config.js)
logs

# Test reports
coverage