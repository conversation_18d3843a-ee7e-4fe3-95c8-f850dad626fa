<template>
  <div class="login-modal">
    <form @submit.prevent="login">
      <div class="form-group">
        <label for="email">邮箱</label>
        <input link id="email" v-model="email" placeholder="请输入邮箱" />
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <div class="password-input-group">
          <input :type="showPassword ? 'text' : 'password'" id="password" v-model="password" placeholder="请输入密码" />
          <button type="button" class="toggle-password" tabindex="-1" @click="showPassword = !showPassword">
            <SvgIcon :name="showPassword ? 'eye-rs-flaticon' : 'crossed-eye-rs-flaticon'" />
          </button>
        </div>
      </div>
      <div class="form-group captcha-group">
        <label for="captcha">验证码</label>
        <input type="text" id="captcha" v-model="userCaptcha" :placeholder="'请' + '输入' + '验证码'" />
        <canvas ref="captchaCanvas" @click="generateCaptcha" class="captcha-image"></canvas>
      </div>

      <div class="form-group remember-password">
        <input type="checkbox" id="rememberPassword" v-model="rememberPassword" />
        <label for="rememberPassword">记住密码</label>
      </div>

      <button class="login-btn" type="submit">登录</button>
    </form>

    <!-- 修改注册入口为操作按钮组 -->
    <div class="action-links">
      <a @click="$emit('switch-to-register')">注册账号</a>
      <span class="divider">|</span>
      <a @click="$emit('switch-to-reset')">忘记密码</a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useAuthStore } from "@/stores/auth";
import SvgIcon from "@/components/SvgIcon.vue";
import { ElMessage } from "element-plus";
import { getErrorMessage } from "@packages/shared";
import type { LoginRequest } from "@packages/shared";

// Composition API (如 useRouter/ref) 必须在 setup 顶层调用
// 因为只有 setup 阶段能访问组件实例上下文，这是 Vue 追踪响应式依赖的关键

const email = ref("");
const password = ref("");
const rememberPassword = ref<boolean>(false);
const userCaptcha = ref("");
const captchaText = ref("");
const captchaCanvas = ref<HTMLCanvasElement | null>(null);
const showPassword = ref(false);

onMounted(() => {
	loadSavedCredentials();
	generateCaptcha();
});

function generateCaptcha(): void {
	const canvas = captchaCanvas.value;
	if (!canvas) return;
	const ctx = canvas.getContext("2d");
	if (!ctx) return;
	canvas.width = 100;
	canvas.height = 40;

	captchaText.value = Math.random().toString(36).substring(2, 8).toUpperCase();

	ctx.fillStyle = "#f0f0f0";
	ctx.fillRect(0, 0, canvas.width, canvas.height);

	ctx.font = "24px Arial";
	ctx.fillStyle = "#333";
	ctx.textBaseline = "middle";
	ctx.textAlign = "center";

	for (let i = 0; i < captchaText.value.length; i++) {
		ctx.fillText(captchaText.value[i], 15 + i * 15, 22 + Math.random() * 8 - 4);
	}

	for (let i = 0; i < 4; i++) {
		ctx.strokeStyle = getRandomColor(40, 180);
		ctx.beginPath();
		ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
		ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
		ctx.stroke();
	}
}

function getRandomColor(min: number, max: number): string {
	const r = Math.floor(Math.random() * (max - min + 1) + min);
	const g = Math.floor(Math.random() * (max - min + 1) + min);
	const b = Math.floor(Math.random() * (max - min + 1) + min);
	return `rgb(${r},${g},${b})`;
}

const login = async () => {
	if (!validateEmail(email.value)) {
		ElMessage.error("邮箱格式无效");
		return;
	}

	if (userCaptcha.value.toLowerCase() !== captchaText.value.toLowerCase()) {
		ElMessage.error("验证码错误");
		userCaptcha.value = "";
		generateCaptcha();
		return;
	}

	try {
		const loginData: LoginRequest = {
			email: email.value,
			password: password.value,
		};

		await useAuthStore().login(loginData);
		if (rememberPassword.value) {
			saveCredentials();
		} else {
			clearSavedCredentials();
		}
		ElMessage.success("登录成功");
	} catch (error) {
		ElMessage.error(`登录失败：${getErrorMessage(error)}`);
		generateCaptcha();
		userCaptcha.value = "";
	}
};

function saveCredentials(): void {
	localStorage.setItem("rememberedUsername", email.value);
	localStorage.setItem("rememberedPassword", btoa(password.value));
}

function loadSavedCredentials(): void {
	const savedUsername = localStorage.getItem("rememberedUsername");
	const savedPassword = localStorage.getItem("rememberedPassword");
	if (savedUsername && savedPassword) {
		email.value = savedUsername;
		password.value = atob(savedPassword);
		rememberPassword.value = true;
	}
}

function clearSavedCredentials(): void {
	localStorage.removeItem("rememberedUsername");
	localStorage.removeItem("rememberedPassword");
}

defineEmits(["switch-to-register", "switch-to-reset"]);

// 添加邮箱验证函数
const validateEmail = (email: string): boolean => {
	const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
	return pattern.test(email);
};
</script>

<style scoped>
.form-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  background-color: var(--el-fill-color-dark);
  border-radius: 5px;
  gap: 8px;
}

/* :last-of-type 基于元素名而非类名 */
.form-group.remember-password {
  margin-bottom: 0;
}

.form-group.captcha-group {
  margin-bottom: 12px;
}

.login-modal input {
  flex: 1;
  min-width: 0;
  padding: 10px 8px;
  border: none;
  border-radius: 5px;
  background-color: unset;
}

.login-modal label {
  font-size: 15px;
  width: 64px;
  padding: 0 10px;
  text-align-last: justify;
  white-space: nowrap;
  border-right: 1px solid #666;
}

.captcha-group :deep(.slider-captcha) {
  flex: 1;
}

.remember-password {
  flex-direction: row;
  justify-content: flex-start;
  align-self: flex-start;
  background-color: transparent;
}

.remember-password input[type='checkbox'] {
  flex: none;
  appearance: none;
  background-color: field;
  transform: scale(0.7);
  border: 1px solid #ccc;
  width: 20px;
  height: 20px;
}

.remember-password input[type='checkbox']:checked {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.remember-password input[type='checkbox']:checked::after {
  content: '\2714';
  font-size: 12px;
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.remember-password label {
  color: var(--el-text-color-primary);
  border-right: none;
  width: auto;
  padding: 0;
}

.login-btn {
  width: 50%;
  padding: 4px 8px;
  border: none;
  border-radius: 5px;
  color: var(--el-text-color-primary);
  cursor: pointer;
  margin-top: 12px;
  background-color: var(--el-color-primary);

  &:hover {
    background-color: var(--el-color-primary-light-3);
  }
}

.action-links {
  margin: 16px 0 4px;
  text-align: center;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.action-links a {
  color: var(--el-text-color-primary);
  text-decoration: underline;
  cursor: pointer;
}

.divider {
  color: var(--el-text-color-primary);
  opacity: 0.6;
}

.form-group input:focus {
  outline: none;
  border-color: var(--el-color-primary);
}

.form-group.error input {
  border-color: var(--el-color-error);
}

.password-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 200px;
}

.password-input-group {
  display: flex;
  align-items: center;
  flex: 1;
}

.toggle-password {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  display: flex;
}

.toggle-password:hover {
  opacity: 0.8;
}

.toggle-password .svg-icon {
  width: 16px;
  height: 16px;
  color: var(--icon-color);
}

/* 隐藏 Edge/Chrome 的密码显示按钮 */
input[type='password']::-ms-reveal,
input[type='password']::-ms-clear,
input[type='password']::-webkit-contacts-auto-fill-button,
input[type='password']::-webkit-credentials-auto-fill-button {
  display: none !important;
  pointer-events: none;
  visibility: hidden;
}

/* 移除 Edge 浏览器的密码显示按钮 */
input[type='password']::-ms-reveal {
  display: none !important;
}

.captcha-group {
  position: relative;
}

.captcha-image {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  height: 30px;
  cursor: pointer;
  border-radius: 3px;
}

/* 添加移动端适配 */
@media (max-width: 768px) {
  .form-group {
    margin-bottom: 12px;
  }

  .form-group label {
    font-size: 14px;
    width: 56px;
    padding: 0 8px;
  }

  .form-group input {
    padding: 8px;
    font-size: 14px;
  }

  .login-btn {
    width: 60%;
    margin-top: 8px;
    padding: 8px;
  }

  .action-links {
    margin: 12px 0 4px;
    gap: 12px;
    font-size: 14px;
  }

  .captcha-image {
    height: 28px;
  }

  .remember-password {
    margin-bottom: 8px;
  }

  .remember-password label {
    font-size: 14px;
  }
}

/* 添加横屏模式适配 */
@media (max-height: 600px) and (orientation: landscape) {
  .form-group {
    margin-bottom: 8px;
  }

  .login-btn {
    margin-top: 4px;
    padding: 6px;
  }

  .action-links {
    margin: 8px 0 4px;
  }
}
</style>