import { ENV } from "@/config/configManager.js";
import { createTransport } from "nodemailer";
import type { Transporter } from "nodemailer";
import type { Options as SMTPTransportOptions } from "nodemailer/lib/smtp-transport";
import logger from "./logger.js";
import { AppError } from "../core/appError.js";
import { notificationThrottle } from "./notificationThrottle.js";
import { getPlatformConfig } from "@/services/admin/configService.js";

// Email template types
type TemplateType =
	| "WITHDRAWAL"
	| "DEPOSIT"
	| "FORCE_NOTIFICATION"
	| "EXPIRY_REMINDER"
	| "EXPIRY_NOTIFICATION"
	| "EXERCISE_INSTRUCTION"
	| "ORDER_INSTRUCTION"
	| "AUDIT_NOTIFICATION"
	| "INFO_UPDATE"
	| "PASSWORD_CHANGE"
	| "TRANSFER_OUT"
	| "TRANSFER_IN"
	| "PAYMENT_PASSWORD_SET"
	| "PAYMENT_PASSWORD_REMOVED"
	| "VERIFICATION_CODE"
	| "NEW_AUDIT_APPLICATION"
	| "NEW_AUDIT_APPLICATION_DEMO"
	| "ERROR_NOTIFICATION";

interface TemplateParams {
	WITHDRAWAL: Record<string, number>; // done
	DEPOSIT: Record<string, number>; // done
	FORCE_NOTIFICATION: Record<string, never>; // done
	EXPIRY_REMINDER: {
		date: string;
		userName: string;
		uid: string;
		count: number;
	}; // done
	EXPIRY_NOTIFICATION: { userName: string; uid: string; count: number }; // done
	EXERCISE_INSTRUCTION: Record<string, never>; // done
	ORDER_INSTRUCTION: Record<string, never>; // done
	AUDIT_NOTIFICATION: Record<string, never>; // done
	INFO_UPDATE: Record<string, never>; // done
	PASSWORD_CHANGE: Record<string, never>; // done
	TRANSFER_OUT: { amount: number };
	TRANSFER_IN: { amount: number };
	PAYMENT_PASSWORD_SET: Record<string, never>;
	PAYMENT_PASSWORD_REMOVED: Record<string, never>;
	VERIFICATION_CODE: { code: string };
	NEW_AUDIT_APPLICATION: { name: string };
	NEW_AUDIT_APPLICATION_DEMO: { name: string };
	ERROR_NOTIFICATION: {
		errorTime: string;
		appId: string;
		errorMessage: string;
	};
}

// Email templates as strings
const EMAIL_TEMPLATE_CONTENT: Record<
	TemplateType,
	{
		subject: string;
		template: string;
	}
> = {
	WITHDRAWAL: {
		subject: "资金支出",
		template: `尊敬的用户：
您有一笔出金已处理，出金金额：{amount}，到账时间依据银行处理时间为准，请注意查收。
祝好！`,
	},
	DEPOSIT: {
		subject: "资金入账",
		template: `尊敬的用户：
您有一笔入金已到账，到账金额：{amount}，该笔资金已存入您的账户，请注意查收。
祝好！`,
	},
	FORCE_NOTIFICATION: {
		subject: "场外期权交易确认",
		template: `尊敬的用户：
您的合约已达到强制敲出的标准。如有异议请自本邮件发出之日的1个工作日内联系系统。若无反馈即默认确认。
顺祝商祺！`,
	},
	EXPIRY_REMINDER: {
		subject: "合约即将到期",
		template: `尊敬的{userName}：
您有{count}笔合约将于{date}到期，请做好行权安排。如未行权，则按到期日的收盘价结算。
顺祝商祺！

--
邮件ID: {uid}`,
	},
	EXPIRY_NOTIFICATION: {
		subject: "合约到期",
		template: `尊敬的{userName}：
您有{count}笔合约已到期，已按到期日的收盘价结算。
顺祝商祺！

--
邮件ID: {uid}`,
	},
	// 隐藏结算通知书
	EXERCISE_INSTRUCTION: {
		subject: "场外期权结算通知",
		template: `尊敬的用户：
您已行权成功，请在平台查看详情。如有异议请自本邮件发出之日的1个工作日内联系系统。若无反馈即默认确认。
顺祝商祺！`,
	},
	// 隐藏交易确认书
	ORDER_INSTRUCTION: {
		subject: "场外期权交易确认",
		template: `尊敬的用户：
您已下单成功，请在平台查看详情。如有异议请自本邮件发出之日的1个工作日内联系系统。若无反馈即默认确认。
顺祝商祺！`,
	},
	AUDIT_NOTIFICATION: {
		subject: "资质认证审核通过",
		template: `尊敬的用户：
您注册的账号已通过审核，请登录平台查看。
顺祝商祺！`,
	},
	INFO_UPDATE: {
		subject: "账户信息变更",
		template: `尊敬的用户：
您的账户信息变动成功，请登录平台查看。
祝好！`,
	},
	PASSWORD_CHANGE: {
		subject: "密码修改成功",
		template: `尊敬的用户：
您的账户密码已修改成功，请重新登录。
祝好！`,
	},
	TRANSFER_OUT: {
		subject: "转账支出",
		template: `尊敬的用户：
您已成功转出资金，转出金额：{amount}，请确认交易信息。
顺祝商祺！`,
	},
	TRANSFER_IN: {
		subject: "转账收入",
		template: `尊敬的用户：
您已收到一笔转账，转入金额：{amount}，请注意查收。
顺祝商祺！`,
	},
	PAYMENT_PASSWORD_SET: {
		subject: "支付密码设置成功",
		template: `尊敬的用户：
您的支付密码已设置成功，请妥善保管，切勿告知他人。如非本人操作，请立即联系客服。
顺祝商祺！`,
	},
	PAYMENT_PASSWORD_REMOVED: {
		subject: "支付密码已清除",
		template: `尊敬的用户：
您的支付密码已清除，请重新设置。
顺祝商祺！`,
	},
	VERIFICATION_CODE: {
		subject: "验证码 - 账户验证",
		template: `尊敬的用户：
您的验证码为：{code}，5分钟内有效，请及时操作。`,
	},
	NEW_AUDIT_APPLICATION: {
		subject: "新审核申请",
		template: `待审核：用户"{name}"发起了新的审核申请，请注意查看。`,
	},
	NEW_AUDIT_APPLICATION_DEMO: {
		subject: "（DEMO）新审核申请",
		template: `待审核：用户"{name}"发起了新的审核申请，请注意查看。`,
	},
	ERROR_NOTIFICATION: {
		subject: "🚨 系统错误通知",
		template: `【系统错误监控】

===========================================
🔍 错误详情
===========================================
📍 系统标识: {appId}
⏰ 发生时间: {errorTime}
❌ 错误信息: {errorMessage}

===========================================
📋 处理建议
===========================================
请尽快检查系统日志获取详细的错误堆栈信息，
并根据错误类型采取相应的修复措施。

---
此邮件由系统自动发送，请勿直接回复。`,
	},
};

class EmailService {
	private transporter: Transporter;

	constructor() {
		if (!ENV.EMAIL_HOST || !ENV.EMAIL_USER || !ENV.EMAIL_PASS) {
			throw AppError.create(
				"EXTERNAL_API_ERROR",
				"Email credentials not configured",
			);
		}

		const config: SMTPTransportOptions = {
			host: ENV.EMAIL_HOST,
			port: ENV.EMAIL_PORT || 587,
			secure: Boolean(ENV.EMAIL_SECURE),
			auth: {
				user: ENV.EMAIL_USER,
				pass: ENV.EMAIL_PASS,
			},
			tls: {
				// 开发环境禁用证书验证，生产环境保持启用
				rejectUnauthorized: ENV.NODE_ENV === "production",
			},
		};

		this.transporter = createTransport(config);
	}

	generateCode(): string {
		return Math.floor(100000 + Math.random() * 900000).toString();
	}

	private getTemplateContent<T extends TemplateType>(
		templateType: T,
		params: TemplateParams[T],
	): { subject: string; html: string } {
		const template = EMAIL_TEMPLATE_CONTENT[templateType];
		let content = template.template;

		// 改进参数替换逻辑
		if (params && typeof params === "object") {
			// 使用类型安全的方式处理参数
			for (const [key, value] of Object.entries(params)) {
				const placeholder = `{${key}}`;
				if (content.includes(placeholder)) {
					content = content.replaceAll(placeholder, String(value));
				} else {
					logger.warn(
						`Template ${templateType} does not contain placeholder for parameter: ${key}`,
					);
				}
			}
		}

		// Convert plain text to HTML
		const html = content.split("\n").join("<br>");

		return {
			subject: template.subject,
			html,
		};
	}

	/**
	 * 发送邮件
	 * @param to - recipients, cannot be empty string
	 */
	async sendEmail<T extends TemplateType>(
		to: string,
		templateType: T,
		params: TemplateParams[T],
		options?: {
			attachments?: Array<{
				filename: string;
				content: Buffer;
				contentType: string;
			}>;
		},
	): Promise<void> {
		// 邮箱过滤逻辑: 避免向无效邮箱发送邮件以降低退信率
		// 1. 过滤测试账号邮箱 @example.com
		// 2. 过滤用户名以11位数字加小数点开头的邮箱（异常数据）
		// 注意：SMTP协议中，RCPT TO命令无法可靠验证邮箱是否存在。
		// 许多邮件服务器会返回250 OK（即使邮箱不存在）以防止垃圾邮件发送者探测有效邮箱地址，
		// 实际投递时才会退信。因此，建议手动监控退信率，避免触发反垃圾邮件机制。
		if (to.endsWith("@example.com")) {
			logger.info(`Skipping email to test account: ${to}`);
			return;
		}

		// 检查是否是手机号+小数点格式的用户名（异常数据）
		const usernameMatch = to.match(/^(\d{11}\..+)@/);
		if (usernameMatch) {
			logger.info(`Skipping email to invalid format account: ${to}`);
			return;
		}

		// 对于特定类型的消息进行节流
		if (
			!(await notificationThrottle.shouldSend(
				to,
				"EMAIL",
				`${templateType}`,
				`${JSON.stringify(params)}`,
			))
		) {
			return;
		}

		try {
			// 如果是 demo 环境且是审核申请消息，使用 demo 专用模板
			const finalTemplateType =
				ENV.IS_DEMO && templateType === "NEW_AUDIT_APPLICATION"
					? "NEW_AUDIT_APPLICATION_DEMO"
					: templateType;

			const { subject, html } = this.getTemplateContent(
				finalTemplateType as T,
				params,
			);

			// 获取平台配置，用于自定义发件人显示名称
			let fromEmail = `<${ENV.EMAIL_USER}>`;
			try {
				const platformConfig = await getPlatformConfig();
				if (platformConfig.email_display_name) {
					// 格式化发件人地址：显示名 <email>
					fromEmail = `${platformConfig.email_display_name} <${ENV.EMAIL_USER}>`;
				}
			} catch (err) {
				logger.warn(
					"Unable to get platform config for email display name, using default",
				);
			}

			const mailOptions = {
				from: fromEmail,
				to,
				subject,
				html,
				attachments: options?.attachments,
			};

			try {
				// 尝试使用主邮箱发送
				const result = await this.transporter.sendMail(mailOptions);
				logger.info(`Email sent to ${to} using primary email`, result);
			} catch (primaryError) {
				logger.warn(
					primaryError,
					`Failed to send email to ${to} using primary email, trying backup email`,
				);

				// 主邮箱发送失败，尝试使用备用邮箱
				if (ENV.EMAIL_USER_BACKUP) {
					// 创建备用邮箱的临时transporter
					const backupConfig: SMTPTransportOptions = {
						host: ENV.EMAIL_HOST,
						port: ENV.EMAIL_PORT || 587,
						secure: Boolean(ENV.EMAIL_SECURE),
						auth: {
							user: ENV.EMAIL_USER_BACKUP,
							pass: ENV.EMAIL_PASS, // 使用与主邮箱相同的密码
						},
						tls: {
							rejectUnauthorized: ENV.NODE_ENV === "production",
						},
					};

					const backupTransporter = createTransport(backupConfig);

					// 更新发件人地址为备用邮箱
					let backupFromEmail = `<${ENV.EMAIL_USER_BACKUP}>`;
					try {
						const platformConfig = await getPlatformConfig();
						if (platformConfig.email_display_name) {
							backupFromEmail = `${platformConfig.email_display_name} <${ENV.EMAIL_USER_BACKUP}>`;
						}
					} catch (err) {
						// 忽略错误，继续使用默认地址
					}

					const backupMailOptions = {
						...mailOptions,
						from: backupFromEmail,
					};

					// 尝试使用备用邮箱发送
					const backupResult =
						await backupTransporter.sendMail(backupMailOptions);
					logger.info(`Email sent to ${to} using backup email`, backupResult);
				} else {
					// 没有配置备用邮箱，重新抛出主邮箱的错误
					throw primaryError;
				}
			}
		} catch (error) {
			logger.error(
				error,
				`Failed to send email to ${to} with all configured email addresses`,
			);
			throw AppError.create("EXTERNAL_API_ERROR", "Failed to send email", {
				error: error instanceof Error ? error.message : "Unknown error",
			});
		}
	}

	/* START_SEND_BULK_EMAIL_COMMENT
	 * 批量发送邮件功能，因可能导致邮箱服务商将发件邮箱标记为垃圾邮件，暂时禁用以保护邮箱信誉。
	async sendBulkEmail<T extends TemplateType>(
		toList: string[],
		templateType: T,
		params: TemplateParams[T],
	): Promise<{
		success: string[];
		failed: Array<{ email: string; error: string }>;
	}> {
		if (ENV.NODE_ENV === "development") {
			logger.info(
				params,
				`[DEV] Bulk Email Params for ${toList.length} addresses:`,
			);
			return { success: toList, failed: [] };
		}

		const results = {
			success: [] as string[],
			failed: [] as Array<{ email: string; error: string }>,
		};
		
		// Process in batches of 50 to avoid overwhelming the email server
		const batchSize = 50;
		for (let i = 0; i < toList.length; i += batchSize) {
			const batch = toList.slice(i, i + batchSize);
			const batchPromises = batch.map(async (email) => {
				try {
					await this.sendEmail(email, templateType, params);
					results.success.push(email);
				} catch (error) {
					results.failed.push({
						email,
						error: error instanceof Error ? error.message : "Unknown error",
					});
				}
			});

			// Wait for current batch to complete before processing next batch
			await Promise.all(batchPromises);
		}

		logger.info(
			`Bulk email completed. Success: ${results.success.length}, Failed: ${results.failed.length}`,
		);

		return results;
	}
	END_SEND_BULK_EMAIL_COMMENT */

	/* START_SEND_TO_ALL_USERS_COMMENT
	 * 给所有用户发送邮件功能，此功能依赖 sendBulkEmail。
	 * 由于 sendBulkEmail 已被禁用，此功能也一并禁用。
	// 给所有用户发送
	async sendToAllUsers<T extends TemplateType>(
		templateType: T,
		params: TemplateParams[T],
	): Promise<void> {
		const users = await User.getAllContactInfo();
		const emails = users.map((user) => user.email);

		if (emails.length > 0) {
			await this.sendBulkEmail(emails, templateType, params);
		}
	}
	END_SEND_TO_ALL_USERS_COMMENT */
}

const emailService = new EmailService();
export default emailService;
