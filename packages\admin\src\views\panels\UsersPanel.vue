<template>
	<div class="users-panel">
		<div class="header-controls">
			<div class="search-bar">
				<el-input v-model="searchQuery" :placeholder="getSearchPlaceholder()" class="search-input" clearable
					@keydown.enter="handleSearch" @clear="handleClear">
					<template #prefix>
						<el-icon>
							<Search />
						</el-icon>
					</template>
				</el-input>
				<el-select v-model="searchType" class="search-type" placeholder="搜索类型">
					<el-option label="用户ID" value="id" />
					<el-option label="邮箱" value="email" />
					<el-option label="手机号" value="phone" />
					<el-option label="用户名" value="username" />
				</el-select>
			</div>
			<div class="controls">
				<el-button :loading="exporting" @click="exportData">
					<el-icon>
						<Download />
					</el-icon>
					导出数据
				</el-button>
			</div>
		</div>

		<el-table :data="users" stripe style="width: 100%;cursor: pointer;" @sort-change="handleSortChange"
			@row-click="(row: UserInfo) => showUserDetails(row.user_id)">
			<el-table-column prop="user_id" label="用户ID" min-width="108" sortable="custom">
				<template #default="{ row }">
					<el-button type="primary" link @click.stop="showUserDetails(row.user_id)">
						{{ row.user_id }}
					</el-button>
				</template>
			</el-table-column>
			<el-table-column prop="phone_number" label="手机号" min-width="140" />
			<el-table-column prop="name" label="用户名" min-width="140" sortable="custom" />
			<el-table-column prop="email" label="邮箱" min-width="140" />
			<el-table-column label="账户余额" min-width="250">
				<template #default="{ row }">
					<div class="balance-group">
						<el-tag>人民币: {{ formatMoney(row.balance_cny) }}</el-tag>
						<el-tag type="success">港币: {{ formatMoney(row.balance_hkd) }}</el-tag>
						<el-tag type="warning">美元: {{ formatMoney(row.balance_usd) }}</el-tag>
					</div>
				</template>
			</el-table-column>
			<el-table-column label="资质" min-width="90">
				<template #default="{ row }">
					<el-tag :type="row.is_qualified ? 'success' : 'info'">
						{{ row.is_qualified ? '已认证' : '未认证' }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column v-if="hasQualifyPermission || (hasConfigPermission && authStore.app_type === AppType.CHANNEL)"
				label="操作" width="100">
				<template #default="{ row }">
					<el-button type="primary" size="small" link @click.stop="showProfileForm(row)">
						更新资料
					</el-button>
					<el-button v-if="hasConfigPermission && authStore.app_type === AppType.CHANNEL" type="primary" size="small"
						link @click.stop="showTradeParamsForm(row)" style="margin-left: 0;">
						交易参数
					</el-button>
				</template>
			</el-table-column>
			<el-table-column prop="deposit" label="累计入金" min-width="120" sortable="custom">
				<template #default="{ row }">
					<div class="stats-group">
						<el-tooltip content="累计入金">
							<el-tag type="info">
								<el-icon>
									<Money />
								</el-icon>
								{{ formatMoney(row.deposit) }}
							</el-tag>
						</el-tooltip>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="premium" label="累计期权费" min-width="140" sortable="custom">
				<template #default="{ row }">
					<div class="stats-group">
						<el-tooltip content="累计期权费">
							<el-tag type="info">
								<el-icon>
									<Wallet />
								</el-icon>
								{{ formatMoney(row.premium) }}
							</el-tag>
						</el-tooltip>
						<el-tooltip content="期权费/入金">
							<el-tag type="warning">
								<el-icon>
									<TrendCharts />
								</el-icon>
								{{ calculatePremiumRatio(row.premium, row.deposit) }}%
							</el-tag>
						</el-tooltip>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="created_at" label="创建时间" min-width="170" sortable="custom">
				<template #default="{ row }">
					{{ formatDate(row.created_at) }}
				</template>
			</el-table-column>
		</el-table>

		<div class="pagination-container">
			<el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
				:total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
				@current-change="handleCurrentChange" />
		</div>

		<user-details-dialog v-model:visible="userDialogVisible" :user-id="selectedUserId" />

		<!-- 用户资料更新弹窗 -->
		<user-profile-change-form v-model:visible="profileDialogVisible" :user-id="selectedUserId"
			@saved="handleProfileSaved" />

		<!-- 用户交易参数弹窗 -->
		<user-trade-params-form v-model:visible="tradeParamsDialogVisible" :user-id="selectedUserIdForTradeParams" />
	</div>

</template>

<script setup lang="ts">
import { ref, onMounted, computed, onBeforeUnmount } from "vue";
import {
	Search,
	Money,
	Wallet,
	TrendCharts,
	Download,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { basicApi } from "@/api";
import type { UserInfo, UserFilters } from "@packages/shared";
import { formatMoney, formatDate } from "@/utils/format";
import UserDetailsDialog from "@/components/UserDetailsDialog.vue";
import UserProfileChangeForm from "@/views/panels/UserProfileChangeForm.vue";
import UserTradeParamsForm from "./UserTradeParamsForm.vue";
import { exportToCsv } from "@/utils/export";
import { useAuthStore } from "@/stores/auth";
import { AdminPermission, AppType } from "@packages/shared";

const authStore = useAuthStore();
const hasQualifyPermission = computed(() =>
	authStore.permissions.includes(AdminPermission.QUALIFY),
);

const users = ref<UserInfo[]>([]);
const searchQuery = ref("");
const searchType = ref<"id" | "email" | "phone" | "username">("id");
const userDialogVisible = ref(false);
const selectedUserId = ref<number>();
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const sortBy = ref("created_at");
const sortOrder = ref<"ASC" | "DESC">("DESC");
const exporting = ref(false);

// 用户资料更新相关状态
const profileDialogVisible = ref(false);
const tradeParamsDialogVisible = ref(false);
const selectedUserIdForTradeParams = ref<number | undefined>(undefined);

const loadUsers = async (
	options: {
		searchType?: string;
		searchQuery?: string;
	} = {},
) => {
	try {
		const { searchType: type, searchQuery: query } = options;
		const filters: UserFilters = {};

		if (query) {
			switch (type) {
				case "id":
					filters.user_id = Number.parseInt(query);
					break;
				case "email":
					filters.email = query;
					break;
				case "phone":
					filters.phone_number = query;
					break;
				case "username":
					filters.name = query;
					break;
			}
		}

		const result = await basicApi.getUsers({
			page: currentPage.value,
			pageSize: pageSize.value,
			sortBy: sortBy.value,
			sortOrder: sortOrder.value,
			filters,
		});

		users.value = result?.items || [];
		total.value = result?.total || 0;
	} catch (error) {
		console.error("Failed to fetch data:", error);
		ElMessage.error("加载用户列表失败");
	}
};

const handleSortChange = ({
	prop,
	order,
}: { prop?: string; order?: string }) => {
	sortBy.value = prop || "created_at";
	sortOrder.value = order === "ascending" ? "ASC" : "DESC";
	loadUsers({ searchType: searchType.value, searchQuery: searchQuery.value });
};

const handleSearch = () => {
	currentPage.value = 1; // Reset to first page when searching
	loadUsers({ searchType: searchType.value, searchQuery: searchQuery.value });
};

const handleSizeChange = (newSize: number) => {
	pageSize.value = newSize;
	loadUsers({ searchType: searchType.value, searchQuery: searchQuery.value });
};

const handleCurrentChange = (newPage: number) => {
	currentPage.value = newPage;
	loadUsers({ searchType: searchType.value, searchQuery: searchQuery.value });
};

const handleClear = () => {
	loadUsers();
};

const showUserDetails = (userId: number) => {
	selectedUserId.value = userId;
	userDialogVisible.value = true;
};

// 打开用户资料更新弹窗
const showProfileForm = (user: UserInfo) => {
	selectedUserId.value = user.user_id;
	profileDialogVisible.value = true;
};

const calculatePremiumRatio = (premium: number, deposit: number) => {
	if (!deposit) return "0.00";
	const ratio = (premium / deposit) * 100;
	return ratio.toFixed(2);
};

const getSearchPlaceholder = () => {
	switch (searchType.value) {
		case "id":
			return "输入用户ID搜索";
		case "phone":
			return "输入手机号搜索";
		case "username":
			return "输入用户名搜索";
		default:
			return "输入用户ID/手机号/用户名搜索";
	}
};

const exportData = async () => {
	exporting.value = true;
	try {
		const response = await basicApi.getUsers({
			page: 1,
			pageSize: 999999,
			sortBy: sortBy.value,
			sortOrder: sortOrder.value,
			filters: searchQuery.value
				? {
					[searchType.value]: searchQuery.value,
				}
				: undefined,
		});

		const headers = [
			"用户ID",
			"手机号",
			"用户名",
			"邮箱",
			"人民币余额",
			"港币余额",
			"美元余额",
			"累计入金",
			"累计期权费",
			"期权费/入金比率",
			"资质状态",
			"创建时间",
		];
		const csvData =
			response?.items.map((item) => [
				item.user_id,
				item.phone_number,
				item.name,
				item.email || "",
				item.balance_cny,
				item.balance_hkd,
				item.balance_usd,
				item.deposit,
				item.premium,
				calculatePremiumRatio(item.premium, item.deposit),
				item.is_qualified ? "已认证" : "未认证",
				new Date(item.created_at),
			]) || [];

		exportToCsv(headers, csvData, "users");
		ElMessage.success("导出成功");
	} catch (error) {
		console.error("Export failed:", error);
		ElMessage.error("导出失败");
	} finally {
		exporting.value = false;
	}
};

// 用户资料更新成功处理
const handleProfileSaved = () => {
	// 刷新用户列表
	loadUsers({ searchType: searchType.value, searchQuery: searchQuery.value });
};

// 保存页面状态
const saveState = () => {
	const state = {
		searchQuery: searchQuery.value,
		searchType: searchType.value,
		currentPage: currentPage.value,
		pageSize: pageSize.value,
		sortBy: sortBy.value,
		sortOrder: sortOrder.value,
	};
	sessionStorage.setItem("usersPanel_state", JSON.stringify(state));
};

// 恢复页面状态
const restoreState = () => {
	const savedState = sessionStorage.getItem("usersPanel_state");
	if (savedState) {
		try {
			const state = JSON.parse(savedState);
			searchQuery.value = state.searchQuery || "";
			searchType.value = state.searchType || "id";
			currentPage.value = state.currentPage || 1;
			pageSize.value = state.pageSize || 10;
			sortBy.value = state.sortBy || "created_at";
			sortOrder.value = state.sortOrder || "DESC";
		} catch (e) {
			console.error("Failed to parse saved state:", e);
		}
	}
};

// 权限计算示例 (根据实际情况调整)
const hasConfigPermission = computed(
	() =>
		authStore.permissions.includes(AdminPermission.CONFIG) ||
		authStore.permissions.includes(AdminPermission.ADMIN),
);

const showTradeParamsForm = (user: UserInfo) => {
	selectedUserIdForTradeParams.value = user.user_id;
	tradeParamsDialogVisible.value = true;
};

onMounted(() => {
	restoreState();
	loadUsers({ searchType: searchType.value, searchQuery: searchQuery.value });
});

onBeforeUnmount(() => {
	saveState();
});
</script>

<style scoped>
.users-panel {
	width: 100%;
}

.header-controls {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 24px;
	margin-bottom: 20px;
}

.search-bar {
	display: flex;
	gap: 16px;
}

.search-input {
	width: 300px;
}

.balance-group {
	display: flex;
	gap: 8px;
	flex-wrap: wrap;
}

.clickable {
	cursor: pointer;
	color: var(--el-color-primary);
}

.clickable:hover {
	opacity: 0.8;
}

.stats-group {
	display: flex;
	gap: 8px;
	flex-wrap: wrap;
}

.search-type {
	width: 100px;
}

@media (max-width: 768px) {
	.header-controls {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.search-bar {
		width: 100%;
	}

	.search-input {
		flex: 1;
	}
}
</style>