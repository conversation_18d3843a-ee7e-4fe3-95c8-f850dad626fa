<template>
  <div class="permissions-view view">
    <h2>权限管理</h2>
    <!-- 管理员列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>管理员列表</span>
          <el-button type="primary" @click="createDialogVisible = true">创建管理员</el-button>
        </div>
      </template>

      <el-table :data="filteredAdmins" style="width: 100%" class="dark-table">
        <el-table-column prop="admin_id" label="ID" width="80">
          <template #default="{ row }">
            <span class="wrap-text">{{ row.admin_id }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" min-width="80">
          <template #default="{ row }">
            <span class="wrap-text">{{ row.username }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" min-width="80">
          <template #default="{ row }">
            <span class="wrap-text">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="permissions" label="权限" min-width="120">
          <template #default="{ row }">
            <div class="permissions-container" @click="handleEditPermissions(row)"
              :class="{ 'clickable-permissions': row.admin_id !== currentAdmin }">
              <el-tag v-for="permission in filterHiddenPermissions(parsePermissions(row.permissions))" :key="permission"
                class="permission-tag" size="small" type="info" :effect="'dark'">
                {{ PERMISSION_DESCRIPTIONS[permission as AdminPermission] || permission }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" min-width="120">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" class="clickable-tag" @click="handleToggleStatus(row)"
              v-bind="row.admin_id === currentAdmin ? { disabled: '' } : {}">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <el-button link @click="showInactiveAdmins = !showInactiveAdmins">
          {{ showInactiveAdmins ? '隐藏' : '显示' }}已禁用管理员
        </el-button>
      </div>
    </el-card>

    <!-- 创建管理员对话框 -->
    <el-dialog v-model="createDialogVisible" title="创建管理员" width="500px">
      <el-form ref="createFormRef" :model="createForm" :rules="formRules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="createForm.username" maxlength="32" />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input v-model="createForm.password" type="password" maxlength="32" show-password />
        </el-form-item>

        <el-form-item label="姓名" prop="name">
          <el-input v-model="createForm.name" maxlength="32" />
        </el-form-item>

        <el-form-item label="权限" prop="permissions">
          <el-checkbox-group v-model="createForm.permissions">
            <el-checkbox v-for="permission in visiblePermissions" :key="permission" :value="permission">
              {{ PERMISSION_DESCRIPTIONS[permission] }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCreate">创建</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改权限对话框 -->
    <el-dialog v-model="editPermissionsDialogVisible" title="修改权限" width="500px">
      <el-form ref="editPermissionsFormRef" :model="editPermissionsForm" :rules="{ permissions: formRules.permissions }"
        label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="editPermissionsForm.username" disabled />
        </el-form-item>

        <el-form-item label="姓名">
          <el-input v-model="editPermissionsForm.name" disabled />
        </el-form-item>

        <el-form-item label="权限" prop="permissions">
          <el-checkbox-group v-model="editPermissionsForm.permissions">
            <el-checkbox v-for="permission in visiblePermissions" :key="permission" :value="permission">
              {{ PERMISSION_DESCRIPTIONS[permission] }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editPermissionsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpdatePermissions">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { permissionApi } from "@/api";
import { formatDate } from "@/utils/format";
import { ElMessage, ElMessageBox } from "element-plus";
import type { AdminData } from "@packages/shared";
import { AdminPermission, PERMISSION_DESCRIPTIONS } from "@packages/shared";
import { useAuthStore } from "@/stores/auth";

const admins = ref<AdminData[]>([]);
const createDialogVisible = ref(false);
const showInactiveAdmins = ref(false);
const createFormRef = ref();
const editPermissionsDialogVisible = ref(false);
const editPermissionsFormRef = ref();
const editPermissionsForm = ref({
  admin_id: 0,
  username: "",
  name: "",
  permissions: [] as AdminPermission[],
});

const createForm = ref({
  username: "",
  password: "",
  name: "",
  permissions: [] as AdminPermission[],
});

const availablePermissions = Object.values(AdminPermission).filter(
  (permission) => permission !== AdminPermission.ADMIN,
);

// OrderManage权限相关UI暂时隐藏，但默认勾选此权限
// 因为发起增改请求功能暂时移除，但需保留权限结构
const hiddenPermissions: AdminPermission[] = [AdminPermission.ORDER_MANAGE];
const visiblePermissions = availablePermissions.filter(
  (permission) => !hiddenPermissions.includes(permission),
);

const filteredAdmins = computed(() => {
  return admins.value.filter((admin) =>
    showInactiveAdmins.value ? true : admin.is_active,
  );
});

const loadAdmins = async () => {
  try {
    admins.value = (await permissionApi.getAllAdmins()) || [];
  } catch (error) {
    console.error("Failed to load admins:", error);
    ElMessage.error("获取管理员列表失败");
  }
};

const formRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 32,
      message: "长度应在3到32个字符之间",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    {
      min: 6,
      max: 32,
      message: "长度应在6到32个字符之间",
      trigger: "blur",
    },
  ],
  permissions: [
    {
      type: "array",
      required: true,
      message: "请至少选择一个权限",
      trigger: "change",
    },
  ],
  name: [
    { required: true, message: "请输入姓名", trigger: "blur" },
    {
      min: 2,
      max: 32,
      message: "长度应在2到32个字符之间",
      trigger: "blur",
    },
  ],
};

const handleCreate = async () => {
  if (!createFormRef.value) return;

  try {
    await createFormRef.value.validate();
    const { username, password, name, permissions } = createForm.value;

    // 自动添加被隐藏的ORDER_MANAGE权限
    const finalPermissions = [...permissions];
    if (!finalPermissions.includes(AdminPermission.ORDER_MANAGE)) {
      finalPermissions.push(AdminPermission.ORDER_MANAGE);
    }

    await permissionApi.createAdmin(username, password, name, finalPermissions);
    ElMessage.success("管理员创建成功");
    createDialogVisible.value = false;
    await loadAdmins();
  } catch (error) {
    console.error("Failed to create admin:", error);
    ElMessage.error("创建管理员失败");
  }
};

const handleToggleStatus = async (admin: AdminData) => {
  // 检查是否为当前账号（即主管理员账号）
  if (admin.admin_id === currentAdmin.value) {
    ElMessage.warning("不能禁用主管理员账号");
    return;
  }

  try {
    const action = admin.is_active ? "禁用" : "启用";
    await ElMessageBox.confirm(
      `确定要${action}管理员 ${admin.username} 吗？`,
      "警告",
      {
        confirmButtonText: action === "启用" ? "启用" : "禁用",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    await permissionApi.toggleAdminStatus(admin.admin_id);
    ElMessage.success(`管理员${action}成功`);
    await loadAdmins();
  } catch (error) {
    if (error !== "cancel") {
      console.error("Failed to toggle admin status:", error);
      ElMessage.error("更新管理员状态失败");
    }
  }
};

// 获取当前管理员ID
const currentAdmin = computed(() => useAuthStore().adminId);

const parsePermissions = (permissions: string | string[]): string[] => {
  if (typeof permissions === "string") {
    // 处理 "{admin}" 格式的字符串
    return permissions.replace(/[{}]/g, "").split(",");
  }
  return Array.isArray(permissions) ? permissions : [];
};

// 过滤隐藏权限（用于权限显示）
const filterHiddenPermissions = (permissions: string[]): string[] => {
  return permissions.filter(
    (permission) => !hiddenPermissions.includes(permission as AdminPermission),
  );
};

const handleEditPermissions = (admin: AdminData) => {
  // 检查是否为当前账号（即主管理员账号）
  if (admin.admin_id === currentAdmin.value) {
    ElMessage.warning("不能修改主管理员账号权限");
    return;
  }

  editPermissionsForm.value = {
    admin_id: admin.admin_id,
    username: admin.username,
    name: admin.name,
    permissions: parsePermissions(admin.permissions).filter(
      (permission) =>
        !hiddenPermissions.includes(permission as AdminPermission),
    ) as AdminPermission[],
  };

  editPermissionsDialogVisible.value = true;
};

const handleUpdatePermissions = async () => {
  if (!editPermissionsFormRef.value) return;

  try {
    await editPermissionsFormRef.value.validate();
    const { admin_id, permissions } = editPermissionsForm.value;

    // 自动添加被隐藏的ORDER_MANAGE权限
    const finalPermissions = [...permissions];
    if (!finalPermissions.includes(AdminPermission.ORDER_MANAGE)) {
      finalPermissions.push(AdminPermission.ORDER_MANAGE);
    }

    await permissionApi.updateAdminPermissions(admin_id, finalPermissions);
    ElMessage.success("权限更新成功");
    editPermissionsDialogVisible.value = false;
    await loadAdmins();
  } catch (error) {
    console.error("Failed to update admin permissions:", error);
    ElMessage.error("更新权限失败");
  }
};

onMounted(loadAdmins);
</script>

<style scoped lang="scss">
.permissions-view {
  h2 {
    margin: 0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .wrap-text {
    word-break: break-word;
    white-space: normal;
    line-height: 1.5;
  }

  .permissions-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    padding: 4px 0;
  }

  .permission-tag {
    margin: 0;
    white-space: nowrap;
  }

  :deep(.el-table) {
    background-color: var(--el-bg-color-overlay);
    color: var(--el-text-color-primary);

    // 修改表头样式
    th.el-table__cell {
      background-color: var(--el-bg-color-overlay);
      color: var(--el-text-color-regular);
      border-bottom: 1px solid var(--el-border-color-lighter);
    }

    // 修改单元格样式
    td.el-table__cell {
      background-color: var(--el-bg-color-overlay);
      border-bottom: 1px solid var(--el-border-color-lighter);
      color: var(--el-text-color-primary);
    }

    // 修改悬浮效果
    tr.el-table__row {
      &:hover>td.el-table__cell {
        background-color: var(--el-table-row-hover-bg-color) !important;
      }

      &.hover-row>td.el-table__cell {
        background-color: var(--el-table-row-hover-bg-color) !important;
      }
    }

    .el-table__cell {
      .cell {
        white-space: normal;
        line-height: 1.5;
      }
    }
  }

  .table-footer {
    margin-top: 16px;
    text-align: center;
  }

  .clickable-tag {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }

    &[disabled] {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  :deep(.el-dialog) {
    background: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color);

    .el-dialog__title {
      color: var(--el-text-color-primary);
    }

    .el-dialog__body {
      color: var(--el-text-color-primary);
    }
  }

  :deep(.el-form-item__label) {
    color: var(--el-text-color-primary);
  }

  :deep(.el-input__wrapper) {
    background-color: var(--el-bg-color);
  }

  :deep(.el-input__inner) {
    color: var(--el-text-color-primary);
  }

  :deep(.el-checkbox__label) {
    color: var(--el-text-color-regular);
  }

  .clickable-permissions {
    cursor: pointer;

    &:hover {
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
    }
  }
}
</style>
