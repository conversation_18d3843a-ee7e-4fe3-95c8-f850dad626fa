<template>
  <input :value="displayValue" @input="handleInput" @blur="handleBlur" link inputmode="decimal"
    :placeholder="placeholder" :required="required" />
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

const props = defineProps<{
	modelValue: number | string;
	placeholder?: string;
	required?: boolean;
}>();

const emit = defineEmits(["update:modelValue"]);

const rawValue = ref(props.modelValue.toString());

function formatFloat(value: string): string {
	return value.replace(/[^\d.]/g, "").replace(/(\..*)\./g, "$1");
}

const displayValue = computed(() => {
	const [intPart, decimalPart] = formatFloat(rawValue.value)
		.toString()
		.split(".");
	if (!intPart && !decimalPart) return "";

	const formattedInt = intPart
		? Number.parseInt(intPart.replace(/,/g, "")).toLocaleString("en-US")
		: "0";
	return decimalPart ? `${formattedInt}.${decimalPart}` : formattedInt;
});

watch(
	() => props.modelValue,
	(newValue) => {
		rawValue.value = newValue.toString();
	},
);

function formatFull(value: string): string {
	const [intPart, decimalPart] = formatFloat(value).split(".");
	if (!intPart && !decimalPart) return "";

	const formattedInt = intPart
		? Number.parseInt(intPart.replace(/,/g, "")).toLocaleString("en-US")
		: "0";
	const formattedDecimal = decimalPart
		? decimalPart.padEnd(2, "0").slice(0, 2)
		: "00";
	return `${formattedInt}.${formattedDecimal}`;
}

function handleInput(event: Event) {
	const input = event.target as HTMLInputElement;
	let value = input.value.replace(/[^\d.]/g, "");

	// 确保只有一个小数点
	const decimalIndex = value.indexOf(".");
	if (decimalIndex !== -1) {
		value =
			value.slice(0, decimalIndex + 1) +
			value.slice(decimalIndex + 1).replace(/\./g, "");
	}

	rawValue.value = value;
	const emitValue = value === "" ? "" : Number.parseFloat(value) || 0;
	emit("update:modelValue", emitValue);
}

function handleBlur() {
	rawValue.value = formatFull(rawValue.value);
}
</script>

<style scoped>
input {
  font-size: 16px;
  color: var(--color-on-surface);
  background-color: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-5);
  border-radius: var(--el-border-radius-base);
  padding: 6px 8px;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
  width: 160px;
  text-align: center;
}

input:hover,
input:focus {
  border-color: var(--el-color-primary);
  box-shadow: var(--box-shadow);
}

input:focus::placeholder {
  opacity: 0.5;
}
</style>
