// 业务配置
export interface BusinessConfig {
	// 限制
	STOCK_SCALE_LIMIT: number;
	TOTAL_SCALE_LIMIT: number;
	// 参数
	OPTION_MULTIPLIER: number;
	PUT_MULTIPLIER: number;
	MONTH_MULTIPLIER: number;
	TWO_WEEKS_MULTIPLIER: number;
	DISCOUNT_MULTIPLIER: number;
	// 最小报价结构
	MIN_QUOTE_STRUCTURE_100: number;
	MIN_QUOTE_STRUCTURE_103: number;
	MIN_QUOTE_STRUCTURE_105: number;
	MIN_QUOTE_STRUCTURE_110: number;
	// 通道预授权额度
	CHANNEL_CREDIT_LIMIT: number;
}

// 平台配置
export interface PlatformConfig {
	quote_providers: {
		[provider: string]: {
			enabled: boolean;
			price_adjustment: number;
			display_name: string;
		};
	};
	profit_sharing: {
		enabled: boolean;
		percentage: number; // 盈利分成比例，0-100之间的数值
	};
	// 邮件配置
	email_display_name?: string; // 邮箱发送人显示名
	qualification_audit_email?: string[]; // 资质审核通知邮箱，最多5个
	fund_audit_email?: string[]; // 资金审核通知邮箱，最多5个
}

// 共享配置（交易台到通道）
export interface SharedConfig {
	// 协议模板配置
	agreement_templates: Record<
		string,
		{
			filter: { uid: string; name: string };
			stamp: Array<[number, number, number]>; // [页码, X坐标, Y坐标]
			signature: Array<[number, number, number]>; // [页码, X坐标, Y坐标]
		}
	>;
	// 通道管理配置
	channel_management?: {
		enable_transfer_auth?: boolean; // 是否启用转账授权功能
		enable_external_order?: boolean; // 是否启用外部报价下单功能
	};
	// 未来可能需要添加的其他共享配置
	// channel_settings: Record<string, any>;
	// global_parameters: Record<string, any>;
}

export interface PlatformConfigHistory {
	config_id: number;
	config: PlatformConfig;
	created_at: string;
	admin_id?: number;
}

// 系统状态相关
export enum StatusChange {
	MANUAL = "manual", // 手动操作
	AUTOMATIC = "automatic", // 自动（日常开关市）
	RISK_CONTROL = "risk_control", // 风控触发
}

// 基础状态接口（用于历史记录）
export interface BaseSystemStatus {
	SYSTEM_ENABLED: boolean;
	POSITION_ENTRY_ENABLED: boolean;
	INQUIRY_ENABLED: boolean;
	change_type: StatusChange;
}

// 完整状态接口（包含自动管理配置）
export interface SystemStatus extends BaseSystemStatus {
	auto_manage_enabled: boolean[];
}

// 历史记录项使用基础状态
export interface StatusHistoryItem {
	status_id: number;
	status: BaseSystemStatus;
	changed_at: string;
	admin_id?: number;
}

export interface StockData {
	ts_code: string;
	scale: number;
}
