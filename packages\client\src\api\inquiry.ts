import { request } from "./request";
import type { InquiriesResponse } from "./types";
import type {
	SearchStocksResponse,
	BatchQuoteRequest,
	QuoteResponse,
} from "@packages/shared";

// 询价相关 API
export const inquiryApi = {
	searchStocks: (query: string, offset: number) =>
		request.get<SearchStocksResponse>("/inquiry/search", {
			params: { query, offset },
		}),

	createBatchQuotes: (data: BatchQuoteRequest) =>
		request.post<QuoteResponse[]>("/inquiry/quotes", data),

	getInquiries: (
		page: number,
		pageSize: number,
		isDescending: boolean,
		filters?: { ts_codes?: string[] },
	) =>
		request.get<InquiriesResponse>("/inquiry/list", {
			params: {
				page,
				pageSize,
				isDescending,
				ts_codes: filters?.ts_codes?.join(","),
			},
		}),
} as const;
