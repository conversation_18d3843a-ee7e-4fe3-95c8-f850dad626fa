/* 视图容器通用样式 */
.view {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 卡片容器通用样式 */
.card {
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 20px 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color);
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--el-color-primary-dark-2);
}

/* 表格通用样式 */
.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--el-border-color);
}

.data-table th {
  background-color: var(--el-fill-color);
  font-weight: 500;
}

.data-table tr:hover {
  background-color: var(--el-fill-color-light);
}

/* 操作按钮通用样式 */
.action-button {
  padding: 4px 12px;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  background-color: var(--el-bg-color-overlay);
  cursor: pointer;
  transition: all 0.25s ease;
}

.action-button:hover {
  border-color: var(--el-color-primary);
  color: var(--el-text-color-primary);
}

/* 表单组通用样式 */
.form-group {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 5px;
}

/* 筛选行通用样式 */
.filter-row {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

/* 表格加载遮罩 */
.table-container {
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
  transition: opacity 0.3s ease;
  opacity: 0;
  /* 不可见时，禁用点击 */
  pointer-events: none;
}

.loading-overlay.visible {
  opacity: 1;
  /* 可见时，启用点击 */
  pointer-events: auto;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .card {
    padding: 8px 0;
    margin: 8px 0;
    border-left: none;
    border-right: none;
    border-bottom: none;
    border-radius: 0;
    box-shadow: none;

    /* 移动端下减小卡片内边距 */
    .card-header {
      padding: 12px;
      border-bottom: none;
      margin-bottom: 0;
    }
  }

  /* 调整表格在移动端的边距 */
  .data-table th,
  .data-table td {
    padding: 8px;
  }

  .form-group {
    gap: 12px;
  }
}

// 对话框 header 移除关闭按钮 padding
.el-dialog__header.show-close {
  padding-right: 0;
}

// index.css 效果补偿
:root {
  color: var(--el-text-color-primary);
}

button {
  border-radius: var(--el-border-radius-base);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  color: var(--el-text-color-primary);
}