import { Router } from "express";
import { requirePermission } from "@/middlewares/adminPermission.js";
import { wrapRoute, wrapAdminRoute } from "@/utils/routeWrapper.js";
import * as siteConfigService from "@/services/admin/siteConfigService.js";
import multer from "multer";
import { AppError } from "@/core/appError.js";
import { AdminPermission } from "@packages/shared";
import type { SiteConfig } from "@packages/shared";
import { authenticateAdmin } from "@/middlewares/jwtAuth.js";
import { csrfProtection } from "@/middlewares/csrf.js";

const router = Router();
const upload = multer({ storage: multer.memoryStorage() });

/**
 * @api {get} /admin/site-config Get site configuration
 * @apiName GetSiteConfig
 * @apiGroup Admin/SiteConfig
 * @apiDescription Get site configuration information
 */
router.get(
	"/",
	wrapRoute(async (req, res) => {
		const siteConfig = await siteConfigService.getSiteConfig();
		res.status(200).json(siteConfig);
	}),
);

/**
 * @api {post} /admin/site-config Update site configuration
 * @apiName UpdateSiteConfig
 * @apiGroup Admin/SiteConfig
 * @apiDescription Update site configuration information
 */
router.post(
	"/",
	csrfProtection,
	authenticateAdmin,
	requirePermission(AdminPermission.CONFIG),
	wrapAdminRoute<SiteConfig>(async (req, res) => {
		const updates = req.body;
		const result = await siteConfigService.updateSiteConfig(
			updates,
			req.jwt.admin_id,
		);
		res.status(200).json(result);
	}),
);

/**
 * @api {get} /admin/site-config/history Get site configuration history
 * @apiName GetSiteConfigHistory
 * @apiGroup Admin/SiteConfig
 * @apiDescription Get history of site configuration changes with pagination
 */
router.get(
	"/history",
	csrfProtection,
	authenticateAdmin,
	requirePermission(AdminPermission.CONFIG),
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder = req.query.sortOrder as "ASC" | "DESC";

		const history = await siteConfigService.getSiteConfigHistory(
			page,
			pageSize,
			{
				sortBy,
				sortOrder,
			},
		);
		res.status(200).json(history);
	}),
);

/**
 * @api {post} /admin/site-config/upload-icon Upload icon
 * @apiName UploadIcon
 * @apiGroup Admin/SiteConfig
 * @apiDescription Upload site favicon or logo
 */
router.post(
	"/upload-icon",
	csrfProtection,
	authenticateAdmin,
	requirePermission(AdminPermission.CONFIG),
	upload.single("file"),
	wrapAdminRoute(async (req, res) => {
		if (!req.file) {
			throw AppError.create("BAD_REQUEST", "No file uploaded");
		}

		const type = (req.query.type as "favicon" | "logo") || "favicon";
		if (type !== "favicon" && type !== "logo") {
			throw AppError.create("BAD_REQUEST", "Invalid icon type");
		}

		const result = await siteConfigService.uploadIcon(
			req.file,
			type,
			req.jwt.admin_id,
		);

		res.status(200).json(result);
	}),
);

/**
 * @api {get} /admin/site-config/asset/:assetId Get asset by ID
 * @apiName GetAsset
 * @apiGroup Admin/SiteConfig
 * @apiDescription Get an asset by its ID
 */
router.get(
	"/asset/:assetId",
	wrapRoute(async (req, res) => {
		try {
			const assetId = req.params.assetId;
			const asset = await siteConfigService.getAsset(assetId);

			// 设置适当的内容类型
			res.set("Content-Type", asset.mimeType);
			// 设置缓存策略
			res.set("Cache-Control", "public, max-age=86400");

			// 返回资源内容
			res.send(asset.content);
		} catch (error) {
			// 处理错误情况
			if (error instanceof AppError) {
				const status =
					error.errorKey === "NOT_FOUND"
						? 404
						: error.errorKey === "ASSET_RETRIEVAL_FAILED"
							? 500
							: 500;
				res.status(status).send(error.message);
			} else {
				res.status(500).send("Server error");
			}
		}
	}),
);

export default router;
