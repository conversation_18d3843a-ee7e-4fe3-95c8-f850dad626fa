# 分布式锁验证指南

## 📋 概述

本指南将帮助您在运行多个INK数据同步实例时，验证分布式锁机制是否正常工作。

## 🔧 验证工具

### 1. 锁验证工具 (`lockVerification.ts`)

提供多种验证方法：

```bash
# 进入项目目录
cd packages/server

# 监控锁状态（持续10分钟）
pnpm tsx src/queue/ink/lockVerification.ts monitor 10

# 锁竞争测试（持续60秒）
pnpm tsx src/queue/ink/lockVerification.ts test 60

# 生成验证报告
pnpm tsx src/queue/ink/lockVerification.ts report

# 检测锁泄漏
pnpm tsx src/queue/ink/lockVerification.ts leaks
```

### 2. 实时仪表板 (`lockDashboard.ts`)

可视化锁状态监控：

```bash
# 启动实时仪表板
pnpm tsx src/queue/ink/lockDashboard.ts

# 按 Ctrl+C 退出
```

### 3. 健康检查工具

```bash
# 执行健康检查
pnpm tsx src/queue/ink/healthCheck.ts

# 或使用集成的测试脚本
pnpm tsx src/queue/ink/test-refactor.ts
```

## 🚀 验证步骤

### 第一步：准备环境

1. **确保Redis服务运行**
   ```bash
   # 检查Redis状态
   redis-cli ping
   # 应该返回 PONG
   ```

2. **启动第一个实例**
   ```bash
   # 终端1
   cd packages/server
   pnpm start
   ```

3. **启动第二个实例**
   ```bash
   # 终端2 - 使用不同端口
   cd packages/server
   PORT=3001 pnpm start
   ```

### 第二步：实时监控锁状态

在第三个终端窗口启动仪表板：

```bash
# 终端3
cd packages/server
pnpm tsx src/queue/ink/lockDashboard.ts
```

仪表板会显示：
- 🔒 **锁状态表格**：实时显示每个锁的状态
- 📊 **锁统计信息**：活跃锁数量和利用率
- 📝 **最近活动**：锁的获取和释放记录
- 🟢 **健康状态**：系统整体健康情况

### 第三步：观察锁竞争

正常情况下，您应该看到：

#### ✅ 正常现象
```
INK数据更新        🔒 已锁定    platform:001     120s     45s
价格报价更新       🔓 空闲      -               -        -
业务配置更新       🔓 空闲      -               -        -
同步初始化         🔓 空闲      -               -        -

最近活动:
14:30:15 获取 INK数据更新     by platform:001
14:29:50 释放 价格报价更新    by platform:002
14:29:48 获取 价格报价更新    by platform:002
```

#### ⚠️ 异常现象
- 同一时间多个实例持有相同的锁
- 锁长时间未释放（超过10分钟）
- 频繁的锁获取失败

### 第四步：主动测试锁竞争

在第四个终端运行竞争测试：

```bash
# 终端4
cd packages/server
pnpm tsx src/queue/ink/lockVerification.ts test 120
```

测试结果示例：
```
=== 锁竞争测试结果 ===
总尝试次数: 24
成功获取: 12 (50.0%)
获取失败: 12 (50.0%)  
冲突次数: 10 (41.7%)
✅ 检测到锁冲突，说明分布式锁机制正常工作！
```

### 第五步：检查业务日志

在两个实例的日志中查找锁相关信息：

```bash
# 在各个实例的日志中搜索锁信息
grep -i "lock" logs/app.log | tail -20

# 查找具体的锁操作
grep "Lock acquired\|Lock released" logs/app.log
```

期望看到的日志：
```
2023-05-15 14:30:15 INFO [DistributedLockManager] Lock acquired: ink:data:update:lock by platform:001
2023-05-15 14:30:18 INFO [DistributedLockManager] Lock renewed: ink:data:update:lock
2023-05-15 14:30:35 INFO [DistributedLockManager] Lock released: ink:data:update:lock by platform:001
2023-05-15 14:30:36 INFO [DistributedLockManager] Failed to acquire lock: ink:data:update:lock, held by: platform:002
```

## 📊 验证指标

### 关键指标说明

| 指标 | 正常值 | 异常值 | 说明 |
|------|--------|--------|------|
| **锁冲突率** | 20-60% | <5% 或 >90% | 太低说明只有一个实例，太高说明竞争激烈 |
| **锁持有时间** | 1-300秒 | >600秒 | 超过10分钟可能是锁泄漏 |
| **锁利用率** | 10-80% | >90% | 过高可能影响性能 |
| **获取成功率** | 30-70% | <10% | 太低说明锁竞争过于激烈 |

### Redis直接检查

```bash
# 直接查看Redis中的锁
redis-cli keys "*lock*"

# 查看特定锁的值和TTL
redis-cli get ink:data:update:lock
redis-cli ttl ink:data:update:lock

# 监控Redis操作
redis-cli monitor | grep -i lock
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 未检测到锁冲突
**现象**：竞争测试显示冲突次数为0

**可能原因**：
- 只有一个实例在运行
- 实例启动时间不同，还未产生竞争
- 配置问题导致不使用分布式锁

**解决方案**：
```bash
# 检查进程数量
ps aux | grep node | grep ink

# 检查不同端口的实例
netstat -tlnp | grep :300

# 强制触发锁竞争
pnpm tsx src/queue/ink/lockVerification.ts test 300
```

#### 2. 锁长时间未释放
**现象**：仪表板显示锁持有时间超过10分钟

**排查步骤**：
```bash
# 检查持有锁的实例是否还在运行
redis-cli get ink:data:update:lock
# 返回: "platform:001:1684156215"

# 检查该实例状态
ps aux | grep platform:001

# 如果实例已死，手动清理锁
redis-cli del ink:data:update:lock
```

#### 3. 频繁的锁获取失败
**现象**：获取成功率低于10%

**可能原因**：
- 锁持有时间过长
- 并发实例过多
- Redis性能瓶颈

**解决方案**：
```bash
# 调整锁过期时间
export INK_SYNC_LOCK_EXPIRY=180

# 检查Redis性能
redis-cli --latency-history

# 查看Redis内存使用
redis-cli info memory
```

#### 4. 锁机制完全失效
**现象**：多个实例同时持有相同锁

**紧急处理**：
```bash
# 立即停止所有实例
pkill -f "ink.*worker"

# 清理所有锁
redis-cli eval "return redis.call('del', unpack(redis.call('keys', 'ink:*:lock')))" 0

# 检查Redis配置
redis-cli config get save
redis-cli config get appendonly
```

## 📈 监控和告警

### 自动化监控脚本

创建监控脚本 `monitor-locks.sh`：

```bash
#!/bin/bash
# monitor-locks.sh

while true; do
    echo "=== $(date) ==="
    
    # 生成验证报告
    cd /path/to/packages/server
    pnpm tsx src/queue/ink/lockVerification.ts report
    
    # 检查异常情况
    if pnpm tsx src/queue/ink/lockVerification.ts leaks | grep -q "检测到"; then
        echo "⚠️ 发现锁泄漏，发送告警..."
        # 这里可以添加告警逻辑（邮件、Slack等）
    fi
    
    echo "下次检查将在5分钟后进行..."
    sleep 300
done
```

### 集成到监控系统

如果您使用Prometheus监控：

```bash
# 导出指标到Prometheus格式
pnpm tsx -e "
import { metricsCollector } from './src/queue/ink/metricsCollector.js';
console.log(await metricsCollector.exportMetrics('prometheus'));
"
```

## 🎯 最佳实践

### 1. 监控频率
- **开发环境**：每30秒检查一次
- **测试环境**：每2分钟检查一次  
- **生产环境**：每5分钟检查一次

### 2. 告警阈值
- 锁持有时间 > 5分钟：发出警告
- 锁持有时间 > 10分钟：发出严重告警
- 锁利用率 > 90%：性能警告
- 连续5次获取失败：竞争激烈告警

### 3. 日志保留
- 锁操作日志保留7天
- 健康检查报告保留30天
- 性能指标保留90天

### 4. 定期维护
- 每周检查一次锁泄漏
- 每月清理过期的锁记录
- 每季度评估锁配置的合理性

## 🔧 配置调优

根据验证结果调整配置：

```bash
# 如果锁竞争激烈，增加锁过期时间
export INK_SYNC_LOCK_EXPIRY=600

# 如果锁持有时间过长，减少续期间隔
export INK_SYNC_LOCK_RENEWAL_INTERVAL=60

# 如果并发度不够，减少锁过期时间
export INK_SYNC_LOCK_EXPIRY=120
```

## 📞 获得帮助

如果遇到问题：

1. **查看健康检查报告**
   ```bash
   pnpm tsx src/queue/ink/test-refactor.ts
   ```

2. **查看详细日志**
   ```bash
   export LOG_LEVEL=debug
   ```

3. **重启服务**
   ```bash
   # 优雅重启
   pkill -TERM -f "ink.*worker"
   ```

4. **联系开发团队**
   - 提供监控截图
   - 提供验证报告
   - 描述具体现象

---

通过以上验证步骤，您可以全面了解分布式锁机制在多实例环境中的工作状态，确保系统的稳定性和数据一致性。 