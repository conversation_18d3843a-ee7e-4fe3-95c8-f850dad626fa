import { fetchCurrentPrice } from "@/financeUtils/marketData.js";
import type { PendingOrderData, SellingOrderData } from "@packages/shared";
import { NotificationType, OrderStatus, OrderType } from "@packages/shared";
import * as ConfirmOrder from "./confirmOrder.js";
import * as PendingOrder from "@/models/trade/pendingOrder.js";
import logger from "@/utils/logger.js";
import * as NotifService from "@/services/notifService.js";
import { AppError } from "@/core/appError.js";
import { ENV } from "@/config/configManager.js";

export class LimitStrategy {
	private activeOrders = new Map<number, NodeJS.Timeout>();
	private readonly INTERVAL = 5000; // 5 seconds，充足的处理时间
	private isInitialized = false;

	private constructor() {}
	private static instance: LimitStrategy;

	static getInstance(): LimitStrategy {
		if (!LimitStrategy.instance) {
			LimitStrategy.instance = new LimitStrategy();
		}
		return LimitStrategy.instance;
	}

	async initialize(): Promise<void> {
		if (this.isInitialized) return;

		// 只有生产环境的主进程处理任务
		const isPrimaryProcess =
			ENV.NODE_ENV === "production" &&
			Number(process.env.pm_id) === ENV.PRIMARY_PROCESS_ID;

		if (!isPrimaryProcess) {
			logger.info("Skipping limit orders monitoring in non-primary process");
			this.isInitialized = true;
			return;
		}

		try {
			const pendingOrders = await PendingOrder.findAllLimitOrders();
			for (const order of pendingOrders) {
				await this.startMonitoring(order);
			}
			this.isInitialized = true;
			logger.info(
				`Initialized ${pendingOrders.length} limit orders monitoring`,
			);
		} catch (error) {
			logger.error(error, "Failed to initialize limit orders");
			throw error;
		}
	}

	async execute(
		order: PendingOrderData & { limit_price: number },
	): Promise<void> {
		await this.startMonitoring(order);
	}

	private async startMonitoring(
		order: PendingOrderData & { limit_price: number },
	): Promise<void> {
		let isExecuting = false;
		// 调用 clearInterval 时：
		// ? 只是阻止了新的回调被添加到队列中，但已经在执行中的回调会继续执行完成
		let isStopped = false;

		// ? pm2 cluster模式下，所有需要2次操作的定时器都要考虑进程！
		const checkOrder = async () => {
			// 每次检查前先从数据库获取最新订单状态
			const freshOrder = await PendingOrder.findById(order.pending_id);
			if (!freshOrder) {
				logger.info(
					`Limit order ${order.pending_id} no longer exists, stopping monitoring`,
				);
				this.cleanupOrder(order.pending_id);
				return;
			}

			if (isExecuting || isStopped) {
				logger.info(
					`Limit order ${order.pending_id} is ${isExecuting ? "executing" : "stopped"}, skip checking`,
				);
				return;
			}

			try {
				const currentPrice = (await fetchCurrentPrice(order.ts_code)).price;

				let shouldExecute = false;
				const isCall = order.structure.endsWith("C");
				if (order.status === OrderStatus.LIMIT_BUYING) {
					// 买入订单：
					// 看涨期权(Call)：当前价格需要低于等于限价
					// 看跌期权(Put)：当前价格需要高于等于限价
					shouldExecute = isCall
						? currentPrice <= order.limit_price
						: currentPrice >= order.limit_price;
				} else {
					// 卖出订单：
					// 看涨期权(Call)：当前价格需要高于等于限价
					// 看跌期权(Put)：当前价格需要低于等于限价
					shouldExecute = isCall
						? currentPrice >= order.limit_price
						: currentPrice <= order.limit_price;
				}
				logger.info(
					`Limit order ${order.pending_id} is checking, current price: ${currentPrice}, limit price: ${order.limit_price}`,
				);

				if (shouldExecute) {
					isExecuting = true;
					try {
						await this.executeLimitOrder(order);
						isStopped = true;
					} finally {
						isExecuting = false;
					}
				}
			} catch (error) {
				isExecuting = false;
				logger.error(error, `Error monitoring limit order ${order.pending_id}`);
			}
		};

		// 立即执行一次检查
		await checkOrder();

		if (!isStopped) {
			// 设置定时检查
			const interval = setInterval(checkOrder, this.INTERVAL);
			logger.info(
				`Set interval ${interval} for limit order ${order.pending_id}`,
			);
			this.activeOrders.set(order.pending_id, interval);
		}
	}

	private async executeLimitOrder(order: PendingOrderData): Promise<void> {
		try {
			// 在执行订单前，再次检查数据库中订单是否还存在
			const freshOrder = await PendingOrder.findById(order.pending_id);
			if (!freshOrder) {
				logger.info(
					`Limit order ${order.pending_id} no longer exists, skipping execution`,
				);
				this.cleanupOrder(order.pending_id); // 清理当前进程中的定时器
				return;
			}

			// ConfirmOrder.execute 支持部分卖出和全部卖出
			await ConfirmOrder.executeLimit(order, {
				orderType: OrderType.LIMIT,
			});
			logger.info(`Limit order ${order.pending_id} executed successfully`);
			// ConfirmOrder.execute 中完成先清理定时器，再关闭订单
		} catch (error) {
			logger.error(error, `Failed to execute limit order ${order.pending_id}`);

			// 提取特定错误进行用户友好的显示
			let errorReason = error instanceof AppError ? error.name : "未知错误";
			let errorMessage = "";

			// 针对价格波动错误提供更友好的消息
			if (
				error instanceof AppError &&
				error.name === "PRICE_VOLATILITY_EXCEEDED"
			) {
				errorReason = "价格波动超限";
				errorMessage = error.message;
			}

			await this.handleOrderFailure(order, {
				reason: errorReason,
				message: errorMessage,
			});
		}
	}

	private async handleOrderFailure(
		order: PendingOrderData,
		details?: { reason: string; message?: string; limit_price?: number },
	): Promise<void> {
		try {
			// 清理订单
			this.cleanupOrder(order.pending_id);

			// 构建通知内容
			let notificationContent = `您的限价挂单 ${order.ts_code} 执行失败。`;
			if (details?.reason) {
				notificationContent += `\n原因：${details.reason}`;
			}
			if (details?.message) {
				notificationContent += `\n${details.message}`;
			}

			// 通知用户
			NotifService.sendNotification(order.user_id, {
				title: "限价挂单执行失败",
				content: notificationContent,
				type: NotificationType.ORDER,
				metadata: {
					type: "pending_failed",
					trade_no: (order as SellingOrderData).trade_no,
					ts_code: order.ts_code,
					status: order.status,
					limit_price: details?.limit_price,
				},
			});
		} catch (error) {
			logger.error(
				error,
				`Failed to handle order failure for ${order.pending_id}`,
			);
		}
	}

	public async cleanupOrder(orderId: number): Promise<void> {
		const interval = this.activeOrders.get(orderId);
		if (interval) {
			clearInterval(interval);
			this.activeOrders.delete(orderId);
			logger.info(`Clear interval ${interval} for limit order ${orderId}`);
		}
		logger.info(`Limit order ${orderId} cleanup successfully`);
		await PendingOrder.close(orderId);
		await PendingOrder.deletePendingOrder(orderId);
	}

	public async cleanupAllOrders(): Promise<void> {
		for (const [orderId, interval] of this.activeOrders.entries()) {
			clearInterval(interval);
			this.activeOrders.delete(orderId);
			await PendingOrder.close(orderId);
			await PendingOrder.deletePendingOrder(orderId);
		}
	}
}

export const limitStrategy = LimitStrategy.getInstance();
