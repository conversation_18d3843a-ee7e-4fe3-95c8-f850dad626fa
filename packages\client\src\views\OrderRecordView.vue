<template>
  <div class="order-record-view view">
    <div class="pending-orders card">
      <div class="card-header clickable" @click="isPendingCollapsed = !isPendingCollapsed">
        <div class="card-title">挂单买入</div>
        <el-icon class="collapse-icon">
          <component :is="isPendingCollapsed ? ArrowDown : ArrowUp" />
        </el-icon>
      </div>

      <transition name="fade">
        <div v-show="!isPendingCollapsed">
          <TableWrapper v-model:page-size="pendingPageSize" v-model:current-page="pendingCurrentPage"
            v-model:is-descending="pendingIsDescending" :total-pages="pendingTotalPages">
            <LoadingState :loading="isPendingLoading" :has-data="pendingOrders.length > 0" :icon="DataLine" />
            <template v-if="!isPendingLoading && pendingOrders.length">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>标的</th>
                    <th>开仓日期</th>
                    <th>开仓名本</th>
                    <th>结构</th>
                    <th>期限</th>
                    <th>期权费率</th>
                    <th>期权费</th>
                    <th>限价</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="order in paginatedPendingOrders" :key="order.pending_id">
                    <td>{{ formatSubject(order.ts_code) }}</td>
                    <td>{{ formatDate(order.created_at!) }}</td>
                    <td>{{ order.scale + "万" }}</td>
                    <td>{{ formatStructure(order.structure) }}</td>
                    <td>{{ order.term === 14 ? '2周' : order.term + "个月" }}</td>
                    <td>{{ order.quote + '%' }}</td>
                    <td>{{ formatNumber(order.scale * order.quote / 100) + "万" }}</td>
                    <td>{{ order.limit_price || '-' }}</td>
                    <td :class="{ 'color-success': order.status === OrderStatus.HOLDING }">
                      {{ formatStatus(order.status) }}
                    </td>
                    <td>
                      <button class="action-button" @click="handleCancelOrder(order)">取消</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </template>
          </TableWrapper>
        </div>
      </transition>
    </div>

    <div class="order-record card">
      <div class="card-header">
        <div class="card-title">订单记录</div>
      </div>

      <div class="filter-row">
        <div class="form-group">
          <label for="order-dateRange">开仓日期：</label>
          <DateRangePicker id="order-dateRange" v-model:startDate="startDate" v-model:endDate="endDate"
            placeholder="所有" />
        </div>
        <div class="form-group">
          <label for="subject">选择标的：</label>
          <MySelect id="subject" v-model="selectedSubject" :options="subjectOptions" placeholder="全部" :page-size="20" />
        </div>
      </div>

      <TableWrapper v-model:page-size="pageSize" v-model:current-page="currentPage" v-model:is-descending="isDescending"
        :total-pages="totalPages">
        <LoadingState v-if="!orderRecords?.length" :loading="isLoading" :has-data="orderRecords?.length > 0"
          :icon="DataLine" />
        <template v-else>
          <div class="table-container">
            <LoadingState :loading="isLoading" :has-data="orderRecords?.length > 0" :icon="DataLine"
              :class="['loading-overlay', { 'visible': isLoading }]" />
            <table class="data-table">
              <thead>
                <tr>
                  <th>标的</th>
                  <th>开仓日期</th>
                  <th>状态</th>
                  <th class="desktop-only">开仓名本</th>
                  <th class="desktop-only">结构</th>
                  <th class="desktop-only">执行价</th>
                  <th class="desktop-only">期权费率</th>
                  <th>期权费</th>
                  <th class="desktop-only">成交价</th>
                  <th class="desktop-only">成交名本</th>
                  <!-- <th class="desktop-only">交易确认书</th> -->
                </tr>
              </thead>
              <tbody>
                <template v-for="order in orderRecords" :key="order.trade_no">
                  <tr class="order-row" :class="{ 'selected': selectedOrder?.trade_no === order.trade_no }"
                    @click="toggleOrderDetails(order)">
                    <td>{{ formatSubject(order.ts_code) }}</td>
                    <td>{{ formatDate(order.created_at!) }}</td>
                    <td :class="{ 'color-success': order.status === OrderStatus.HOLDING }">
                      {{ formatStatus(order.status) }}
                    </td>
                    <td class="desktop-only">{{ order.total_scale + "万" }}</td>
                    <td class="desktop-only">{{ formatStructure(order.structure) }}</td>
                    <td class="desktop-only">{{ Number(order.exercise_price).toFixed(2) }}</td>
                    <td class="desktop-only">{{ order.quote + '%' }}</td>
                    <td>{{ formatNumber(order.scale * order.quote / 100) + "万" }}</td>
                    <td class="desktop-only">{{ isBuyOrder(order.status) ? '-' : order.settle_price }}</td>
                    <td class="desktop-only">{{ isBuyOrder(order.status) ? '-' : formatNumber(order.scale) + "万" }}</td>
                    <!-- <td class="desktop-only">
                      <el-button class="action-button" @click.stop="handleOrder(order)">查看</el-button>
                    </td> -->
                  </tr>
                  <template v-if="selectedOrder?.trade_no === order.trade_no">
                    <tr class="mobile-only details-row">
                      <td colspan="4">
                        <div class="order-details details-transition">
                          <div class="detail-item">
                            <span class="label">开仓名本：</span>
                            <span class="value">{{ order.total_scale + "万" }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">结构：</span>
                            <span class="value">{{ formatStructure(order.structure) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">执行价：</span>
                            <span class="value">{{ Number(order.exercise_price).toFixed(2) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">期权费率：</span>
                            <span class="value">{{ order.quote + '%' }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">成交价：</span>
                            <span class="value">{{ isBuyOrder(order.status) ? '-' : order.settle_price }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">成交名本：</span>
                            <span class="value">{{ isBuyOrder(order.status) ? '-' : formatNumber(order.scale) + "万"
                            }}</span>
                          </div>
                          <!-- <div class="detail-item">
                            <el-button class="action-button" @click="handleOrder(order)">查看交易确认书</el-button>
                          </div> -->
                        </div>
                      </td>
                    </tr>
                    <!-- 添加一个空的 tr 来保持奇偶性 -->
                    <tr class="dummy-row"></tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </template>
      </TableWrapper>
    </div>

    <PdfPreview v-model="showPdfPreview" :pdf-url="pdfUrl" :title="pdfTitle" :filename="pdfFilename" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onActivated, onUnmounted } from "vue";
import { tradeApi } from "@/api";
import MySelect from "@/components/MySelect.vue";
import DateRangePicker from "@/components/DateRangePicker.vue";
import TableWrapper from "@/components/TableWrapper.vue";
import type { OrderData, BuyingOrderData } from "@packages/shared";
import { OrderStatus } from "@packages/shared";
import { ElMessage } from "element-plus";
// import { generateTradeConfirmation } from "@/utils/pdf-generator";
import { useStockStore } from "@/stores/stock";
import { DataLine, ArrowDown, ArrowUp } from "@element-plus/icons-vue";
import LoadingState from "@/components/LoadingState.vue";
import { useTableSettings } from "@/composables/useTableSettings";
import { formatStructure } from "@/utils/format";
import { eventBus } from "@/utils/eventBus.js";
import PdfPreview from "@/components/PdfPreview.vue";

const { formatSubject } = useStockStore();

// 状态管理
const orderRecords = ref<OrderData[]>([]);
const startDate = ref("");
const endDate = ref("");
const selectedSubject = ref<string[]>([]);
const isLoading = ref(false);

// Table Settings
const { initSettings, pageSize, isDescending, currentPage, totalPages } =
  useTableSettings();

// 添加挂单相关的状态
const pendingOrders = ref<BuyingOrderData[]>([]);
const isPendingLoading = ref(false);

// 添加挂单的分页设置
const pendingPageSize = ref(10);
const pendingCurrentPage = ref(1);
const pendingIsDescending = ref(true);
const pendingTotalPages = computed(() =>
  Math.ceil(pendingOrders.value.length / pendingPageSize.value),
);

// 添加挂单的分页计算属性
const paginatedPendingOrders = computed(() => {
  const start = (pendingCurrentPage.value - 1) * pendingPageSize.value;
  const end = start + pendingPageSize.value;
  return pendingOrders.value.slice(start, end);
});

// 添加新的响应式变量存储所有可用的标的代码
const availableTsCodes = ref<string[]>([]);

// 修改标的选项计算属性
const subjectOptions = computed(() => {
  if (!availableTsCodes.value) return [];

  return availableTsCodes.value.map((ts_code) => ({
    value: ts_code,
    label: formatSubject(ts_code),
  }));
});

// 方法
const fetchOrderRecords = async () => {
  try {
    isLoading.value = true;
    const response = await tradeApi.getOrderHistory(
      currentPage.value,
      pageSize.value,
      isDescending.value,
      {
        ts_codes: selectedSubject.value,
        startDate: startDate.value,
        endDate: endDate.value,
      },
    );
    orderRecords.value = response?.items || [];
    totalPages.value = Math.ceil((response?.total || 1) / pageSize.value);
    // 更新可用标的列表
    availableTsCodes.value = response?.ts_codes || [];
  } catch (error) {
    console.error("Failed to fetch order records:", error);
    ElMessage.error("加载订单记录失败");
  } finally {
    isLoading.value = false;
  }
};

const fetchPendingOrders = async () => {
  isPendingLoading.value = true;
  try {
    const buyOrders = await tradeApi.getPendingBuyHistory();
    pendingOrders.value =
      buyOrders?.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      ) || [];
    // 如果列表为空，自动折叠面板
    if (pendingOrders.value.length === 0) {
      isPendingCollapsed.value = true;
    }
  } catch (error) {
    console.error("Failed to fetch pending buy orders:", error);
    ElMessage.error("加载挂单记录失败");
  } finally {
    isPendingLoading.value = false;
  }
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString();
};

const formatNumber = (num: number) => {
  return new Intl.NumberFormat().format(num);
};

const formatStatus = (status: string) => {
  const statuses: Record<string, string> = {
    [OrderStatus.HOLDING]: "持有",
    [OrderStatus.SOLD]: "平仓",
    [OrderStatus.LIMIT_BUYING]: "限价待购",
    [OrderStatus.LIMIT_SELLING]: "限价待沽",
    [OrderStatus.VWAP_BUYING]: "均价待购",
    [OrderStatus.VWAP_SELLING]: "均价待沽",
  };
  return statuses[status] || status;
};

// 添加判断是否为买入订单的方法
const isBuyOrder = (status: string) => {
  return (
    status === OrderStatus.HOLDING ||
    status === OrderStatus.LIMIT_BUYING ||
    status === OrderStatus.VWAP_BUYING
  );
};

// 添加移动端检测
// const isMobile = () => {
// 	return window.innerWidth <= 768;
// };

// 查看交易确认书
// const handleOrder = async (order: OrderData) => {
// 	try {
// 		const pdfBlob = await generateTradeConfirmation(order);
// 		const url = URL.createObjectURL(pdfBlob);

// 		if (isMobile()) {
// 			// 移动端：新标签页打开
// 			window.open(url, "_blank");
// 			// 延迟释放 URL
// 			setTimeout(() => {
// 				URL.revokeObjectURL(url);
// 			}, 100);
// 		} else {
// 			// 桌面端：使用对话框预览
// 			pdfUrl.value = url;
// 			pdfTitle.value = "交易确认书";
// 			pdfFilename.value = `交易确认书_${order.trade_no}.pdf`;
// 			showPdfPreview.value = true;
// 		}
// 	} catch (error) {
// 		console.error("Failed to generate trade confirmation:", error);
// 		ElMessage.error("生成交易确认书失败");
// 	}
// };

const handleCancelOrder = async (order: BuyingOrderData) => {
  try {
    await tradeApi.cancelPendingOrder({ pending_id: order.pending_id });
    ElMessage.success("成功取消挂单");
  } catch (error) {
    console.error("Failed to cancel order:", error);
    ElMessage.error("取消挂单失败");
  }
};

// 添加 activated 钩子
onActivated(async () => {
  // 只在数据为空时加载数据
  if (orderRecords.value.length === 0) {
    await Promise.all([fetchOrderRecords(), fetchPendingOrders()]);
  }
});

onMounted(() => {
  // 只初始化设置，不加载数据
  initSettings();
  // 添加事件监听
  eventBus.on("order-updated", fetchOrderRecords);
  eventBus.on("order-updated", fetchPendingOrders);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  eventBus.off("order-updated", fetchOrderRecords);
  eventBus.off("order-updated", fetchPendingOrders);
});

// 监听筛选条件变化
watch([selectedSubject, startDate, endDate], () => {
  currentPage.value = 1; // 重置到第一页
  fetchOrderRecords(); // 重新获取数据
});

const isPendingCollapsed = ref(true);

watch(pendingOrders, (newOrders) => {
  if (newOrders.length > 0) {
    isPendingCollapsed.value = false;
  }
});

// 添加监听分页和排序变化
watch([currentPage, pageSize, isDescending], () => {
  fetchOrderRecords();
});

// 在 setup 中添加状态
const showPdfPreview = ref(false);
const pdfUrl = ref("");
const pdfTitle = ref("");
const pdfFilename = ref("");

// 在组件卸载时清理
onUnmounted(() => {
  if (pdfUrl.value) {
    URL.revokeObjectURL(pdfUrl.value);
  }
});

// 添加选中订单的状态
const selectedOrder = ref<OrderData | null>(null);

// 切换订单详情的显示
const toggleOrderDetails = (order: OrderData) => {
  if (selectedOrder.value?.trade_no === order.trade_no) {
    selectedOrder.value = null;
  } else {
    selectedOrder.value = order;
  }
};
</script>

<style scoped>
.action-button {
  width: 58px;
}

.collapse-icon {
  cursor: pointer;
  transition: transform 0.3s;
  padding: 4px;
  border-radius: 4px;
}

.collapse-icon:hover {
  background-color: var(--el-fill-color-light);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.color-success {
  color: var(--el-color-success);
}

.card-header.clickable {
  cursor: pointer;
  user-select: none;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .data-table {
    min-width: unset;
    width: 100%;
  }

  .order-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .order-row:hover {
    background-color: var(--el-fill-color-light);
  }

  .order-row.selected {
    background-color: var(--el-fill-color);
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }

  .order-details {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px 12px;
    font-size: 14px;
  }

  .detail-item {
    display: flex;
    padding: 6px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .detail-item:nth-last-child(2),
  .detail-item:last-child {
    border-bottom: none;
  }

  .detail-item .label {
    color: var(--el-text-color-secondary);
    width: 70px;
    flex-shrink: 0;
  }

  .detail-item .value {
    flex-grow: 1;
  }

  /* 最后一列单独占一行 */
  /* .detail-item:last-child {
    grid-column: 1 / -1;
    justify-content: center;
    padding: 0;
  } */

  .detail-item .action-button {
    width: 140px;
  }

  /* 隐藏空行但保持其对奇偶计数的影响 */
  tr.dummy-row {
    display: none;
  }

  .details-transition {
    animation: expand 0.25s ease-in-out forwards;
  }

  @keyframes expand {
    from {
      max-height: 133px;
      opacity: 0.5;
    }

    to {
      max-height: 400px;
      opacity: 1;
    }
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }

  /* 筛选器的移动端间隔 */
  .filter-row {
    gap: 8px;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
}
</style>
