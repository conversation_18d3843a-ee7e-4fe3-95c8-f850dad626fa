import { Router } from "express";
import { wrapUserRoute } from "@/utils/routeWrapper.js";
import * as bankAccountService from "@/services/admin/bankAccountService.js";

const router = Router();

// 获取银行账户信息: GET /api/basic/bank-account
router.get(
	"/bank-account",
	wrapUserRoute(async (req, res) => {
		const bankAccount = await bankAccountService.getBankAccount(false);
		res.status(200).json(bankAccount);
	}),
);

export default router;
