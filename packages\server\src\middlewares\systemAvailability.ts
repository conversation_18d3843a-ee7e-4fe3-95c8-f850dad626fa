import type { Request, Response, NextFunction } from "express";
import * as SystemAvailabilityManager from "@/core/systemAvailabilityManager.js";

// 系统可用性中间件
export const checkSystemAvailable = async (
	req: Request,
	_res: Response,
	next: NextFunction,
) => {
	try {
		await SystemAvailabilityManager.checkSystemAvailable();
		next();
	} catch (error) {
		next(error);
	}
};

// 持仓准入控制中间件
export const checkPositionEntryAvailable = async (
	req: Request,
	_res: Response,
	next: NextFunction,
) => {
	try {
		// 只对新增持仓相关的请求进行检查
		await SystemAvailabilityManager.checkPositionEntryAvailable();
		await SystemAvailabilityManager.checkStockScaleLimit(
			req.body.ts_code,
			req.body.scale,
		);
		next();
	} catch (error) {
		next(error);
	}
};

// 询价可用性中间件
export const checkInquiryAvailable = async (
	_req: Request,
	_res: Response,
	next: NextFunction,
) => {
	try {
		await SystemAvailabilityManager.checkInquiryAvailable();
		next();
	} catch (error) {
		next(error);
	}
};
