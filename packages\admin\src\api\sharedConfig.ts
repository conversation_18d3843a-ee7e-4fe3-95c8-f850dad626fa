import { request } from "./request";
import type { SharedConfig } from "@packages/shared";

// 共享配置相关API
export const sharedConfigApi = {
	getSharedConfig: async (): Promise<SharedConfig | null> =>
		request.get<SharedConfig | null>("/admin/shared-config"),

	updateSharedConfig: async (
		config: SharedConfig,
	): Promise<{ message: string } | null> =>
		request.post<{ message: string } | null>("/admin/shared-config", config),
} as const;
