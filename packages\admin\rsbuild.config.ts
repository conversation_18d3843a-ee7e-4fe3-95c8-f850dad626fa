import { defineConfig } from "@rsbuild/core";
import { pluginVue } from "@rsbuild/plugin-vue";
import { pluginSass } from "@rsbuild/plugin-sass";

export default defineConfig({
	plugins: [pluginVue(), pluginSass()],
	html: {
		template: "./static/index.html",
		title: "管理控制台", // 使用静态标题，将在运行时动态更新
		favicon: "./src/assets/favicon/default.svg", // 使用默认图标
		mountId: "app",
	},
	dev: {
		hmr: true,
	},
	server: {
		port: 3002, // 让出服务器和客户端的端口
		proxy: {
			"/api": {
				target: "http://localhost:3000",
				changeOrigin: true,
				ws: true,
			},
		},
	},
	performance: {
		removeConsole: true,
		preload: {
			include: [/vendors\..*\.js$/], // 只预加载 vendors js 文件
		},
	},
	tools: {
		bundlerChain: (chain) => {
			// 应用结构使得大部分依赖在初始加载时就会被使用
			chain.optimization.splitChunks({
				cacheGroups: {
					vendor: {
						test: /[\\/]node_modules[\\/]/,
						name: "vendors", // 将所有第三方依赖打包到单一文件中便于预加载，减少HTTP请求
						chunks: "all",
						priority: 10,
						reuseExistingChunk: true,
					},
				},
				minSize: 20000,
				minChunks: 1,
			});
		},
	},
	output: {
		minify: true, // 使用 Rsbuild 内置的压缩配置
		filenameHash: true, // 确保文件名包含内容哈希，在代码变动时更新缓存
	},
});
