<template>
  <div class="finance-audit-panel">
    <div class="header-container">
      <div class="header-controls">
        <div class="refresh-controls">
          <span class="last-refresh">上次刷新: {{ lastRefreshTime }}</span>
          <el-select v-model="refreshInterval" class="refresh-select" size="small">
            <el-option label="30秒" :value="30" />
            <el-option label="1分钟" :value="60" />
            <el-option label="5分钟" :value="300" />
          </el-select>
          <el-button size="small" :loading="loading" @click="refreshData" :icon="Refresh">
          </el-button>
        </div>
        <div class="controls">
          <el-button :loading="exporting" @click="exportData">
            <el-icon>
              <Download />
            </el-icon>
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="dark-tabs">
      <el-tab-pane v-for="status in ['pending', 'approved', 'rejected']" :key="status" :label="getStatusLabel(status)"
        :name="status">
        <div class="audit-list">
          <el-table v-loading="loading" :data="currentAudits" style="width: 100%;cursor: pointer;"
            :empty-text="loading ? ' ' : '暂无数据'" @row-click="(row: FundAuditData) => showUserDetails(row.user_id)"
            @sort-change="handleSortChange">
            <el-table-column prop="audit_id" label="审核ID" sortable="custom" min-width="100" show-overflow-tooltip />
            <el-table-column prop="user_id" label="用户ID" sortable="custom" min-width="100" show-overflow-tooltip>
              <template #default="{ row }">
                <el-button type="primary" link @click.stop="showUserDetails(row.user_id)">
                  {{ row.user_id }}
                </el-button>
              </template>
            </el-table-column>

            <!-- 移动操作列到更靠前的位置 -->
            <!-- el-table-column 的 fixed="right" 属性存在导致滚动异常的问题 -->
            <el-table-column v-if="status === 'pending'" label="操作" min-width="120">
              <template #default="{ row }">
                <el-button-group>
                  <el-button type="success" size="small" @click.stop="handleAudit(row, AuditStatus.APPROVED)">
                    通过
                  </el-button>
                  <el-button type="danger" size="small" @click.stop="handleAudit(row, AuditStatus.REJECTED)">
                    拒绝
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>

            <el-table-column prop="name" label="用户名" min-width="100" show-overflow-tooltip />
            <el-table-column prop="bank_name" label="银行名称" min-width="150">
              <template #default="{ row }">
                <span class="cell-text">{{ row.bank_name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="bank_code" label="银行编号" min-width="120">
              <template #default="{ row }">
                <span class="cell-text">{{ row.bank_code }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="bank_account" label="银行账号" min-width="160">
              <template #default="{ row }">
                <span class="cell-text">{{ row.bank_account }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" sortable="custom" min-width="160">
              <template #default="{ row }">
                <span class="cell-text">{{ formatDate(row.created_at) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="详情" min-width="220">
              <template #default="{ row }">
                <div class="audit-details">
                  <div class="audit-details-content">
                    <span class="amount copy-enabled" @click.stop="copyToClipboard(row.amount.toString())">
                      {{ formatAmount(row.amount) }}
                      <el-icon>
                        <CopyDocument />
                      </el-icon>
                    </span>
                    <el-tag size="small" class="currency">{{ row.currency }}</el-tag>
                    <el-tag size="small" :type="getOperationTagType(row.operation)" class="operation-tag">
                      {{ formatOperation(row.operation) }}
                    </el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="累计数据" min-width="280">
              <template #default="{ row }">
                <div class="stats-container">
                  <el-tooltip content="累计入金">
                    <span class="stat-item">
                      <el-icon>
                        <Money />
                      </el-icon>
                      {{ formatAmount(row.deposit) }}
                    </span>
                  </el-tooltip>
                  <el-tooltip content="累计期权费">
                    <span class="stat-item">
                      <el-icon>
                        <Wallet />
                      </el-icon>
                      {{ formatAmount(row.premium) }}
                    </span>
                  </el-tooltip>
                  <el-tooltip content="期权费/入金比率">
                    <span class="stat-item">
                      <el-icon>
                        <TrendCharts />
                      </el-icon>
                      {{ calculatePremiumRatio(row.premium, row.deposit) }}%
                    </span>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next" :total="total"
              @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>

          <el-empty v-if="!loading && (!currentAudits || currentAudits.length === 0)" description="暂无审核请求" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加用户详情对话框 -->
    <user-details-dialog v-model:visible="userDialogVisible" :user-id="selectedUserId" />

    <!-- 审核确认对话框（带用户详情） -->
    <user-details-dialog v-model:visible="dialogVisible" :user-id="currentAudit?.user_id" class="audit-dialog">
      <template #footer>
        <div class="audit-footer">
          <!-- 添加资金详情标题部分 -->
          <div class="audit-header">
            <h3>{{ dialogTitle }}</h3>
            <div class="audit-amount-info">
              <span class="amount">{{ formatAmount(currentAudit?.amount || 0) }}</span>
              <el-tag size="small" class="currency">{{ currentAudit?.currency }}</el-tag>
              <el-tag size="small" :type="currentAudit ? getOperationTagType(currentAudit.operation) : 'info'"
                class="operation-tag">
                {{ currentAudit ? formatOperation(currentAudit.operation) : '' }}
              </el-tag>
            </div>
          </div>

          <el-form>
            <el-form-item label="备注">
              <el-input v-model="auditComment" type="textarea" :rows="3" placeholder="请输入审核备注" />
            </el-form-item>
          </el-form>
          <div class="audit-actions">
            <el-button @click="cancelAudit">取消</el-button>
            <el-button :type="pendingAction === AuditStatus.APPROVED ? 'success' : 'danger'" @click="confirmAudit"
              :loading="processing">
              {{ pendingAction === AuditStatus.APPROVED ? '通过' : '拒绝' }}
            </el-button>
          </div>
        </div>
      </template>
    </user-details-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  watch,
  onUnmounted,
  nextTick,
  onBeforeUnmount,
} from "vue";
import { financeApi } from "@/api";
import { ElMessage } from "element-plus";
import {
  AuditStatus,
  getErrorMessage,
  FundTransactionType,
} from "@packages/shared";
import type { FundAuditData } from "@packages/shared";
import {
  Refresh,
  CopyDocument,
  Money,
  Wallet,
  TrendCharts,
  Download,
} from "@element-plus/icons-vue";
import UserDetailsDialog from "@/components/UserDetailsDialog.vue";
import { formatDate, formatTime } from "@/utils/format";
import { exportToCsv } from "@/utils/export";
import { showAuditNotification } from "@/utils/notification";

// 状态管理
const activeTab = ref("pending");
const loading = ref(false);
const processing = ref(false);
const dialogVisible = ref(false);
const auditComment = ref("");
const currentAudit = ref<FundAuditData | null>(null);
const pendingAction = ref<AuditStatus.APPROVED | AuditStatus.REJECTED | null>(
  null,
);

// 分页和排序相关的状态
const currentPage = ref(1);
const pageSize = ref(10);
const sortBy = ref("created_at");
const sortOrder = ref<"ASC" | "DESC">("DESC");
const total = ref(0);

// 刷新相关的状态
const refreshInterval = ref(30);
const lastRefreshTime = ref(formatTime(new Date()));
const refreshTimer = ref<ReturnType<typeof setInterval> | null>(null);

// 上次计数状态
const lastPendingCount = ref(0);

// 审核数据
const audits = ref<Record<AuditStatus, FundAuditData[]>>({
  [AuditStatus.PENDING]: [],
  [AuditStatus.APPROVED]: [],
  [AuditStatus.REJECTED]: [],
});

// 计算属性：获取当前标签页的审核数据
const currentAudits = computed(() => {
  const status = activeTab.value as AuditStatus;
  return audits.value[status] || [];
});

// 加载审核数据
const loadAudits = async (status: AuditStatus) => {
  loading.value = true;
  try {
    const response = await financeApi.getFinanceAudits(status, {
      page: currentPage.value,
      pageSize: pageSize.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
    });
    audits.value[status] = response?.items || [];
    total.value = response?.total || 0;
    updateLastRefreshTime();

    // 检查是否有新的待审核请求
    if (status === AuditStatus.PENDING) {
      checkForNewAudits(response?.total || 0);
    }
  } catch (error) {
    console.error(`加载${getStatusLabel(status)}资金审核失败:`, error);
    ElMessage.error(`加载${getStatusLabel(status)}资金审核失败`);
  } finally {
    loading.value = false;
  }
};

// 处理排序变化
const handleSortChange = ({
  prop,
  order,
}: { prop?: string; order?: string }) => {
  sortBy.value = prop || "created_at";
  sortOrder.value = order === "ascending" ? "ASC" : "DESC";
  loadAudits(activeTab.value as AuditStatus);
};

// 处理页码变化
const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  loadAudits(activeTab.value as AuditStatus);
};

// 处理每页条数变化
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  currentPage.value = 1; // 重置到第一页
  loadAudits(activeTab.value as AuditStatus);
};

// 监听标签页变化
watch(activeTab, async (newStatus) => {
  // 如果该状态的数据还未加载，则加载数据
  if (!audits.value[newStatus as AuditStatus]?.length) {
    await loadAudits(newStatus as AuditStatus);
  }
});

// 对话框标题
const dialogTitle = computed(() => {
  return pendingAction.value === AuditStatus.APPROVED ? "审核通过" : "审核拒绝";
});

// 处理审核操作
const handleAudit = (
  audit: FundAuditData,
  action: AuditStatus.APPROVED | AuditStatus.REJECTED,
) => {
  currentAudit.value = audit;
  pendingAction.value = action;
  dialogVisible.value = true;
};

// 确认审核
const confirmAudit = async () => {
  if (!currentAudit.value || !pendingAction.value) return;

  processing.value = true;
  try {
    await financeApi.processFinanceAudit(
      currentAudit.value.audit_id,
      pendingAction.value,
      auditComment.value,
    );
    ElMessage.success("审核处理成功");
    dialogVisible.value = false;
    auditComment.value = "";
    await loadAudits(AuditStatus.PENDING);

    // 通过重置 userId 触发两个对话框的数据刷新
    const tempSelectedUserId = selectedUserId.value;
    const tempCurrentAudit = currentAudit.value;

    selectedUserId.value = undefined;
    currentAudit.value = null;

    nextTick(() => {
      selectedUserId.value = tempSelectedUserId;
      currentAudit.value = tempCurrentAudit;
    });
  } catch (error) {
    console.error("审核处理失败:", error);
    ElMessage.error(`审核处理失败：${getErrorMessage(error)}`);
  } finally {
    processing.value = false;
  }
};

// 取消审核
const cancelAudit = () => {
  dialogVisible.value = false;
};

// 保存页面状态
const saveState = () => {
  const state = {
    activeTab: activeTab.value,
    currentPage: currentPage.value,
    pageSize: pageSize.value,
    sortBy: sortBy.value,
    sortOrder: sortOrder.value,
    refreshInterval: refreshInterval.value,
  };
  sessionStorage.setItem("financeAuditPanel_state", JSON.stringify(state));
};

// 恢复页面状态
const restoreState = () => {
  const savedState = sessionStorage.getItem("financeAuditPanel_state");
  if (savedState) {
    try {
      const state = JSON.parse(savedState);
      activeTab.value = state.activeTab || "pending";
      currentPage.value = state.currentPage || 1;
      pageSize.value = state.pageSize || 10;
      sortBy.value = state.sortBy || "created_at";
      sortOrder.value = state.sortOrder || "DESC";
      refreshInterval.value = state.refreshInterval || 30;
    } catch (e) {
      console.error("Failed to parse saved state:", e);
    }
  }
};

// 修改初始化函数
onMounted(async () => {
  // 恢复状态
  restoreState();

  // 加载初始数据
  loadAudits(activeTab.value as AuditStatus);

  // 设置定时刷新
  refreshTimer.value = setInterval(refreshData, refreshInterval.value * 1000);
});

// 更新最后刷新时间
const updateLastRefreshTime = () => {
  lastRefreshTime.value = formatTime(new Date());
};

// 刷新数据
const refreshData = async () => {
  if (loading.value) return;
  await loadAudits(activeTab.value as AuditStatus);
  updateLastRefreshTime();
};

// 监听刷新间隔变化
watch(refreshInterval, (newInterval) => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
    refreshTimer.value = null;
  }

  if (newInterval > 0) {
    refreshTimer.value = setInterval(refreshData, newInterval * 1000);
  }
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
  }
});

// 组件卸载前保存状态
onBeforeUnmount(() => {
  saveState();
});

const userDialogVisible = ref(false);
const selectedUserId = ref<number>();

const showUserDetails = (userId: number) => {
  selectedUserId.value = userId;
  userDialogVisible.value = true;
};

// 添加状态标签映射函数
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: "待审核",
    approved: "已通过",
    rejected: "已拒绝",
  };
  return statusMap[status] || status;
};

// 从 AuditList 移植的工具函数
const getOperationTagType = (operation: string) => {
  const typeMap: Record<string, string> = {
    platform_deposit: "success",
    withdraw: "warning",
  };
  return typeMap[operation] || "info";
};

const formatOperation = (operation: FundTransactionType) => {
  const operationMap: Record<string, string> = {
    platform_deposit: "平台入金",
    withdraw: "出金",
    deposit: "入金",
  };
  return operationMap[operation] || operation;
};

const formatAmount = (amount: string | number) => {
  const numAmount =
    typeof amount === "string" ? Number.parseFloat(amount) : amount;
  if (Number.isNaN(numAmount)) return "0";
  return new Intl.NumberFormat("en-US", {
    maximumFractionDigits: 0,
  }).format(numAmount);
};

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    ElMessage.success("已复制到剪贴板");
  } catch (err) {
    ElMessage.error("复制失败");
  }
};

const calculatePremiumRatio = (premium: number, deposit: number) => {
  if (!deposit) return "0.00";
  const ratio = (premium / deposit) * 100;
  return Math.round(ratio);
};

const exporting = ref(false);

const exportData = async () => {
  exporting.value = true;
  try {
    const response = await financeApi.getFinanceAudits(undefined, {
      page: 1,
      pageSize: 999999,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
    });

    const headers = [
      "审核ID",
      "用户ID",
      "金额",
      "货币",
      "操作类型",
      "状态",
      "创建时间",
      "备注",
    ];
    const csvData =
      response?.items.map((item) => [
        item.audit_id,
        item.user_id,
        item.amount,
        item.currency,
        formatOperation(item.operation),
        getStatusLabel(item.status),
        formatDate(item.created_at),
        item.comment || "",
      ]) || [];

    exportToCsv(headers, csvData, "finance-all");

    ElMessage.success("导出成功");
  } catch (error) {
    console.error("Export failed:", error);
    ElMessage.error("导出失败");
  } finally {
    exporting.value = false;
  }
};

// 修改检查新审核的函数
const checkForNewAudits = (currentCount: number) => {
  // 当前标签是待审核时才发送通知
  if (
    activeTab.value === "pending" &&
    lastPendingCount.value &&
    currentCount > lastPendingCount.value
  ) {
    const newCount = currentCount - lastPendingCount.value;
    showAuditNotification("finance", newCount);
  }
  lastPendingCount.value = currentCount;
};

// 公开方法以便父组件可以调用
defineExpose({
  refreshData,
});
</script>

<style scoped>
.finance-audit-view {
  padding: 20px;
}

.header-container {
  margin-bottom: 20px;
}

.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.last-refresh {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.refresh-select {
  width: 100px;
}

h2 {
  margin: 0;
  color: var(--el-text-color-primary);
}

@media (max-width: 768px) {
  .header-main {
    align-items: flex-start;
    gap: 12px;
  }

  .refresh-controls {
    padding: 8px 0;
  }
}

:deep(.el-tabs__item) {
  color: var(--el-text-color-secondary);
}

:deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: var(--el-border-color-light);
}

:deep(.el-dialog) {
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color);
}

:deep(.dialog-approved .el-dialog) {
  border-top: 4px solid var(--el-color-success);
}

:deep(.dialog-rejected .el-dialog) {
  border-top: 4px solid var(--el-color-danger);
}

:deep(.dialog-approved .el-dialog__title) {
  color: var(--el-color-success);
}

:deep(.dialog-rejected .el-dialog__title) {
  color: var(--el-color-danger);
}

/* 从 AuditList 移植的样式 */
.audit-list {
  min-height: 200px;
}

.audit-details {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.audit-details:hover {
  background-color: var(--el-fill-color-light);
}

.audit-details-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.amount {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.currency {
  background-color: var(--el-color-info-light-9);
  color: var(--el-text-color-secondary);
}

.operation-tag {
  text-transform: capitalize;
}

.copy-enabled {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: var(--el-color-primary);
  transition: opacity 0.2s;
}

.copy-enabled:hover {
  opacity: 0.8;
}

.copy-enabled .el-icon {
  font-size: 14px;
}

.stats-container {
  display: flex;
  gap: 16px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-regular);
}

.stat-item .el-icon {
  font-size: 14px;
}

.cell-text {
  word-break: break-all;
  white-space: normal;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}

/* 确保表格单元格可以换行 */
:deep(.el-table .cell) {
  white-space: normal;
  height: auto;
  line-height: 1.5;
}

/* 调整行高以适应多行内容 */
:deep(.el-table__row) {
  height: auto;
}

.audit-footer {
  padding: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.audit-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  text-align: center;
}

.audit-header h3 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
}

.audit-amount-info {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.audit-amount-info .amount {
  font-size: 18px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.audit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

:deep(.audit-dialog) {
  margin-top: 40px;
}
</style>
