<template>
  <div class="platform-config-view view">
    <div class="header-container">
      <h2>平台配置</h2>
      <div class="controls">
        <el-button type="primary" :loading="saving" @click="saveConfigurations">
          保存配置
        </el-button>
      </div>
    </div>

    <!-- 盈利分成配置 -->
    <el-card v-if="authStore.app_type === AppType.CHANNEL" class="config-card">
      <div class="config-section">
        <h3 class="section-title">盈利分成配置</h3>

        <div class="config-items">
          <div class="config-item">
            <span class="config-label">启用盈利分成：</span>
            <el-switch v-model="config.profit_sharing.enabled" />
          </div>

          <div class="config-item">
            <span class="config-label">分成比例：</span>
            <el-input-number v-model="config.profit_sharing.percentage" :min="0" :max="100" :step="1" :precision="0"
              :disabled="!config.profit_sharing.enabled" style="width: 150px">
              <template #suffix>%</template>
            </el-input-number>
            <span class="config-description">用户盈利订单结算时，将按此比例抽取收益分成</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 报价提供商配置 -->
    <el-card class="config-card">
      <div class="config-section">
        <h3 class="section-title">报价提供商配置</h3>

        <el-table :data="providerList" border style="width: 100%" :row-style="{ height: '52px' }">
          <el-table-column prop="display_name" label="提供商" min-width="180">
            <template #default="scope">
              {{ scope.row.provider_key === 'INK' ? siteConfigStore.shortName() : scope.row.display_name }}
              <span style="font-size: 12px; margin-left: 5px; color: var(--el-color-info)">
                {{ ['INK', 'HAIYING'].includes(scope.row.provider_key) ? 'HK' : 'CN' }}
              </span>
              <span v-if="getTradingRestriction(scope.row.provider_key) !== undefined" class="trading-restriction">
                T+{{ getTradingRestriction(scope.row.provider_key) }}
              </span>
              <el-tag v-if="authStore.app_type === AppType.CHANNEL && providerDiscounts[scope.row.provider_key]"
                size="small" type="success" class="discount-tag">
                {{ Math.round((1 - providerDiscounts[scope.row.provider_key]) * 100) }}% 折扣
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="启用状态" min-width="120">
            <template #default="scope">
              <el-tooltip content="基准实时报价不可禁用" placement="top" :disabled="scope.row.provider_key !== 'INK'">
                <el-switch v-model="config.quote_providers[scope.row.provider_key].enabled"
                  :disabled="scope.row.provider_key === 'INK'" />
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column v-if="authStore.app_type === AppType.CHANNEL" label="每单加价" min-width="200">
            <template #default="scope">
              <el-input-number v-model="config.quote_providers[scope.row.provider_key].price_adjustment" :min="0"
                :step="100" :precision="0" controls-position="right" style="width: 150px"
                @change="handlePriceAdjustmentChange($event, scope.row.provider_key)" />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 用户自定义交易参数快捷入口 -->
    <el-card v-if="authStore.app_type === AppType.CHANNEL" class="config-card">
      <div class="config-section">
        <h3 class="section-title">用户自定义交易参数</h3>
        <p class="config-description" style="margin-bottom: 16px; line-height: 1.6;">
          平台默认的盈利分成和报价加价规则适用于所有用户。如需为特定用户单独设置，请前往"用户列表-操作-交易参数"进行配置。
        </p>
        <el-button @click="navigateToUsers">
          前往配置
        </el-button>
      </div>
    </el-card>

    <!-- 邮件通知配置 -->
    <el-card class="config-card">
      <div class="config-section">
        <h3 class="section-title">邮件通知配置</h3>

        <div class="config-items">
          <div class="config-item email-config-item">
            <span class="config-label">邮箱发送人显示名：</span>
            <div class="input-with-description">
              <el-input v-model="config.email_display_name" placeholder="发件人显示名称" style="width: 300px" />
              <span class="config-description">用户收到邮件时看到的发件人名称</span>
            </div>
          </div>

          <div class="config-item email-config-item">
            <span class="config-label">资质审核通知邮箱：</span>
            <div class="email-tags-input">
              <div class="tag-container" v-if="config.qualification_audit_email?.length">
                <el-tag v-for="(email, index) in config.qualification_audit_email" :key="index" closable
                  @close="removeQualificationEmail(index)">
                  {{ email }}
                </el-tag>
              </div>
              <el-input v-model="qualificationEmail" placeholder="输入邮箱后按回车添加，最多5个" style="width: 300px"
                @keyup.enter="addQualificationEmail" />
              <span class="config-description">用户提交资质审核时，系统会向这些邮箱发送通知</span>
            </div>
          </div>

          <div class="config-item email-config-item">
            <span class="config-label">资金审核通知邮箱：</span>
            <div class="email-tags-input">
              <div class="tag-container" v-if="config.fund_audit_email?.length">
                <el-tag v-for="(email, index) in config.fund_audit_email" :key="index" closable
                  @close="removeFundEmail(index)">
                  {{ email }}
                </el-tag>
              </div>
              <el-input v-model="fundEmail" placeholder="输入邮箱后按回车添加，最多5个" style="width: 300px"
                @keyup.enter="addFundEmail" />
              <span class="config-description">用户提交资金操作审核时，系统会向这些邮箱发送通知</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { configApi, basicApi } from "@/api";
import type { PlatformConfig } from "@packages/shared";
import { ProviderTradingRestrictions, AppType } from "@packages/shared";
import { useAuthStore } from "@/stores/auth";
import { useSiteConfigStore } from "@/stores/siteConfig";

const authStore = useAuthStore();
const siteConfigStore = useSiteConfigStore();
const router = useRouter();

// 输入框的临时邮箱
const qualificationEmail = ref("");
const fundEmail = ref("");

// 配置状态
const saving = ref(false);
const config = ref<PlatformConfig>({
  // 初始化 INK 提供商
  quote_providers: {
    INK: {
      display_name: siteConfigStore.shortName(),
      enabled: true,
      price_adjustment: 0,
    },
  },
  // 初始化盈利分成配置
  profit_sharing: {
    enabled: false,
    percentage: 5,
  },
  // 初始化邮件配置
  email_display_name: "",
  qualification_audit_email: [],
  fund_audit_email: [],
});

// 加载提供商折扣配置
const providerDiscounts = ref<Record<string, number>>({});

// 获取交易限制天数
const getTradingRestriction = (providerKey: string): number | undefined => {
  return ProviderTradingRestrictions[
    providerKey as keyof typeof ProviderTradingRestrictions
  ];
};

// 计算报价提供商列表用于表格展示
const providerList = computed(() => {
  const providers = Object.entries(config.value.quote_providers).map(
    ([provider_key, settings]) => ({
      provider_key,
      display_name: settings.display_name,
    }),
  );

  // 将 INK 和 HAIYING 放在最前面
  return providers.sort((a, b) => {
    if (a.provider_key === "INK") return -1;
    if (b.provider_key === "INK") return 1;
    if (a.provider_key === "HAIYING") return -1;
    if (b.provider_key === "HAIYING") return 1;
    return a.display_name.localeCompare(b.display_name);
  });
});

// 加载配置
const loadConfigurations = async () => {
  try {
    const platformConfig = await configApi.getPlatformConfig();
    if (platformConfig) {
      config.value = platformConfig;
    }

    // 加载提供商折扣配置
    const discounts = await basicApi.getProviderDiscounts();
    providerDiscounts.value = discounts || {};
  } catch (error) {
    console.error("Failed to load platform configurations:", error);
    ElMessage.error("加载配置失败");
  }
};

// 保存配置
const saveConfigurations = async () => {
  saving.value = true;
  try {
    await configApi.updatePlatformConfig(config.value);
    ElMessage.success("平台配置保存成功");
  } catch (error) {
    console.error("Failed to save platform configurations:", error);
    ElMessage.error("保存配置失败");
  } finally {
    saving.value = false;
  }
};

const handlePriceAdjustmentChange = (value: number, providerKey: string) => {
  if (value % 100 !== 0) {
    // 如果不是100的倍数，自动调整为最接近的100的倍数
    const adjustedValue = Math.round(value / 100) * 100;
    config.value.quote_providers[providerKey].price_adjustment = adjustedValue;

    ElMessage.warning("加价金额必须为100的整数倍，已自动调整");
  }
};

// 添加邮箱管理方法
const addQualificationEmail = () => {
  if (!qualificationEmail.value) return;

  // 邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(qualificationEmail.value)) {
    ElMessage.warning("请输入有效的邮箱地址");
    return;
  }

  // 确保初始化数组
  if (!Array.isArray(config.value.qualification_audit_email)) {
    config.value.qualification_audit_email = [];
  }

  // 检查邮箱数量限制
  if (config.value.qualification_audit_email.length >= 5) {
    ElMessage.warning("最多添加5个邮箱地址");
    return;
  }

  // 检查邮箱是否已存在
  if (
    config.value.qualification_audit_email.includes(qualificationEmail.value)
  ) {
    ElMessage.warning("该邮箱已添加");
    return;
  }

  config.value.qualification_audit_email.push(qualificationEmail.value);
  qualificationEmail.value = "";
};

const removeQualificationEmail = (index: number) => {
  if (Array.isArray(config.value.qualification_audit_email)) {
    config.value.qualification_audit_email.splice(index, 1);
  }
};

const addFundEmail = () => {
  if (!fundEmail.value) return;

  // 邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(fundEmail.value)) {
    ElMessage.warning("请输入有效的邮箱地址");
    return;
  }

  // 确保初始化数组
  if (!Array.isArray(config.value.fund_audit_email)) {
    config.value.fund_audit_email = [];
  }

  // 检查邮箱数量限制
  if (config.value.fund_audit_email.length >= 5) {
    ElMessage.warning("最多添加5个邮箱地址");
    return;
  }

  // 检查邮箱是否已存在
  if (config.value.fund_audit_email.includes(fundEmail.value)) {
    ElMessage.warning("该邮箱已添加");
    return;
  }

  config.value.fund_audit_email.push(fundEmail.value);
  fundEmail.value = "";
};

const removeFundEmail = (index: number) => {
  if (Array.isArray(config.value.fund_audit_email)) {
    config.value.fund_audit_email.splice(index, 1);
  }
};

const navigateToUsers = () => {
  router.push({ name: "users-list" });
};

// 组件挂载时加载配置
onMounted(loadConfigurations);
</script>

<style scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

h2 {
  margin: 0;
}

.section-title {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  border-left: 3px solid var(--el-color-primary);
  padding-left: 10px;
}

.config-section {
  margin-bottom: 10px;
}

.config-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.email-config-item {
  align-items: flex-start;
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 16px;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.config-label {
  line-height: 1.4;
  flex-shrink: 0;
}

.info-icon {
  color: var(--el-color-info);
  cursor: pointer;
}

.config-description {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.trading-restriction {
  font-size: 12px;
  margin-left: 5px;
  color: var(--el-color-warning);
  font-weight: 600;
}

.discount-tag {
  margin-left: 8px;
}

.email-tags-input,
.input-with-description {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 4px;
  min-height: 32px;
}
</style>