/**
 * 全局移动端适配样式
 */

@media (max-width: 767px) {
	/* --- 移动端间距和字体调整 --- */
	/* 常用间距类调整 */
	.m-2 {
		margin: 5px !important;
	}

	.mt-3 {
		margin-top: 8px !important;
	}

	.mb-3 {
		margin-bottom: 8px !important;
	}

	.p-2 {
		padding: 5px !important;
	}

	/* 标题字体大小调整 */
	h1 {
		font-size: 20px !important;
	}

	h2 {
		font-size: 18px !important;
	}

	h3 {
		font-size: 16px !important;
	}

	/* 内容区域调整 */
	.content-section {
		padding: 10px !important;
	}

	.view {
		padding: 0 !important;
		gap: 14px !important;
	}

	.pagination-container {
		margin-top: 14px;
	}

	/* --- 移动端表格样式优化 --- */
	/* 表格响应式处理 */
	.el-table__body {
		width: 100% !important;
	}

	/* 表格操作按钮样式优化 */
	.el-table .cell .el-button {
		padding: 6px 10px;
		font-size: 12px;
	}

	/* 表格内按钮组样式 */
	.el-table .cell .el-button + .el-button {
		margin-left: 4px;
	}

	/* 多按钮操作列处理 */
	.operation-column .cell {
		white-space: nowrap;
		overflow-x: auto;
		padding-bottom: 5px;
	}

	/* 分页控件响应式优化 */
	.el-pagination {
		white-space: normal !important;
		text-align: center;
	}

	.el-pagination .el-pagination__sizes {
		display: none !important;
	}

	/* 过滤和排序图标增大点击区域 */
	.el-table__column-filter-trigger,
	.caret-wrapper {
		padding: 0 8px;
	}

	/* 确保加载动画不会显示在移动端侧边栏上面 */
	.el-loading-mask {
		z-index: 900 !important;
	}

	/* 确保移动端侧边栏层级高于加载动画 */
	.el-aside {
		z-index: 1000 !important;
	}

	/* --- 移动端侧边栏菜单样式优化 --- */
	.el-menu {
		width: 100% !important;
	}

	.el-menu-item,
	.el-sub-menu__title {
		width: 100% !important;
		min-width: 100% !important;
		padding-right: 20px !important;
		box-sizing: border-box !important;
	}

	.el-menu--collapse {
		width: 100% !important;
	}

	/* 移动端表单样式优化 */
	/* 表单项布局调整 */
	.el-form-item {
		margin-bottom: 15px;
	}

	/* 输入框高度调整 */
	.el-input__wrapper,
	.el-textarea__wrapper {
		padding: 0 10px !important;
	}

	.el-input__inner {
		height: 36px !important;
	}

	/* 弹性布局表单调整 */
	.form-flex-row {
		flex-direction: column;
	}

	.form-flex-row .el-form-item {
		width: 100% !important;
		margin-right: 0 !important;
	}

	/* 查询表单样式优化 */
	.search-form {
		padding: 10px;
	}

	.search-form .el-form-item {
		margin-bottom: 10px;
	}

	.search-form .search-buttons {
		display: flex;
		justify-content: flex-end;
		margin-top: 5px;
	}

	/* --- 移动端弹窗样式优化 --- */
	/* 确保弹窗内容可滚动 */
	.el-dialog__body {
		overflow-y: auto;
		padding: 15px !important;
	}

	/* 弹窗标题和按钮调整 */
	.el-dialog__header {
		padding: 15px !important;
	}

	.el-dialog__footer {
		padding: 10px 15px !important;
	}

	/* --- 移动端统计卡片和图表样式 --- */
	/* 统计卡片紧凑布局 */
	.stat-card {
		height: auto !important;
		padding: 10px !important;
	}

	/* 图表容器高度调整 */
	.chart-container {
		height: 250px !important;
	}

	/* 数据详情项调整 */
	.data-item {
		margin-bottom: 8px;
	}

	.data-item .label {
		font-size: 12px;
	}

	.data-item .value {
		font-size: 14px;
	}

	/* --- 自定义滚动条，便于移动端操作 --- */
	::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 4px;
	}

	::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 4px;
	}

	::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
	}

	/* 暗色模式滚动条适配 */
	html.dark ::-webkit-scrollbar-track {
		background: #2c2c2c;
	}

	html.dark ::-webkit-scrollbar-thumb {
		background: #5c5c5c;
	}

	html.dark ::-webkit-scrollbar-thumb:hover {
		background: #6e6e6e;
	}

	/* --- 移动端过渡动画优化 --- */
	/* 确保所有交互元素都有过渡效果 */
	.el-button,
	.el-menu-item,
	.el-dropdown-item,
	.el-checkbox,
	.el-radio,
	.el-switch,
	.el-tag,
	.el-badge,
	.el-link {
		transition: all 0.2s ease-in-out !important;
	}

	/* 按钮触摸反馈效果 - 使用不透明度变化而不是涟漪 */
	.el-button:not(.menu-toggle-mobile):active {
		opacity: 0.7;
		transform: scale(0.98);
		transition: opacity 0.1s, transform 0.1s;
	}

	/* 为菜单项添加触摸效果 */
	.el-menu-item:active {
		background-color: var(--el-menu-hover-bg-color) !important;
		transition: background-color 0.1s !important;
	}

	/* 为下拉菜单项添加触摸效果 */
	.el-dropdown-item:active {
		background-color: var(--el-dropdown-menuItem-hover-fill) !important;
		transition: background-color 0.1s !important;
	}
}
