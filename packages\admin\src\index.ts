import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router, { initializeRouter } from "./router";
import "./index.css";
import "./index.scss";
import ElementPlus from "element-plus";
// 导入移动端适配样式
import "./assets/styles/mobile.css";

const app = createApp(App);
const pinia = createPinia();
app.use(pinia);

// 在使用router前初始化
initializeRouter();

app.use(ElementPlus);
app.use(router);

app.mount("#app");
