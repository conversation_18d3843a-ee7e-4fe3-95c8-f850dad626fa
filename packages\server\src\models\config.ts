import prisma from "@/lib/prisma.js";
import type { Prisma } from "@prisma/client";
import type {
	BusinessConfig,
	SystemStatus,
	StatusHistoryItem,
	BaseSystemStatus,
	PlatformConfig,
	PlatformConfigHistory,
} from "@packages/shared";
import logger from "@/utils/logger.js";

function toJsonValue<T>(data: T): Prisma.InputJsonValue {
	return data as Prisma.InputJsonValue;
}

// Business config methods
export async function getConfig(): Promise<BusinessConfig | null> {
	const result = await prisma.business_configs.findFirst({
		orderBy: { config_id: "desc" },
		select: { config: true },
	});
	return result?.config as BusinessConfig | null;
}

export async function saveConfig(
	config: BusinessConfig,
	admin_id?: number,
): Promise<void> {
	logger.info(config, "Saving business config");
	await prisma.business_configs.create({
		data: {
			config: toJsonValue(config),
			...(admin_id ? { admin_id } : {}),
		},
	});
}

// System status methods
export async function getSystemStatus(): Promise<SystemStatus | null> {
	try {
		const [statusResult, autoManageResult] = await Promise.all([
			prisma.system_status_history.findFirst({
				orderBy: { status_id: "desc" },
				select: { status: true },
			}),
			prisma.system_auto_manage_config.findFirst({
				orderBy: { manage_id: "desc" },
				select: { config: true },
			}),
		]);

		if (!statusResult?.status) return null;

		// 合并状态和自动管理配置
		return {
			...(statusResult.status as unknown as BaseSystemStatus),
			auto_manage_enabled: (
				autoManageResult?.config as { auto_manage_enabled: boolean[] }
			)?.auto_manage_enabled || [true, true, true],
		};
	} catch (error) {
		logger.error(error, "Failed to get system status");
		return null;
	}
}

export async function saveSystemStatus(
	status: BaseSystemStatus,
	admin_id?: number,
): Promise<void> {
	const lastRecord = await prisma.system_status_history.findFirst({
		orderBy: { status_id: "desc" },
		select: { status_id: true, changed_at: true },
	});

	const now = new Date();
	const lastTime = lastRecord?.changed_at;
	const timeDiff = lastTime
		? now.getTime() - new Date(lastTime).getTime()
		: Number.POSITIVE_INFINITY;

	// 如果最后一次更新在10秒内，更新该记录而不是创建新记录
	if (lastRecord && timeDiff < 10000) {
		await prisma.system_status_history.update({
			where: { status_id: lastRecord.status_id },
			data: {
				status: toJsonValue(status),
				...(admin_id ? { admin_id } : {}),
			},
		});
	} else {
		await prisma.system_status_history.create({
			data: {
				status: toJsonValue(status),
				...(admin_id ? { admin_id } : {}),
			},
		});
	}
}

// 拆分保存自动管理配置的方法
export async function saveAutoManageConfig(
	auto_manage_enabled: boolean[],
): Promise<void> {
	await prisma.system_auto_manage_config.create({
		data: {
			config: toJsonValue({ auto_manage_enabled }),
		},
	});
}

export async function getSystemStatusHistory(
	page = 1,
	pageSize = 10,
	options: { sortBy?: string; sortOrder?: "ASC" | "DESC" } = {},
): Promise<{ total: number; items: StatusHistoryItem[] }> {
	const { sortBy = "changed_at", sortOrder = "DESC" } = options;
	const allowedSortFields = ["changed_at", "status", "admin_id"];
	const orderBy = allowedSortFields.includes(sortBy) ? sortBy : "changed_at";

	const [count, items] = await Promise.all([
		prisma.system_status_history.count(),
		prisma.system_status_history.findMany({
			skip: (page - 1) * pageSize,
			take: pageSize,
			orderBy: [{ [orderBy]: sortOrder.toLowerCase() }, { changed_at: "desc" }],
		}),
	]);

	return {
		total: count,
		items: items as unknown as StatusHistoryItem[],
	};
}

// Platform config methods
export async function getPlatformConfig(): Promise<PlatformConfig | null> {
	const result = await prisma.platform_configs.findFirst({
		orderBy: { config_id: "desc" },
		select: { config: true },
	});
	return result?.config as PlatformConfig | null;
}

export async function savePlatformConfig(
	config: PlatformConfig,
	admin_id?: number,
): Promise<void> {
	logger.info(config, "Saving platform config");
	await prisma.platform_configs.create({
		data: {
			config: toJsonValue(config),
			...(admin_id ? { admin_id } : {}),
		},
	});
}

export async function getPlatformConfigHistory(): Promise<
	PlatformConfigHistory[]
> {
	const result = await prisma.platform_configs.findMany();
	return result as unknown as PlatformConfigHistory[];
}
