import prisma, { platformPrisma } from "@/lib/prisma.js";
import type { Prisma } from "@prisma/client";
import {
	withTransaction,
	withCrossDbTransaction2,
	withExistingTxPlatformOperation,
} from "@/core/dbTxnManager.js";
import * as Stock from "./stock.js";
import type {
	PositionData,
	PositionChangeData,
	StructureType,
	StockData,
} from "@packages/shared";
import { AppError } from "@/core/appError.js";
import logger from "@/utils/logger.js";
import { isChannel } from "@/config/configManager.js";

type PrismaPosition = Omit<
	PositionData,
	| "entry_price"
	| "exercise_price"
	| "structure"
	| "quote"
	| "expiry_date"
	| "expiry_date_confirmed"
	| "created_at"
	| "quote_provider"
	| "quote_diff"
> & {
	entry_price: Prisma.Decimal;
	exercise_price: Prisma.Decimal;
	structure: string;
	quote: Prisma.Decimal;
	expiry_date: Date | null;
	expiry_date_confirmed: boolean | null;
	created_at: Date | null;
	quote_provider: string | null;
	quote_diff: Prisma.Decimal | null;
};

function convertPrismaPosition(p: PrismaPosition): PositionData {
	return {
		...p,
		entry_price: Number(p.entry_price),
		exercise_price: Number(p.exercise_price),
		structure: p.structure as StructureType,
		quote: Number(p.quote),
		expiry_date: p.expiry_date?.toISOString() ?? "",
		expiry_date_confirmed: p.expiry_date_confirmed ?? false,
		created_at: p.created_at?.toISOString() ?? new Date().toISOString(),
		quote_provider: p.quote_provider ?? "INK",
		quote_diff: p.quote_diff ? Number(p.quote_diff) : 0,
	};
}

//
export async function createOrUpdate(
	data: PositionChangeData,
	client?: Prisma.TransactionClient,
): Promise<PositionData | null> {
	// 如果已经提供了事务客户端，需要处理跨数据库事务
	if (client) {
		const updateFn = async (tx: Prisma.TransactionClient) => {
			const position = await findByTradeNo(data.trade_no, tx);

			let result: PrismaPosition;

			if (position) {
				// Update existing position
				const new_scale = position.scale + data.scale_change;
				if (new_scale === 0) {
					// Delete position if new scale is 0
					result = await tx.positions.delete({
						where: { trade_no: data.trade_no },
					});
				} else {
					// Update scale
					result = await tx.positions.update({
						where: { trade_no: data.trade_no },
						data: { scale: new_scale },
					});
				}
			} else {
				// 过滤掉不需要的字段
				const { scale_change, ...positionData } = data;
				result = await tx.positions.create({
					data: {
						...positionData,
						scale: scale_change,
					},
				});
			}

			// 检查是否是通道环境，如果是则使用平台数据库更新库存
			if (isChannel() && platformPrisma) {
				// 使用withExistingTxPlatformOperation将平台操作与主事务联动
				await withExistingTxPlatformOperation(async (platformTx) => {
					await Stock.createOrUpdate(
						{
							ts_code: data.ts_code,
							scale: data.scale_change,
						},
						platformTx,
					);
					return true; // 操作成功标志
				});
			} else {
				// 非通道环境，直接使用主事务更新库存
				await Stock.createOrUpdate(
					{
						ts_code: data.ts_code,
						scale: data.scale_change,
					},
					tx,
				);
			}

			return convertPrismaPosition(result);
		};

		return updateFn(client);
	}

	// 如果是通道环境且没有提供客户端，使用平台先行的跨数据库事务
	if (isChannel() && platformPrisma) {
		// 平台数据库事务函数 - 先执行
		const platformDbFn = async (platformTx: Prisma.TransactionClient) => {
			// 更新库存
			const stockResult = await Stock.createOrUpdate(
				{
					ts_code: data.ts_code,
					scale: data.scale_change,
				},
				platformTx,
			);

			// 返回库存数据，以便在主事务中使用
			return {
				ts_code: data.ts_code,
				scale: data.scale_change,
				stockResult,
			};
		};

		// 主数据库事务函数 - 平台事务成功后执行
		const mainDbFn = async (
			platformResult: {
				ts_code: string;
				scale: number;
				stockResult: StockData;
			},
			tx: Prisma.TransactionClient,
		) => {
			const position = await findByTradeNo(data.trade_no, tx);

			let result: PrismaPosition;

			if (position) {
				// Update existing position
				const new_scale = position.scale + data.scale_change;
				if (new_scale === 0) {
					// Delete position if new scale is 0
					result = await tx.positions.delete({
						where: { trade_no: data.trade_no },
					});
				} else {
					// Update scale
					result = await tx.positions.update({
						where: { trade_no: data.trade_no },
						data: { scale: new_scale },
					});
				}
			} else {
				// 过滤掉不需要的字段
				const { scale_change, ...positionData } = data;
				result = await tx.positions.create({
					data: {
						...positionData,
						scale: scale_change,
					},
				});
			}

			return convertPrismaPosition(result);
		};

		// 平台事务回滚函数 - 主事务失败时回滚平台库存变更
		const platformRollbackFn = async (
			platformResult: {
				ts_code: string;
				scale: number;
				stockResult: StockData;
			},
			error: unknown,
		) => {
			// 创建抵消变更，使用相反数量
			await Stock.createOrUpdate(
				{
					ts_code: platformResult.ts_code,
					scale: -platformResult.scale, // 取相反数，完全抵消之前的变更
				},
				undefined, // 不使用事务客户端，直接操作
			);

			// 记录日志
			logger.info(
				`已回滚平台库存变更 - 股票: ${platformResult.ts_code}, 原始变更: ${platformResult.scale}, 错误: ${error instanceof Error ? error.message : String(error)}`,
			);
		};

		// 使用平台先行的跨数据库事务，并添加回滚函数
		return withCrossDbTransaction2(platformDbFn, mainDbFn, platformRollbackFn);
	}

	// 普通环境下的标准事务处理
	const updateFn = async (tx: Prisma.TransactionClient) => {
		const position = await findByTradeNo(data.trade_no, tx);

		let result: PrismaPosition;

		if (position) {
			// Update existing position
			const new_scale = position.scale + data.scale_change;
			if (new_scale === 0) {
				// Delete position if new scale is 0
				result = await tx.positions.delete({
					where: { trade_no: data.trade_no },
				});
			} else {
				// Update scale
				result = await tx.positions.update({
					where: { trade_no: data.trade_no },
					data: { scale: new_scale },
				});
			}
		} else {
			// 过滤掉不需要的字段
			const { scale_change, ...positionData } = data;
			result = await tx.positions.create({
				data: {
					...positionData,
					scale: scale_change,
				},
			});
		}

		// Update stock position within the same transaction
		await Stock.createOrUpdate(
			{
				ts_code: data.ts_code,
				scale: data.scale_change,
			},
			tx,
		);

		return convertPrismaPosition(result);
	};

	return withTransaction(updateFn);
}

export async function getAllForUser(
	user_id: number,
	offset: number,
	limit: number,
	isDescending: boolean,
	filters?: {
		ts_codes?: string[];
	},
): Promise<{
	items: PositionData[];
	total: number;
}> {
	const [positions, total] = await prisma.$transaction([
		prisma.positions.findMany({
			where: {
				user_id,
				ts_code: filters?.ts_codes?.length
					? { in: filters.ts_codes }
					: undefined,
			},
			orderBy: { created_at: isDescending ? "desc" : "asc" },
			take: limit,
			skip: offset,
		}),
		prisma.positions.count({
			where: {
				user_id,
				ts_code: filters?.ts_codes?.length
					? { in: filters.ts_codes }
					: undefined,
			},
		}),
	]);

	return {
		items: positions.map(convertPrismaPosition),
		total,
	};
}

export async function getAllByApi(): Promise<PositionData[]> {
	return prisma.positions
		.findMany()
		.then((positions) => positions.map(convertPrismaPosition));
}

export async function getAllByAdmin(
	page = 1,
	pageSize = 10,
): Promise<{ total: number; items: PositionData[] }> {
	const [total, items] = await prisma.$transaction([
		prisma.positions.count(),
		prisma.positions.findMany({
			orderBy: { created_at: "desc" },
			take: pageSize,
			skip: (page - 1) * pageSize,
		}),
	]);

	return { total, items: items.map(convertPrismaPosition) };
}

export async function findByTradeNo(
	trade_no: string,
	client?: Prisma.TransactionClient,
): Promise<PositionData | null> {
	const findFn = async (tx: Prisma.TransactionClient) => {
		const result = await tx.positions.findUnique({
			where: { trade_no },
		});
		return result ? convertPrismaPosition(result) : null;
	};

	return client ? findFn(client) : withTransaction(findFn);
}

export async function findByStock(ts_code: string): Promise<PositionData[]> {
	return prisma.positions
		.findMany({
			where: { ts_code },
		})
		.then((positions) => positions.map(convertPrismaPosition));
}

export async function updatePricesByStock(
	ts_code: string,
	ratio: number,
	client?: Prisma.TransactionClient,
): Promise<void> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		await tx.positions.updateMany({
			where: { ts_code },
			data: {
				entry_price: { multiply: ratio },
				exercise_price: { multiply: ratio },
			},
		});
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function countByUserId(
	user_id: number,
	filters?: {
		ts_codes?: string[];
	},
): Promise<number> {
	return prisma.positions.count({
		where: {
			user_id,
			...(filters?.ts_codes?.length && {
				ts_code: { in: filters.ts_codes },
			}),
		},
	});
}

export async function getAllTsCodes(user_id: number): Promise<string[]> {
	const positions = await prisma.positions.findMany({
		where: { user_id },
		select: { ts_code: true },
		distinct: ["ts_code"],
		orderBy: { ts_code: "asc" },
	});

	return positions.map((p) => p.ts_code);
}

export async function update(
	trade_no: string,
	updates: Partial<PositionData>,
	client?: Prisma.TransactionClient,
): Promise<PositionData> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		return tx.positions
			.update({
				where: { trade_no },
				data: updates,
			})
			.then((position) => convertPrismaPosition(position));
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function getForUserByAdmin(
	user_id: number,
	page = 1,
	pageSize = 10,
	filters?: {
		ts_codes?: string[];
	},
): Promise<{ total: number; items: PositionData[] }> {
	const [total, items] = await prisma.$transaction([
		prisma.positions.count({
			where: {
				user_id,
				...(filters?.ts_codes?.length && {
					ts_code: { in: filters.ts_codes },
				}),
			},
		}),
		prisma.positions.findMany({
			where: {
				user_id,
				...(filters?.ts_codes?.length && {
					ts_code: { in: filters.ts_codes },
				}),
			},
			orderBy: { created_at: "desc" },
			take: pageSize,
			skip: (page - 1) * pageSize,
		}),
	]);

	return { total, items: items.map(convertPrismaPosition) };
}

// 到期日处理
export async function getAll(): Promise<PositionData[]> {
	return prisma.positions
		.findMany()
		.then((positions) => positions.map(convertPrismaPosition));
}

// 人工修改订单的持仓变更
export async function updateByTradeNo(
	trade_no: string,
	updates: Partial<PositionData>,
	client?: Prisma.TransactionClient,
): Promise<PositionData> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		// 只允许更新特定字段
		const allowedFields = [
			"entry_price",
			"scale",
			"structure",
			"quote",
			"term",
		];

		// 过滤更新字段
		const filteredUpdates: Record<string, unknown> = {};
		for (const key of Object.keys(updates)) {
			if (allowedFields.includes(key)) {
				filteredUpdates[key] = updates[key as keyof PositionData];
			}
		}

		const updateData: Prisma.positionsUpdateInput = {
			...filteredUpdates,
		};

		// 如果修改了开仓价或结构，需要重新计算执行价
		if (updates.entry_price !== undefined || updates.structure !== undefined) {
			const position = await tx.positions.findUnique({
				where: { trade_no },
				select: { entry_price: true, structure: true },
			});
			if (!position) {
				throw AppError.create(
					"NOT_FOUND",
					`Position not found for ${trade_no}`,
				);
			}

			updateData.exercise_price =
				((updates.entry_price ?? Number(position.entry_price)) *
					Number.parseInt(updates.structure ?? position.structure)) /
				100;
		}

		const position = await tx.positions.update({
			where: { trade_no },
			data: updateData,
		});

		return convertPrismaPosition(position);
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}
