<template>
  <div class="positions-view view">
    <div class="current-positions card">
      <div class="card-header">
        <div class="card-title">当前持仓</div>
      </div>

      <div class="filter-row">
        <div class="form-group">
          <label for="positionSubject">选择标的：</label>
          <MySelect id="positionSubject" v-model="selectedSubject" :options="subjectOptions" placeholder="全部"
            :page-size="20" />
        </div>
      </div>

      <TableWrapper v-model:page-size="pageSize" v-model:current-page="currentPage" v-model:is-descending="isDescending"
        :total-pages="totalPages">
        <!-- 当没有数据时显示居中的 loading -->
        <LoadingState v-if="!computedPositions.length" :loading="isLoading" :has-data="computedPositions.length > 0"
          :icon="DataLine" :overlay="true" />

        <!-- 有数据时显示表格和覆盖式 loading -->
        <template v-else>
          <div class="table-container">
            <LoadingState :loading="isLoading" :has-data="computedPositions.length > 0" :icon="DataLine"
              :class="['loading-overlay', { 'visible': isLoading }]" />
            <table class="data-table">
              <thead>
                <tr>
                  <th>标的</th>
                  <th class="desktop-only">开仓日期</th>
                  <th class="desktop-only">名本</th>
                  <th class="desktop-only">期限</th>
                  <th>到期日</th>
                  <th class="desktop-only">开仓价</th>
                  <th class="desktop-only">结构</th>
                  <th class="desktop-only">执行价</th>
                  <th class="desktop-only">交易方</th>
                  <th class="desktop-only">市价</th>
                  <th class="desktop-only">期权费率</th>
                  <th>预估收益</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <template v-for="position in computedPositions" :key="position.trade_no">
                  <tr class="order-row" :class="{ 'selected': selectedPositionRow?.trade_no === position.trade_no }"
                    @click="togglePositionDetails(position)">
                    <td>{{ formatSubject(position.ts_code) }}</td>
                    <td class="desktop-only">{{ formatDate(position.created_at) }}</td>
                    <td class="desktop-only">{{ position.scale + "万" }}</td>
                    <td class="desktop-only">{{ position.term === 14 ? '2周' : position.term + "个月" }}</td>
                    <td>{{ formatDate(position.expiry_date) }}</td>
                    <td class="desktop-only">{{ position.entry_price }}</td>
                    <td class="desktop-only">{{ formatStructure(position.structure) }}</td>
                    <td class="desktop-only">{{ formatNumber(position.exercise_price) }}</td>
                    <td class="desktop-only">
                      <span class="provider" :class="{ 'internal': position.quote_provider === 'INK' }">
                        {{ getProviderName(position.quote_provider) }}
                        <span v-if="getTradingRestriction(position.quote_provider) !== undefined" class="trading-tag">T+{{ getTradingRestriction(position.quote_provider) }}</span>
                      </span>
                    </td>
                    <td class="desktop-only">{{ getCurrentPrice(position.ts_code)?.toFixed(2) || '-' }}</td>
                    <td class="desktop-only">{{ position.quote + '%' }}</td>
                    <td :class="getProfitClass(position.positionValue)">
                      {{ position.positionValue == 0 ? '0' : formatNumber(position.positionValue) }}
                    </td>
                    <td>
                      <el-button class="action-button" @click.stop="handlePosition(position)"
                        :disabled="!systemStatusManager.isSystemEnabled.value || submittingTradeNo === position.trade_no">
                        结算
                      </el-button>
                    </td>
                  </tr>
                  <template v-if="selectedPositionRow?.trade_no === position.trade_no">
                    <tr class="mobile-only details-row">
                      <td colspan="4">
                        <div class="order-details details-transition">
                          <div class="detail-item">
                            <span class="label">开仓日期：</span>
                            <span class="value">{{ formatDate(position.created_at) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">名本：</span>
                            <span class="value">{{ position.scale + "万" }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">期限：</span>
                            <span class="value">{{ position.term === 14 ? '2周' : position.term + "个月" }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">开仓价：</span>
                            <span class="value">{{ position.entry_price }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">结构：</span>
                            <span class="value">{{ formatStructure(position.structure) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">执行价：</span>
                            <span class="value">{{ formatNumber(position.exercise_price) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">交易方：</span>
                            <span class="value">
                              <span class="provider" :class="{ 'internal': position.quote_provider === 'INK' }">
                                {{ getProviderName(position.quote_provider) }}
                                <span v-if="getTradingRestriction(position.quote_provider) !== undefined" class="trading-tag">T+{{ getTradingRestriction(position.quote_provider) }}</span>
                              </span>
                            </span>
                          </div>
                          <div class="detail-item">
                            <span class="label">市价：</span>
                            <span class="value">{{ getCurrentPrice(position.ts_code)?.toFixed(2) || '-' }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">期权费率：</span>
                            <span class="value">{{ position.quote + '%' }}</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr class="dummy-row"></tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </template>
      </TableWrapper>
    </div>

    <!-- 添加模态框 -->
    <SettleConfirmModal v-model="showSettleModal" :position="currentPosition"
      :current-price="getCurrentPrice(currentPosition?.ts_code || '')" @confirm="handleSettleConfirm" />
  </div>
</template>

<script setup lang="ts">
import {
	ref,
	computed,
	watch,
	onMounted,
	onUnmounted,
	onActivated,
	onDeactivated,
} from "vue";
import MySelect from "@/components/MySelect.vue";
import TableWrapper from "@/components/TableWrapper.vue";
import { positionApi } from "@/api";
import { fetchCurrentPrices } from "@/utils/stock";
import { useStockStore } from "@/stores/stock";
import SettleConfirmModal from "./modals/SettleConfirmModal.vue";
import type {
	PositionData,
	SellRequest,
	ExternalQuoteProvider,
} from "@packages/shared";
import {
	OrderType,
	TradeDirection,
	getErrorMessage,
	PriceProviderNames,
	ProviderTradingRestrictions,
} from "@packages/shared";
import { tradeApi } from "@/api";
import { ElMessage } from "element-plus";
import { DataLine } from "@element-plus/icons-vue";
import LoadingState from "@/components/LoadingState.vue";
import { useTableSettings } from "@/composables/useTableSettings";
import { formatStructure, formatDate } from "@/utils/format";
import { systemStatusManager } from "@/core/systemStatusManager";
import { eventBus } from "@/utils/eventBus.js";
import { useSiteConfigStore } from "@/stores/siteConfig";

const siteConfigStore = useSiteConfigStore();

// Table Settings
const { initSettings, pageSize, isDescending, currentPage, totalPages } =
	useTableSettings();

const selectedSubject = ref<string[]>([]);
const positions = ref<PositionData[]>([]);
const currentPrices = ref<Map<string, number>>(new Map());
let pricePollingTimer: number | null = null;

const INTERVAL_PRICE_POLLING = 5000;

const { formatSubject } = useStockStore();

// 添加状态
const showSettleModal = ref(false);
const currentPosition = ref<PositionData | null>(null);

// 添加加载状态
const isLoading = ref(false);

// 添加新的响应式变量存储所有可用的标的代码
const availableTsCodes = ref<string[]>([]);

// 修改标的选项计算属性
const subjectOptions = computed(() => {
	if (!availableTsCodes.value) return [];

	return availableTsCodes.value.map((ts_code) => ({
		value: ts_code,
		label: formatSubject(ts_code),
	}));
});

// 修改计算属性，添加空值检查
const computedPositions = computed(() => {
	if (!positions.value) return [];

	return positions.value.map((position) => ({
		...position,
		positionValue: calculateEstimatedValue(position),
	}));
});

// 计算持仓估值=名本*(市价/开仓价-结构)=名本*(市价-执行价)/开仓价
const calculateEstimatedValue = (position: PositionData): number => {
	const currentPrice = getCurrentPrice(position.ts_code);
	if (!currentPrice) return 0;

	// 根据结构判断是看涨还是看跌
	const isCall = position.structure.endsWith("C");

	if (isCall) {
		// 看涨期权：当市价高于执行价时有价值
		if (currentPrice <= position.exercise_price) return 0;
		return (
			(position.scale * 10000 * (currentPrice - position.exercise_price)) /
			position.entry_price
		);
	}

	// 看跌期权：当市价低于执行价时有价值
	if (currentPrice >= position.exercise_price) return 0;
	return (
		(position.scale * 10000 * (position.exercise_price - currentPrice)) /
		position.entry_price
	);
};

const getProfitClass = (value: number | string): string => {
	const numValue = typeof value === "string" ? Number.parseFloat(value) : value;
	if (Number.isNaN(numValue) || value === "-") return "";
	return numValue > 0 ? "profit-positive" : "";
};

const formatNumber = (num: number) => {
	return new Intl.NumberFormat("zh-CN", {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(num);
};

// 获取提供商中文名
const getProviderName = (provider: string): string => {
	if (provider === "INK") return siteConfigStore.shortName();
	return PriceProviderNames[provider as ExternalQuoteProvider] || provider;
};

// 获取交易限制信息
const getTradingRestriction = (providerKey: string): number | undefined => {
	return ProviderTradingRestrictions[
		providerKey as keyof typeof ProviderTradingRestrictions
	];
};

// 修改获取持仓数据方法
const fetchPositions = async () => {
	try {
		isLoading.value = true;
		const response = await positionApi.getPositions(
			currentPage.value,
			pageSize.value,
			isDescending.value,
			{
				ts_codes: selectedSubject.value,
			},
		);
		positions.value = response?.items || [];
		totalPages.value = Math.ceil((response?.total || 1) / pageSize.value);
		// 更新可用标的列表
		availableTsCodes.value = response?.ts_codes || [];
	} catch (error) {
		console.error("Failed to fetch positions:", error);
		ElMessage.error("获取持仓数据失败");
	} finally {
		isLoading.value = false;
	}
};

// 修改为记录正在提交的交易编号
const submittingTradeNo = ref("");

// 更新处理持仓的方法
const handlePosition = (position: PositionData) => {
	currentPosition.value = position;
	showSettleModal.value = true;
};

// 更新结算确认处理
const handleSettleConfirm = async (
	orderType: string,
	sellScale: number,
	limitPrice?: number,
) => {
	if (!currentPosition.value) return;

	try {
		// 记录当前正在提交的交易编号
		submittingTradeNo.value = currentPosition.value.trade_no;
		if (!sellScale || sellScale <= 0) {
			ElMessage.error("无效的平仓名本");
			return;
		}

		let sellRequest: Omit<SellRequest, ""> = {
			type: orderType as OrderType,
			direction: TradeDirection.SELL,
			user_id: 0, // 将由后端从 session 获取
			trade_no: currentPosition.value.trade_no,
			ts_code: currentPosition.value.ts_code,
			scale: sellScale,
			quote_provider: currentPosition.value.quote_provider,
			quote_diff: currentPosition.value.quote_diff,
		};

		if (orderType === OrderType.LIMIT) {
			if (!limitPrice || limitPrice <= 0) {
				ElMessage.error("限价价格无效");
				return;
			}
			sellRequest = {
				...sellRequest,
				limit_price: limitPrice,
			};
		}

		await tradeApi.settleOrder(sellRequest);

		ElMessage.success("结算成功");

		// 更新当前选中的持仓数据
		if (currentPosition.value) {
			const updatedPosition = positions.value.find(
				(p) => p.trade_no === currentPosition.value?.trade_no,
			);
			currentPosition.value = updatedPosition || null;
		}
	} catch (error) {
		console.error("Failed to settle order:", error);
		ElMessage.error(`结算失败：${getErrorMessage(error)}`);
	} finally {
		// 只清除提交状态
		submittingTradeNo.value = "";
		// 如果持仓已经完全平仓（在最新数据中找不到），才清除当前选中的持仓
		if (
			currentPosition.value &&
			!positions.value.find(
				(p) => p.trade_no === currentPosition.value?.trade_no,
			)
		) {
			currentPosition.value = null;
			showSettleModal.value = false;
		}
	}
};

// 批量获取当前价格
const updateCurrentPrices = async () => {
	if (computedPositions.value.length === 0) return;

	try {
		const codes = computedPositions.value.map((position) => position.ts_code);
		const prices = await fetchCurrentPrices(codes);

		// 更新价格映射
		prices.forEach((price, index) => {
			currentPrices.value.set(codes[index], price);
		});
	} catch (error) {
		console.error("Failed to fetch current prices:", error);
	}
};

// 设置轮询
const setupPricePolling = () => {
	// 清除现有定时器
	if (pricePollingTimer) {
		clearInterval(pricePollingTimer);
	}

	// 立即执行一次
	updateCurrentPrices();

	// 设置定时轮询（例如每5秒更新一次）
	pricePollingTimer = window.setInterval(
		updateCurrentPrices,
		INTERVAL_PRICE_POLLING,
	);
};

// 修改价格获取方法
const getCurrentPrice = (tsCode: string): number | undefined => {
	return currentPrices.value.get(tsCode);
};

// 监听分页数据变化，重新设置轮询
watch(computedPositions, () => {
	setupPricePolling();
});

// 组件卸载时清理定时器
onUnmounted(() => {
	if (pricePollingTimer) {
		clearInterval(pricePollingTimer);
	}
});

onActivated(async () => {
	// 只在数据为空时加载数据
	if (positions.value.length === 0) {
		await fetchPositions();
	}
	// 每次激活时都更新价格
	setupPricePolling();
});

onMounted(() => {
	// 只初始化设置，不加载数据
	initSettings();
	// 添加事件监听
	eventBus.on("order-updated", fetchPositions);
});

// 组件失活时清理定时器
onDeactivated(() => {
	if (pricePollingTimer) {
		clearInterval(pricePollingTimer);
	}
});

// 监听筛选变化
watch([selectedSubject], () => {
	currentPage.value = 1; // 重置到第一页
	fetchPositions(); // 重新获取数据
});

// 保存用户设置
watch([pageSize, isDescending], ([newPageSize, newIsDescending]) => {
	localStorage.setItem("positionsPageSize", newPageSize.toString());
	localStorage.setItem("positionsIsDescending", newIsDescending.toString());
});

// 添加监听分页变化
watch([currentPage, pageSize, isDescending], () => {
	fetchPositions();
});

// 组件卸载时移除事件监听
onUnmounted(() => {
	eventBus.off("order-updated", fetchPositions);
});

// 在 script setup 中添加
const selectedPositionRow = ref<PositionData | null>(null);

const togglePositionDetails = (position: PositionData) => {
	if (selectedPositionRow.value?.trade_no === position.trade_no) {
		selectedPositionRow.value = null;
	} else {
		selectedPositionRow.value = position;
	}
};
</script>

<style scoped>
.action-button {
  width: 58px;
}

/* 国内股市红涨绿跌 */
.profit-positive {
  color: var(--el-color-error);
}

.profit-negative {
  color: var(--el-color-success);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .data-table {
    min-width: unset;
    width: 100%;
  }

  .order-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .order-row:hover {
    background-color: var(--el-fill-color-light);
  }

  .order-row.selected {
    background-color: var(--el-fill-color);
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }

  .order-details {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px 12px;
    font-size: 14px;
  }

  .detail-item {
    display: flex;
    padding: 6px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .detail-item:nth-last-child(2),
  .detail-item:last-child {
    border-bottom: none;
  }

  .detail-item .label {
    color: var(--el-text-color-secondary);
    width: 70px;
    flex-shrink: 0;
  }

  .detail-item .value {
    flex-grow: 1;
  }

  /* 隐藏空行但保持其对奇偶计数的影响 */
  tr.dummy-row {
    display: none;
  }

  /* 详情展开动画 */
  .details-transition {
    animation: expand 0.25s ease-in-out forwards;
  }

  @keyframes expand {
    from {
      max-height: 134px;
      opacity: 0.5;
    }

    to {
      max-height: 400px;
      opacity: 1;
    }
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
}

/* 添加样式支持交易方显示 */
.provider {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 13px;
}

.provider.internal {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.trading-tag {
  font-size: 10px;
  color: var(--el-color-warning-dark-2);
  margin-left: 3px;
}
</style>
