import { calculateExpiryDate, StatusChange } from "@packages/shared";
import type { SystemStatus } from "@packages/shared";
import logger from "@/utils/logger.js";
import * as ConfigService from "@/services/admin/index.js";
import { fetchTradeCalendar, fetchTradeCalendarRange } from "./marketData.js";
import { TRADING_PERIODS, INQUIRY_PERIODS } from "@/config/defaultParams.js";
import {
	dateToISOString,
	isoStringToDate,
	getChinaDate,
} from "@/utils/dateUtils.js";

/**
 * 判断是否为交易日，包含数据缺失的处理逻辑
 * @param date 目标日期
 * @returns 是否为交易日
 */
export async function isMarketDay(date?: Date): Promise<boolean> {
	try {
		const targetDate = date || getChinaDate();
		const calendar = await fetchTradeCalendar(targetDate);

		// 如果有数据，直接返回结果
		if (calendar?.is_open === 1) {
			return true;
		}
		if (calendar?.is_open === 0) {
			return false;
		}

		// 数据缺失时的默认处理逻辑
		const day = targetDate.getDay();

		// 周末默认非交易日
		if (day === 0 || day === 6) {
			return false;
		}

		// 工作日默认为交易日
		return true;
	} catch (error) {
		logger.error(error, "Error checking market day");
		return false;
	}
}

export function isMarketOpen(): boolean {
	const now = new Date();
	// getHours() 会根据服务器当前时区解释时间
	const currentTime = now.getHours() * 100 + now.getMinutes();

	return TRADING_PERIODS.some(
		(period) => currentTime >= period.start && currentTime < period.end,
	);
}

function isInquiryTime(): boolean {
	const now = new Date();
	const currentTime = now.getHours() * 100 + now.getMinutes();

	return INQUIRY_PERIODS.some(
		(period) => currentTime >= period.start && currentTime < period.end,
	);
}

export async function getDefaultStatus(): Promise<SystemStatus> {
	const isTradeDay = await isMarketDay();
	const open = isTradeDay && isMarketOpen();
	const canInquiry = isTradeDay && isInquiryTime();
	return {
		SYSTEM_ENABLED: open,
		POSITION_ENTRY_ENABLED: open,
		INQUIRY_ENABLED: canInquiry,
		change_type: StatusChange.AUTOMATIC,
		auto_manage_enabled: [true, true, true],
	};
}

export async function canAutoEnable(): Promise<boolean> {
	return true;
}

export async function manageMarketStatus(): Promise<void> {
	try {
		const currentStatus = await ConfigService.getSystemStatus();
		const defaultStatus = await getDefaultStatus();

		const updates: Partial<SystemStatus> = {
			change_type: StatusChange.AUTOMATIC,
		};

		if (currentStatus.auto_manage_enabled?.[0]) {
			updates.SYSTEM_ENABLED = defaultStatus.SYSTEM_ENABLED;
		}
		if (currentStatus.auto_manage_enabled?.[1]) {
			updates.POSITION_ENTRY_ENABLED = defaultStatus.POSITION_ENTRY_ENABLED;
		}
		if (currentStatus.auto_manage_enabled?.[2]) {
			updates.INQUIRY_ENABLED = defaultStatus.INQUIRY_ENABLED;
		}

		if ((await canAutoEnable()) && Object.keys(updates).length > 1) {
			await ConfigService.updateSystemStatus(updates);
		}
	} catch (error) {
		logger.error(error, "Failed to manage market status");
	}
}

/**
 * 计算预估的到期日，考虑数据缺失情况
 * @param createdAt 创建时间
 * @param term 期限
 * @returns 预估的到期日和确定性标志
 */
export async function calculateEstimatedExpiryDate(
	createdAt: Date,
	term: number,
): Promise<{ expiryDate: Date; isConfirmed: boolean }> {
	// 记录输入参数用于调试
	logger.info(`开始计算到期日: createdAt=${createdAt}, term=${term}`);

	// 初始计算的到期日（不考虑交易日）
	const initialExpiryDate = calculateExpiryDate(createdAt, term);
	logger.info(`理论到期日: ${initialExpiryDate}`);

	// 查询从到期日开始往后15天的交易日历（覆盖最长假期）
	const endDate = new Date(initialExpiryDate);
	endDate.setDate(endDate.getDate() + 15);

	try {
		// 获取交易日历
		const calendar = await fetchTradeCalendarRange(initialExpiryDate, endDate);

		// 如果没有交易日历数据，使用理论到期日
		if (!calendar || calendar.length === 0) {
			logger.info(`未找到交易日历数据，使用理论到期日: ${initialExpiryDate}`);
			return {
				expiryDate: initialExpiryDate,
				isConfirmed: false,
			};
		}

		// 按日期升序排序
		calendar.sort(
			(a, b) => new Date(a.cal_date).getTime() - new Date(b.cal_date).getTime(),
		);

		// 找出理论到期日对应的日期字符串形式，用于比较
		const initialExpiryDateStr = dateToISOString(initialExpiryDate);

		// 找到第一个在预期到期日之后且是交易日的日期
		const targetDate = calendar.find(
			(day) =>
				day.is_open === 1 &&
				dateToISOString(new Date(day.cal_date)) >= initialExpiryDateStr,
		);

		// 如果找到了合适的交易日，返回该日期作为到期日
		if (targetDate) {
			// 确保生成正确的日期对象
			// 使用 dateToISOString 和 isoStringToDate 确保日期对象是该日期在 UTC 的午夜，避免时区问题并使用项目工具函数。
			const expiryDate = isoStringToDate(
				dateToISOString(targetDate.cal_date as Date),
			);
			logger.info(`找到交易日，实际到期日: ${expiryDate}`);

			return {
				expiryDate,
				isConfirmed: true,
			};
		}

		// 如果未找到合适的交易日，返回理论到期日
		logger.info(`未找到合适的交易日，使用理论到期日: ${initialExpiryDate}`);
	} catch (error) {
		// 错误处理
		logger.error(error, "计算到期日时出错");
	}

	// 兜底返回理论到期日
	return {
		expiryDate: initialExpiryDate,
		isConfirmed: false,
	};
}

/**
 * 获取最近三个交易日，降序排列
 */
export async function getLastThreeTradingDays(): Promise<Date[]> {
	const today = new Date();
	const startDate = new Date(today);
	startDate.setDate(startDate.getDate() - 15); // 往前查15天以确保能获取足够的交易日

	const calendar = await fetchTradeCalendarRange(startDate, today);
	if (!calendar.length) {
		logger.warn("No trading calendar data found");
		return [];
	}

	return calendar
		.filter((day) => day.is_open === 1) // 筛选交易日
		.sort(
			(a, b) => new Date(b.cal_date).getTime() - new Date(a.cal_date).getTime(),
		) // 按日期降序排序
		.slice(0, 3)
		.map((day) => new Date(day.cal_date));
}

/**
 * 获取最近n个交易日，降序排列 (包括今天)
 * @param n 需要获取的交易日数量
 * @param extraDays 额外的缓冲天数，默认为10（考虑法定假期）
 * @returns 交易日数组，按日期降序排序
 */
export async function getLastNTradingDays(
	n: number,
	extraDays = 10,
): Promise<Date[]> {
	const today = new Date();
	const startDate = new Date(today);
	// 考虑周末和法定假期的缓冲：
	// n * (7/5) 考虑周末
	// + extraDays 考虑法定假期等特殊情况
	const bufferDays = Math.ceil(n * (7 / 5)) + extraDays;
	startDate.setDate(startDate.getDate() - bufferDays);

	const calendar = await fetchTradeCalendarRange(startDate, today);
	if (!calendar.length) {
		logger.warn("No trading calendar data found");
		return [];
	}

	return calendar
		.filter((day) => day.is_open === 1)
		.sort(
			(a, b) => new Date(b.cal_date).getTime() - new Date(a.cal_date).getTime(),
		)
		.slice(0, n)
		.map((day) => new Date(day.cal_date));
}

/**
 * 获取下一个交易日
 */
export async function getNextTradingDay(): Promise<Date> {
	const today = new Date();
	today.setHours(0, 0, 0, 0);
	const endDate = new Date(today);
	endDate.setDate(endDate.getDate() + 15); // 往后查15天以确保能获取到下一个交易日

	const calendar = await fetchTradeCalendarRange(today, endDate);
	if (!calendar.length) {
		logger.warn("No trading calendar data found");
		return today; // 如果获取失败，返回今天的日期作为fallback
	}

	const nextTradingDay = calendar
		.filter((day) => day.is_open === 1) // 筛选交易日
		.sort(
			(a, b) => new Date(a.cal_date).getTime() - new Date(b.cal_date).getTime(),
		) // 按日期升序排序
		.find((day) => {
			const date = new Date(day.cal_date);
			date.setHours(0, 0, 0, 0);
			return date > today;
		});

	if (!nextTradingDay) {
		logger.warn("No next trading day found");
		return today; // 如果找不到下一个交易日，返回今天的日期作为fallback
	}

	return new Date(nextTradingDay.cal_date);
}

/**
 * 检查明天是否开始节假日（包含周末的非交易日）
 * @returns 如果明天开始节假日，返回节假日的开始和结束日期；否则返回null
 */
export async function checkUpcomingHoliday(): Promise<{
	start: Date;
	end: Date;
} | null> {
	const tomorrow = new Date();
	tomorrow.setDate(tomorrow.getDate() + 1);
	tomorrow.setHours(0, 0, 0, 0);

	const endDate = new Date(tomorrow);
	endDate.setDate(endDate.getDate() + 15); // 往后查15天以覆盖大部分节假日

	const calendar = await fetchTradeCalendarRange(tomorrow, endDate);
	if (!calendar.length) {
		logger.warn("No trading calendar data found");
		return null;
	}

	// 按日期升序排序
	calendar.sort(
		(a, b) => new Date(a.cal_date).getTime() - new Date(b.cal_date).getTime(),
	);

	let holidayStart: Date | null = null;
	let consecutiveNonTradingDays = 0;
	let hasWeekend = false;

	for (const day of calendar) {
		const date = new Date(day.cal_date);

		if (day.is_open === 0) {
			// 非交易日
			if (!holidayStart) {
				// 只有当明天就是非交易日时才开始计算
				if (date.toDateString() === tomorrow.toDateString()) {
					holidayStart = date;
					consecutiveNonTradingDays = 1;
					// 检查是否是周末
					const dayOfWeek = date.getDay();
					if (dayOfWeek === 0 || dayOfWeek === 6) {
						hasWeekend = true;
					}
				}
			} else {
				consecutiveNonTradingDays++;
				// 检查是否是周末
				const dayOfWeek = date.getDay();
				if (dayOfWeek === 0 || dayOfWeek === 6) {
					hasWeekend = true;
				}
			}
		} else {
			// 交易日
			if (holidayStart && hasWeekend) {
				// 找到了一段包含周末的非交易日
				const holidayEnd = new Date(date);
				holidayEnd.setDate(holidayEnd.getDate() - 1); // 结束日期是这个交易日的前一天
				return {
					start: holidayStart,
					end: holidayEnd,
				};
			}
			// 重置计数
			holidayStart = null;
			consecutiveNonTradingDays = 0;
			hasWeekend = false;
		}
	}

	// 如果日历数据到尾部仍然是非交易日
	if (holidayStart && hasWeekend) {
		return {
			start: holidayStart,
			end: new Date(calendar[calendar.length - 1].cal_date),
		};
	}

	return null;
}
