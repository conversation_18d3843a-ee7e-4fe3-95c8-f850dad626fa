// 执行命令：pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\tests\test-pino-roll.ts

import pino from "pino";
import fs from "node:fs";
import { join } from "node:path";

const logDir = "packages/server/test-logs";

// 清理之前的测试日志文件
if (fs.existsSync(logDir)) {
	try {
		const files = fs.readdirSync(logDir);
		for (const file of files) {
			fs.unlinkSync(join(logDir, file));
		}
		console.log("已清理旧测试日志文件");
	} catch (err) {
		console.error("清理旧测试日志失败:", err);
	}
} else {
	fs.mkdirSync(logDir, { recursive: true });
	console.log("创建日志目录:", logDir);
}

// 添加与应用相同的 baseOptions
const baseOptions = {
	level: "info",
	base: {
		pid: process.pid,
		pm_id: process.env.pm_id,
	},
	timestamp: pino.stdTimeFunctions.isoTime,
	formatters: {
		level: (label: string) => ({ level: label }),
		log: (object: Record<string, unknown>) => {
			// 删除pino默认添加的一些字段，保持输出简洁
			const { hostname, ...rest } = object;
			return rest;
		},
	},
	// 控制序列化如何处理错误对象
	serializers: {
		err: pino.stdSerializers.err,
	},
};

// 配置pino-roll
const transport = pino.transport({
	targets: [
		{
			target: "pino-roll",
			options: {
				file: join(logDir, "roll-test.log"),
				frequency: "daily",
				mkdir: true,
				size: "10M",
				limit: {
					count: 10,
				},
			},
			level: "info",
		},
	],
});

// 正确初始化 pino logger
const logger = pino.default(baseOptions, transport);

// 添加优雅关闭功能
let shuttingDown = false;

/**
 * Gracefully shutdown the logger
 */
function shutdownLogger() {
	if (shuttingDown) return;
	shuttingDown = true;

	logger.info("Logger shutting down...");

	// 确保日志被刷新到磁盘
	setTimeout(() => {
		// 注意：pino不像winston那样需要显式结束
		logger.info("Logger shut down successfully");
		process.exit(0);
	}, 200);
}

// 处理进程退出信号
process.on("SIGTERM", shutdownLogger);
process.on("SIGINT", shutdownLogger);

// 自动关闭功能（可选，与您原代码类似）
let lastLogTime: number = Date.now();
let shutdownTimer: NodeJS.Timeout | null = null;

/**
 * 检查是否一段时间内没有新日志，如果是则关闭
 */
function checkAndShutdown(): void {
	if (Date.now() - lastLogTime > 5000 && !shutdownTimer && !shuttingDown) {
		shutdownTimer = setTimeout(() => {
			logger.info("No new logs for 5 seconds. Shutting down logger...");
			shutdownLogger();
		}, 100);
	}
}

type LoggerMethod = (...args: unknown[]) => void;

/**
 * 设置自动关闭功能
 * @param enabled 是否启用自动关闭
 */
function setupAutoShutdown(enabled = false): void {
	if (enabled) {
		// 包装所有日志方法
		for (const level of ["info", "error", "warn", "debug", "trace", "fatal"]) {
			const originalMethod = (
				logger as unknown as Record<string, LoggerMethod>
			)[level];
			(logger as unknown as Record<string, LoggerMethod>)[level] = function (
				...args: unknown[]
			) {
				lastLogTime = Date.now();
				if (shutdownTimer) {
					clearTimeout(shutdownTimer);
					shutdownTimer = null;
				}
				return originalMethod.apply(this, args);
			};
		}

		// 设置定期检查
		setInterval(checkAndShutdown, 1000);
	}
}

// 默认禁用自动关闭功能
setupAutoShutdown(false);

// 记录一些日志
logger.info("测试日志 1");
logger.error("测试错误日志");
logger.info("测试日志 2");

console.log("日志已写入，检查目录:", logDir);
console.log("目录内容:", fs.readdirSync(logDir));

// 等待一段时间确保日志写入
setTimeout(() => {
	console.log("5秒后检查目录内容:", fs.readdirSync(logDir));

	// 尝试读取日志文件内容
	const files = fs.readdirSync(logDir);
	for (const file of files) {
		if (file.includes("roll-test")) {
			try {
				const content = fs.readFileSync(join(logDir, file), "utf8");
				console.log(`文件 ${file} 内容:`, content || "(空)");
			} catch (err) {
				console.error(err, `无法读取文件 ${file}`);
			}
		}
	}

	console.log("测试完成");
}, 5000);
