import { Router } from "express";
import * as inquiryService from "@/services/inquiryService.js";
import { wrapRoute } from "@/utils/routeWrapper.js";

const router = Router();

/**
 * Get all stocks route: GET /api/reference/stocks
 */
router.get(
	"/stocks",
	wrapRoute(async (_req, res) => {
		const result = await inquiryService.getBasicStocks();
		// 改用紧凑格式：code~name;code~name
		const compactData = result
			.map((stock) => `${stock.ts_code}~${stock.name}`)
			.join(";");

		res.status(200).json(compactData);
	}),
);

export default router;
