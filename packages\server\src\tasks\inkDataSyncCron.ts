import cron from "node-cron";
import logger from "@/utils/logger.js";
import * as InkApi from "@/api/inkApi.js";
import redis from "@/lib/redis.js";
import * as ConfigService from "@/services/admin/index.js";
import { isMarketDay, isMarketOpen } from "@/financeUtils/marketTimeManager.js";
import type { BusinessConfig } from "@packages/shared";
import { ENV } from "@/config/configManager.js";
import { getChinaDateCompactString } from "@/utils/dateUtils.js";

/**
 * INK数据同步策略
 *
 * 采用基于交易时间的固定更新策略，原因：
 * 1. API时间戳仅包含时分秒，跨天时难以比较
 * 2. 交易时间特点：开盘频繁更新，收盘后更新较少
 *
 * 更新频率：
 * - 开盘前(00:00-9:25)：每30分钟更新
 * - 交易时间(9:25-15:00)：每5秒更新一次
 * - 收盘后(15:00-23:59)：每30分钟更新
 * - 非交易时间：不更新
 *
 * 外部报价更新频率：
 * - 交易前准备(8:30-9:25)：每5分钟更新一次
 *
 * 设计原则：
 * 1. 状态监控持续化
 *    - 系统状态的检查应该是持续的，而不是一次性的
 *    - 通过定时任务持续监控，而不是依赖启动时的一次性检查
 *
 * 2. 启动时间无关性
 *    - 系统行为不应依赖于启动时间点
 *    - 在任何时间点启动都能正确进入预期的运行状态
 */

// Redis key constants
export const INK_SWING_DATA_KEY = "ink:swing:data";
export const INK_PRICE_QUOTES_KEY_PREFIX = "ink:price:quotes";

// ! WARNING: Do not use process-specific caching variables here!
// In a multi-process environment (e.g. PM2), each process maintains its own memory space.
// Using process-specific variables can lead to data inconsistency across processes.
// Always use Redis or other shared storage for cross-process data sharing.

// 添加时间和更新频率相关的常量
const TRADING_START_HOUR = 9;
const TRADING_START_MINUTE = 25;
const TRADING_END_HOUR = 15;
const PRE_MARKET_START_HOUR = 8; // 外部报价盘前准备时间
const PRE_MARKET_START_MINUTE = 30;
const HIGH_FREQ_INTERVAL = 5000; // 5秒
const PRICE_QUOTES_INTERVAL = 300000; // 5分钟
const CRON_EXPRESSIONS = {
	EVERY_MINUTE: "* * * * *",
	EVERY_30_MIN_NON_TRADING: "*/30 0-9,15-23 * * *",
	EVERY_HOUR: "0 * * * *",
	EVERY_5_MIN_PREMARKET: "*/5 8-9 * * *", // 8:00-9:59 每5分钟
} as const; // TypeScript断言：1)使所有属性只读 2)保留字面量的精确类型 3)增强类型安全

// 添加外部报价Redis缓存过期时间常量
const CACHE_EXPIRATION_TIME = 60 * 60 * 24; // 24小时，以秒为单位

// 用于开发调试的今日数据使用的设定日期，谨慎使用避免影响服务器报价数据
const DEV_DATE = "20250403";

// 定义价格数组索引的常量映射，确保访问时的一致性
// 格式: 100C2W/100C1M/100C2M/100C3M/103C2W/103C1M/103C2M/103C3M/105C2W/105C1M/105C2M/105C3M/110C2W/110C1M/110C2M/110C3M

const PRICE_INDICES = {
	C100_2W: 0,
	C100_1M: 1,
	C100_2M: 2,
	C100_3M: 3,
	C103_2W: 4,
	C103_1M: 5,
	C103_2M: 6,
	C103_3M: 7,
	C105_2W: 8,
	C105_1M: 9,
	C105_2M: 10,
	C105_3M: 11,
	C110_2W: 12,
	C110_1M: 13,
	C110_2M: 14,
	C110_3M: 15,
} as const;

// 将报价对象转换为数组
function optionQuoteToArray(
	quote: InkApi.OptionPriceQuote,
): Array<number | null> {
	const result: Array<number | null> = Array(16).fill(null);

	// 填充数组
	result[PRICE_INDICES.C100_2W] = quote.c100_2w;
	result[PRICE_INDICES.C100_1M] = quote.c100_1m;
	result[PRICE_INDICES.C100_2M] = quote.c100_2m;
	result[PRICE_INDICES.C100_3M] = quote.c100_3m;
	result[PRICE_INDICES.C103_2W] = quote.c103_2w;
	result[PRICE_INDICES.C103_1M] = quote.c103_1m;
	result[PRICE_INDICES.C103_2M] = quote.c103_2m;
	result[PRICE_INDICES.C103_3M] = quote.c103_3m;
	result[PRICE_INDICES.C105_2W] = quote.c105_2w;
	result[PRICE_INDICES.C105_1M] = quote.c105_1m;
	result[PRICE_INDICES.C105_2M] = quote.c105_2m;
	result[PRICE_INDICES.C105_3M] = quote.c105_3m;
	result[PRICE_INDICES.C110_2W] = quote.c110_2w;
	result[PRICE_INDICES.C110_1M] = quote.c110_1m;
	result[PRICE_INDICES.C110_2M] = quote.c110_2m;
	result[PRICE_INDICES.C110_3M] = quote.c110_3m;

	return result;
}

export class InkDataSyncCron {
	private static instance: InkDataSyncCron;
	private marketTimeInterval: NodeJS.Timeout | null = null;
	private priceQuotesInterval: NodeJS.Timeout | null = null;

	// 私有构造函数，确保单例模式
	private constructor() {}

	/**
	 * 获取单例实例
	 */
	public static getInstance(): InkDataSyncCron {
		if (!InkDataSyncCron.instance) {
			InkDataSyncCron.instance = new InkDataSyncCron();
		}
		return InkDataSyncCron.instance;
	}

	/**
	 * Fetch and cache the necessary INK data
	 */
	private async updateInkData(): Promise<void> {
		try {
			logger.info("Fetching new INK API data");

			// 使用工具函数获取业务日期
			const date = getChinaDateCompactString();

			logger.info(`Attempting to fetch swing data for today: ${date}`);

			const swingData = await InkApi.fetchSwingData(date);

			// 如果今天没有数据，保持使用缓存
			if (!swingData || swingData.size === 0) {
				logger.warn(
					`No data available for today (${date}), keeping existing cache`,
				);
				return;
			}

			logger.info(
				`Successfully fetched swing data for today with ${swingData.size} entries`,
			);

			// Convert Map to Record for caching
			const swingRecord: Record<string, number> = {};
			swingData.forEach((value, key) => {
				swingRecord[key] = value;
			});

			// 存储到Redis
			await redis.set(INK_SWING_DATA_KEY, JSON.stringify(swingRecord));

			logger.info("INK swing data updated successfully and stored in Redis");
		} catch (error) {
			logger.error(
				`Failed to update INK swing data: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	/**
	 * 更新外部报价数据并缓存
	 * @param forceUpdate 是否强制更新已存在的缓存，默认为false
	 */
	private async updatePriceQuotes(forceUpdate = false): Promise<void> {
		try {
			logger.info("Fetching external price quotes data");

			// 使用工具函数获取业务日期
			const date = getChinaDateCompactString();

			logger.info(`Attempting to fetch price quotes for today: ${date}`);

			// 获取所有报价提供商
			const providers = Object.values(InkApi.PriceProvider);

			// 并行获取所有提供商的数据
			const providerDataPromises = providers.map(async (provider) => {
				try {
					const redisKey = `${INK_PRICE_QUOTES_KEY_PREFIX}:${provider}:${date}`;

					// 检查缓存是否已存在
					const cachedData = await redis.get(redisKey);

					// 如果不是强制更新且缓存存在，跳过更新
					if (!forceUpdate && cachedData) {
						logger.info(
							`Cache already exists for ${provider}, skipping update`,
						);
						return;
					}

					let useTestData = false;

					// 获取新数据，获取失败且非交易时间才在开发模式下获取旧数据
					const quoteData = await InkApi.fetchPriceQuotes(provider, date).then(
						async (data) => {
							// 如果是强制更新模式获取失败，清除缓存避免使用可能过期的数据
							if (forceUpdate && (!data || data.size === 0)) {
								if (
									ENV.NODE_ENV !== "development" ||
									((await isMarketDay()) && isMarketOpen())
								) {
									logger.warn(
										`Force update failed for ${provider}, clearing cache to avoid stale data`,
									);
									await redis.del(redisKey);
									return null;
								}

								logger.warn(
									`Force update failed for ${provider}, using test data from ${DEV_DATE}`,
								);
								useTestData = true;
								return InkApi.fetchPriceQuotes(provider, DEV_DATE);
							}
							// Return the data when it exists!
							return data;
						},
					);

					// 如果获取新数据成功且有数据
					if (quoteData && quoteData.size > 0) {
						// 使用数组格式存储
						const quotesRecord: Record<string, Array<number | null>> = {};
						quoteData.forEach((quote, stockCode) => {
							quotesRecord[stockCode] = optionQuoteToArray(quote);
						});

						// 更新缓存
						await redis.set(
							redisKey,
							JSON.stringify(quotesRecord),
							"EX",
							CACHE_EXPIRATION_TIME,
						);
						logger.info(
							`${provider}: Successfully updated with new data - cached ${quoteData.size} quotes for ${useTestData ? `test data from ${DEV_DATE}` : date}`,
						);
						return;
					}

					// 如果获取新数据失败或为空，且是强制更新模式
					if (forceUpdate) {
						logger.warn(
							`Force update requested but no new data available for ${provider}, clearing cache`,
						);
						await redis.del(redisKey);
					}
				} catch (error) {
					logger.error(error, `Failed to fetch price quotes from ${provider}`);
					// 如果是强制更新模式出错，也清除缓存
					if (forceUpdate) {
						const redisKey = `${INK_PRICE_QUOTES_KEY_PREFIX}:${provider}:${date}`;
						await redis.del(redisKey);
					}
				}
			});

			await Promise.all(providerDataPromises);
			logger.info("Price quotes update completed");
		} catch (error) {
			logger.error(error, "Failed to update price quotes data");
		}
	}

	/**
	 * 更新各种业务配置到数据库中
	 */
	private async updateBusinessConfig(): Promise<void> {
		try {
			logger.info("Checking for business config updates from INK API");

			// 获取当前配置
			const config = await ConfigService.getConfig();

			// 准备更新
			const updates: Partial<BusinessConfig> = {};
			let hasUpdates = false;

			// 需要检查的业务配置键名
			const configKeys = [
				"OPTION_MULTIPLIER",
				"STOCK_SCALE_LIMIT",
				"TOTAL_SCALE_LIMIT",
				"CHANNEL_CREDIT_LIMIT",
			] as const;

			// 循环检查每个配置项
			for (const key of configKeys) {
				try {
					// 直接通过键名获取API数据
					const apiData = await InkApi.fetchBusinessConfig(key);
					const newValue = apiData.get(key);
					const currentValue = config[key];

					// 检查值是否有效且需要更新
					if (
						newValue !== undefined &&
						typeof newValue === "number" &&
						!Number.isNaN(newValue) &&
						newValue > 0 &&
						currentValue !== newValue
					) {
						updates[key] = newValue;
						hasUpdates = true;
						logger.info(
							`Found new ${key}: ${newValue} (previous: ${currentValue})`,
						);
					}
				} catch (error) {
					// 单个配置项获取失败不应影响其他配置项的更新
					logger.error(error, `Failed to fetch or process ${key}`);
				}
			}

			// 如有更新，统一更新到配置中
			if (hasUpdates) {
				logger.info("Updating business config with new values:", updates);
				await ConfigService.updateConfig(updates);
				logger.info("Business config updated successfully");
			} else {
				logger.debug(
					"No changes in business config values, config not updated",
				);
			}
		} catch (error) {
			logger.error(error, "Failed to update business config values");
			// 错误发生时不执行任何操作，让定时任务下次再重试
		}
	}

	/**
	 * 检查是否为交易日，如果是则执行更新
	 */
	private async updateIfMarketDay(): Promise<void> {
		try {
			const isTradeDay = await isMarketDay();
			if (!isTradeDay) {
				return;
			}
			await this.updateInkData();
		} catch (error) {
			logger.error(error, "Error in updateIfMarketDay");
		}
	}

	/**
	 * 检查是否为交易日，如果是则更新外部报价
	 * @param forceUpdate 是否强制更新已存在的缓存，默认为false
	 */
	private async updatePriceQuotesIfMarketDay(
		forceUpdate = false,
	): Promise<void> {
		try {
			const isTradeDay = await isMarketDay();
			if (!isTradeDay && !forceUpdate) {
				return;
			}
			await this.updatePriceQuotes(forceUpdate);
		} catch (error) {
			logger.error(error, "Error in updatePriceQuotesIfMarketDay");
		}
	}

	/**
	 * 检查是否在交易时间内 (9:25-15:00)
	 */
	private isInTradingHours(): boolean {
		const now = new Date();
		const hour = now.getHours();
		const minute = now.getMinutes();
		return (
			(hour === TRADING_START_HOUR && minute >= TRADING_START_MINUTE) ||
			(hour > TRADING_START_HOUR && hour < TRADING_END_HOUR)
		);
	}

	/**
	 * 检查是否在盘前准备时间 (8:30-9:25)
	 */
	private isInPreMarketHours(): boolean {
		const now = new Date();
		const hour = now.getHours();
		const minute = now.getMinutes();
		return (
			(hour === PRE_MARKET_START_HOUR && minute >= PRE_MARKET_START_MINUTE) ||
			(hour === TRADING_START_HOUR && minute < TRADING_START_MINUTE)
		);
	}

	/**
	 * 在交易时间内更新数据
	 * 只在9:25-15:00之间运行，每5秒检查一次
	 */
	private async setupMarketTimeUpdate(): Promise<void> {
		// 清理已存在的interval
		if (this.marketTimeInterval) {
			clearInterval(this.marketTimeInterval);
			this.marketTimeInterval = null;
		}

		// 设置5秒间隔的更新
		this.marketTimeInterval = setInterval(
			() => this.updateIfMarketDay(),
			HIGH_FREQ_INTERVAL,
		);
		logger.info("Started market time updates - running every 5 seconds");

		// 立即执行一次更新
		await this.updateIfMarketDay();
	}

	/**
	 * 在盘前准备时间更新外部报价
	 * 只在8:30-9:25之间运行，每5分钟检查一次
	 */
	private async setupPreMarketPriceQuoteUpdate(): Promise<void> {
		// 清理已存在的interval
		if (this.priceQuotesInterval) {
			clearInterval(this.priceQuotesInterval);
			this.priceQuotesInterval = null;
		}

		// 设置5分钟间隔的更新
		this.priceQuotesInterval = setInterval(
			() => this.updatePriceQuotesIfMarketDay(),
			PRICE_QUOTES_INTERVAL,
		);
		logger.info(
			"Started pre-market price quotes updates - running every 5 minutes",
		);

		// 立即执行一次更新
		await this.updatePriceQuotesIfMarketDay();
	}

	/**
	 * 管理交易时间更新的启动和停止
	 */
	private async manageMarketTimeUpdates(): Promise<void> {
		const shouldBeRunning = this.isInTradingHours();

		if (shouldBeRunning && !this.marketTimeInterval) {
			logger.info("Entering trading hours, starting high-frequency updates");
			await this.setupMarketTimeUpdate();
		} else if (!shouldBeRunning && this.marketTimeInterval) {
			clearInterval(this.marketTimeInterval); // 停止定时器
			this.marketTimeInterval = null; // 重置定时器引用
			logger.info("Exiting trading hours, stopped high-frequency updates");
		}
	}

	/**
	 * 管理盘前报价更新的启动和停止
	 */
	private async managePreMarketPriceQuoteUpdates(): Promise<void> {
		const shouldBeRunning = this.isInPreMarketHours();

		if (shouldBeRunning && !this.priceQuotesInterval) {
			logger.info("Entering pre-market hours, starting price quote updates");
			await this.setupPreMarketPriceQuoteUpdate();
		} else if (!shouldBeRunning && this.priceQuotesInterval) {
			clearInterval(this.priceQuotesInterval); // 停止定时器
			this.priceQuotesInterval = null; // 重置定时器引用
			logger.info("Exiting pre-market hours, stopped price quote updates");
		}
	}

	/**
	 * Initialize the cron job and attempt to restore data from Redis
	 */
	public async start(): Promise<void> {
		logger.info("Initializing INK data synchronization...");

		// 立即执行一次数据更新
		try {
			logger.info("Performing initial INK data update...");
			await this.updateIfMarketDay();
			logger.info("Initial INK data update completed");
		} catch (error) {
			logger.error(error, "Error in initial INK data update");
		}

		// 立即执行一次外部报价更新，强制更新以刷新缓存
		try {
			logger.info("Performing initial price quotes update...");
			await this.updatePriceQuotesIfMarketDay(true); // 强制更新
			logger.info("Initial price quotes update completed");
		} catch (error) {
			logger.error(error, "Error in initial price quotes update");
		}

		// 立即执行一次业务配置更新
		try {
			logger.info("Performing initial business config update...");
			await this.updateBusinessConfig();
			logger.info("Initial business config update completed");
		} catch (error) {
			logger.error(error, "Error in initial business config update");
		}

		// 每分钟检查是否需要启动或停止高频更新和报价更新
		cron.schedule(CRON_EXPRESSIONS.EVERY_MINUTE, async () => {
			await this.manageMarketTimeUpdates();
			await this.managePreMarketPriceQuoteUpdates();
		});

		// 非交易时间每30分钟更新一次（合并0:00-9:30和15:00-23:59的更新）
		cron.schedule(CRON_EXPRESSIONS.EVERY_30_MIN_NON_TRADING, () =>
			this.updateIfMarketDay(),
		);

		// 每天9:25强制更新一次报价数据，确保数据为最新
		cron.schedule("25 9 * * *", async () => {
			try {
				const isTradeDay = await isMarketDay();
				if (!isTradeDay) {
					logger.debug("Skipping 9:25 force update: not a trading day");
					return;
				}
				logger.info("Performing 9:25 force update of price quotes");
				await this.updatePriceQuotes(true);
			} catch (error) {
				logger.error(error, "Error in 9:25 force update of price quotes");
			}
		});

		// 每小时检查一次配置数据更新
		cron.schedule(CRON_EXPRESSIONS.EVERY_HOUR, async () => {
			try {
				const isTradeDay = await isMarketDay();
				if (!isTradeDay) {
					logger.debug("Skipping business config update: not a trading day");
					return;
				}
				await this.updateBusinessConfig();
			} catch (error) {
				logger.error(error, "Error in business config sync cron job");
			}
		});

		logger.info("INK data synchronization initialized successfully");
	}
}

// 导出单例实例
export const inkDataSyncCron = InkDataSyncCron.getInstance();

/**
 * Get the cached swing data
 * Always fetches from Redis to ensure data consistency across processes
 */
export async function getCachedSwingData(): Promise<Record<
	string,
	number
> | null> {
	try {
		const redisData = await redis.get(INK_SWING_DATA_KEY);
		if (redisData) {
			logger.info("Loaded swing data from Redis cache");
			return JSON.parse(redisData);
		}
	} catch (error) {
		logger.error(error, "Error loading swing data from Redis");
	}

	return null;
}

/**
 * 获取缓存的期权报价数据
 * @param stockCode 股票代码
 * @param provider 报价提供商
 * @param strikePercent 行权价百分比 (100, 103, 105, 110)
 * @param period 期限 (2w, 1m, 2m, 3m)
 * @returns 报价值或null
 */
export async function getCachedOptionPrice(
	stockCode: string,
	provider: InkApi.PriceProviderValue,
	strikePercent: 100 | 103 | 105 | 110,
	period: "2w" | "1m" | "2m" | "3m",
): Promise<number | null> {
	try {
		const formattedCode = stockCode.split(".")[0];
		const date = getChinaDateCompactString();
		const redisKey = `${INK_PRICE_QUOTES_KEY_PREFIX}:${provider}:${date}`;

		// 确定数组索引位置
		const indexKey =
			`C${strikePercent}_${period.toUpperCase()}` as keyof typeof PRICE_INDICES;
		const index = PRICE_INDICES[indexKey];

		const redisData = await redis.get(redisKey);
		if (!redisData) {
			return null;
		}

		// 解析缓存数据 - 使用数组结构
		const quotesRecord = JSON.parse(redisData) as Record<
			string,
			Array<number | null>
		>;
		const stockQuote = quotesRecord[formattedCode];

		if (!stockQuote || index === undefined) {
			return null;
		}

		return stockQuote[index];
	} catch (error) {
		logger.error(error, `Error getting cached option price for ${stockCode}`);
		return null;
	}
}
