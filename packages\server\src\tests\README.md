# 测试模块说明

本目录包含各种服务器端功能的测试模块。

## 跨数据库事务测试

`testDatabaseTransaction.ts` 是一个全面的测试脚本，用于测试系统的跨数据库事务机制。该脚本验证了在各种故障场景下的事务一致性和回滚能力。

### 执行方式

```
pnpm tsx --tsconfig D:/ink-dev/packages/server/tsconfig.json D:/ink-dev/packages/server/src/tests/testDatabaseTransaction.ts
```

### 测试场景

1. **正常场景** - 验证两个数据库操作都成功的情况
2. **平台数据库失败** - 验证平台数据库操作失败时，主数据库变更被正确回滚
3. **回滚失败** - 验证回滚操作本身失败时的日志和错误处理
4. **资金转账** - 模拟资金转账业务场景的事务处理
5. **主数据库失败** - 验证主数据库操作失败时，平台操作不会被执行
6. **资金审核转账** - 模拟真实的资金审核业务场景，基于 `auditService.ts`

### 关键设计说明

我们的跨数据库事务使用的是"先提交再补偿"模式，而非真正的两阶段提交。这主要是因为：

1. PostgreSQL和Prisma不支持真正的跨数据库两阶段提交
2. 需要分布式事务协调器(XA协议)支持，但Prisma不提供此API
3. 独立数据库实例通常没有通信和协调能力
4. 在微服务架构中，跨数据库事务会导致紧耦合

实现方式：
- 先执行主数据库事务
- 如果成功，再执行平台数据库操作
- 如果平台操作失败，自动执行补偿操作回滚主数据库的变更
- 如果补偿也失败，记录详细日志以便手动介入

### 测试注意事项

- 测试需要配置适当的数据库连接（主数据库和平台数据库）
- 测试会创建临时表 `test_users` 和 `test_channels`
- 测试完成后会自动清理测试数据 