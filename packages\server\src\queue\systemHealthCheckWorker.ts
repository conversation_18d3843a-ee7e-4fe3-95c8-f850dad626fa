import type { Job } from "bullmq";
import {
	createWorker,
	systemHealthCheckTasksQueue,
	addRepeatedJob,
} from "./index.js";
import logger from "@/utils/logger.js";
import * as SystemHealthService from "@/services/systemHealthService.js";

// 定义系统健康检查相关的作业类型
export const SYSTEM_HEALTH_CHECK_JOBS = {
	RUN_HEALTH_CHECKS: "system-run-health-checks",
};

// 处理系统健康检查相关的作业
async function processSystemHealthCheckJob(job: Job) {
	const { name } = job;

	logger.info(`Processing system health check job: ${name}`);

	try {
		switch (name) {
			case SYSTEM_HEALTH_CHECK_JOBS.RUN_HEALTH_CHECKS:
				await runHealthChecks();
				break;
			default:
				logger.warn(`Unknown system health check job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process system health check job: ${name}`);
		throw error;
	}
}

// 运行系统健康检查
async function runHealthChecks(): Promise<void> {
	try {
		logger.info("Starting daily system health checks");

		// Run all health checks
		const results = await SystemHealthService.runAllHealthChecks();

		// Log summary of results
		const summary = Object.entries(results).map(([type, result]) => ({
			type,
			status: result.status,
			execution_time: result.execution_time,
		}));

		logger.info({ summary }, "Daily system health checks completed");
	} catch (error) {
		logger.error(error, "Error running daily system health checks");
	}
}

// 创建Worker实例
export const systemHealthCheckWorker = createWorker(
	systemHealthCheckTasksQueue,
	processSystemHealthCheckJob,
);

// 初始化定时作业调度
export async function initializeSystemHealthCheckJobs() {
	try {
		// Run health checks daily at 7:30 AM (before market open)
		await addRepeatedJob(
			systemHealthCheckTasksQueue,
			SYSTEM_HEALTH_CHECK_JOBS.RUN_HEALTH_CHECKS,
			{},
			"30 7 * * *",
		);

		logger.info("System health check jobs scheduled successfully");
	} catch (error) {
		logger.error(error, "Failed to schedule system health check jobs");
		throw error;
	}
}

// 用于应用程序启动时初始化所有系统健康检查任务
export async function initializeOnStartup() {
	await initializeSystemHealthCheckJobs();
	logger.info("System health check worker initialized successfully");
}
