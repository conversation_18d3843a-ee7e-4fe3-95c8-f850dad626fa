// 执行命令：pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\tests\tradeCalendarTest.ts

import { pool } from "@/lib/mysql.js";
import { dateToISOString } from "@/utils/dateUtils.js";
import logger from "@/utils/logger.js";

interface TradeCalendarRange {
	cal_date: string;
	is_open: number;
	pretrade_date: string;
}

/**
 * 计算给定日期后指定月数的到期日
 * @param startDate 起始日期
 * @param months 月数
 * @returns 到期日
 */
export function calculateExpiryDate(startDate: Date, months: number): Date {
	const result = new Date(startDate);
	result.setMonth(result.getMonth() + months);

	// 处理月末日期的特殊情况
	// 例如：1月31日 + 1个月，应该返回2月28日/29日
	const originalDate = startDate.getDate();
	if (result.getDate() !== originalDate) {
		// 如果日期不一致，说明发生了月末溢出，将日期设置为当月最后一天
		result.setDate(0);
	}

	return result;
}

/**
 * Fetch the trade calendar from database.
 * @returns Trade calendar data
 */
export async function fetchTradeCalendarRange(
	startDate: Date,
	endDate: Date,
): Promise<TradeCalendarRange[]> {
	try {
		const start = dateToISOString(startDate);
		const end = dateToISOString(endDate);

		const [rows] = await pool.query(`
			SELECT cal_date, is_open, pretrade_date
			FROM trade_calendar
			WHERE cal_date BETWEEN '${start}' AND '${end}'
			ORDER BY cal_date ASC
		`);
		return rows as TradeCalendarRange[];
	} catch (error) {
		logger.error(error, "Error fetching trade calendar range");
		throw error;
	}
}

async function calculateEstimatedExpiryDate(
	term: number,
): Promise<{ expiryDate: Date; isConfirmed: boolean }> {
	const createdAt = new Date();
	logger.info(
		`Starting calculation with term: ${term} months, created date: ${createdAt.toISOString()}`,
	);

	const initialExpiryDate = calculateExpiryDate(createdAt, term);
	logger.info(
		`Initial estimated expiry date: ${initialExpiryDate.toISOString()}`,
	);

	// 查询从到期日开始往后15天的交易日历（覆盖最长假期）
	const endDate = new Date(initialExpiryDate);
	endDate.setDate(endDate.getDate() + 15);
	logger.info(
		`Searching calendar from ${initialExpiryDate.toISOString()} to ${endDate.toISOString()}`,
	);

	const calendar = await fetchTradeCalendarRange(initialExpiryDate, endDate);
	logger.info(`Found ${calendar.length} calendar entries`);

	if (!calendar.length) {
		// 无数据，使用预估日期
		logger.warn("No calendar data found, using estimated date");
		return {
			expiryDate: initialExpiryDate,
			isConfirmed: false,
		};
	}

	// 按日期升序排序
	calendar.sort((a, b) => Number(a.cal_date) - Number(b.cal_date));

	// 找到第一个在预期到期日之后的交易日
	const targetDate = calendar.find(
		(day) =>
			day.is_open === 1 && // 是交易日
			day.cal_date >= dateToISOString(initialExpiryDate), // 在预期到期日之后
	);

	if (targetDate) {
		logger.info(`Found valid trading day: ${JSON.stringify(targetDate)}`);
		return {
			expiryDate: new Date(targetDate.cal_date),
			isConfirmed: true,
		};
	}

	// 如果没找到合适的交易日，返回未确认的预估日期
	logger.warn("No valid trading day found, using estimated date");
	return {
		expiryDate: initialExpiryDate,
		isConfirmed: false,
	};
}

// 执行测试
calculateEstimatedExpiryDate(1)
	.then((result) => {
		logger.info(
			`Final result: ${JSON.stringify({
				expiryDate: result.expiryDate.toISOString(),
				isConfirmed: result.isConfirmed,
			})}`,
		);
		// 等待一会儿确保日志写入完成
		setTimeout(() => process.exit(0), 500);
	})
	.catch((error) => {
		logger.error(error, "Test execution failed");
		process.exit(1);
	});
