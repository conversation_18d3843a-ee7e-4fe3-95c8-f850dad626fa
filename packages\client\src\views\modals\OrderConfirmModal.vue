<template>
  <el-dialog v-model="dialogVisible" title="下单确认" width="360px" class="order-dialog">
    <div class="modal-body">
      <div class="order-info">
        <div class="info-grid">
          <span class="label">标的：</span>
          <span class="value">{{ formatSubject(inquiry?.subject || '') }}</span>

          <span class="label">规模：</span>
          <span class="value">{{ inquiry?.scale + "万" }}</span>

          <span class="label">结构：</span>
          <span class="value">{{ inquiry?.structure ? formatStructure(inquiry.structure) : '' }}</span>

          <span class="label">期限：</span>
          <span class="value">{{ inquiry?.term === 14 ? "2周" : inquiry?.term + "个月" }}</span>

          <span class="label">报价：</span>
          <span class="value quote-value">{{ getQuoteValue() }}%</span>

          <span class="label">交易方：</span>
          <span class="value">
            {{ getProviderName(selectedProvider) }}
            <span v-if="getTradingRestriction(selectedProvider) !== undefined" class="restriction-notice">
              T+{{ getTradingRestriction(selectedProvider) }}
            </span>
          </span>

          <span class="label">期权费：</span>
          <span class="value option-fee">{{ formatOptionFee(inquiry) + "万" }}</span>
        </div>
      </div>

      <!-- 外部报价商警告提示 -->
      <div v-if="selectedProvider !== 'INK'" class="provider-warning">
        <el-icon>
          <WarningFilled />
        </el-icon>
        除{{ siteConfigStore.shortName() }}以外券商，股票异动可能会导致报价失效。
      </div>

      <div class="order-type">
        <div class="form-label">订单类型：</div>
        <div class="radio-group">
          <label>
            <input type="radio" v-model="orderType" value="market" />
            市价
          </label>
          <label>
            <input type="radio" v-model="orderType" value="limit" />
            限价
          </label>
          <label :class="{ disabled: !canVwapOrder }">
            <input type="radio" v-model="orderType" value="vwap" :disabled="!canVwapOrder" />
            均价
          </label>
        </div>
      </div>
      <div v-if="!canVwapOrder" class="time-warning">当前时间无法创建均价订单</div>
      <div v-if="orderType === 'limit'" class="limit-price">
        <div class="form-label">限价价格：</div>
        <div class="price-input">
          <el-input-number v-model="limitPrice" :min="0" :precision="2" :step="0.01" controls-position="right"
            class="limit-price-input" placeholder="请输入限价价格" />
          <div class="price-hint">当前股价：{{ currentPrice }}</div>
        </div>
      </div>
      <!-- 二次确认提示 -->
      <div v-if="needConfirmation" class="confirmation-message">
        请确认是否执行{{ orderType === 'market' ? '市价' : '限价' }}下单，订单成交后不可撤销
      </div>
    </div>

    <template #footer>
      <div class="button-group">
        <el-button class="cancel-button" @click="close">取消</el-button>
        <el-button class="submit-button" @click="handleButtonClick"
          :disabled="orderType === 'limit' && (!limitPrice || limitPrice <= 0) || confirmCountdown > 0"
          :class="{ 'countdown-active': confirmCountdown > 0 }">
          {{ needConfirmation
            ? confirmCountdown > 0
              ? `确认下单 (${confirmCountdown}s)`
              : '确认下单'
            : '下单'
          }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from "vue";
import { useStockStore } from "@/stores/stock";
import { formatStructure } from "@/utils/format";
import { fetchCurrentPrice } from "@/utils/stock";
import { validateVwapTime } from "@/utils/validateVwapTime";
import {
	PriceProviderNames,
	ProviderTradingRestrictions,
} from "@packages/shared";
import type { InquiryItem, ExternalQuoteProvider } from "@packages/shared";
import { WarningFilled } from "@element-plus/icons-vue";
import { useSiteConfigStore } from "@/stores/siteConfig";

const siteConfigStore = useSiteConfigStore();

const props = defineProps<{
	modelValue: boolean;
	inquiry: InquiryItem | null;
	selectedProvider?: string;
}>();

const currentPrice = ref<number | null>(null);
let priceUpdateTimer: number | null = null;

const { formatSubject } = useStockStore();

const emit = defineEmits<{
	(e: "update:modelValue", value: boolean): void;
	(e: "confirm", orderType: string, limitPrice?: number): void;
}>();

const dialogVisible = computed({
	get: () => props.modelValue,
	set: (value) => emit("update:modelValue", value),
});

const orderType = ref("market");
const limitPrice = ref(0);
const showConfirmation = ref(false);
const confirmCountdown = ref(0);
let countdownTimer: number | null = null;

// 获取实时价格
const updateCurrentPrice = async () => {
	if (!props.inquiry?.subject) return;
	try {
		const price = await fetchCurrentPrice(props.inquiry.ts_code);
		currentPrice.value = price || null;
	} catch (error) {
		console.error("Failed to fetch current price:", error);
	}
};

// 启动定时器
const startPriceUpdate = () => {
	updateCurrentPrice(); // 立即执行一次
	priceUpdateTimer = window.setInterval(updateCurrentPrice, 5000);
};

// 清理定时器
const stopPriceUpdate = () => {
	if (priceUpdateTimer) {
		window.clearInterval(priceUpdateTimer);
		priceUpdateTimer = null;
	}
};

const needConfirmation = computed(
	() => showConfirmation.value && orderType.value !== "vwap",
);

const handleButtonClick = () => {
	if (orderType.value === "vwap" || needConfirmation.value) {
		confirm();
	} else {
		showConfirmationDialog();
	}
};
const showConfirmationDialog = () => {
	showConfirmation.value = true;
	confirmCountdown.value = 2; // 改为2秒倒计时

	countdownTimer = window.setInterval(() => {
		confirmCountdown.value--;
		if (confirmCountdown.value <= 0) {
			if (countdownTimer) {
				clearInterval(countdownTimer);
				countdownTimer = null;
			}
		}
	}, 1000);
};

const close = () => {
	dialogVisible.value = false;
};

const confirm = () => {
	if (
		orderType.value === "limit" &&
		(!limitPrice.value || limitPrice.value <= 0)
	) {
		return;
	}
	emit("confirm", orderType.value, limitPrice.value);
	close(); // 防抖
};

// 组件挂载时启动价格更新
onMounted(() => {
	startPriceUpdate();
});

// 监听 show 属性变化
watch(
	() => props.modelValue,
	(newShow) => {
		if (newShow) {
			orderType.value = "market";
			showConfirmation.value = false;
			confirmCountdown.value = 0;
			if (countdownTimer) {
				clearInterval(countdownTimer);
				countdownTimer = null;
			}

			startPriceUpdate();
		} else {
			stopPriceUpdate();
		}
	},
);

// 组件卸载时清理
onUnmounted(() => {
	stopPriceUpdate();
	if (countdownTimer) {
		clearInterval(countdownTimer);
	}
});

// 计算期权费，增加 useOriginal 参数
const formatOptionFee = (inquiry: InquiryItem | null) => {
	if (!inquiry) return "";

	// 使用当前所选报价商的报价
	const quoteValue = getQuoteValue();
	const quote = typeof quoteValue === "number" ? quoteValue : 0;

	const optionFee = (inquiry.scale * quote) / 100; // 单位：万
	return new Intl.NumberFormat("zh-CN", {
		style: "decimal",
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(optionFee);
};

// 只检查 VWAP 订单是否可用
const canVwapOrder = computed(() => validateVwapTime().valid);

// 监听 VWAP 可用性变化
watch(canVwapOrder, (newCanVwap) => {
	if (!newCanVwap && orderType.value === "vwap") {
		orderType.value = "market";
	}
});

// 监听订单类型变化
watch(
	() => orderType.value,
	(newType) => {
		// 设置限价默认值
		if (newType === "limit" && currentPrice.value) {
			limitPrice.value = currentPrice.value;
		}
		// 重置确认状态
		showConfirmation.value = false;
		if (countdownTimer) {
			clearInterval(countdownTimer);
			countdownTimer = null;
		}
		confirmCountdown.value = 0;
	},
);

// 添加报价商名称获取函数
const getProviderName = (provider?: string): string => {
	if (!provider) return "";
	if (provider === "INK") return siteConfigStore.shortName();
	return PriceProviderNames[provider as ExternalQuoteProvider] || provider;
};

// 在计算属性中添加
const selectedProvider = computed(() => props.selectedProvider || "INK");

// 计算 getQuoteValue 函数
const getQuoteValue = () => {
	if (!props.inquiry) return "";

	// 使用选中报价商的报价
	if (props.selectedProvider) {
		const price =
			props.selectedProvider === "INK"
				? props.inquiry.quote
				: props.inquiry.external_quotes?.[props.selectedProvider] || 0;

		// 加上对应报价商的差价
		const priceDiff = props.inquiry.quote_diffs?.[props.selectedProvider] || 0;
		return Number((price + priceDiff).toFixed(2));
	}

	return "";
};

const getTradingRestriction = (providerKey: string): number | undefined => {
	return ProviderTradingRestrictions[
		providerKey as keyof typeof ProviderTradingRestrictions
	];
};
</script>

<style>
.order-dialog .el-dialog__header {
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}
</style>

<style scoped>
.modal-body {
  margin: 0 16px;
}

.order-info {
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.info-grid {
  display: grid;
  grid-template-columns: auto auto;
  gap: 12px 24px;
  align-items: center;
}

.info-grid .label {
  color: var(--el-text-color-regular);
  justify-self: end;
  white-space: nowrap;
}

.info-grid .value {
  justify-self: start;
}

.order-type {
  margin: 6px 0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.radio-group {
  display: flex;
  gap: 12px;
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 4px;
  border: 1px solid transparent;
  border-radius: 6px;
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.radio-group label:hover {
  color: var(--el-color-primary-dark-2);
  border-color: var(--el-color-primary-dark-2);
  background: var(--el-fill-color-light);
}

.radio-group input {
  cursor: pointer;
  margin: 3px;
  margin-left: 1px;
}

.radio-group input:checked+span {
  color: var(--el-color-primary);
}

.order-dialog .button-group {
  display: flex;
  justify-content: flex-end;
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.cancel-button,
.submit-button {
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.cancel-button {
  border: 1px solid var(--el-border-color);
  background-color: var(--color-secondary);
  color: var(--el-text-color-primary);
}

.submit-button {
  border: none;
  background-color: var(--el-color-primary-dark-2);
  color: white;
}

.submit-button:hover {
  background-color: var(--el-color-primary-dark-2);
}

.price-input {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.form-label {
  margin-top: 2px;
}

.limit-price {
  display: flex;
  margin-top: 12px;
  gap: 20px;
}

.limit-price-input {
  width: 204px;
}

.price-input input {
  padding: 5px 10px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  width: 150px;
}

.price-input input:focus {
  border-color: var(--el-color-primary-dark-2);
  outline: none;
}

.price-hint {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.quote-value,
.option-fee {
  display: flex;
  align-items: center;
  gap: 8px;
}

.restriction-notice {
  display: inline-block;
  margin-left: 5px;
  font-size: 12px;
  color: var(--el-color-warning);
  font-weight: 600;
  padding: 0 3px;
  border-radius: 2px;
  background-color: rgba(var(--el-color-warning-rgb), 0.1);
}

.time-warning {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  margin: 2px 0;
}

.confirmation-message {
  color: var(--el-color-warning);
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
}

.submit-button.countdown-active {
  opacity: 0.8;
  cursor: not-allowed;
}

.submit-button {
  transition: opacity 0.3s ease;
}

.provider-warning {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  margin-top: 10px;
  padding: 8px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  background-color: var(--el-fill-color-light);
}

.provider-warning i {
  margin-right: 8px;
}
</style>
