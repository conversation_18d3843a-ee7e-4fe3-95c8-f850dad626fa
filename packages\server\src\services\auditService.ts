import * as Audit from "@/models/audit.js";
import * as User from "@/models/user.js";
import type {
	QualificationAuditData,
	FundAuditData,
	CreateQualificationAuditInput,
	CreateFundAuditInput,
} from "@packages/shared";
import { AuditType, TransactionType } from "@packages/shared";
import { AppError } from "@/core/appError.js";
import logger from "@/utils/logger.js";
import { ENV } from "@/config/configManager.js";
import { verifyPaymentPassword } from "./fundService.js";
import EmailService from "@/utils/email.js";
import { getPlatformConfig } from "@/services/admin/configService.js";

// 发送管理员审核通知邮件
async function sendAdminNotificationEmails(
	auditType: AuditType,
	userData: { email: string; name?: string } | null,
): Promise<void> {
	try {
		if (!userData) {
			logger.warn("Cannot send admin notification: user data is missing");
			return;
		}

		// 获取平台配置
		const platformConfig = await getPlatformConfig();

		// 根据审核类型选择对应的邮箱列表
		const emailList =
			auditType === AuditType.QUALIFICATION
				? platformConfig.qualification_audit_email
				: platformConfig.fund_audit_email;

		// 确保邮箱列表存在且非空
		if (!emailList || emailList.length === 0) {
			logger.warn(
				`No admin notification emails configured for ${auditType} audits`,
			);
			return;
		}

		// 准备模板和参数
		const templateType = "NEW_AUDIT_APPLICATION";
		const params = {
			name: userData?.name || userData?.email || "未知用户",
		};

		// 向每个配置的邮箱发送通知
		for (const email of emailList) {
			try {
				await EmailService.sendEmail(email, templateType, params);
				logger.info(
					`Admin notification sent to ${email} for ${auditType} audit`,
				);
			} catch (error) {
				logger.error(error, `Failed to send admin notification to ${email}`);
			}
		}
	} catch (error) {
		logger.error(error, "Error sending admin notification emails");
	}
}

export async function getUserHistory(user_id: number) {
	return Audit.getUserHistory(user_id);
}

export async function createQualificationAudit({
	user_id,
	data,
	comment,
}: CreateQualificationAuditInput) {
	// Check for duplicate phone number
	if (data.phone_number) {
		const existingUserByPhone = await User.findByPhoneNumber(data.phone_number);
		if (existingUserByPhone && existingUserByPhone.user_id !== user_id) {
			throw AppError.create("PHONE_EXISTS", "该手机号已被其他用户使用");
		}
	}

	const audit = await Audit.createQualificationAudit(user_id, data, comment);

	const user = await User.findById(user_id);
	if (!user) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}

	// 发送管理员审核通知邮件
	await sendAdminNotificationEmails(AuditType.QUALIFICATION, {
		email: user.email,
		name: user.name,
	});

	return audit;
}

export async function getQualificationStatus(
	user_id: number,
): Promise<boolean> {
	const status = await User.getQualificationStatus(user_id);
	logger.info(`Qualification status for user ${user_id}: ${!!status}`);
	return !!status;
}

export async function getPendingAudits(user_id: number) {
	const pendingAudits = await Audit.getPendingByUser(user_id);
	return pendingAudits.reduce<{
		qualification?: QualificationAuditData;
		fund?: FundAuditData[];
	}>((result, audit: QualificationAuditData | FundAuditData) => {
		if (audit.type === AuditType.QUALIFICATION) {
			result.qualification = audit as QualificationAuditData;
		} else if (audit.type === AuditType.FUND) {
			if (!result.fund) result.fund = [];
			result.fund.push(audit as FundAuditData);
		}
		return result;
	}, {});
}

export async function createFundAudit({
	user_id,
	operation,
	amount,
	currency,
	password,
}: CreateFundAuditInput) {
	// 平台入金允许负数，其他操作必须为正数
	if (operation !== TransactionType.PLATFORM_DEPOSIT && amount <= 0) {
		throw AppError.create(
			"INVALID_FUND_AMOUNT",
			"Fund amount must be positive",
		);
	}

	// 如果是提现操作，检查余额
	if (operation === TransactionType.WITHDRAW) {
		if (!password) {
			throw AppError.create("PAYMENT_PASSWORD_REQUIRED", "支付密码不能为空");
		}
		// 验证支付密码
		await verifyPaymentPassword(user_id, password);

		const balance = await User.getBalance(user_id, currency);
		if (balance < amount) {
			throw AppError.create(
				"INSUFFICIENT_BALANCE",
				"Insufficient balance for the operation",
			);
		}
	}

	// 如果是平台入金且为负数，检查余额
	if (operation === TransactionType.PLATFORM_DEPOSIT && amount < 0) {
		const balance = await User.getBalance(user_id, currency);
		if (balance < Math.abs(amount)) {
			throw AppError.create(
				"INSUFFICIENT_BALANCE",
				"Insufficient balance for the platform deduction",
			);
		}
	}

	const audit = await Audit.createFundAudit(
		user_id,
		operation,
		amount,
		currency,
	);

	const user = await User.findById(user_id);
	if (!user) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}

	// 发送管理员审核通知邮件
	await sendAdminNotificationEmails(AuditType.FUND, {
		email: user.email,
		name: user.name,
	});

	return audit;
}
