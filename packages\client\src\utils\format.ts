import { StructureValue, type StructureType } from "@packages/shared";

export const formatSubject = (
	tsCode: string,
	stockCache: Map<string, string>,
): string => {
	const stockName = stockCache.get(tsCode);
	return stockName ? `${stockName} (${tsCode})` : tsCode;
};

export const formatStructure = (structure: StructureType): string => {
	return StructureValue[structure] || structure;
};

export function formatDate(date: Date | string): string {
	const dateObj = typeof date === "string" ? new Date(date) : date;
	return dateObj.toLocaleDateString("zh-CN", {
		year: "numeric",
		month: "2-digit",
		day: "2-digit",
	});
}

export function formatDateTime(date: Date | string): string {
	const dateObj = typeof date === "string" ? new Date(date) : date;
	return dateObj.toLocaleString("zh-CN", {
		year: "numeric",
		month: "2-digit",
		day: "2-digit",
		hour: "2-digit",
		minute: "2-digit",
		second: "2-digit",
	});
}
