<template>
  <div class="loading-state" :class="{ 'has-data': hasData && !loading }">
    <el-empty v-if="!loading && !hasData" :image-size="200" description="暂无数据">
      <template #image>
        <el-icon :size="60" class="empty-icon">
          <component :is="icon || DataLine" />
        </el-icon>
      </template>
    </el-empty>
    <div v-else-if="loading" class="loading-wrapper">
      <el-icon :size="60" class="loading-icon">
        <Loading />
      </el-icon>
      <div class="loading-text">加载中</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Loading, DataLine } from "@element-plus/icons-vue";

defineProps<{
	loading: boolean;
	hasData: boolean;
	icon?: typeof DataLine;
}>();
</script>

<style scoped>
.loading-state {
  padding: 40px 0;
  text-align: center;
}

.loading-state.has-data {
  padding: 0;
}

:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.empty-icon) {
  color: var(--el-color-primary);
  opacity: 0.8;
}

:deep(.el-empty__description) {
  margin-top: 20px;
  color: var(--text-color-secondary);
}

.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.loading-icon {
  color: var(--el-color-primary);
  animation: rotate 2s linear infinite;
}

.loading-text {
  color: var(--text-color-secondary);
  font-size: 14px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
