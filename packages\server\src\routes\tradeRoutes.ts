import { Router } from "express";
import * as tradeService from "@/services/trade/tradeService.js";
import { wrapUserRoute } from "@/utils/routeWrapper.js";
import type { BuyRequest, SellRequest } from "@packages/shared";
import { cancelPendingOrder } from "@/services/trade/confirmOrder.js";
import * as pendingOrderModel from "@/models/trade/pendingOrder.js";
import { AppError } from "@/core/appError.js";
import * as userModel from "@/models/user.js";

const router = Router();

/**
 * Unified order route: POST /api/trade/order
 */
router.post(
	"/order",
	wrapUserRoute<BuyRequest>(async (req, res) => {
		const result = await tradeService.placeOrder({
			...req.body,
			user_id: req.jwt.user_id,
		});
		res.status(200).json(result);
	}),
);

/**
 * Settle order route: POST /api/trade/settle
 */
router.post(
	"/settle",
	wrapUserRoute<SellRequest>(async (req, res) => {
		const result = await tradeService.placeOrder({
			...req.body,
			user_id: req.jwt.user_id,
		});
		res.status(200).json(result);
	}),
);

/**
 * Get all orders history: GET /api/trade/history/order
 */
router.get(
	"/history/order",
	wrapUserRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const isDescending = req.query.isDescending !== "false";
		const { ts_codes, startDate, endDate } = req.query;

		const history = await tradeService.getOrderHistory(
			req.jwt.user_id,
			page,
			pageSize,
			isDescending,
			{
				ts_codes: ts_codes ? (ts_codes as string).split(",") : undefined,
				startDate: startDate as string,
				endDate: endDate as string,
			},
		);
		res.status(200).json(history);
	}),
);

/**
 * Get settle orders history: GET /api/trade/history/settle
 */
router.get(
	"/history/settle",
	wrapUserRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const isDescending = req.query.isDescending !== "false"; // Default true
		const { ts_codes, startDate, endDate } = req.query;

		const history = await tradeService.getSettleHistory(
			req.jwt.user_id,
			page,
			pageSize,
			isDescending,
			{
				ts_codes: ts_codes ? (ts_codes as string).split(",") : undefined,
				startDate: startDate as string,
				endDate: endDate as string,
			},
		);
		res.status(200).json(history);
	}),
);

/**
 * Get all pending buy orders list: GET /api/trade/list/buying
 */
router.get(
	"/list/buying",
	wrapUserRoute(async (req, res) => {
		const history = await tradeService.getBuyingList(req.jwt.user_id);
		res.status(200).json(history);
	}),
);

/**
 * Get all pending sell orders list: GET /api/trade/list/selling
 */
router.get(
	"/list/selling",
	wrapUserRoute(async (req, res) => {
		const history = await tradeService.getSellingList(req.jwt.user_id);
		res.status(200).json(history);
	}),
);

/**
 * Get pending orders by trade_no: GET /api/trade/pending
 */
router.get(
	"/pending",
	wrapUserRoute(async (req, res) => {
		const { trade_no } = req.query;
		const pendingOrders = await pendingOrderModel.findByTradeNo(
			trade_no as string,
		);
		res.status(200).json(pendingOrders);
	}),
);

/**
 * Cancel order route: POST /api/trade/pending/cancel
 */
router.post(
	"/pending/cancel",
	wrapUserRoute<{ pending_id: number }>(async (req, res) => {
		const pendingOrder = await pendingOrderModel.findById(req.body.pending_id);
		if (!pendingOrder) {
			throw AppError.create("NOT_FOUND", "Pending order not found");
		}

		if (pendingOrder.user_id !== req.jwt.user_id) {
			throw AppError.create(
				"ORDER_NOT_OWNED",
				"This order does not belong to the current user",
			);
		}

		await cancelPendingOrder(pendingOrder);
		res.status(200).json({ message: "Pending order cancelled successfully" });
	}),
);

/**
 * Get User Info: GET /api/trade/user
 */
router.get(
	"/user",
	wrapUserRoute(async (req, res) => {
		const user = await userModel.findById(req.jwt.user_id);
		res.status(200).json(user);
	}),
);

export default router;
