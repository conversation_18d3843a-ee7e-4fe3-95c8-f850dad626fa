import { Router } from "express";
import { wrapRoute } from "@/utils/routeWrapper.js";
import path from "node:path";
import fs from "node:fs/promises";
import * as sharedConfigService from "@/services/admin/sharedConfigService.js";
import { AppError } from "@/core/appError.js";
import logger from "@/utils/logger.js";
import { ENV } from "@/config/configManager.js";

const router = Router();

// 使用与 templateRoutes 相同的逻辑确定目录位置
const cwd = process.cwd();
const isInRootDir = !cwd.endsWith("server");
const PUBLIC_DIR = isInRootDir
	? path.join(cwd, "packages", "server", "public")
	: path.join(cwd, "public");

// 自定义模板存储目录
const UPLOAD_DIR = ENV.UPLOAD_DIR || "/var/uploads";
const CUSTOM_TEMPLATES_DIR = path.join(UPLOAD_DIR, "custom-templates");

// 获取ISDA主协议: GET api/public/agreements/isda-master
router.get(
	"/agreements/isda-master",
	wrapRoute(async (req, res) => {
		try {
			// 先尝试获取自定义模板
			const config = await sharedConfigService.getSharedConfig();
			const agreementTemplates = config.agreement_templates;
			const uid = agreementTemplates?.["isda-master"]?.filter?.uid;

			if (uid) {
				const customTemplatePath = path.join(CUSTOM_TEMPLATES_DIR, uid);

				// 检查自定义模板文件是否存在
				try {
					await fs.access(customTemplatePath);
					// 存在则返回自定义模板
					return res.sendFile(uid, { root: CUSTOM_TEMPLATES_DIR });
				} catch (err) {
					// 自定义模板不存在，降级到默认模板
					logger.warn("自定义ISDA主协议模板文件不存在，使用默认模板");
				}
			}

			// 使用默认模板
			const templatePath = path.join(
				PUBLIC_DIR,
				"agreements/ISDA主协议及附约-中英.pdf",
			);

			// 检查文件是否存在
			await fs.access(templatePath);

			// 返回文件
			res.sendFile(templatePath);
		} catch (error) {
			logger.error(error, "获取ISDA主协议失败");
			throw AppError.create("NOT_FOUND", "协议文件不存在");
		}
	}),
);

// 获取ISDA补充协议: GET api/public/agreements/isda-supplement
router.get(
	"/agreements/isda-supplement",
	wrapRoute(async (req, res) => {
		try {
			// 先尝试获取自定义模板
			const config = await sharedConfigService.getSharedConfig();
			const agreementTemplates = config.agreement_templates;
			const uid = agreementTemplates?.["isda-supplement"]?.filter?.uid;

			if (uid) {
				const customTemplatePath = path.join(CUSTOM_TEMPLATES_DIR, uid);

				// 检查自定义模板文件是否存在
				try {
					await fs.access(customTemplatePath);
					// 存在则返回自定义模板
					return res.sendFile(uid, { root: CUSTOM_TEMPLATES_DIR });
				} catch (err) {
					// 自定义模板不存在，降级到默认模板
					logger.warn("自定义ISDA补充协议模板文件不存在，使用默认模板");
				}
			}

			// 使用默认模板
			const templatePath = path.join(
				PUBLIC_DIR,
				"agreements/有关于2002主协议的补充协议-中英-v2.pdf",
			);

			// 检查文件是否存在
			await fs.access(templatePath);

			// 返回文件
			res.sendFile(templatePath);
		} catch (error) {
			logger.error(error, "获取ISDA补充协议失败");
			throw AppError.create("NOT_FOUND", "协议文件不存在");
		}
	}),
);

// 待定的公共路由: GET api/public/xxx
router.get(
	"/xxx",
	wrapRoute(async (_req, res) => {
		// TODO: 待定
		res.status(200).json({});
	}),
);

export default router;
