import prisma, { platformPrisma } from "@/lib/prisma.js";
import type { Prisma } from "@prisma/client";
import { withTransaction } from "@/core/dbTxnManager.js";
import { isChannel } from "@/config/configManager.js";
import { AppError } from "@/core/appError.js";
import type { StockData } from "@packages/shared";

export async function createOrUpdate(
	data: StockData,
	client?: Prisma.TransactionClient,
): Promise<StockData> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		// Update or insert specific stock position
		const result = await tx.stocks.upsert({
			where: { ts_code: data.ts_code },
			create: {
				ts_code: data.ts_code,
				scale: data.scale,
			},
			update: {
				scale: {
					increment: data.scale,
				},
			},
		});

		// If the scale becomes 0 after updating, delete the record (except for 'all' record)
		if (result.scale === 0 && result.ts_code !== "all") {
			await tx.stocks.delete({
				where: { ts_code: data.ts_code },
			});
		}

		// Update total position (for 'all' record)
		const totalScale = await tx.stocks.aggregate({
			where: { ts_code: { not: "all" } },
			_sum: { scale: true },
		});

		await tx.stocks.update({
			where: { ts_code: "all" },
			data: { scale: totalScale._sum.scale ?? 0 },
		});

		return result;
	};
	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function getByCode(ts_code: string): Promise<StockData> {
	const prismaClient = isChannel() ? platformPrisma : prisma;
	if (!prismaClient) {
		throw AppError.create("NOT_FOUND", "Database client not available");
	}

	const result = await prismaClient.stocks.findUnique({
		where: { ts_code },
	});
	return result || { ts_code, scale: 0 };
}

export async function getTotalScale(): Promise<number> {
	const prismaClient = isChannel() ? platformPrisma : prisma;
	if (!prismaClient) {
		throw AppError.create("NOT_FOUND", "Database client not available");
	}

	const result = await prismaClient.stocks.findUnique({
		where: { ts_code: "all" },
	});
	return result?.scale ?? 0;
}
