/* 执行命令：
pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\tests\testConnection.ts
 */

import axiosWithDNSFailover from "@/api/dnsFailoverAxios.js";
import logger from "@/utils/logger.js";
import dns from "node:dns";
import axios from "axios";
import type { AxiosError, AxiosResponse } from "axios";

// 全局错误处理增强
process.on("unhandledRejection", (reason, promise) => {
	logger.error(promise, "Unhandled Rejection at");
	logger.error(reason, "reason");
});

// 确保测试环境
process.env.NODE_ENV = "development";

async function testDNSResolution() {
	// 测试原生 DNS 解析
	logger.info("===== 测试原生 DNS 解析 =====");
	const domains = ["www.baidu.com", "qt.gtimg.cn", "api.tushare.pro"];

	for (const domain of domains) {
		try {
			const result = await new Promise<string>((resolve, reject) => {
				dns.lookup(domain, (err, address) => {
					if (err) reject(err);
					else resolve(address);
				});
			});
			logger.info(`✅ 原生DNS解析 ${domain} 成功: ${result}`);
		} catch (error) {
			logger.error(error, `❌ 原生DNS解析 ${domain} 失败`);
		}
	}
}

async function testDirectRequests() {
	logger.info("===== 测试直接HTTP请求 =====");

	// 测试直接使用 axios（不使用我们的 DNS 增强版本）
	const endpoints = [
		{ url: "https://qt.gtimg.cn/q=s_sh000001", name: "腾讯行情API (直接)" },
		{
			url: "http://api.tushare.pro",
			name: "Tushare API (直接)",
			method: "post" as const,
			data: {
				api_name: "stock_basic",
				token: process.env.TUSHARE_TOKEN || "",
				params: { list_status: "L" },
				fields: "ts_code,name",
				limit: 5,
			},
		},
	];

	for (const endpoint of endpoints) {
		try {
			const startTime = Date.now();
			let response: AxiosResponse;

			if (endpoint.method === "post") {
				response = await axios.post(endpoint.url, endpoint.data, {
					timeout: 10000,
				});
			} else {
				response = await axios.get(endpoint.url, { timeout: 10000 });
			}

			const responseTime = Date.now() - startTime;
			logger.info(
				`✅ ${endpoint.name} 请求成功，响应时间: ${responseTime}ms，状态码: ${response.status}`,
			);

			// 简单显示响应数据
			if (typeof response.data === "string") {
				logger.info(`数据示例: ${response.data.substring(0, 100)}...`);
			} else {
				logger.info(
					`数据示例: ${JSON.stringify(response.data).substring(0, 100)}...`,
				);
			}
		} catch (error) {
			const axiosError = error as AxiosError;
			logger.error(
				`❌ ${endpoint.name} 请求失败: [${axiosError.code || "UNKNOWN"}] ${axiosError.message}`,
			);

			if (axiosError.response) {
				logger.error(
					axiosError.response.data,
					`状态码: ${axiosError.response.status}`,
				);
			}

			if (axiosError.request) {
				logger.error("  请求已发送但无响应");
			}
		}
	}
}

async function testWithDNSFailover() {
	logger.info("===== 测试使用 DNS 故障转移的请求 =====");

	// 1. 先测试我们是否使用了自定义DNS服务器
	logger.info("检查DNS服务器配置...");
	try {
		// 导入DNS服务器管理模块路径，以便我们可以检查它的配置
		const dnsModule = await import("@/api/dnsFailoverAxios.js");

		// 此方法会显示所有配置的DNS服务器并尝试连接
		await dnsModule.testDNSConnection();

		// 测试一下我们是否实际上使用了自定义DNS服务器
		logger.info("模拟DNS服务器故障...");
		// 模拟一个DNS服务器故障情况
		// 这将测试我们的故障转移功能是否正常工作
		const domains = ["www.baidu.com", "www.qq.com", "api.tushare.pro"];
		for (const domain of domains) {
			// 对同一域名进行多次DNS解析，看是否会切换服务器
			for (let i = 0; i < 3; i++) {
				try {
					const response = await axiosWithDNSFailover.head(`https://${domain}`);
					logger.info(
						`第${i + 1}次请求 ${domain} 成功，状态码: ${response.status}`,
					);
				} catch (error) {
					logger.warn(
						`第${i + 1}次请求 ${domain} 失败: ${(error as Error).message}`,
					);
				}
				// 等待短暂时间再次请求
				await new Promise((resolve) => setTimeout(resolve, 500));
			}
		}

		// 测试当前使用的DNS解析方式
		logger.info("===== 验证DNS解析实现方式 =====");

		// 1. 检查URL重写拦截器是否工作
		const testUrl = "https://www.example.com/test?param=1";
		const config = { url: testUrl, headers: {} };

		// 获取拦截器的引用(非公开API，仅用于测试)
		// @ts-ignore - 访问私有属性用于测试
		const interceptors = axiosWithDNSFailover.interceptors.request.handlers;
		if (interceptors && interceptors.length > 0) {
			logger.info(`检测到 ${interceptors.length} 个请求拦截器`);

			// 手动调用第一个拦截器函数
			try {
				// @ts-ignore - 访问私有API用于测试
				const result = await interceptors[0].fulfilled(config);
				const originalUrl = new URL(testUrl);
				const newUrl = new URL(result.url || testUrl);

				if (
					originalUrl.hostname !== newUrl.hostname &&
					result.headers?.Host === originalUrl.hostname
				) {
					logger.info("✅ URL重写已确认工作：请求拦截器成功将域名替换为IP地址");
					logger.info(`   原始URL: ${testUrl}`);
					logger.info(`   重写URL: ${result.url}`);
					logger.info(`   Host头: ${result.headers?.Host}`);
				} else {
					logger.warn("❌ URL重写未生效或未正确配置");
					logger.info(`   原始URL: ${testUrl}`);
					logger.info(`   处理后URL: ${result.url}`);
				}
			} catch (error) {
				logger.error(error, "测试URL重写失败");
			}
		} else {
			logger.warn("未检测到请求拦截器");
		}

		// 2. 确认HTTP和HTTPS代理是否配置了自定义DNS
		logger.info("检查HTTP代理DNS配置...");
		// 读取Axios实例的配置
		// @ts-ignore - 访问私有属性用于测试
		const httpAgent = axiosWithDNSFailover.defaults.httpAgent;
		// @ts-ignore - 访问私有属性用于测试
		const httpsAgent = axiosWithDNSFailover.defaults.httpsAgent;

		if (httpAgent && httpsAgent) {
			// @ts-ignore - 访问私有属性用于测试
			const hasCustomDNS = httpAgent.options?.lookup !== undefined;
			if (hasCustomDNS) {
				logger.info("✅ HTTP代理已配置自定义DNS查找函数");
			} else {
				logger.warn("❌ HTTP代理未配置自定义DNS查找函数");
			}

			// @ts-ignore - 访问私有属性用于测试
			const hasCustomDNSHttps = httpsAgent.options?.lookup !== undefined;
			if (hasCustomDNSHttps) {
				logger.info("✅ HTTPS代理已配置自定义DNS查找函数");
			} else {
				logger.warn("❌ HTTPS代理未配置自定义DNS查找函数");
			}
		} else {
			logger.warn("未检测到HTTP或HTTPS代理配置");
		}

		logger.info("DNS解析实现方式验证完成");
	} catch (error) {
		logger.error(error, "DNS服务器测试失败");
	}

	// 2. 测试 API 端点
	const endpoints = [
		{ url: "https://qt.gtimg.cn/q=s_sh000001", name: "腾讯行情API" },
		{
			url: "http://api.tushare.pro",
			name: "Tushare API",
			method: "post" as const,
			data: {
				api_name: "stock_basic",
				token: process.env.TUSHARE_TOKEN || "",
				params: { list_status: "L" },
				fields: "ts_code,name",
				limit: 5,
			},
		},
	];

	for (const endpoint of endpoints) {
		try {
			const startTime = Date.now();
			let response: AxiosResponse;

			if (endpoint.method === "post") {
				response = await axiosWithDNSFailover.post(
					endpoint.url,
					endpoint.data,
					{ timeout: 10000 },
				);
			} else {
				response = await axiosWithDNSFailover.get(endpoint.url, {
					timeout: 10000,
				});
			}

			const responseTime = Date.now() - startTime;
			logger.info(
				`✅ ${endpoint.name} 请求成功，响应时间: ${responseTime}ms，状态码: ${response.status}`,
			);

			// 简单显示响应数据
			if (typeof response.data === "string") {
				logger.info(`数据示例: ${response.data.substring(0, 100)}...`);
			} else {
				logger.info(
					`数据示例: ${JSON.stringify(response.data).substring(0, 100)}...`,
				);
			}
		} catch (error) {
			const axiosError = error as AxiosError;
			logger.error(
				`❌ ${endpoint.name} 请求失败: [${axiosError.code || "UNKNOWN"}] ${axiosError.message}`,
			);

			if (axiosError.response) {
				logger.error(
					axiosError.response.data,
					`状态码: ${axiosError.response.status}`,
				);
			}

			if (axiosError.request) {
				logger.error("  请求已发送但无响应");
			}
		}
	}
}

async function runTests() {
	logger.info("======= 开始API连接测试 =======");

	try {
		await testDNSResolution();
		await testDirectRequests();
		await testWithDNSFailover();
	} catch (error) {
		logger.error(error, "测试过程中发生错误");
	}

	logger.info("======= API连接测试完成 =======");
}

// 执行测试
runTests().catch((error) => {
	logger.error(error, "测试脚本执行失败");
	process.exit(1);
});
