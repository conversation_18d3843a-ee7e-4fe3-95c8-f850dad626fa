import prisma from "@/lib/prisma.js";
import { withTransaction } from "@/core/dbTxnManager.js";
import type {
	NotificationData,
	NotificationType,
	TargetType,
	NotificationMetadata,
} from "@packages/shared";
import type {
	Prisma,
	notification_type,
	notification_target,
} from "@prisma/client";

interface PrismaNotification {
	notif_id: number;
	title: string;
	content: string;
	type: notification_type;
	target_type: notification_target;
	user_id: number | null;
	metadata: Prisma.JsonValue | null;
	created_at: Date | null;
}

function transformNotification(
	n: PrismaNotification,
): Omit<NotificationData, "is_read"> {
	return {
		...n,
		user_id: n.user_id ?? undefined,
		type: n.type as NotificationType,
		target_type: n.target_type as TargetType,
		metadata: n.metadata as unknown as NotificationMetadata,
		created_at: n.created_at ?? undefined,
	};
}

export async function create(
	data: Omit<NotificationData, "id" | "is_read" | "created_at">,
	client?: Prisma.TransactionClient,
): Promise<NotificationData> {
	const createFn = async (tx: Prisma.TransactionClient) => {
		const notification = await tx.notifications.create({
			data: {
				...data,
				metadata: (data.metadata || {}) as Prisma.InputJsonValue,
			},
		});

		return {
			...transformNotification(notification),
			is_read: false,
		};
	};

	return client ? createFn(client) : withTransaction(createFn);
}

export async function markAsRead(
	notifId: number,
	userId: number,
): Promise<void> {
	await prisma.notification_reads.upsert({
		where: {
			notif_id_user_id: {
				// 使用复合唯一键
				notif_id: notifId,
				user_id: userId,
			},
		},
		create: {
			notif_id: notifId,
			user_id: userId,
		},
		update: {}, // 如果已存在，不需要更新任何字段
	});
}

// 标记所有通知为已读
export async function markAllAsRead(userId: number): Promise<void> {
	await prisma.$executeRaw`
		INSERT INTO notification_reads (notif_id, user_id)
		SELECT n.notif_id, ${userId}
		FROM notifications n
		WHERE (n.user_id = ${userId} OR n.target_type = 'system')
		AND NOT EXISTS (
			SELECT 1 FROM notification_reads nr
			WHERE nr.notif_id = n.notif_id AND nr.user_id = ${userId}
		)
	`;
}

export async function getUserNotifications(
	userId: number,
	options: { limit?: number; offset?: number } = {},
): Promise<{ notifications: NotificationData[]; total: number }> {
	const { limit = 20, offset = 0 } = options;

	const [notifications, total] = await Promise.all([
		prisma.notifications.findMany({
			where: {
				OR: [{ user_id: userId }, { target_type: "system" }],
			},
			include: {
				notification_reads: { where: { user_id: userId } },
			},
			orderBy: { created_at: "desc" },
			take: limit,
			skip: offset,
		}),
		prisma.notifications.count({
			where: {
				OR: [{ user_id: userId }, { target_type: "system" }],
			},
		}),
	]);

	return {
		notifications: notifications.map((n) => ({
			...transformNotification(n),
			is_read: n.notification_reads.length > 0,
		})),
		total,
	};
}

export async function getUnreadCount(userId: number): Promise<number> {
	return prisma.notifications.count({
		where: {
			OR: [{ user_id: userId }, { target_type: "system" }],
			NOT: {
				notification_reads: { some: { user_id: userId } },
			},
		},
	});
}
