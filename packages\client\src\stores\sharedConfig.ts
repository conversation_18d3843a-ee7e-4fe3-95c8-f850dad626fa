import { defineStore } from "pinia";
import { ref } from "vue";
import { sharedConfigApi } from "@/api";
import type { SharedConfig } from "@packages/shared";

const SESSION_STORAGE_KEY = "sharedConfigCache";

export const useSharedConfigStore = defineStore("sharedConfig", () => {
	// 正确解析 sessionStorage 中的 JSON 数据
	const cachedConfig = (() => {
		try {
			const cached = sessionStorage.getItem(SESSION_STORAGE_KEY);
			return cached ? JSON.parse(cached) : null;
		} catch (err) {
			console.error("Failed to parse cached shared config:", err);
			return null;
		}
	})();

	const config = ref<SharedConfig | null>(cachedConfig);
	const isLoading = ref(false);
	const error = ref<string | null>(null);

	// 从缓存加载配置
	const loadFromCache = () => {
		try {
			const cachedData = sessionStorage.getItem(SESSION_STORAGE_KEY);
			if (cachedData) {
				const parsedData = JSON.parse(cachedData);
				config.value = parsedData;
			}
		} catch (err) {
			console.error("Failed to load shared config from cache:", err);
		}
	};

	// 加载共享配置
	const loadSharedConfig = async () => {
		if (isLoading.value) return;

		isLoading.value = true;
		error.value = null;

		// 先尝试从缓存加载
		loadFromCache();

		try {
			const data = await sharedConfigApi.getSharedConfig();

			if (data) {
				config.value = data;

				// 更新缓存
				sessionStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(data));
			}

			return config.value;
		} catch (err) {
			console.error("Failed to load shared configuration:", err);
			error.value = "Failed to load shared configuration";
			return null;
		} finally {
			isLoading.value = false;
		}
	};

	return {
		config,
		isLoading,
		error,
		loadSharedConfig,
	};
});
