import { ref, computed } from "vue";
import { request } from "@/api/request";
import { positionPaths, inquiryPaths, tradePaths } from "@packages/shared";
import type { HealthCheckResponse } from "@packages/shared";
import { messageThrottle } from "@/utils/messageThrottle";

export enum OperationType {
	READ = "read",
	TRADE = "trade",
	POSITION = "position",
	AUTH = "auth",
}

export class SystemStatusManager {
	private _isSystemEnabled = ref(true);
	private _isPositionEntryEnabled = ref(true);
	private _isInquiryEnabled = ref(true);
	private _hasCheckedStatus = ref(false);
	private lastStatusCheck = 0;
	private readonly STATUS_CHECK_INTERVAL = 60000;
	private checkingPromise: Promise<void> | null = null;

	private constructor() {} // Prevent new instances
	private static instance: SystemStatusManager;

	static getInstance(): SystemStatusManager {
		if (!SystemStatusManager.instance) {
			SystemStatusManager.instance = new SystemStatusManager();
		}
		return SystemStatusManager.instance;
	}

	// Convert static computed to instance getters
	get isSystemEnabled() {
		return computed(() => this._isSystemEnabled.value);
	}
	get isPositionEntryEnabled() {
		return computed(() => this._isPositionEntryEnabled.value);
	}
	get isInquiryEnabled() {
		return computed(() => this._isInquiryEnabled.value);
	}
	get hasCheckedStatus() {
		return computed(() => this._hasCheckedStatus.value);
	}

	// Convert other static methods to instance methods
	async initialize() {
		await this.checkSystemStatus();
	}

	// 检查系统状态
	async checkSystemStatus() {
		// 如果已经有检查在进行中，直接返回那个 Promise
		if (this.checkingPromise) {
			return this.checkingPromise;
		}

		this.checkingPromise = (async () => {
			try {
				const data = (await request.get("/health")) as HealthCheckResponse;
				this.updateStatus(
					data.system_enabled,
					data.position_entry_enabled,
					data.inquiry_enabled,
				);
				this.lastStatusCheck = Date.now();

				if (!data.system_enabled) {
					messageThrottle.show("非交易时间，系统当前仅支持查看功能", {
						type: "warning",
					});
				} else if (!data.position_entry_enabled) {
					messageThrottle.show("当前不允许新增持仓，请稍后再试", {
						type: "warning",
					});
				}

				if (!data.inquiry_enabled)
					messageThrottle.show("当前不允许询价，请稍后再试", {
						type: "warning",
					});
			} catch (error) {
				console.error("Failed to check system status:", error);
				this.updateStatus(false, false, false);
				messageThrottle.show("系统状态检查失败，已切换至只读模式", {
					type: "error",
				});
			} finally {
				this.checkingPromise = null;
			}
		})();

		return this.checkingPromise;
	}

	// 更新系统状态
	updateStatus(
		systemEnabled: boolean,
		positionEntryEnabled: boolean,
		inquiryEnabled: boolean,
	) {
		this._isSystemEnabled.value = systemEnabled;
		this._isPositionEntryEnabled.value = positionEntryEnabled;
		this._isInquiryEnabled.value = inquiryEnabled;
		this._hasCheckedStatus.value = true;
	}

	// 检查并更新系统状态
	async updateSystemStatusIfExpired() {
		const shouldCheckStatus =
			Date.now() - this.lastStatusCheck >= this.STATUS_CHECK_INTERVAL;
		if (shouldCheckStatus) {
			await this.checkSystemStatus();
		}
	}

	/**
	 * 检查操作权限，已包含消息提示
	 */
	checkOperationPermission(url: string): boolean {
		if (!this._hasCheckedStatus.value) {
			return false;
		}

		if (tradePaths.some((path) => url.includes(path))) {
			if (!this._isSystemEnabled.value) {
				messageThrottle.show("非交易时间，系统当前仅支持查询功能", {
					type: "warning",
				});
				return false;
			}

			if (
				positionPaths.some((path) => url.includes(path)) &&
				!this._isPositionEntryEnabled.value
			) {
				messageThrottle.show("当前不允许新增持仓", {
					type: "warning",
				});
				return false;
			}

			return true;
		}

		if (inquiryPaths.some((path) => url.includes(path))) {
			if (!this._isInquiryEnabled.value) {
				messageThrottle.show("当前不允许询价", {
					type: "warning",
				});
				return false;
			}
			return true;
		}

		return true;
	}
}

export const systemStatusManager = SystemStatusManager.getInstance();
