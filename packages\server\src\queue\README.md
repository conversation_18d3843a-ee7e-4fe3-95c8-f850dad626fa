# BullMQ Queue System

This directory contains the implementation of a distributed queue system using BullMQ to handle background jobs and scheduled tasks across multiple processes in a PM2 cluster environment.

## Overview

We've migrated from using node-cron with a primary process ID approach to BullMQ, which provides several advantages:

1. **Native multi-process support** - No need to designate a primary process ID
2. **Reliable job processing** - Jobs are stored in Redis and processed exactly once
3. **Delayed and repeated jobs** - Advanced scheduling capabilities
4. **Job prioritization** - Control which jobs run first
5. **Job retries** - Automatically retry failed jobs
6. **Graceful shutdown** - Proper cleanup on process exit

## Architecture

The queue system consists of:

- **Queues**: Dedicated queues for different types of tasks:
  - `stock-data`: Stock data synchronization tasks
  - `ink-data-sync`: INK API data synchronization
  - `vwap-orders`: VWAP order processing
  - `limit-orders`: Limit order processing
  - `system-tasks`: System maintenance tasks

- **Workers**: Process jobs from specific queues
  - Each worker runs in every PM2 process
  - BullMQ ensures each job is only executed by one worker

- **Schedulers**: Handle delayed jobs and job retries
  - One scheduler per queue

## How It Works

1. **Initialization**:
   - On startup, each process creates its workers and schedulers
   - A system task adds all recurring jobs to the queues
   - Workers start processing jobs

2. **Strategies**:
   - VWAP and limit order strategies now use BullMQ instead of in-memory timers
   - Orders are persisted in Redis and can survive process restarts

3. **Cron Jobs**:
   - Replace node-cron with BullMQ's repeated jobs feature
   - Jobs are added with a cron-like pattern string

## Development

### Adding a New Queue

```typescript
// 1. Add to QUEUES constant in index.ts
export const QUEUES = {
  // ... existing queues
  NEW_QUEUE: 'new-queue',
};

// 2. Create the queue and scheduler
export const newQueue = new Queue(QUEUES.NEW_QUEUE, { connection });
export const newScheduler = new QueueScheduler(QUEUES.NEW_QUEUE, { connection });

// 3. Update closeQueues() function
export async function closeQueues() {
  await Promise.all([
    // ... existing queues
    newQueue.close(),
  ]);
  // ...
}
```

### Creating a Worker

```typescript
import { createWorker, myQueue } from './index.js';

async function processJob(job: Job) {
  const { name, data } = job;
  
  // Process different job types
  switch (name) {
    case 'job-type-1':
      await processJobType1(data);
      break;
    // ...
  }
}

// Create and export the worker
export const myWorker = createWorker(myQueue, processJob);
```

### Adding Jobs

```typescript
// One-time job
await myQueue.add('job-name', { key: 'value' });

// Delayed job
await myQueue.add('job-name', { key: 'value' }, { delay: 5000 });

// Repeated job (cron pattern)
await addRepeatedJob(myQueue, 'job-name', { key: 'value' }, '0 * * * *');
```

## Best Practices

1. **Job Data**: Keep job data small and serializable
2. **Error Handling**: Always handle errors in job processors
3. **Idempotency**: Make job processors idempotent when possible
4. **Monitoring**: Use BullMQ's events for monitoring job status
5. **Cleanup**: Clean up completed and failed jobs to avoid Redis memory growth

## Further Resources

- [BullMQ Documentation](https://docs.bullmq.io/)
- [Bull Dashboard](https://github.com/felixmosh/bull-board) - UI for monitoring queues 