-- 通知表字段顺序调整迁移脚本
-- 该脚本将 notifications 表中的 target_type 字段移动到 type 字段之后，
-- 并调整相关的外键约束、序列和索引结构。
-- 主要目的是优化表结构和约束，使表格更加符合业务逻辑。

-- 注意：运行此脚本前，请确保完成数据库备份以防意外。

-- ! 创建新表会导致序列重置

BEGIN;

-- 步骤 1：创建带有所需字段顺序的新表
CREATE TABLE notifications_new (
    notif_id SERIAL PRIMARY KEY,
    title VARCHAR(64) NOT NULL,
    content TEXT NOT NULL,
    type notification_type NOT NULL,
    target_type notification_target NOT NULL,  -- 移动到 type 字段之后
    user_id INTEGER REFERENCES users(user_id),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    CONSTRAINT chk_user_id CHECK (
        (target_type = 'system' AND user_id IS NULL) OR
        (target_type = 'personal' AND user_id IS NOT NULL)
    )
);

-- 步骤 2：复制数据到新表
INSERT INTO notifications_new (
    notif_id, title, content, type, target_type, user_id, metadata, created_at
)
SELECT 
    notif_id, title, content, type, target_type, user_id, metadata, created_at
FROM notifications;

-- 步骤 2.1：校准新表的序列值
-- 在复制数据后，将新创建的序列 (notifications_new_notif_id_seq) 的值设置为新表中当前最大的 notif_id
-- 以确保下一个生成的 ID 是正确的，避免主键冲突。
SELECT setval(pg_get_serial_sequence('notifications_new', 'notif_id'), COALESCE((SELECT MAX(notif_id) FROM notifications_new), 1));

-- 步骤 3：交换表名（原子操作）
-- 将多个操作合并为一个事务，以在 postgres console 中正确执行
DO $$
BEGIN
  ALTER TABLE notifications RENAME TO notifications_old;
  ALTER TABLE notifications_new RENAME TO notifications;
END $$;

-- 步骤 4: 处理依赖和约束

-- （1）外键约束

-- 首先，删除原来的外键约束
ALTER TABLE notification_reads 
DROP CONSTRAINT notification_reads_notif_id_fkey;

ALTER TABLE notifications
DROP CONSTRAINT notifications_new_user_id_fkey;

-- 添加新的外键约束到新表
ALTER TABLE notification_reads 
ADD CONSTRAINT notification_reads_notif_id_fkey 
FOREIGN KEY (notif_id) REFERENCES notifications(notif_id) 
ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE notifications
ADD CONSTRAINT notifications_user_id_fkey
FOREIGN KEY (user_id) REFERENCES users(user_id)
ON DELETE NO ACTION ON UPDATE NO ACTION;

-- （2）主键约束
ALTER TABLE notifications_old
RENAME CONSTRAINT notifications_pkey TO notifications_old_pkey;
ALTER TABLE notifications 
RENAME CONSTRAINT notifications_new_pkey TO notifications_pkey;

-- （3）序列
ALTER SEQUENCE notifications_notif_id_seq 
RENAME TO notifications_old_notif_id_seq;
ALTER SEQUENCE notifications_new_notif_id_seq 
RENAME TO notifications_notif_id_seq;

-- （4）更新列的默认值以使用新序列名
ALTER TABLE notifications 
ALTER COLUMN notif_id 
SET DEFAULT nextval('notifications_notif_id_seq'::regclass);

-- （5）索引

-- 重命名旧表的索引（如果存在且需要保留）
ALTER INDEX idx_notifications_personal RENAME TO idx_notifications_old_personal;
ALTER INDEX idx_notifications_system RENAME TO idx_notifications_old_system;

-- 为新表创建新索引
CREATE INDEX idx_notifications_personal ON notifications(user_id, created_at) WHERE target_type = 'personal';
CREATE INDEX idx_notifications_system ON notifications(created_at) WHERE target_type = 'system';

-- 步骤 5: 确认无误后删除旧表（会自动删除旧索引）
DROP TABLE notifications_old;

-- 添加注释记录模式变更
COMMENT ON TABLE notifications IS '已调整字段顺序，移动 target_type 字段到 type 字段之后';

-- 提交事务
COMMIT;

-- 注意：运行此脚本后，需要使用 npx prisma db pull 更新 Prisma 模型，
-- 并确保在更新后检查或删除任何多余的 map 属性。
