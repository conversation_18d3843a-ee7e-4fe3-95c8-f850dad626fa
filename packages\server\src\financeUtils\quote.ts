import * as ConfigService from "@/services/admin/index.js";
import { StructureValue, QUOTE_REJECTED } from "@packages/shared";
import type { StructureType, BusinessConfig } from "@packages/shared";
import logger from "@/utils/logger.js";
import { getEffectiveSwingWithCache } from "./marketData.js";

// 结构映射保持不变
const STRUCTURE_MAP = Object.keys(StructureValue).reduce<
	Record<StructureType, number>
>(
	(acc, key) => {
		acc[key as StructureType] = Number.parseInt(key);
		return acc;
	},
	{} as Record<StructureType, number>,
);

/**
 * Calculate the final quote
 * @param effectiveVol Effective volatility
 * @param structure Structure type (e.g., "100C", "103C", "97P")
 * @param term Term (14 for 2 weeks, 1, 2, or 3 for months)
 * @returns Final quote
 */
export async function calculateFinalQuote(
	ts_code: string,
	structure: StructureType,
	term: number,
): Promise<number | null> {
	// 获取配置
	const envVars = await ConfigService.getConfig();
	logger.info(`envVars: ${JSON.stringify(envVars)}`);

	// 0. 获取有效振幅
	const effectiveVol = await getEffectiveSwingWithCache(ts_code);
	logger.info(`effectiveVol: ${effectiveVol}`);

	if (!effectiveVol) {
		return null;
	}

	// 1. 计算基础的100% call报价
	let quote = effectiveVol * envVars.OPTION_MULTIPLIER;
	logger.info(`quote: ${quote}`);

	// 2. 根据期限调整
	if (term === 14) {
		// 使用 14 代表两周期限，并使用特定乘数
		quote *= envVars.TWO_WEEKS_MULTIPLIER;
		logger.info(`quote after two weeks multiplier: ${quote}`);
	} else if (term === 2) {
		quote *= envVars.MONTH_MULTIPLIER;
		logger.info(`quote after month multiplier: ${quote}`);
	} else if (term === 3) {
		quote *= envVars.MONTH_MULTIPLIER * envVars.MONTH_MULTIPLIER;
		logger.info(`quote after month multiplier: ${quote}`);
	} else if (term !== 1) {
		throw new Error(`Invalid term: ${term}`);
	}

	// 3. 检查结构是否有效，并根据结构调整报价
	const exercise = STRUCTURE_MAP[structure];
	logger.info(`Exercise: ${exercise}`);
	if (!exercise) {
		throw new Error(`Invalid structure: ${structure}`);
	}
	if (exercise !== 100) {
		quote =
			((effectiveVol - Math.abs(100 - exercise) / 5) / effectiveVol) * quote;
		logger.info(`quote after structure: ${quote}`);
	}

	// 4. 检查对应结构的最低价格限制
	let minQuote = 0;
	const minQuoteKey =
		`MIN_QUOTE_STRUCTURE_${Math.abs(exercise - 100) + 100}` as keyof BusinessConfig;
	minQuote =
		(envVars[minQuoteKey] as number) *
		(term === 14 ? envVars.TWO_WEEKS_MULTIPLIER : 1); // 两周最低限价乘0.8

	logger.info(`minQuote: ${minQuote}`);

	if (quote < minQuote) {
		logger.info(`quote ${quote} is below minimum ${minQuote}, using minimum`);
		quote = minQuote;
	}

	// 5. 判断是否为put，并应用put乘数
	const isPut = structure.endsWith("P");
	if (isPut) {
		quote *= envVars.PUT_MULTIPLIER;
		logger.info(`quote after put multiplier: ${quote}`);
	}

	return Number(quote.toFixed(2));
}

/**
 * 计算日均成交额
 * @param data 86天的数据 { amount }[] amount 单位：万元
 */
export function calculateDailyTurnover(data: { amount: number }[]): number {
	const dailyTurnover =
		data.slice(0, 86).reduce((acc, item) => acc + item.amount, 0) / 86;
	return dailyTurnover;
}

/**
 * Validates parameters for calculateQuoteMarkupOffset.
 * @param notional 名义本金 (万元)
 * @param baseQuote 原始报价 (%)
 * @param markup 渠道加价 (元)
 * @throws Error if parameters are invalid
 */
function validateQuoteMarkupOffsetParams(
	notional: number,
	baseQuote: number,
	markup: number,
): void {
	if (notional <= 0) {
		throw new Error("Notional must be a positive number");
	}
	if (baseQuote < 0 || markup < 0) {
		throw new Error("Base quote and markup must be non-negative numbers");
	}
}

/**
 * 计算渠道加价后的报价差值 (Offset)。
 * 此函数将加价金额 (markup) 转换为相对于名义本金 (notional) 的百分比形式。
 * @param notional 名义本金 (万元)
 * @param baseQuote 原始报价 (%) - NOTE: This parameter is not used in the calculation, but is kept for conceptual clarity if needed for validation or context.
 * @param markup 渠道加价 (元)
 * @returns 报价差值 (%) - 这是一个百分比值，表示加价金额相对于名义本金的比例。
 */
export function calculateQuoteMarkupOffset(
	notional: number,
	baseQuote: number, // Kept as parameter, though not used in calculation after validation extraction
	markup: number,
): number {
	// Validate parameters before calculation
	validateQuoteMarkupOffsetParams(notional, baseQuote, markup); // Call the new validation function

	// 计算加价后的报价差值 (将markup金额转换为百分比)
	// markup (元) / (notional (万元) * 10000 元/万元) * 100 (%) = offset (%)
	const offset = (markup / (notional * 10000)) * 100;

	return Number(offset.toFixed(2));
}

/**
 * 比较内部报价与外部最优报价，确保内部报价的最低价为外部报价的99%
 * @param calculatedQuote 内部计算的报价 (可能为 QUOTE_REJECTED)
 * @param allExternalQuotes 所有外部报价（包含所有报价提供商，无论是否启用）
 * @returns 调整后的报价 (可能为 QUOTE_REJECTED)
 */
export async function adjustQuoteBasedOnExternal(
	calculatedQuote: number | null,
	allExternalQuotes: Record<string, number | null>,
): Promise<number | null> {
	// 如果内部报价为 null 或 QUOTE_REJECTED，或者没有外部报价，直接返回内部报价
	if (
		calculatedQuote === null ||
		calculatedQuote === QUOTE_REJECTED ||
		Object.keys(allExternalQuotes).length === 0
	) {
		return calculatedQuote;
	}

	// 获取折扣参数
	const config = await ConfigService.getConfig();
	const discountMultiplier = config.DISCOUNT_MULTIPLIER;

	// 先对内部报价应用比低价折扣
	const discountedQuote = calculatedQuote * discountMultiplier;

	// 获取所有有效的外部报价（包括未启用的提供商）用于价格下限计算
	const validAllExternalQuotes = Object.values(allExternalQuotes).filter(
		(quote): quote is number =>
			quote !== null && !Number.isNaN(quote) && quote > 0,
	);

	// 如果没有有效的外部报价，直接返回原始的内部报价
	if (validAllExternalQuotes.length === 0) {
		return calculatedQuote;
	}

	// 获取外部最优报价（最低）
	const bestExternalQuote = Math.min(...validAllExternalQuotes);

	// 计算最优外部报价的99%
	const externalQuoteThreshold = bestExternalQuote * 0.99;

	// 如果折扣后的内部报价低于最优外部报价的99%，则返回最优外部报价的99%
	if (discountedQuote < externalQuoteThreshold) {
		logger.info(
			`Adjusting quote: discountedQuote ${discountedQuote} is below externalQuoteThreshold ${externalQuoteThreshold}, using externalQuoteThreshold`,
		);
		return Number(externalQuoteThreshold.toFixed(2));
	}

	// 否则返回原始的内部报价
	return calculatedQuote;
}
