import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { referenceApi } from "@/api";
import type { StockInfo } from "@packages/shared";

const REFRESH_INTERVAL = 8 * 60 * 60 * 1000; // 8 hours in ms

interface StockCache {
	data: Omit<StockInfo, "market">[];
	timestamp: number;
}

const CACHE_KEY = "stock_list_cache";
const CACHE_DURATION = 8 * 60 * 60 * 1000; // 8 hours in ms

function loadFromCache(): StockCache | null {
	const cached = localStorage.getItem(CACHE_KEY);
	if (cached) {
		const { data, timestamp } = JSON.parse(cached);
		if (Date.now() - timestamp < CACHE_DURATION) {
			return { data, timestamp };
		}
	}
	return null;
}

function saveToCache(data: Omit<StockInfo, "market">[]) {
	const cache: StockCache = {
		data,
		timestamp: Date.now(),
	};
	localStorage.setItem(CACHE_KEY, JSON.stringify(cache));
}

export const useStockStore = defineStore("stock", () => {
	const stockCache = ref(new Map<string, string>());
	const isInitialized = ref(false);
	let refreshInterval: number | undefined;

	const getStockName = (tsCode: string) => {
		return stockCache.value.get(tsCode) ?? tsCode;
	};

	// 根据股票代码查找完整 ts_code
	const findCompleteCode = (partialCode: string): string | undefined => {
		// 支持数字查找，自动补全后缀
		if (/^\d{6}$/.test(partialCode)) {
			const candidates = [];

			// 遍历所有可能的后缀
			for (const [fullCode] of stockCache.value.entries()) {
				if (fullCode.startsWith(partialCode)) {
					candidates.push(fullCode);
				}
			}

			// 如果只有一个匹配项，直接返回完整代码
			if (candidates.length === 1) {
				return candidates[0];
			}
		}

		// 如果已经是完整代码，直接返回
		if (stockCache.value.has(partialCode)) {
			return partialCode;
		}

		return undefined;
	};

	const formatSubject = (tsCode: string) => {
		const stockName = getStockName(tsCode);
		return stockName ? `${stockName} (${tsCode})` : tsCode;
	};

	const getAllFormattedSubjects = () => {
		return Array.from(stockCache.value.entries()).map(([tsCode, name]) => ({
			value: tsCode,
			label: `${name} (${tsCode})`,
		}));
	};

	const initialize = async () => {
		if (!isInitialized.value) {
			// 先检查本地缓存
			const cached = loadFromCache();
			if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
				// 使用缓存数据
				stockCache.value = new Map(
					cached.data.map((stock) => [stock.ts_code, stock.name]),
				);
				isInitialized.value = true;
				return;
			}

			// 缓存过期或不存在时才请求
			const response = await referenceApi.getAllStocks();

			if (!response) {
				console.error("[StockStore] No response data");
				return false;
			}

			// 解析紧凑格式
			const stockList = response.split(";").map((item) => {
				const [ts_code, name] = item.split("~");
				return { ts_code, name };
			});

			stockCache.value = new Map(
				stockList.map((stock) => [stock.ts_code, stock.name]),
			);
			saveToCache(stockList);

			// 启动定时刷新（8小时）
			startAutoRefresh();
			isInitialized.value = true;
		}
	};

	// 启动自动刷新
	const startAutoRefresh = () => {
		stopAutoRefresh(); // 确保之前的定时器被清理
		refreshInterval = window.setInterval(initialize, REFRESH_INTERVAL);
	};

	// 停止自动刷新
	const stopAutoRefresh = () => {
		if (refreshInterval) {
			clearInterval(refreshInterval);
			refreshInterval = undefined;
		}
	};

	return {
		getStockName,
		formatSubject,
		getAllFormattedSubjects,
		findCompleteCode,
		initialize,
		stopAutoRefresh,
		isInitialized: computed(() => isInitialized.value),
	};
});
