
```nginx
# /etc/nginx/sites-available/ink.hk.cn

# HTTP 重定向
server {
    listen 80;
    server_name ink.hk.cn www.ink.hk.cn admin.ink.hk.cn;
    return 301 https://$host$request_uri;
}

# 主域名重定向到 www (正式站)
server {
    listen 443 ssl;
    server_name ink.hk.cn;

    ssl_certificate /etc/letsencrypt/live/ink.hk.cn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ink.hk.cn/privkey.pem;

    return 301 https://www.ink.hk.cn$request_uri;
}

# 用户端配置
server {
    listen 443 ssl;
    server_name www.ink.hk.cn;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/ink.hk.cn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ink.hk.cn/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header Strict-Transport-Security "max-age=********" always;
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    # Frontend static files
    root /var/www/ink-dev/packages/client/dist;
    index index.html;

    # Handle frontend routing (SPA)
    location / {
        try_files $uri $uri/ /index.html;  # 支持前端路由
        expires 1h;  # 静态资源缓存
        add_header Cache-Control "public, no-transform";
    }

    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 7d;
        add_header Cache-Control "public, no-transform";
    }

    # API requests
    location /api/ {
        client_max_body_size 50m;
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Websocket support (if needed)
    location /ws {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Deny access to . files
    location ~ /\. {
        deny all;
    }
}

# 管理端配置
server {
    listen 443 ssl;
    server_name admin.ink.hk.cn;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/ink.hk.cn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ink.hk.cn/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header Strict-Transport-Security "max-age=********" always;
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    # 管理端配置 server 块内

    # 1. 首先匹配 API 文件下载请求
    location ~* ^/api/admin/file/download/ {
        client_max_body_size 50m;
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 2. 其他 API 请求
    location /api/ {
        client_max_body_size 50m;
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 3. 静态资源（确保在 API 规则之后）
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root /var/www/ink-dev/packages/admin/dist;
        expires 7d;
        add_header Cache-Control "public, no-transform";
        try_files $uri =404;
    }

    # 4. 前端路由
    location / {
        root /var/www/ink-dev/packages/admin/dist;
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public, no-transform";
    }

    # Websocket support (if needed)
    location /ws {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Deny access to . files
    location ~ /\. {
        deny all;
    }
}
```