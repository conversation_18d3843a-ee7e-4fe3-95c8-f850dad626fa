import { ENV } from "@/config/configManager.js";
import type { Request, Response, NextFunction } from "express";
import crypto from "node:crypto";

export function generateCSRFToken(
	_req: Request,
	res: Response,
	next: NextFunction,
): void {
	const token: string = crypto.randomBytes(32).toString("hex");
	res.cookie("XSRF-TOKEN", token, {
		httpOnly: false,
		sameSite: "strict",
		secure: ENV.NODE_ENV !== "development",
	});
	res.locals.csrfToken = token;
	next();
}

export function csrfProtection(
	req: Request,
	res: Response,
	next: NextFunction,
): void {
	const token: string | undefined =
		(req.headers["x-xsrf-token"] as string) || req.body._csrf;
	const cookieToken: string | undefined = req.cookies["XSRF-TOKEN"];

	if (!token || !cookieToken || token !== cookieToken) {
		res.status(403).json({ error: "CSRF token validation failed" });
		return;
	}
	next();
}
