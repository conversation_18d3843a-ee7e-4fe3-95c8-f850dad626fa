import { createRouter, createWebHistory } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { AdminPermission, AppType } from "@packages/shared";
import { ElMessage } from "element-plus";

// 创建基础路由配置
const createRoutes = () => {
	return [
		{
			path: "/auth",
			name: "Auth",
			component: () => import("@/views/AuthPage.vue"),
		},
		{
			path: "/",
			component: () => import("@/layouts/AdminLayout.vue"),
			meta: { requiresAuth: true },
			children: [
				{
					path: "",
					name: "root",
					redirect: { name: "dashboard" },
				},
				{
					path: "dashboard",
					name: "dashboard",
					component: () => import("@/views/DashboardView.vue"),
					meta: { permission: AdminPermission.BASIC },
				},
				// 用户管理 - 使用子视图模式（多标签页）
				{
					path: "users",
					name: "users",
					component: () => import("@/views/UsersView.vue"),
					meta: { permission: AdminPermission.BASIC },
					redirect: { name: "users-list" },
					children: [
						{
							path: "list",
							name: "users-list",
							component: () => import("@/views/UsersView.vue"),
							meta: { permission: AdminPermission.BASIC, subView: "list" },
						},
						{
							path: "leads",
							name: "users-leads",
							component: () => import("@/views/UsersView.vue"),
							meta: {
								permission: AdminPermission.BASIC,
								subView: "leads",
								appType: AppType.CHANNEL,
							},
						},
					],
				},
				{
					path: "inquiries",
					name: "inquiries",
					component: () => import("@/views/InquiriesView.vue"),
					meta: { permission: AdminPermission.BASIC },
				},
				{
					path: "orders",
					name: "orders",
					component: () => import("@/views/OrdersView.vue"),
					meta: { permission: AdminPermission.BASIC },
				},
				{
					path: "transactions",
					name: "transactions",
					component: () => import("@/views/TransactionsView.vue"),
					meta: { permission: AdminPermission.FINANCE },
				},
				{
					path: "deposit",
					name: "deposit",
					component: () => import("@/views/DepositView.vue"),
					meta: { permission: AdminPermission.FINANCE },
				},
				{
					path: "account-security",
					name: "account-security",
					component: () => import("@/views/AccountSecurityView.vue"),
					meta: { permission: AdminPermission.FINANCE },
				},
				// 添加通道管理路由，位于通道资金审核前面
				{
					path: "channel-management",
					name: "channel-management",
					component: () => import("@/views/ChannelManagementView.vue"),
					meta: {
						permission: AdminPermission.FINANCE,
						appType: AppType.TRADING_PLATFORM,
					},
				},
				// 交易台特有路由
				{
					path: "channel-fund",
					name: "channel-fund",
					component: () => import("@/views/ChannelFundView.vue"),
					meta: {
						permission: AdminPermission.FINANCE,
						appType: AppType.TRADING_PLATFORM,
					},
				},
				{
					path: "channel-orders",
					name: "channel-orders",
					component: () => import("@/views/ChannelOrdersView.vue"),
					meta: {
						permission: AdminPermission.FINANCE,
						appType: AppType.TRADING_PLATFORM,
					},
				},
				// 通道特有路由
				{
					path: "platform-fund",
					name: "platform-fund",
					component: () => import("@/views/PlatformFundRequestView.vue"),
					meta: { permission: AdminPermission.BASIC, appType: AppType.CHANNEL },
				},
				{
					path: "platform-config",
					name: "platform-config",
					component: () => import("@/views/PlatformConfigView.vue"),
					meta: { permission: AdminPermission.CONFIG },
				},
				{
					path: "system-status",
					name: "system-status",
					component: () => import("@/views/SystemStatusView.vue"),
					meta: { permission: AdminPermission.CONFIG },
				},
				{
					path: "permissions",
					name: "permissions",
					component: () => import("@/views/PermissionsView.vue"),
					meta: { permission: AdminPermission.ADMIN },
				},
				// 审核管理 - 使用子视图模式（多标签页）
				{
					path: "audits",
					name: "audits",
					component: () => import("@/views/AuditView.vue"),
					meta: {
						permission: [AdminPermission.FINANCE, AdminPermission.QUALIFY],
					},
					redirect: { name: "audits-qualify" },
					children: [
						{
							path: "qualify",
							name: "audits-qualify",
							component: () => import("@/views/AuditView.vue"),
							meta: {
								permission: [AdminPermission.FINANCE, AdminPermission.QUALIFY],
								subView: "qualify",
							},
						},
						{
							path: "finance",
							name: "audits-finance",
							component: () => import("@/views/AuditView.vue"),
							meta: {
								permission: [AdminPermission.FINANCE, AdminPermission.QUALIFY],
								subView: "finance",
							},
						},
					],
				},
				{
					path: "/bank-account",
					name: "BankAccount",
					component: () => import("@/views/BankAccountView.vue"),
					meta: {
						requiresAuth: true,
						title: "银行账户管理",
					},
				},
				{
					path: "/site-config",
					name: "SiteConfig",
					component: () => import("@/views/SiteConfigView.vue"),
					meta: {
						requiresAuth: true,
						permission: AdminPermission.CONFIG,
						title: "网站配置管理",
					},
				},
				{
					path: "agreements",
					name: "agreements",
					component: () => import("@/views/AgreementManagementView.vue"),
					meta: {
						requiresAuth: true,
						permission: AdminPermission.QUALIFY,
						title: "协议管理",
					},
				},
			],
		},
	];
};

const router = createRouter({
	history: createWebHistory(),
	routes: [], // 初始化为空数组
});

// 在应用初始化后再添加路由
let routesAdded = false;

const initializeRouter = () => {
	if (routesAdded) return;

	const routes = createRoutes();
	for (const route of routes) {
		router.addRoute(route);
	}
	routesAdded = true;
};

router.beforeEach(async (to, from, next) => {
	// 确保路由已初始化
	if (!routesAdded) {
		initializeRouter();
	}

	const authStore = useAuthStore();

	// 如果未登录且不是访问登录页，重定向到登录页
	if (!authStore.isLoggedIn && to.path !== "/auth") {
		next("/auth");
		return;
	}

	// 如果已登录且访问登录页，重定向到首页
	if (authStore.isLoggedIn && to.path === "/auth") {
		next("/");
		return;
	}

	// 检查 appType
	const requiredAppType = to.meta.appType;
	const currentAppType = authStore.app_type;
	if (requiredAppType && requiredAppType !== currentAppType) {
		ElMessage.warning("当前站点类型无权访问此页面");
		next(from.path);
		return;
	}

	// 检查权限
	const requiredPermission = to.meta.permission as AdminPermission | undefined;
	if (
		requiredPermission &&
		!hasPermission(authStore.permissions, requiredPermission)
	) {
		ElMessage.warning("您没有权限访问此页面");
		next(from.path);
		return;
	}

	next();
});

// 检查权限 - 有其中一个权限即可
function hasPermission(
	permissions: AdminPermission[],
	permission: AdminPermission | AdminPermission[],
): boolean {
	if (!permissions.length) return false;
	if (permission === AdminPermission.BASIC) return true;
	if (Array.isArray(permission)) {
		return permission.some((p) => permissions.includes(p));
	}
	return permissions.includes(permission);
}

export { initializeRouter };
export default router;
