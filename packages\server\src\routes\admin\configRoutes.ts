import { Router } from "express";
import * as configService from "@/services/admin/configService.js";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import { requirePermission } from "@/middlewares/adminPermission.js";
import { AppError } from "@/core/appError.js";
import * as userService from "@/services/userService.js";
import { StatusChange, AdminPermission } from "@packages/shared";
import type { SystemStatus, PlatformConfig, UserInfo } from "@packages/shared";

const router = Router();

// Get system status: GET /api/admin/config/system-status
router.get(
	"/system-status",
	wrapAdminRoute(async (_, res) => {
		const status = await configService.getSystemStatus();
		res.status(200).json(status);
	}),
);

// Update system status: POST /api/admin/config/system-status
router.post(
	"/system-status",
	wrapAdminRoute<Partial<SystemStatus>>(async (req, res) => {
		const updates = {
			...req.body,
			change_type: StatusChange.MANUAL,
		};

		const message = await configService.updateSystemStatus(
			updates,
			req.jwt.admin_id,
		);
		res.status(200).json(message);
	}),
);

// Get system status history: GET /api/admin/config/system-status-history
router.get(
	"/system-status-history",
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder = req.query.sortOrder as "ASC" | "DESC";

		const history = await configService.getSystemStatusHistory(page, pageSize, {
			sortBy,
			sortOrder,
		});
		res.status(200).json(history);
	}),
);

// Get platform config: GET /api/admin/config/platform
router.get(
	"/platform",
	wrapAdminRoute(async (_, res) => {
		const config = await configService.getPlatformConfig();
		res.status(200).json(config);
	}),
);

// Update platform config: POST /api/admin/config/platform
router.post(
	"/platform",
	wrapAdminRoute<Partial<PlatformConfig>>(async (req, res) => {
		const message = await configService.updatePlatformConfig(
			req.body,
			req.jwt.admin_id,
		);
		res.status(200).json(message);
	}),
);

// Get platform config history: GET /api/admin/config/platform-history
router.get(
	"/platform-history",
	wrapAdminRoute(async (_, res) => {
		const history = await configService.getPlatformConfigHistory();
		res.status(200).json(history);
	}),
);

// Update user trade parameters: POST /api/admin/config/user-trade-params/:userId
router.post(
	"/user-trade-params/:userId",
	requirePermission(AdminPermission.CONFIG),
	wrapAdminRoute<
		Pick<UserInfo, "custom_profit_sharing_percentage" | "custom_quote_diffs">
	>(async (req, res) => {
		const admin_id = req.jwt.admin_id;
		const targetUserId = Number.parseInt(req.params.userId);

		if (Number.isNaN(targetUserId)) {
			throw AppError.create(
				"BAD_REQUEST",
				"Invalid user ID in path parameter.",
			);
		}

		// req.body 应包含 custom_profit_sharing_percentage 和/或 custom_quote_diffs
		const tradeParamsUpdates = req.body;

		const updatedUser = await userService.updateUserTradeParams(
			targetUserId,
			tradeParamsUpdates,
			admin_id,
		);
		res
			.status(200)
			.json({
				message: "User trade parameters updated successfully.",
				user: updatedUser,
			});
	}),
);

export default router;
