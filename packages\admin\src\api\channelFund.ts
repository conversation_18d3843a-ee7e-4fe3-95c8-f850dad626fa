import { request } from "./request";
import type {
	ChannelBalance,
	ChannelTransactionData,
	ChannelTransactionStatus,
	CreateChannelTransactionRequest,
	ChannelExchangeCurrencyData,
	ExchangeRateResult,
} from "@packages/shared";

export const channelFundApi = {
	// 通道余额相关API
	balance: {
		get: () => request.get<ChannelBalance>("/admin/platform/balance"),
	},

	// 交易相关API
	transaction: {
		create: (data: CreateChannelTransactionRequest) =>
			request.post<ChannelTransactionData>("/admin/platform/transaction", data),

		getAll: (params: {
			status?: ChannelTransactionStatus;
			page?: number;
			size?: number;
		}) =>
			request.get<{
				items: ChannelTransactionData[];
				total: number;
			}>("/admin/platform/transactions", { params }),
	},

	// 汇率相关API
	exchange: {
		// 执行货币兑换
		exchangeCurrency: (data: ChannelExchangeCurrencyData) =>
			request.post<ChannelTransactionData>("/admin/platform/exchange", data),

		// 获取当前汇率
		getRates: () => request.get<ExchangeRateResult>("/admin/platform/rates"),
	},

	// 预授权额度API
	creditLimit: {
		get: () =>
			request.get<{ creditLimit: number }>("/admin/platform/credit-limit"),
	},
};
