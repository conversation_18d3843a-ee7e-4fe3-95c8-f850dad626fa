<template>
  <div class="site-config-view view">
    <div class="page-header">
      <h2>网站配置管理</h2>
      <el-button type="primary" @click="loadData">
        <el-icon>
          <Refresh />
        </el-icon>
        刷新
      </el-button>
    </div>

    <!-- Site Config Form -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>网站基本设置</span>
          <el-button type="primary" @click="submitForm" :loading="loading.submitForm">保存更改</el-button>
        </div>
      </template>

      <el-form ref="formRef" :model="siteConfig" :label-width="isMobile ? 'auto' : '120px'">
        <el-tabs v-model="activeTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="tab-content-padding">
              <el-form-item label="公司常用名称">
                <el-input v-model="siteConfig.companyLegalName" placeholder="请输入公司常用名称" />
              </el-form-item>

              <el-form-item label="简短英文缩写">
                <el-input v-model="siteConfig.companyShortName" placeholder="请输入简短英文缩写" />
              </el-form-item>

              <el-form-item label="用户端网站名">
                <el-input v-model="siteConfig.clientSiteName" placeholder="请输入用户端网站名" />
              </el-form-item>

              <el-form-item label="管理端网站名">
                <el-input v-model="siteConfig.adminSiteName" placeholder="请输入管理端网站名" />
              </el-form-item>
            </div>
          </el-tab-pane>

          <!-- 品牌资源 -->
          <el-tab-pane label="品牌资源" name="branding">
            <el-form-item label="网站图标" class="resource-form-item">
              <div class="upload-container">
                <div class="preview-row">
                  <div class="preview favicon-preview" v-if="faviconPreview">
                    <img :src="faviconPreview" alt="Favicon Preview" />
                  </div>
                  <div class="preview favicon-preview empty-preview" v-else>
                    <span class="logo-text">LOGO</span>
                  </div>
                  <span class="upload-location">显示在浏览器标签页和收藏夹中</span>
                </div>
                <div class="file-select-area">
                  <el-upload class="uploader" action="#" :auto-upload="false" :show-file-list="false"
                    :on-change="handleFaviconChange">
                    <el-button type="primary" class="upload-btn">{{ faviconFile ? '重新选择' : '选择文件' }}</el-button>
                  </el-upload>
                  <div class="upload-tip">支持SVG, PNG格式，建议尺寸32x32px，大小200KB以内</div>
                </div>
                <div class="file-info-area" v-if="faviconFile">
                  <div class="file-details">
                    <div class="file-name">{{ faviconFile.name }}</div>
                    <span class="file-size" :class="{ 'size-exceeded': !faviconValid }">
                      {{ (faviconFile.size / 1024).toFixed(2) }}KB
                      <span v-if="!faviconValid">(超出限制)</span>
                    </span>
                  </div>
                  <el-button type="success" @click="uploadIcon('favicon')" :loading="loading.uploadFavicon"
                    :disabled="!faviconValid" class="upload-btn">
                    上传
                  </el-button>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="品牌Logo" class="resource-form-item">
              <div class="upload-container">
                <div class="preview-row">
                  <div class="preview logo-preview" v-if="logoPreview">
                    <img :src="logoPreview" alt="Logo Preview" />
                  </div>
                  <div class="preview logo-preview empty-preview" v-else>
                    <span class="logo-text">LOGO</span>
                  </div>
                  <span class="upload-location">显示在登录页面和主页面左上角</span>
                </div>
                <div class="file-select-area">
                  <el-upload class="uploader" action="#" :auto-upload="false" :show-file-list="false"
                    :on-change="handleLogoChange">
                    <el-button type="primary" class="upload-btn">{{ logoFile ? '重新选择' : '选择文件' }}</el-button>
                  </el-upload>
                  <div class="upload-tip">支持SVG, PNG格式，建议宽度不超过200px，大小200KB以内</div>
                </div>
                <div class="file-info-area" v-if="logoFile">
                  <div class="file-details">
                    <div class="file-name">{{ logoFile.name }}</div>
                    <span class="file-size" :class="{ 'size-exceeded': !logoValid }">
                      {{ (logoFile.size / 1024).toFixed(2) }}KB
                      <span v-if="!logoValid">(超出限制)</span>
                    </span>
                  </div>
                  <el-button type="success" @click="uploadIcon('logo')" :loading="loading.uploadLogo"
                    :disabled="!logoValid" class="upload-btn">
                    上传
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-tab-pane>

          <!-- 主题色设置 -->
          <el-tab-pane label="主题配色" name="theme">
            <div class="theme-section">
              <h3 class="section-title">管理端主题色</h3>
              <div class="theme-color-container">
                <el-color-picker v-model="siteConfig.adminPrimaryColor" show-alpha format="hex"
                  :predefine="predefineAdminColors" @change="handleAdminColorChange" class="color-picker" />
                <div class="color-preview-container">
                  <div class="color-code">{{ siteConfig.adminPrimaryColor }}</div>
                  <div class="preview-elements">
                    <span class="preview-text" :style="{ color: siteConfig.adminPrimaryColor }">主题色文本预览</span>
                    <el-button :style="{ backgroundColor: siteConfig.adminPrimaryColor, color: '#fff', border: 'none' }"
                      size="small">按钮预览</el-button>
                  </div>
                  <div class="color-description">
                    <p>管理端主题色会应用于当前管理界面，影响按钮、链接等元素的颜色。</p>
                  </div>
                </div>
              </div>

              <div class="color-palette">
                <div class="palette-title">预设颜色</div>
                <div class="palette-colors">
                  <div v-for="(color, index) in predefineAdminColors" :key="index" class="palette-color"
                    :style="{ backgroundColor: color }"
                    @click="siteConfig.adminPrimaryColor = color; handleAdminColorChange(color)">
                  </div>
                </div>
              </div>

              <!-- 使用单独的分隔线元素 -->
              <div class="theme-divider"></div>

              <h3 class="section-title">用户端主题色</h3>
              <div class="theme-color-container">
                <el-color-picker v-model="siteConfig.clientPrimaryColor" show-alpha format="hex"
                  :predefine="predefineClientColors" @change="handleClientColorChange" class="color-picker" />
                <div class="color-preview-container">
                  <div class="color-code">{{ siteConfig.clientPrimaryColor }}</div>
                  <div class="preview-elements">
                    <span class="preview-text" :style="{ color: siteConfig.clientPrimaryColor }">主题色文本预览</span>
                    <el-button
                      :style="{ backgroundColor: siteConfig.clientPrimaryColor, color: '#fff', border: 'none' }"
                      size="small">按钮预览</el-button>
                  </div>
                  <div class="color-description">
                    <p>用户端主题色会应用于前台用户界面，该颜色变化不会在当前管理页面显示。</p>
                  </div>
                </div>
              </div>

              <div class="color-palette">
                <div class="palette-title">预设颜色</div>
                <div class="palette-colors">
                  <div v-for="(color, index) in predefineClientColors" :key="index" class="palette-color"
                    :style="{ backgroundColor: color }"
                    @click="siteConfig.clientPrimaryColor = color; handleClientColorChange(color)">
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </el-card>

    <!-- History Records -->
    <!-- 
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <span>修改历史</span>
        </div>
      </template>

      <el-table :data="configHistory" style="width: 100%" v-loading="loading.history"
        :default-sort="{ prop: 'created_at', order: 'descending' }" @sort-change="handleSortChange">
        <el-table-column prop="created_at" label="修改时间" sortable="custom" min-width="160"
          :formatter="formatCreatedDate" />
        <el-table-column prop="config.companyLegalName" label="公司名称" min-width="120" />
        <el-table-column prop="config.companyShortName" label="英文缩写" min-width="100" />
        <el-table-column prop="config.clientSiteName" label="用户端网站名" min-width="120" />
        <el-table-column prop="config.adminSiteName" label="管理端网站名" min-width="120" />
        <el-table-column prop="admin_id" label="修改人" min-width="80" />
      </el-table>

      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          :total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>
    -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount, reactive } from "vue";
import { ElMessage } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";
import { siteConfigApi } from "@/api";
import type { SiteConfig } from "@packages/shared";
import { getErrorMessage } from "@packages/shared";
import { useSiteConfigStore } from "@/stores/siteConfig";

const siteConfigStore = useSiteConfigStore();

const isMobile = ref(window.innerWidth < 768);

// 监听窗口大小变化，更新移动端状态
const updateIsMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

onBeforeMount(() => {
  window.addEventListener("resize", updateIsMobile);
});

onMounted(() => {
  loadData();
  updateIsMobile();
});

const activeTab = ref("basic");
const formRef = ref();

// 使用reactive创建响应式对象
const siteConfig = reactive<SiteConfig>({
  companyLegalName: "",
  companyShortName: "",
  clientSiteName: "",
  adminSiteName: "",
  faviconId: "",
  logoId: "",
  adminPrimaryColor: "#409eff", // 管理端默认蓝色
  clientPrimaryColor: "#e88234", // 用户端默认橙色
});

// 管理端预定义颜色选项
const predefineAdminColors = [
  "#409eff", // 默认蓝色
  "#67c23a", // 绿色
  "#e6a23c", // 黄色
  "#f56c6c", // 红色
  "#909399", // 灰色
  "#6236ff", // 紫色
  "#1890ff", // 天蓝
  "#13c2c2", // 青色
];

// 用户端预定义颜色选项
const predefineClientColors = [
  "#e88234", // 默认橙色
  "#ff9900", // 橙黄色
  "#f56c6c", // 红色
  "#67c23a", // 绿色
  "#409eff", // 蓝色
  "#9c27b0", // 紫色
  "#795548", // 棕色
  "#ff5722", // 深橙色
];

// 文件上传相关
const faviconFile = ref<File | null>(null);
const logoFile = ref<File | null>(null);
const faviconPreview = ref<string>("");
const logoPreview = ref<string>("");
const faviconValid = ref(true);
const logoValid = ref(true);

/* 
interface ConfigHistoryItem {
  config_id: number;
  config: SiteConfig;
  created_at: string;
  admin_id: number;
}

const configHistory = ref<ConfigHistoryItem[]>([]);
*/

const loading = ref({
  submitForm: false,
  history: false,
  uploadFavicon: false,
  uploadLogo: false,
});

/* 
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const sortBy = ref("created_at");
const sortOrder = ref("DESC");
*/

const loadData = async () => {
  try {
    await loadSiteConfig();
    // await loadHistory();
  } catch (error) {
    console.error(error);
    ElMessage.error("加载数据失败");
  }
};

const loadSiteConfig = async () => {
  try {
    const data = await siteConfigApi.getSiteConfig();
    if (data) {
      Object.assign(siteConfig, data);

      // 设置预览图
      if (data.faviconId) {
        faviconPreview.value = siteConfigApi.getAssetUrl(data.faviconId);
      }

      if (data.logoId) {
        logoPreview.value = siteConfigApi.getAssetUrl(data.logoId);
      }
    }
  } catch (error) {
    console.error(error);
    ElMessage.error("加载网站配置失败");
  }
};

/* 
const loadHistory = async () => {
  loading.value.history = true;
  try {
    const result = await siteConfigApi.getSiteConfigHistory(
      currentPage.value,
      pageSize.value,
      {
        sortBy: sortBy.value,
        sortOrder: sortOrder.value as "ASC" | "DESC",
      },
    );

    if (result) {
      configHistory.value = result.items || [];
      total.value = result.total || 0;
    } else {
      // 如果没有结果，则清空列表
      configHistory.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error("加载修改历史失败");
    // 确保出错时清空历史列表
    configHistory.value = [];
    total.value = 0;
  } finally {
    loading.value.history = false;
  }
};
*/

const submitForm = async () => {
  if (!formRef.value) return;

  loading.value.submitForm = true;
  try {
    const result = await siteConfigApi.updateSiteConfig(siteConfig);
    if (result) {
      ElMessage.success("网站配置更新成功");

      // 应用新的管理端主题色
      if (siteConfig.adminPrimaryColor) {
        siteConfigStore.applyPrimaryColor(siteConfig.adminPrimaryColor);
      }
    }
    await loadData();
  } catch (error) {
    console.error(error);
    ElMessage.error("更新网站配置失败");
  } finally {
    loading.value.submitForm = false;
  }
};

// 处理图标文件选择
interface UploadFile {
  raw: File;
  [key: string]: unknown;
}

const handleFaviconChange = (file: UploadFile) => {
  const isAllowed =
    file.raw.type === "image/svg+xml" || file.raw.type === "image/png";
  if (!isAllowed) {
    ElMessage.error("只支持SVG或PNG格式!");
    faviconValid.value = false;
    return;
  }

  // 检查文件大小
  if (file.raw.size > 200 * 1024) {
    const fileSizeKB = (file.raw.size / 1024).toFixed(2);
    ElMessage.error(`图标文件大小为 ${fileSizeKB}KB，超出200KB限制`);
    faviconFile.value = file.raw;
    faviconPreview.value = URL.createObjectURL(file.raw);
    faviconValid.value = false;
    return;
  }

  faviconFile.value = file.raw;
  faviconPreview.value = URL.createObjectURL(file.raw);
  faviconValid.value = true;
};

const handleLogoChange = (file: UploadFile) => {
  const isAllowed =
    file.raw.type === "image/svg+xml" || file.raw.type === "image/png";
  if (!isAllowed) {
    ElMessage.error("只支持SVG或PNG格式!");
    logoValid.value = false;
    return;
  }

  // 检查文件大小
  if (file.raw.size > 200 * 1024) {
    const fileSizeKB = (file.raw.size / 1024).toFixed(2);
    ElMessage.error(`Logo文件大小为 ${fileSizeKB}KB，超出200KB限制`);
    logoFile.value = file.raw;
    logoPreview.value = URL.createObjectURL(file.raw);
    logoValid.value = false;
    return;
  }

  logoFile.value = file.raw;
  logoPreview.value = URL.createObjectURL(file.raw);
  logoValid.value = true;
};

// 上传图标
const uploadIcon = async (type: "favicon" | "logo") => {
  const file = type === "favicon" ? faviconFile.value : logoFile.value;
  const isValid = type === "favicon" ? faviconValid.value : logoValid.value;

  if (!file) return;

  // 再次验证文件有效性
  if (!isValid) {
    ElMessage.error(
      `${type === "favicon" ? "图标" : "Logo"}文件无效，请重新选择`,
    );
    return;
  }

  const loadingKey = type === "favicon" ? "uploadFavicon" : "uploadLogo";
  loading.value[loadingKey] = true;

  try {
    const result = await siteConfigApi.uploadIcon(file, type);
    if (result) {
      ElMessage.success(`${type === "favicon" ? "网站图标" : "Logo"}上传成功`);
    }

    // 重新加载配置
    await loadSiteConfig();

    // 清除临时上传文件
    if (type === "favicon") {
      faviconFile.value = null;
    } else {
      logoFile.value = null;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error(
      `上传${type === "favicon" ? "网站图标" : "Logo"}失败：${getErrorMessage(error)}`,
    );
  } finally {
    loading.value[loadingKey] = false;
  }
};

/* 
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  loadHistory();
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  loadHistory();
};

const handleSortChange = ({
  prop,
  order,
}: { prop?: string; order?: string }) => {
  sortBy.value = prop || "created_at";
  sortOrder.value = order === "ascending" ? "ASC" : "DESC";
  loadHistory();
};

const formatCreatedDate = (row: ConfigHistoryItem) => {
  return formatDate(row.created_at);
};
*/

// 处理颜色变化
const handleAdminColorChange = (color: string) => {
  siteConfig.adminPrimaryColor = color;
  // 实时应用主题色，用于预览
  siteConfigStore.applyPrimaryColor(color);
};

const handleClientColorChange = (color: string) => {
  siteConfig.clientPrimaryColor = color;
  // 客户端主题色不在管理界面上预览
};
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
  }
}

.config-card,
.history-card {
  background: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-lighter);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: var(--el-text-color-primary);
}

:deep(.el-card__header) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.resource-form-item {
  margin-bottom: 24px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

:deep(.resource-form-item .el-form-item__label) {
  display: flex;
  justify-content: center;
  width: 100px;
  margin: 10px 0;
  position: relative;
  font-weight: 600;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.resource-form-item .el-form-item__content) {
  margin-left: 0 !important;
}

@media (max-width: 767px) {
  :deep(.resource-form-item .el-form-item__label-wrap) {
    margin-left: 0 !important;
  }

  :deep(.resource-form-item) {
    display: flex;
    flex-direction: column;
  }

  :deep(.resource-form-item .el-form-item__label) {
    width: 100%;
    text-align: left;
    justify-content: flex-start;
    padding: 0 0 8px 0;
    margin: 0 0 15px 0;
  }

  :deep(.resource-form-item .el-form-item__content) {
    width: 100%;
  }
}

.upload-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 10px;
  width: 100%;
  position: relative;
}

.preview-row {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 10px 15px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.upload-controls {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 15px;
  padding: 10px 15px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
  position: relative;
}

.file-select-area {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 15px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.file-info-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.file-name {
  font-size: 14px;
  color: var(--el-text-color-primary);
  word-break: break-all;
  max-width: 400px;
}

.preview {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  border: 1px dashed var(--el-border-color);
}

.empty-preview {
  background-color: var(--el-fill-color-lighter);
}

.logo-text {
  font-size: 16px;
  font-weight: bold;
  color: var(--el-text-color-secondary);
  letter-spacing: 1px;
}

.favicon-preview {
  min-width: 64px;
  height: 64px;
}

.logo-preview {
  min-width: 64px;
  height: 64px;
}

.preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.upload-tip {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  flex: 1;
}

.upload-location {
  font-size: 14px;
  color: var(--el-text-color-regular);
  flex: 1;
}

.upload-btn {
  border-radius: 4px;
  min-width: 90px;
}

.file-action {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: auto;
}

.file-size {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.size-exceeded {
  color: #f56c6c;
}

.submit-btn {
  align-self: flex-end;
  min-width: 80px;
  margin-right: 15px;
}

:deep(.el-upload) {
  display: inline-block;
}

.file-info {
  position: absolute;
  right: 120px;
  bottom: 22px;
  font-size: 13px;
  color: var(--el-text-color-regular);
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

/* 主题色设置相关样式 */
.theme-section {
  padding: 0 15px;
}

.section-title {
  font-size: 18px;
  margin-bottom: 24px;
  font-weight: 500;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  bottom: 4px;
  width: 4px;
  background-color: var(--el-color-primary);
  border-radius: 2px;
}

.theme-divider {
  margin: 24px 0;
  border-top: 1px dashed var(--el-border-color-lighter);
  height: 0;
  /* 确保只有边框高度 */
  width: 100%;
}

.theme-color-container {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
}

.color-picker {
  align-self: center;
}

.color-preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.color-code {
  font-family: monospace;
  font-size: 16px;
  padding: 6px 12px;
  background-color: var(--el-fill-color-darker);
  border-radius: 4px;
  display: inline-block;
}

.preview-elements {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 0;
}

.preview-text {
  font-size: 16px;
  font-weight: 500;
}

.color-description {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.color-palette {
  margin-bottom: 40px;
}

.palette-title {
  font-size: 15px;
  margin-bottom: 15px;
  color: var(--el-text-color-secondary);
}

.palette-colors {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.palette-color {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.palette-color:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tab-content-padding {
  padding-top: 20px;
}
</style>