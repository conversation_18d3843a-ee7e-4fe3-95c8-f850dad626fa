import { ENV } from "@/config/configManager.js";
import type { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { AppError } from "@/core/appError.js";
import type { ParamsDictionary } from "express-serve-static-core";
import type { ParsedQs } from "qs";
import type { JWTPayload, UserJWTPayload } from "@packages/shared";

const JWT_SECRET = ENV.JWT_SECRET || "";

const verifyToken = (
	token: string,
	secret: string,
	expectedType: "access" | "admin",
): JWTPayload => {
	const decoded = jwt.verify(token, secret) as JWTPayload;

	if (decoded.type !== expectedType) {
		throw AppError.create("INVALID_TOKEN", "Invalid token");
	}

	// Type guard
	if (expectedType === "access" && !("user_id" in decoded)) {
		throw AppError.create("INVALID_TOKEN", "Invalid token");
	}

	if (expectedType === "admin" && !("admin_id" in decoded)) {
		throw AppError.create("INVALID_TOKEN", "Invalid admin token");
	}

	return decoded;
};

// Modify AuthRequest interface
export interface AuthRequest<
	P = ParamsDictionary,
	ResBody = unknown,
	ReqBody = unknown,
	ReqQuery = ParsedQs,
> extends Request<P, ResBody, ReqBody, ReqQuery> {
	jwt: JWTPayload;
}

/**
 * 认证中间件，用户认证带资质检查
 */
const authenticate =
	(tokenType: "access" | "admin", skipQualificationCheck = false) =>
	(req: Request, _res: Response, next: NextFunction) => {
		const authHeader = req.headers.authorization;
		const token = authHeader?.split(" ")[1];

		if (!token) {
			return next(
				AppError.create("UNAUTHORIZED", "Token of authenticate is required"),
			);
		}

		try {
			const decoded = verifyToken(token, JWT_SECRET, tokenType);
			(req as AuthRequest).jwt = decoded;

			if (
				tokenType === "access" &&
				!skipQualificationCheck &&
				!(decoded as UserJWTPayload)?.is_qualified
			) {
				return next(AppError.create("UNQUALIFIED", "Unqualified"));
			}

			next();
		} catch (error) {
			if (error instanceof jwt.TokenExpiredError) {
				return next(AppError.create("TOKEN_EXPIRED", "Token expired"));
			}
			if (error instanceof jwt.JsonWebTokenError) {
				return next(AppError.create("INVALID_TOKEN", "Invalid token"));
			}
			return next(error);
		}
	};

export const authenticateToken = authenticate("access");
export const authenticateAdmin = authenticate("admin");
export const authenticateTokenWithoutQualification = authenticate(
	"access",
	true,
);

/*
Client                      Server
   |                          |
   |   1. POST /admin/login   |
   |------------------------->|
   |                          |
   |   2. 返回 token          |
   |<-------------------------|
   |                          |
   |   3. GET /admin/status   |
   |   authorization: Bearer xxx
   |------------------------->|
   |                          |
   |   4. 返回数据            |
   |<-------------------------|
*/
