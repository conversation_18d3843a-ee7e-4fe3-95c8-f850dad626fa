import type { Request, Response, NextFunction } from "express";
import { AppError } from "@/core/appError.js";
import type { AuthRequest } from "@/middlewares/jwtAuth.js";
import type { AdminJWTPayload } from "@packages/shared";
import { AdminPermission } from "@packages/shared";

export const requirePermission = (permission: AdminPermission) => {
	return (req: Request, _res: Response, next: NextFunction) => {
		const adminPayload = (req as AuthRequest).jwt as AdminJWTPayload;
		const adminPermissions = adminPayload.permissions || [];

		if (
			(adminPermissions.length > 0 && permission === AdminPermission.BASIC) ||
			adminPermissions.includes(permission)
		) {
			return next();
		}

		return next(AppError.create("FORBIDDEN", "Insufficient permissions"));
	};
};
