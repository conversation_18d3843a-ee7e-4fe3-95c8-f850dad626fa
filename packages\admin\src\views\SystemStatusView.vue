<template>
  <div class="system-status-view view">
    <div class="page-header">
      <h2>系统状态</h2>
      <el-button type="primary" @click="loadData">
        <el-icon>
          <Refresh />
        </el-icon>
        刷新
      </el-button>
    </div>

    <!-- Status Controls -->
    <div class="status-controls" v-if="false">
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <span>交易功能</span>
            <div class="control-group">
              <el-tooltip :content="`切换到${(systemStatus.auto_manage_enabled?.[0] ? '自动' : '手动')}管理`">
                <el-button class="lock-button" :type="systemStatus.auto_manage_enabled?.[0] ? 'info' : 'primary'"
                  :icon="systemStatus.auto_manage_enabled?.[0] ? Unlock : Lock" circle @click="toggleAutoManage(0)" />
              </el-tooltip>
              <el-switch v-model="systemStatus.SYSTEM_ENABLED"
                @change="(val: boolean) => updateStatus('SYSTEM_ENABLED', val)" :loading="loading.SYSTEM_ENABLED" />
            </div>
          </div>
        </template>
        <div class="status-info">
          <el-tag :type="systemStatus.SYSTEM_ENABLED ? 'success' : 'danger'">
            {{ systemStatus.SYSTEM_ENABLED ? '允许交易' : '禁止交易' }}
          </el-tag>
        </div>
      </el-card>

      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <span>下单功能</span>
            <div class="control-group">
              <el-tooltip content="切换自动管理">
                <el-button class="lock-button" :type="systemStatus.auto_manage_enabled?.[1] ? 'info' : 'primary'"
                  :icon="systemStatus.auto_manage_enabled?.[1] ? Unlock : Lock" circle @click="toggleAutoManage(1)" />
              </el-tooltip>
              <el-switch v-model="systemStatus.POSITION_ENTRY_ENABLED"
                @change="(val: boolean) => updateStatus('POSITION_ENTRY_ENABLED', val)"
                :loading="loading.POSITION_ENTRY_ENABLED" :disabled="!systemStatus.SYSTEM_ENABLED" />
            </div>
          </div>
        </template>
        <div class="status-info">
          <el-tag :type="systemStatus.POSITION_ENTRY_ENABLED ? 'success' : 'warning'">
            {{ systemStatus.POSITION_ENTRY_ENABLED ? '允许下单' : '禁止下单' }}
          </el-tag>
        </div>
      </el-card>

      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <span>询价功能</span>
            <div class="control-group">
              <el-tooltip content="切换自动管理">
                <el-button class="lock-button" :type="systemStatus.auto_manage_enabled?.[2] ? 'info' : 'primary'"
                  :icon="systemStatus.auto_manage_enabled?.[2] ? Unlock : Lock" circle @click="toggleAutoManage(2)" />
              </el-tooltip>
              <el-switch v-model="systemStatus.INQUIRY_ENABLED"
                @change="(val: boolean) => updateStatus('INQUIRY_ENABLED', val)" :loading="loading.INQUIRY_ENABLED" />
            </div>
          </div>
        </template>
        <div class="status-info">
          <el-tag :type="systemStatus.INQUIRY_ENABLED ? 'success' : 'warning'">
            {{ systemStatus.INQUIRY_ENABLED ? '允许询价' : '禁止询价' }}
          </el-tag>
        </div>
      </el-card>
    </div>

    <!-- Status History -->
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <span>状态变更历史</span>
        </div>
      </template>

      <el-table :data="statusHistory" style="width: 100%" v-loading="loading.history"
        :default-sort="{ prop: 'changed_at', order: 'descending' }" @sort-change="handleSortChange">
        <el-table-column prop="changed_at" label="时间" sortable="custom" min-width="180"
          :formatter="(row: StatusHistoryItem) => formatDate(row.changed_at)" />
        <el-table-column prop="status.SYSTEM_ENABLED" label="交易" min-width="120">
          <template #default="{ row }">
            <el-tag :type="row.status.SYSTEM_ENABLED ? 'success' : 'danger'" size="small">
              {{ row.status.SYSTEM_ENABLED ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status.POSITION_ENTRY_ENABLED" label="下单" min-width="120">
          <template #default="{ row }">
            <el-tag :type="row.status.POSITION_ENTRY_ENABLED ? 'success' : 'warning'" size="small">
              {{ row.status.POSITION_ENTRY_ENABLED ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status.INQUIRY_ENABLED" label="询价" min-width="120">
          <template #default="{ row }">
            <el-tag :type="row.status.INQUIRY_ENABLED ? 'success' : 'warning'" size="small">
              {{ row.status.INQUIRY_ENABLED ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status.change_type" label="变更类型" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getChangeTypeTag(row.status.change_type)" size="small">
              {{ getChangeTypeLabel(row.status.change_type) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          :total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Refresh, Lock, Unlock } from "@element-plus/icons-vue";
import { configApi } from "@/api";
import type { SystemStatus, StatusHistoryItem } from "@packages/shared";
import { StatusChange } from "@packages/shared";
import { formatDate } from "@/utils/format";

const systemStatus = ref<SystemStatus>({
  SYSTEM_ENABLED: false,
  POSITION_ENTRY_ENABLED: false,
  INQUIRY_ENABLED: false,
  change_type: StatusChange.MANUAL,
  auto_manage_enabled: [true, true, true],
});

const statusHistory = ref<StatusHistoryItem[]>([]);

const loading = ref<
  Record<
    keyof Pick<
      SystemStatus,
      "SYSTEM_ENABLED" | "POSITION_ENTRY_ENABLED" | "INQUIRY_ENABLED"
    >,
    boolean
  > & {
    history: boolean;
  }
>({
  SYSTEM_ENABLED: false,
  POSITION_ENTRY_ENABLED: false,
  INQUIRY_ENABLED: false,
  history: false,
});

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const sortBy = ref("changed_at");
const sortOrder = ref("DESC");

const loadData = async () => {
  try {
    await Promise.all([loadStatus(), loadHistory()]);
  } catch (error) {
    console.error(error);
    ElMessage.error("加载数据失败");
  }
};

const loadStatus = async () => {
  try {
    systemStatus.value =
      (await configApi.getSystemStatus()) || systemStatus.value;
  } catch (error) {
    console.error(error);
    ElMessage.error("加载系统状态失败");
  }
};

const loadHistory = async () => {
  loading.value.history = true;
  try {
    const result = await configApi.getSystemStatusHistory(
      currentPage.value,
      pageSize.value,
      {
        sortBy: sortBy.value,
        sortOrder: sortOrder.value as "ASC" | "DESC",
      },
    );
    statusHistory.value = result?.items || [];
    total.value = result?.total || 0;
  } catch (error) {
    console.error(error);
    ElMessage.error("加载状态历史失败");
  } finally {
    loading.value.history = false;
  }
};

const updateStatus = async (
  type: keyof Pick<
    SystemStatus,
    "SYSTEM_ENABLED" | "POSITION_ENTRY_ENABLED" | "INQUIRY_ENABLED"
  >,
  enabled: boolean,
) => {
  loading.value[type] = true;
  try {
    await configApi.updateSystemStatus({ [type]: enabled });
    await loadData();
    ElMessage.success("状态更新成功");
  } catch (error) {
    console.error(error);
    ElMessage.error("更新状态失败");
    await loadStatus(); // Reload status in case of failure
  } finally {
    loading.value[type] = false;
  }
};

const getChangeTypeTag = (
  type: StatusChange,
): "primary" | "success" | "info" | "warning" | "danger" => {
  const types: Record<
    StatusChange,
    "primary" | "success" | "info" | "warning" | "danger"
  > = {
    [StatusChange.MANUAL]: "primary",
    [StatusChange.AUTOMATIC]: "info",
    [StatusChange.RISK_CONTROL]: "warning",
  };
  return types[type] || "info";
};

const getChangeTypeLabel = (type: StatusChange): string => {
  const labels: Record<StatusChange, string> = {
    [StatusChange.MANUAL]: "手动",
    [StatusChange.AUTOMATIC]: "自动",
    [StatusChange.RISK_CONTROL]: "风控",
  };
  return labels[type] || "未知";
};

const toggleAutoManage = async (index: number) => {
  const newAutoManage = [
    ...(systemStatus.value.auto_manage_enabled || [true, true, true]),
  ];
  newAutoManage[index] = !newAutoManage[index];

  try {
    await configApi.updateSystemStatus({
      ...systemStatus.value,
      auto_manage_enabled: newAutoManage,
      change_type: StatusChange.MANUAL,
    });
    systemStatus.value.auto_manage_enabled = newAutoManage;
    ElMessage.success("自动管理设置已更新");
  } catch (error) {
    console.error("更新自动管理设置失败:", error);
    ElMessage.error("更新自动管理设置失败");
  }
};

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  loadHistory();
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  loadHistory();
};

const handleSortChange = ({
  prop,
  order,
}: { prop?: string; order?: string }) => {
  sortBy.value = prop || "changed_at";
  sortOrder.value = order === "ascending" ? "ASC" : "DESC";
  loadHistory();
};

onMounted(loadData);
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
  }
}

.status-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.status-card {
  background: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-lighter);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: var(--el-text-color-primary);
}

:deep(.el-card__header) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-info {
  margin-top: 10px;
}

.history-card :deep(.el-table) {
  --el-table-bg-color: var(--el-bg-color-overlay);
  --el-table-tr-bg-color: var(--el-bg-color-overlay);
  --el-table-header-bg-color: var(--el-bg-color-overlay);
  --el-table-border-color: var(--el-border-color-lighter);

  .el-table__header-wrapper th {
    background-color: var(--el-bg-color-overlay);
    color: var(--el-text-color-regular);
  }

  .el-table__body-wrapper td {
    background-color: var(--el-bg-color-overlay);
    color: var(--el-text-color-primary);
  }
}


.lock-button {
  padding: 6px;
  font-size: 14px;
}
</style>
