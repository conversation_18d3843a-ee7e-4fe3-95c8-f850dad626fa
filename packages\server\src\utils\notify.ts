import WebSocketManager from "../core/websocket.js";
import type { WebSocket } from "ws";
import type { WebSocketMessage } from "@packages/shared";
import logger from "@/utils/logger.js";

interface CustomWebSocket extends WebSocket {
	user_id?: number;
}

export async function notifyUser(user_id: number, message: WebSocketMessage) {
	try {
		const wss = WebSocketManager.getServer();
		if (!wss) {
			logger.warn("WebSocket notification skipped: Server not initialized");
			return;
		}

		let notified = false;
		for (const client of wss.clients) {
			const customClient = client as CustomWebSocket;
			if (
				customClient.user_id === user_id &&
				customClient.readyState === customClient.OPEN
			) {
				customClient.send(JSON.stringify(message));
				notified = true;
			}
		}

		if (notified) {
			logger.debug(
				{ user_id, messageType: message.type },
				"WebSocket notification sent",
			);
		} else {
			logger.debug(
				{ user_id, messageType: message.type },
				"No active WebSocket connection found for user",
			);
		}
	} catch (error) {
		logger.warn(
			{ error, user_id, messageType: message.type },
			`Failed to send WebSocket notification: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

export async function broadcast(message: WebSocketMessage) {
	try {
		const wss = WebSocketManager.getServer();
		if (!wss) {
			logger.warn("WebSocket notification skipped: Server not initialized");
			return;
		}

		let sentCount = 0;
		// 直接向所有已连接的客户端广播消息
		for (const client of wss.clients) {
			if (client.readyState === client.OPEN) {
				client.send(JSON.stringify(message));
				sentCount++;
			}
		}

		logger.debug(
			{ sentCount, messageType: message.type },
			"WebSocket broadcast completed",
		);
	} catch (error) {
		logger.warn(
			{ error, messageType: message.type },
			`Failed to broadcast WebSocket notification: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}
