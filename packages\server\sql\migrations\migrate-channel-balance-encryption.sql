-- 通道余额加密迁移脚本
-- 执行前请确保已停止所有服务
-- 此脚本将channels表的余额字段从Decimal类型改为VARCHAR(32)以支持加密

DO $$
BEGIN
    -- 1. 备份当前channels表
    CREATE TABLE channels_backup_before_encryption AS SELECT * FROM channels;

    -- 2. 添加新的加密余额字段
    ALTER TABLE channels 
    ADD COLUMN balance_cny_encrypted VARCHAR(32),
    ADD COLUMN balance_hkd_encrypted VARCHAR(32),
    ADD COLUMN balance_usd_encrypted VARCHAR(32);

    -- 3. 将现有余额数据转换为字符串并存储到新字段（暂时存明文，后续脚本会加密）
    UPDATE channels SET 
        balance_cny_encrypted = balance_cny::text,
        balance_hkd_encrypted = balance_hkd::text,
        balance_usd_encrypted = balance_usd::text;

    -- 4. 删除旧的余额字段
    ALTER TABLE channels 
    DROP COLUMN balance_cny,
    DROP COLUMN balance_hkd,
    DROP COLUMN balance_usd;

    -- 5. 重命名新字段
    ALTER TABLE channels 
    RENAME COLUMN balance_cny_encrypted TO balance_cny;
    ALTER TABLE channels 
    RENAME COLUMN balance_hkd_encrypted TO balance_hkd;
    ALTER TABLE channels 
    RENAME COLUMN balance_usd_encrypted TO balance_usd;

    -- 6. 设置字段为非空
    ALTER TABLE channels 
    ALTER COLUMN balance_cny SET NOT NULL,
    ALTER COLUMN balance_hkd SET NOT NULL,
    ALTER COLUMN balance_usd SET NOT NULL;

    -- 输出完成信息
    RAISE NOTICE '数据迁移完成，请手动运行验证查询';
END
$$;

-- 验证数据迁移（需要单独执行）
SELECT 
    channel_id,
    name,
    balance_cny,
    balance_hkd,
    balance_usd,
    is_locked
FROM channels
ORDER BY channel_id;

-- 提示信息
-- 执行完成后请运行：
-- 1. pnpm prisma db pull（更新schema.prisma）
-- 2. pnpm prisma generate（重新生成Prisma客户端）
-- 3. pnpm channel-balance-crypto -- encrypt（加密余额字段） 