// 执行命令：pnpm tsx --tsconfig D:/ink-dev/packages/server/tsconfig.json D:/ink-dev/packages/server/src/tests/testDatabaseTransaction.ts

import prisma, { platformPrisma } from "@/lib/prisma.js";
import { withCrossDbTransaction } from "@/core/dbTxnManager.js";
import logger from "@/utils/logger.js";
import type { Prisma } from "@prisma/client";

// 测试状态追踪
const testResults = {
	passed: 0,
	failed: 0,
	total: 0,
};

// 工具函数：清理测试数据
async function cleanupTestData() {
	// 清理主数据库测试数据
	await prisma.$executeRaw`DELETE FROM test_users WHERE name LIKE 'test_%'`;

	// 清理平台数据库测试数据
	if (platformPrisma) {
		await platformPrisma.$executeRaw`DELETE FROM test_channels WHERE name LIKE 'test_%'`;
	}
}

// 工具函数：验证测试结果
function assert(condition: boolean, message: string) {
	testResults.total++;

	if (condition) {
		testResults.passed++;
		logger.info(`✅ PASS: ${message}`);
	} else {
		testResults.failed++;
		logger.error(`❌ FAIL: ${message}`);
	}
}

// 确保测试表存在
async function setupTestTables() {
	try {
		// 在主数据库中创建测试表
		await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS test_users (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `;

		// 在平台数据库中创建测试表
		if (platformPrisma) {
			await platformPrisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS test_channels (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          balance DECIMAL(12,2) NOT NULL DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
      `;
		}

		logger.info("测试表创建成功");
	} catch (error) {
		logger.error(error, "设置测试表失败");
		throw error;
	}
}

/**
 * 测试场景1: 正常场景 - 两个数据库操作都成功
 */
async function testSuccessfulTransaction() {
	const testName = `test_success_${Date.now()}`;
	logger.info(`🧪 开始测试: 成功的跨数据库事务 (${testName})`);

	try {
		// 主数据库函数
		const mainDbFn = async (tx: Prisma.TransactionClient) => {
			const user = await tx.$executeRaw`
        INSERT INTO test_users (name, email) VALUES (${testName}, '<EMAIL>')
        RETURNING *
      `;
			return { name: testName, success: true };
		};

		// 平台数据库函数
		const platformDbFn = async (
			result: Record<string, unknown>,
			platformTx?: Prisma.TransactionClient,
		) => {
			if (!platformTx) {
				throw new Error("平台事务对象未传递");
			}

			const channel = await platformTx.$executeRaw`
        INSERT INTO test_channels (name, balance) VALUES (${testName}, 1000)
        RETURNING *
      `;

			return { name: testName, platformSuccess: true };
		};

		// 回滚函数 - 这次不应该被调用
		const rollbackFn = async (
			result: Record<string, unknown>,
			error: unknown,
		) => {
			// 这段代码不应该被执行
			logger.error("不应该执行回滚，但回滚函数被调用了");
			throw new Error("回滚函数不应该被调用");
		};

		// 执行跨数据库事务
		const result = await withCrossDbTransaction(
			mainDbFn,
			platformDbFn,
			rollbackFn,
		);

		// 验证结果
		const mainDbUser =
			await prisma.$queryRaw`SELECT * FROM test_users WHERE name = ${testName}`;
		const platformDbChannel = platformPrisma
			? await platformPrisma.$queryRaw`SELECT * FROM test_channels WHERE name = ${testName}`
			: [];

		assert(
			Array.isArray(mainDbUser) && mainDbUser.length > 0,
			"主数据库中应该存在测试用户",
		);

		assert(
			Array.isArray(platformDbChannel) && platformDbChannel.length > 0,
			"平台数据库中应该存在测试渠道",
		);

		logger.info("测试成功场景完成");
	} catch (error) {
		logger.error(error, "测试成功场景失败");
		assert(
			false,
			`测试成功场景失败: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

/**
 * 测试场景2: 平台数据库操作失败，应触发回滚
 */
async function testPlatformFailureWithRollback() {
	const testName = `test_platform_fail_${Date.now()}`;
	logger.info(`🧪 开始测试: 平台数据库失败时的回滚 (${testName})`);

	let rollbackCalled = false;

	try {
		// 主数据库函数
		const mainDbFn = async (tx: Prisma.TransactionClient) => {
			const user = await tx.$executeRaw`
        INSERT INTO test_users (name, email) VALUES (${testName}, '<EMAIL>')
        RETURNING *
      `;
			return { name: testName, userId: 123 }; // 模拟返回数据
		};

		// 平台数据库函数 - 故意抛出错误
		const platformDbFn = async (
			result: Record<string, unknown>,
			platformTx?: Prisma.TransactionClient,
		) => {
			throw new Error("模拟平台数据库操作失败");
		};

		// 回滚函数 - 应该被调用
		const rollbackFn = async (
			result: Record<string, unknown>,
			error: unknown,
		) => {
			rollbackCalled = true;
			logger.info("回滚函数被调用，执行回滚操作");

			// 从主数据库删除用户
			await prisma.$executeRaw`DELETE FROM test_users WHERE name = ${testName}`;
		};

		// 执行跨数据库事务 - 应该失败
		let failed = false;
		try {
			await withCrossDbTransaction(mainDbFn, platformDbFn, rollbackFn);
		} catch (txError) {
			failed = true;
			logger.info("事务预期失败，错误信息:", txError);
		}

		// 验证结果
		assert(failed, "事务应该失败");
		assert(rollbackCalled, "回滚函数应该被调用");

		// 确认主数据库中的数据被回滚
		const mainDbUser =
			await prisma.$queryRaw`SELECT * FROM test_users WHERE name = ${testName}`;
		assert(
			Array.isArray(mainDbUser) && mainDbUser.length === 0,
			"主数据库中不应该存在测试用户(应已回滚)",
		);

		logger.info("平台失败回滚测试场景完成");
	} catch (error) {
		logger.error(error, "平台失败回滚测试场景失败");
		assert(
			false,
			`平台失败回滚测试场景失败: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

/**
 * 测试场景3: 回滚操作本身失败
 */
async function testRollbackFailure() {
	const testName = `test_rollback_fail_${Date.now()}`;
	logger.info(`🧪 开始测试: 回滚操作失败的情况 (${testName})`);

	try {
		// 主数据库函数
		const mainDbFn = async (tx: Prisma.TransactionClient) => {
			const user = await tx.$executeRaw`
        INSERT INTO test_users (name, email) VALUES (${testName}, '<EMAIL>')
        RETURNING *
      `;
			return { name: testName, complexData: { nested: "data" } };
		};

		// 平台数据库函数 - 故意抛出错误
		const platformDbFn = async (
			result: Record<string, unknown>,
			platformTx?: Prisma.TransactionClient,
		) => {
			throw new Error("模拟平台数据库操作失败");
		};

		// 回滚函数 - 故意失败
		const rollbackFn = async (
			result: Record<string, unknown>,
			error: unknown,
		) => {
			logger.info("回滚函数被调用，但将故意失败");
			throw new Error("模拟回滚操作失败");
		};

		// 执行跨数据库事务 - 应该失败
		let failed = false;
		try {
			await withCrossDbTransaction(mainDbFn, platformDbFn, rollbackFn);
		} catch (txError) {
			failed = true;
			logger.info("事务预期失败，错误信息:", txError);
		}

		// 验证结果
		assert(failed, "事务应该失败");

		// 确认主数据库中的数据未被回滚(因为回滚失败)
		const mainDbUser =
			await prisma.$queryRaw`SELECT * FROM test_users WHERE name = ${testName}`;
		assert(
			Array.isArray(mainDbUser) && mainDbUser.length > 0,
			"主数据库中应该存在测试用户(回滚失败)",
		);

		// 手动清理，以避免影响其他测试
		await prisma.$executeRaw`DELETE FROM test_users WHERE name = ${testName}`;

		logger.info("回滚失败测试场景完成");
	} catch (error) {
		logger.error(error, "回滚失败测试场景失败");
		assert(
			false,
			`回滚失败测试场景失败: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

/**
 * 测试场景4: 模拟真实业务场景 - 资金转账
 */
async function testFundTransfer() {
	const testName = `test_fund_transfer_${Date.now()}`;
	logger.info(`🧪 开始测试: 模拟资金转账业务场景 (${testName})`);

	try {
		// 准备测试数据
		const userId = 999;
		const amount = 1000;
		const userName = `${testName}_user`;
		const channelName = `${testName}_channel`;

		// 创建测试用户和渠道
		await prisma.$executeRaw`
      INSERT INTO test_users (id, name, email) VALUES (${userId}, ${userName}, '<EMAIL>')
    `;

		if (platformPrisma) {
			await platformPrisma.$executeRaw`
        INSERT INTO test_channels (name, balance) VALUES (${channelName}, 5000)
      `;
		}

		// 主数据库函数 - 模拟用户资金增加
		const mainDbFn = async (tx: Prisma.TransactionClient) => {
			await tx.$executeRaw`
        UPDATE test_users SET email = '<EMAIL>' WHERE id = ${userId}
      `;
			return { userId, amount, successful: true };
		};

		// 平台数据库函数 - 成功或失败场景
		const platformDbFn = async (
			result: Record<string, unknown>,
			platformTx?: Prisma.TransactionClient,
		) => {
			if (!platformTx) {
				throw new Error("平台事务对象未传递");
			}

			// 模拟更新渠道余额
			await platformTx.$executeRaw`
        UPDATE test_channels SET balance = balance - ${amount} WHERE name = ${channelName}
      `;

			// 随机决定成功或失败
			const shouldFail = Math.random() > 0.5;

			if (shouldFail) {
				throw new Error("模拟渠道资金操作随机失败");
			}

			return { channelName, amount, successful: true };
		};

		// 回滚函数
		const rollbackFn = async (
			result: Record<string, unknown>,
			error: unknown,
		) => {
			logger.info("资金转账回滚函数被调用");

			// 回滚用户资金更新
			await prisma.$executeRaw`
        UPDATE test_users SET email = '<EMAIL>' WHERE id = ${userId}
      `;
		};

		// 执行跨数据库事务
		let transactionSuccess = true;
		try {
			await withCrossDbTransaction(mainDbFn, platformDbFn, rollbackFn);
		} catch (error) {
			transactionSuccess = false;
			logger.info("资金转账事务失败", error);
		}

		// 验证结果
		if (transactionSuccess) {
			// 事务成功 - 两边数据都应该更新
			const user =
				await prisma.$queryRaw`SELECT * FROM test_users WHERE id = ${userId}`;
			assert(
				Array.isArray(user) &&
					user.length > 0 &&
					user[0].email === "<EMAIL>",
				"用户资金应该被成功更新",
			);

			if (platformPrisma) {
				const channel = await platformPrisma.$queryRaw`
          SELECT * FROM test_channels WHERE name = ${channelName}
        `;
				assert(
					Array.isArray(channel) &&
						channel.length > 0 &&
						Number(channel[0].balance) === 4000,
					"渠道余额应该被成功更新",
				);
			}
		} else {
			// 事务失败 - 主数据库应该回滚
			const user =
				await prisma.$queryRaw`SELECT * FROM test_users WHERE id = ${userId}`;
			assert(
				Array.isArray(user) &&
					user.length > 0 &&
					user[0].email === "<EMAIL>",
				"用户资金更新应该被回滚",
			);
		}

		// 清理测试数据
		await prisma.$executeRaw`DELETE FROM test_users WHERE id = ${userId}`;
		if (platformPrisma) {
			await platformPrisma.$executeRaw`DELETE FROM test_channels WHERE name = ${channelName}`;
		}

		logger.info("资金转账测试场景完成");
	} catch (error) {
		logger.error(error, "资金转账测试场景失败");
		assert(
			false,
			`资金转账测试场景失败: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

/**
 * 测试场景5: 主数据库操作失败，不应执行平台操作
 */
async function testMainDbFailure() {
	const testName = `test_main_fail_${Date.now()}`;
	logger.info(`🧪 开始测试: 主数据库失败场景 (${testName})`);

	let platformFnCalled = false;

	try {
		// 主数据库函数 - 故意失败
		const mainDbFn = async (tx: Prisma.TransactionClient) => {
			throw new Error("模拟主数据库操作失败");
		};

		// 平台数据库函数 - 不应被调用
		const platformDbFn = async (
			result: Record<string, unknown>,
			platformTx?: Prisma.TransactionClient,
		) => {
			platformFnCalled = true;
			return { success: true };
		};

		// 回滚函数 - 不应被调用
		const rollbackFn = async (
			result: Record<string, unknown>,
			error: unknown,
		) => {
			logger.error("回滚函数不应该被调用，因为主事务就失败了");
		};

		// 执行跨数据库事务 - 应该失败
		let failed = false;
		try {
			await withCrossDbTransaction(mainDbFn, platformDbFn, rollbackFn);
		} catch (error) {
			failed = true;
			logger.info("事务预期失败，错误信息:", error);
		}

		// 验证结果
		assert(failed, "事务应该失败");
		assert(!platformFnCalled, "平台数据库函数不应该被调用");

		logger.info("主数据库失败测试场景完成");
	} catch (error) {
		logger.error(error, "主数据库失败测试场景失败");
		assert(
			false,
			`主数据库失败测试场景失败: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

/**
 * 测试场景6: 模拟真实业务场景 - 资金审核转账
 * 这个测试基于 auditService.ts 中的 processFundTransferWithChannel 函数
 */
async function testAuditFundTransfer() {
	const testName = `test_audit_transfer_${Date.now()}`;
	logger.info(`🧪 开始测试: 模拟资金审核转账业务场景 (${testName})`);

	try {
		// 准备测试数据
		const userId = 888;
		const amount = 2000;
		const userName = `${testName}_user`;
		const channelId = 101;

		// 创建测试用户和渠道
		await prisma.$executeRaw`
      INSERT INTO test_users (id, name, email) VALUES (${userId}, ${userName}, '<EMAIL>')
    `;

		if (platformPrisma) {
			await platformPrisma.$executeRaw`
        INSERT INTO test_channels (id, name, balance) 
        VALUES (${channelId}, '${testName}_channel', 10000)
      `;
		}

		// 主数据库函数 - 模拟用户资金操作
		const mainDbFn = async (tx: Prisma.TransactionClient) => {
			// 模拟更新用户资金
			await tx.$executeRaw`
        UPDATE test_users SET email = '<EMAIL>' WHERE id = ${userId}
      `;

			// 模拟 processFundTransferCore 函数的返回
			return {
				user_id: userId,
				amount,
				operation: "DEPOSIT",
				currency: "CNY",
				audit_id: 12345,
			};
		};

		// 平台数据库函数 - 模拟通道资金记录
		const platformDbFn = async (
			auditData: Record<string, unknown>,
			platformTx?: Prisma.TransactionClient,
		) => {
			if (!platformTx) {
				throw new Error("平台事务对象未传递");
			}

			// 模拟创建通道交易记录
			await platformTx.$executeRaw`
        INSERT INTO test_channels (name, balance) 
        VALUES ('transaction_record', ${amount})
      `;

			// 50%概率模拟失败
			const shouldFail = Math.random() > 0.5;
			if (shouldFail) {
				throw new Error("模拟通道资金操作失败");
			}

			return auditData;
		};

		// 回滚函数 - 模拟资金回滚
		const rollbackFn = async (
			auditData: Record<string, unknown>,
			error: unknown,
		) => {
			logger.info("资金审核回滚函数被调用");

			// 记录回滚日志
			logger.info(
				{
					audit_id: auditData.audit_id,
					user_id: auditData.user_id,
					operation: auditData.operation,
					amount: auditData.amount,
					error: error instanceof Error ? error.message : String(error),
				},
				"执行资金回滚",
			);

			// 回滚用户资金更新
			await prisma.$executeRaw`
        UPDATE test_users SET email = '<EMAIL>' WHERE id = ${userId}
      `;
		};

		// 执行跨数据库事务
		let transactionSuccess = true;
		try {
			await withCrossDbTransaction(mainDbFn, platformDbFn, rollbackFn);
		} catch (error) {
			transactionSuccess = false;
			logger.info("资金审核事务失败", error);
		}

		// 验证结果
		if (transactionSuccess) {
			// 事务成功 - 用户资金应该更新
			const user =
				await prisma.$queryRaw`SELECT * FROM test_users WHERE id = ${userId}`;
			assert(
				Array.isArray(user) &&
					user.length > 0 &&
					user[0].email === "<EMAIL>",
				"用户资金应该被成功更新",
			);

			// 平台数据库应该有新的交易记录
			if (platformPrisma) {
				const transactions = await platformPrisma.$queryRaw`
          SELECT * FROM test_channels WHERE name = 'transaction_record'
        `;
				assert(
					Array.isArray(transactions) && transactions.length > 0,
					"平台数据库应该有新的交易记录",
				);
			}
		} else {
			// 事务失败 - 用户资金应该回滚
			const user =
				await prisma.$queryRaw`SELECT * FROM test_users WHERE id = ${userId}`;
			assert(
				Array.isArray(user) &&
					user.length > 0 &&
					user[0].email === "<EMAIL>",
				"用户资金应该被回滚",
			);
		}

		// 清理测试数据
		await prisma.$executeRaw`DELETE FROM test_users WHERE id = ${userId}`;
		if (platformPrisma) {
			await platformPrisma.$executeRaw`DELETE FROM test_channels WHERE id = ${channelId}`;
			await platformPrisma.$executeRaw`DELETE FROM test_channels WHERE name = 'transaction_record'`;
		}

		logger.info("资金审核转账测试场景完成");
	} catch (error) {
		logger.error(error, "资金审核转账测试场景失败");
		assert(
			false,
			`资金审核转账测试场景失败: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

/**
 * 主测试函数
 */
async function runTests() {
	logger.info("开始跨数据库事务测试...");

	if (!platformPrisma) {
		logger.warn("警告: 平台数据库未配置，部分测试将被跳过或模拟");
	}

	try {
		// 设置测试表
		await setupTestTables();

		// 清理测试数据
		await cleanupTestData();

		// 执行测试用例
		await testSuccessfulTransaction();
		await testPlatformFailureWithRollback();
		await testRollbackFailure();
		await testFundTransfer();
		await testMainDbFailure();
		await testAuditFundTransfer();

		// 测试结果汇总
		logger.info("\n=== 测试结果汇总 ===");
		logger.info(`总测试数: ${testResults.total}`);
		logger.info(`通过: ${testResults.passed}`);
		logger.info(`失败: ${testResults.failed}`);

		// 打印最终结果
		if (testResults.failed === 0) {
			logger.info("✅ 所有测试通过!");
		} else {
			logger.error(`❌ ${testResults.failed} 个测试失败!`);
		}
	} catch (error) {
		logger.error(error, "测试执行过程中发生错误");
	} finally {
		// 清理测试数据
		await cleanupTestData();

		// 关闭数据库连接
		await prisma.$disconnect();
		if (platformPrisma) {
			await platformPrisma.$disconnect();
		}
	}
}

// 执行测试
runTests().catch((error) => {
	logger.error(error, "测试运行失败");
	process.exit(1);
});
