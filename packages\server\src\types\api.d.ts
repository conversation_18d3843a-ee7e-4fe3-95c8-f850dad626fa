export type DailyRecord = [string?, string?, number?]; // [ts_code, trade_date, close]
export interface LimitRecord {
	trade_date: string;
	ts_code: string;
	pre_close?: number;
	up_limit: number;
	down_limit: number;
}
export type DailyData = DailyRecord[];
export type LimitData = LimitRecord[];

// 添加新的类型定义
export type KnockoutResult = {
	ts_code: string;
	knockoutStartDate: string; // YYYYMMDD 格式
	consecutiveDays: number; // 连续涨跌停天数
	isLimitUp: boolean; // true 表示连续涨停，false 表示连续跌停
};
