import type { <PERSON>risma } from "@prisma/client";
import * as Audit from "@/models/audit.js";
import * as FundService from "../fundService.js";
import * as NotifService from "../notifService.js";
import * as User from "@/models/user.js";
import * as AuditService from "@/services/auditService.js";
import { AppError } from "@/core/appError.js";
import { withTransaction } from "@/core/dbTxnManager.js";
import logger from "@/utils/logger.js";
import {
	NotificationType,
	Currency,
	TransactionType,
	AuditStatus,
	AuditType,
	WebSocketMessageType,
} from "@packages/shared";
import type {
	AuditData,
	FundAuditData,
	QualificationAuditData,
} from "@packages/shared";
import EmailService from "@/utils/email.js";
import { notifyUser } from "@/utils/notify.js";

async function handleApprovedAudit(
	audit: AuditData,
	client: Prisma.TransactionClient,
	comment?: string,
) {
	switch (audit.type) {
		case AuditType.QUALIFICATION:
			await processQualification(audit, client, comment);
			break;
		case AuditType.FUND:
			await processFundTransfer(audit, client, comment);
			break;
		default:
			throw AppError.create("BAD_REQUEST", "Invalid audit type");
	}
}

async function processQualification(
	audit: QualificationAuditData,
	client: Prisma.TransactionClient,
	comment?: string,
) {
	const { user_id, data } = audit;
	const { name, id_number, phone_number, bank_name, bank_code, bank_account } =
		data;

	const user = await User.updateQualificationInfo(
		user_id,
		{
			name,
			id_number,
			phone_number,
			bank_name,
			bank_code,
			bank_account,
		},
		client,
	);

	// 发送系统内通知 - 这个需要等待完成，因为使用了数据库事务
	await NotifService.sendNotification(
		user_id,
		{
			title: "资质审核通过",
			content: `您的资质审核已通过，现在可以进行交易了。${comment ? `\n${comment}` : ""}`,
			type: NotificationType.ACCOUNT,
			metadata: { type: "qualification" },
		},
		client,
	);

	EmailService.sendEmail(user.email, "AUDIT_NOTIFICATION", {});

	await notifyUser(user_id, {
		type: WebSocketMessageType.AUTH_UPDATE,
	});
}

// 核心逻辑函数
async function processFundTransferCore(
	audit: FundAuditData,
	client: Prisma.TransactionClient,
	comment?: string,
) {
	const { user_id, amount, operation, currency } = audit;
	const currencyObj = currency || Currency.CNY;

	// 处理用户资金变动
	await FundService.handleTransaction(
		{
			user_id,
			amount: Number(amount),
			type: operation,
			currency: currencyObj,
		},
		client,
	);

	// 入金时增加用户累计入金金额
	if (operation === TransactionType.DEPOSIT) {
		let cnyAmount = Number(amount);
		if (currency !== Currency.CNY) {
			const exchangeRate = await FundService.getExchangeRate(
				currency,
				Currency.CNY,
			);
			cnyAmount = Number(amount) * exchangeRate;
		}
		await User.addAccumulatedDeposit(user_id, cnyAmount, client);
	}

	// 发送通知
	const formattedAmount = `${amount} ${currency}`;
	await NotifService.sendNotification(
		user_id,
		{
			title: "审核通过: 资金转账",
			content: `您的资金转账已通过。${comment ? `\n${comment}` : ""}`,
			type: NotificationType.ACCOUNT,
			metadata: { type: "fund_transfer", formattedAmount, operation },
		},
		client,
	);

	return audit;
}

async function processFundTransfer(
	audit: FundAuditData,
	client: Prisma.TransactionClient,
	comment?: string,
) {
	const { user_id, amount, operation } = audit;
	logger.info(`处理资金转账-amount type: ${typeof amount}`);

	try {
		// 复用核心逻辑
		await processFundTransferCore(audit, client, comment);

		// 发送邮件通知（非事务部分）
		const user = await User.findById(user_id);
		if (!user) {
			throw AppError.create("USER_NOT_FOUND", "User not found");
		}

		if (operation === TransactionType.WITHDRAW) {
			EmailService.sendEmail(user.email, "WITHDRAWAL", { amount });
		} else if (operation === TransactionType.DEPOSIT) {
			EmailService.sendEmail(user.email, "DEPOSIT", { amount });
		}
	} catch (error) {
		logger.error(error, "Fund transfer failed");
		throw AppError.create("SERVER_ERROR", "Fund transfer failed");
	}
}

export async function processAudit(
	audit_id: number,
	admin_id: number,
	status: AuditStatus,
	comment?: string,
) {
	// 原始交易台版本处理逻辑
	const processFn = async (tx: Prisma.TransactionClient) => {
		const audit = await Audit.updateStatus(
			audit_id,
			admin_id,
			status,
			comment,
			tx,
		);
		if (!audit) {
			throw AppError.create(
				"NOT_FOUND",
				"Audit request not found or already processed",
			);
		}
		if (status === AuditStatus.APPROVED) {
			await handleApprovedAudit(audit, tx, comment);
		} else if (status === AuditStatus.REJECTED) {
			// 根据审核类型发送不同的拒绝通知
			const title =
				audit.type === AuditType.QUALIFICATION
					? "资质审核未通过"
					: "资金审核未通过";
			const content = `您的${audit.type === AuditType.QUALIFICATION ? "资质" : "资金"}审核未通过。${comment ? `\n${comment}` : ""}`;

			await NotifService.sendNotification(
				audit.user_id,
				{
					title,
					content,
					type: NotificationType.ACCOUNT,
					metadata: {
						type:
							audit.type === AuditType.QUALIFICATION
								? "qualification"
								: "fund_transfer",
					},
				},
				tx,
			);

			if (audit.type === AuditType.QUALIFICATION) {
				await notifyUser(audit.user_id, {
					type: WebSocketMessageType.AUTH_UPDATE,
				});
			}
		}
		return audit;
	};

	return withTransaction(processFn);
}

export async function platformDeposit(user_id: number, amount: number) {
	// 验证用户存在
	const user = await User.findById(user_id);
	if (!user) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}
	// 创建审核请求
	const audit = await AuditService.createFundAudit({
		user_id,
		operation: TransactionType.PLATFORM_DEPOSIT,
		amount,
		currency: Currency.CNY,
	});

	return {
		message: "Platform Deposit Audit Created",
		audit: {
			audit_id: audit.audit_id,
			user_id,
			amount,
			currency: Currency.CNY,
			operation: TransactionType.PLATFORM_DEPOSIT,
			timestamp: new Date().toISOString(),
		},
	};
}
