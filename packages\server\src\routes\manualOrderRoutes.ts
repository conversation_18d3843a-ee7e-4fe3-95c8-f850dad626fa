import { Router } from "express";
import * as manualOrderService from "@/services/manualOrderService.js";
import { wrapUserRoute } from "@/utils/routeWrapper.js";
import type { ManualOrderData, ManualOrderRequest } from "@packages/shared";
import { validateSchema } from "@/middlewares/validateSchema.js";
import { z } from "zod";

const router = Router();

// 创建手动录单的基本验证模式
const manualOrderRequestSchema = z.object({
	ts_code: z.string().min(1),
	entry_price: z.number().positive(),
	exercise_price: z.number().positive(),
	scale: z.number().int().positive(),
	term: z.number().int().positive(),
	structure: z.string().min(1),
	quote: z.number().positive(),
	quote_provider: z.string().min(1),
	entry_date: z.string().optional(),
	expiry_date: z.string().optional(),
	remarks: z.string().optional(),
});

/**
 * 创建手动录单: POST /api/manual-order
 */
router.post(
	"/",
	validateSchema(manualOrderRequestSchema),
	wrapUserRoute<ManualOrderRequest>(async (req, res) => {
		const result = await manualOrderService.createManualOrder({
			...req.body,
			user_id: req.jwt.user_id,
		});
		res.status(201).json(result);
	}),
);

/**
 * 获取用户手动录单列表: GET /api/manual-order/list
 */
router.get(
	"/list",
	wrapUserRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const isDescending = req.query.isDescending !== "false";
		const { ts_codes, status, startDate, endDate } = req.query;

		const result = await manualOrderService.getUserManualOrders(
			req.jwt.user_id,
			page,
			pageSize,
			isDescending,
			{
				ts_codes: ts_codes ? (ts_codes as string).split(",") : undefined,
				status: status ? (status as string).split(",") : undefined,
				startDate: startDate as string,
				endDate: endDate as string,
			},
		);
		res.status(200).json(result);
	}),
);

/**
 * 更新手动录单: PUT /api/manual-order/:id
 */
router.put(
	"/:id",
	wrapUserRoute<Partial<ManualOrderData>>(async (req, res) => {
		const id = Number.parseInt(req.params.id);
		if (Number.isNaN(id)) {
			res.status(400).json({ message: "Invalid manual order ID" });
			return;
		}

		const result = await manualOrderService.updateManualOrder(
			id,
			req.jwt.user_id,
			req.body,
		);
		res.status(200).json(result);
	}),
);

/**
 * 删除手动录单: DELETE /api/manual-order/:id
 */
router.delete(
	"/:id",
	wrapUserRoute(async (req, res) => {
		const id = Number.parseInt(req.params.id);
		if (Number.isNaN(id)) {
			res.status(400).json({ message: "Invalid manual order ID" });
			return;
		}

		await manualOrderService.deleteManualOrder(id, req.jwt.user_id);
		res.status(204).send();
	}),
);

export default router;
