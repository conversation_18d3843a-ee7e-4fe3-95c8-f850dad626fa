import { ENV } from "@/config/configManager.js";
import jwt from "jsonwebtoken";
import * as User from "@/models/user.js";
import { appRedis } from "@/lib/redis.js";
import { REDIS_KEYS } from "@/lib/redis.js";
import { AppError } from "@/core/appError.js";
import logger from "@/utils/logger.js";
import { hashPassword, verifyPassword } from "@/utils/encryption.js";
import type {
	BaseAuthResponse,
	AuthServiceResponseWithCookie,
	UserRefreshJWTPayload,
} from "@packages/shared";
import EmailService from "@/utils/email.js";
import { APP_TYPE } from "@/config/configManager.js";

const JWT_SECRET = ENV.JWT_SECRET;
const JWT_EXPIRES_IN = ENV.JWT_EXPIRES_IN as jwt.SignOptions["expiresIn"];
const REFRESH_TOKEN_EXPIRES_IN =
	ENV.REFRESH_TOKEN_EXPIRES_IN as jwt.SignOptions["expiresIn"];

function parseTimeToSeconds(time: string): number {
	const unit = time.slice(-1);
	const value = Number.parseInt(time.slice(0, -1));

	switch (unit) {
		case "h":
			return value * 3600;
		case "d":
			return value * 86400;
		case "m":
			return value * 60;
		case "s":
			return value;
		default:
			return 3600; // 默认1小时
	}
}

const REFRESH_TOKEN_EXPIRES_MS =
	parseTimeToSeconds(REFRESH_TOKEN_EXPIRES_IN as string) * 1000;

function validatePassword(password: string): {
	isValid: boolean;
	message: string;
} {
	if (password.length < 8) {
		return {
			isValid: false,
			message: "Password must be at least 8 characters long",
		};
	}

	if (!/[a-zA-Z]/.test(password)) {
		return {
			isValid: false,
			message: "Password must contain at least one case letter",
		};
	}

	if (!/[0-9]/.test(password)) {
		return {
			isValid: false,
			message: "Password must contain at least one number",
		};
	}

	return { isValid: true, message: "" };
}

async function addToBlacklist(token: string, exp: number): Promise<void> {
	try {
		// 使用 zadd 添加到有序集合，score 为过期时间
		await appRedis.zadd(REDIS_KEYS.TOKEN_BLACKLIST, exp, token);

		// 异步清理过期 token
		const now = Math.floor(Date.now() / 1000);
		appRedis
			.zremrangebyscore(REDIS_KEYS.TOKEN_BLACKLIST, "-inf", now)
			.catch((err) => {
				logger.error(err, "Error cleaning up token blacklist");
			});
	} catch (error) {
		logger.error(error, "Error adding token to blacklist");
		throw AppError.create("FAILED_LOGOUT", "Failed to logout user");
	}
}

async function isTokenBlacklisted(token: string): Promise<boolean> {
	try {
		const now = Math.floor(Date.now() / 1000);

		// 1. 直接检查 token 是否在集合中且未过期
		const score = await appRedis.zscore(REDIS_KEYS.TOKEN_BLACKLIST, token);

		// 如果 token 不存在或已过期，返回 false
		if (score === null || Number(score) < now) {
			return false;
		}

		return true;
	} catch (error) {
		logger.error(error, "Error checking token blacklist");
		throw AppError.create(
			"FAILED_VERIFY_TOKEN_STATUS",
			"Failed to verify token status",
		);
	}
}

async function registerUser(email: string): Promise<{ message: string }> {
	// Check if email is already registered
	const userByEmail = await User.findByEmail(email);
	if (userByEmail) {
		throw AppError.create("EMAIL_EXISTS", "Email already registered");
	}

	try {
		const verification_code = EmailService.generateCode();
		// Store verification code in Redis with 5 minute expiry
		await appRedis.setex(`verify:email:${email}`, 300, verification_code);

		// Send verification email
		await EmailService.sendEmail(email, "VERIFICATION_CODE", {
			code: verification_code,
		});

		return {
			message:
				"Verification code sent to your email, please complete registration",
		};
	} catch (error) {
		if (error instanceof AppError) throw error;
		logger.error(error, "Email registration error");
		throw AppError.create("REGISTRATION_FAILED", "Failed to register user");
	}
}

async function verifyRegistration(
	email: string,
	password: string,
	sms_code: string,
): Promise<{ message: string }> {
	if (!sms_code) {
		throw AppError.create(
			"CODE_NOT_REQUESTED",
			"Verification code not requested",
		);
	}

	const stored_code = await appRedis.get(`verify:email:${email}`);
	if (!stored_code || sms_code !== stored_code) {
		throw AppError.create("INVALID_CODE", "Invalid verification code");
	}

	// Double check email not registered before creating user
	const existingUser = await User.findByEmail(email);
	if (existingUser) {
		throw AppError.create("EMAIL_EXISTS", "Email already registered");
	}

	const { isValid, message } = validatePassword(password);
	if (!isValid) {
		throw AppError.create("INVALID_PASSWORD_FORMAT", message);
	}

	const password_hash = await hashPassword(password);
	await User.create(email, password_hash);

	await appRedis.del(`verify:email:${email}`);

	logger.info(`User registered successfully: ${email}`);
	return { message: "User registered successfully" };
}

async function loginUser(
	email: string,
	password: string,
): Promise<AuthServiceResponseWithCookie> {
	// 查找用户
	const user = await User.findByEmail(email);

	if (!user) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}

	const passwordHash = await User.getPasswordHashByEmail(email);

	if (!passwordHash) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}

	const isPasswordValid = await verifyPassword(password, passwordHash);
	if (!isPasswordValid) {
		throw AppError.create("INVALID_PASSWORD", "Invalid password");
	}

	// Record user active timestamp
	await appRedis.zadd("user:active", Date.now(), user.user_id.toString());

	// Token 只包含认证必需信息
	const accessToken = jwt.sign(
		{
			user_id: user.user_id,
			type: "access",
			is_qualified: user.is_qualified,
			app_type: APP_TYPE,
		},
		JWT_SECRET,
		{ expiresIn: JWT_EXPIRES_IN },
	);

	const refreshToken = jwt.sign(
		{
			user_id: user.user_id,
			type: "refresh",
			app_type: APP_TYPE,
		},
		JWT_SECRET,
		{ expiresIn: REFRESH_TOKEN_EXPIRES_IN },
	);

	logger.info(`User logged in successfully: ${email}`);

	return {
		headers: {
			authorization: `Bearer ${accessToken}`,
		},
		body: {
			message: "登录成功",
			username: user.name,
			can_transfer: user.can_transfer,
		},
		cookies: [
			{
				name: "refresh_token",
				value: refreshToken,
				options: {
					httpOnly: true,
					secure: ENV.NODE_ENV !== "development",
					sameSite: "strict",
					maxAge: REFRESH_TOKEN_EXPIRES_MS,
				},
			},
		],
	};
}

async function refreshToken(
	refresh_token: string,
): Promise<AuthServiceResponseWithCookie> {
	try {
		logger.info("Starting token refresh...");

		// 1. 检查 token 是否在黑名单中
		logger.info("Checking blacklist...");
		const isBlacklisted = await isTokenBlacklisted(refresh_token);
		if (isBlacklisted) {
			throw AppError.create("TOKEN_EXPIRED", "Token has been revoked");
		}
		logger.info("Blacklist check passed");

		// 2. 验证 refresh token
		logger.info("Verifying refresh token...");
		const decoded = jwt.verify(
			refresh_token,
			JWT_SECRET,
		) as UserRefreshJWTPayload;
		logger.info("Token verified successfully");

		// 3. 确认是 refresh token
		if (decoded.type !== "refresh") {
			throw AppError.create("INVALID_TOKEN", "Invalid token type");
		}

		// 更新用户活跃状态
		logger.info("Updating user active status...");
		await appRedis.zadd("user:active", Date.now(), decoded.user_id.toString());
		logger.info("User active status updated");

		// Get updated qualification status
		logger.info("Fetching user data...");
		const user = await User.findById(decoded.user_id);
		if (!user) {
			throw AppError.create("USER_NOT_FOUND", "User not found");
		}
		logger.info("User data fetched");

		// Generate new access token with updated qualification status
		const newAccessToken = jwt.sign(
			{
				user_id: user.user_id,
				type: "access",
				is_qualified: user.is_qualified,
				app_type: APP_TYPE,
			},
			JWT_SECRET,
			{ expiresIn: JWT_EXPIRES_IN },
		);

		// Generate new refresh token（滑动过期）
		const newRefreshToken = jwt.sign(
			{
				user_id: user.user_id,
				type: "refresh",
				app_type: APP_TYPE,
			},
			JWT_SECRET,
			{ expiresIn: REFRESH_TOKEN_EXPIRES_IN },
		);

		return {
			headers: {
				authorization: `Bearer ${newAccessToken}`,
			},
			body: {
				message: "Token refreshed successfully",
				username: user.name,
				can_transfer: user.can_transfer,
			},
			cookies: [
				{
					name: "refresh_token",
					value: newRefreshToken,
					options: {
						httpOnly: true,
						secure: ENV.NODE_ENV !== "development",
						sameSite: "strict",
						maxAge: REFRESH_TOKEN_EXPIRES_MS,
					},
				},
			],
		};
	} catch (error) {
		logger.warn(error, "Token refresh failed");
		throw AppError.create("INVALID_TOKEN", "Invalid or malformed token");
	}
}

async function logout(refresh_token?: string): Promise<BaseAuthResponse> {
	try {
		// 如果有 refresh token,就将其加入黑名单
		if (refresh_token) {
			try {
				const decoded = jwt.verify(
					refresh_token,
					JWT_SECRET,
				) as UserRefreshJWTPayload;

				if (decoded.type === "refresh") {
					await addToBlacklist(refresh_token, decoded.exp);
				}
			} catch (error) {
				// 即使 token 验证失败也继续执行登出流程
				logger.warn(
					`Invalid refresh token during logout: ${error instanceof Error ? error.message : String(error)}`,
				);
			}
		}

		return {
			message: "Logged out successfully",
			username: "",
			can_transfer: false,
		};
	} catch (error) {
		throw AppError.create("FAILED_LOGOUT", `Failed to logout user: ${error}`);
	}
}

async function changePassword(
	user_id: number,
	oldPassword: string,
	newPassword: string,
): Promise<void> {
	const user = await User.findById(user_id);
	if (!user) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}

	// 获取当前密码哈希
	const currentPasswordHash = await User.getPasswordHashByPhoneNumber(
		user.phone_number,
	);
	if (!currentPasswordHash) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}

	// 验证旧密码
	const isPasswordValid = await verifyPassword(
		oldPassword,
		currentPasswordHash,
	);
	if (!isPasswordValid) {
		throw AppError.create("INVALID_PASSWORD", "Current password is incorrect");
	}

	// 生成新密码的哈希值并更新
	const newPasswordHash = await hashPassword(newPassword);
	await User.updatePassword(user.user_id, newPasswordHash);
}

async function sendResetCode(email: string): Promise<{ message: string }> {
	const user = await User.findByEmail(email);

	if (!user) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}

	try {
		// 发送邮件验证码
		const verification_code = EmailService.generateCode();
		await appRedis.setex(`reset:email:${email}`, 300, verification_code); // 5分钟过期

		await EmailService.sendEmail(email, "VERIFICATION_CODE", {
			code: verification_code,
		});

		return {
			message: "Reset code sent to your email, please complete verification",
		};
	} catch (error) {
		if (error instanceof AppError) throw error;
		logger.error(error, "Reset password error");
		throw AppError.create("RESET_FAILED", "Failed to send reset code");
	}
}

async function verifyResetPassword(
	email: string,
	sms_code: string,
	new_password: string,
): Promise<{ message: string }> {
	const user = await User.findByEmail(email);

	if (!user) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}

	if (!sms_code) {
		throw AppError.create("CODE_NOT_REQUESTED", "Reset code not requested");
	}

	const stored_code = await appRedis.get(`reset:email:${email}`);

	if (!stored_code || sms_code !== stored_code) {
		throw AppError.create("INVALID_CODE", "Invalid reset code");
	}

	const { isValid, message } = validatePassword(new_password);
	if (!isValid) {
		throw AppError.create("INVALID_PASSWORD_FORMAT", message);
	}

	const password_hash = await hashPassword(new_password);
	await User.updatePassword(user.user_id, password_hash);

	// 清理验证码
	await appRedis.del(`reset:email:${email}`);

	logger.info(`Password reset successfully for user: ${email}`);
	return { message: "Password reset successfully" };
}

export {
	registerUser,
	verifyRegistration,
	loginUser,
	refreshToken,
	logout,
	changePassword,
	sendResetCode,
	verifyResetPassword,
};
