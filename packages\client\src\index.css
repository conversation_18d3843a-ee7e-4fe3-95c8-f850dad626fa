@import './assets/fonts/font-face.css';


/* 默认 */
:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  background-color: var(--el-bg-color-overlay);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  --box-shadow: 0 2px 4px rgba(var(--shadow-color), 0.12);
  --box-shadow-card: 4px 8px 12px rgba(var(--shadow-color), 0.12);
  --box-shadow-button: 2px 4px 8px rgba(0, 0, 0, 0.2);

  /* 通用 主题色 */
  --hover-bg-color: rgba(0, 0, 0, 0.1);
  --icon-color: #444;
  --text-color: #213547;
  --text-color-call: #003300;
  --text-color-put: #330000;

  --shadow-color: 0, 0, 0;

  --color-surface: #f5f5f5;
  --color-on-surface: #000;
  --theme-logo-color: var(--color-on-surface, #333);
}

/* 暗色模式 */
html.dark {
  --hover-bg-color: rgba(255, 255, 255, 0.1);
  --icon-color: #ccc;
  --text-color: rgba(255, 255, 255, 0.87);
  --text-color-call: #e6ffe6;
  --text-color-put: #ffe6e6;

  --shadow-color: 128, 128, 128;

  --color-surface: #1a1c1e;
  --color-on-surface: #e2e2e6;
  --theme-logo-color: var(--color-on-surface, #fff);

  input::placeholder {
    color: #aaa;
  }
}

.card {
  color: var(--color-on-surface);
  padding: 2rem;
  margin: 1rem;
  box-shadow: var(--box-shadow);
}

.card {
  @media (hover: hover) {
    &:hover {
      box-shadow: var(--box-shadow-card);
    }
  }
}

.card,
button {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  -webkit-user-select: none;
  user-select: none;
}

.card:has(> .card-header) {
  padding-top: 1rem;
}

.card-title {
  text-align: left;
}

.card button:hover {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

button {
  background-color: var(--el-color-primary-light-3);
  color: var(--el-color-text-regular);
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1rem;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: border-color 0.25s;
}

button:hover {
  box-shadow: var(--box-shadow-button);
}

button:focus,
button:focus-visible {
  outline: none;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  font-size: 1rem;
  line-height: 1.5;
}

button svg {
  display: block;
}

h1 {
  font-size: 2rem;
  line-height: 1.1;
}

h2 {
  font-size: 1.5rem;
}

input {
  color: var(--el-text-color-primary);
}

input:focus-visible {
  outline: 1px solid #666;
}

#app {
  width: 100%;
  height: 100%;
  text-align: center;
}

table {
  width: 100%;
  border-collapse: collapse;
}

table th,
table td {
  padding: 10px;
  text-align: center;
  font-size: 14px;
  position: relative;
}

table tr {
  position: relative;
}

table thead tr {
  font-weight: bold;
}

table thead tr,
table tbody tr:nth-child(2n) {
  background-color: var(--el-color-primary-light-3);
  color: var(--el-color-text-regular);
}

table tbody tr {
  background-color: var(--el-color-primary-light-5);
}

table tr:hover {
  transform: scale(1.01);
  box-shadow: var(--box-shadow);
  z-index: 1;
}

/* 表格悬浮行边框跟随放大，但会导致 el-date-picker 显示异常 */
/* table tr::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid var(--el-border-color);
  pointer-events: none;
} */

table tbody tr:not(:hover)::before {
  border-top: none;
}

/* 可以放大的间隙边框，应用后看不到间隙底部背景 */
table tr td:not(:first-child)::before,
table tr th:not(:first-child)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-left: 1px solid var(--el-border-color);
  pointer-events: none;
}

table td:has(button) {
  padding: 5px 10px;
}

table button {
  padding: 0.2em 0.5em;
  border-radius: 0;
}

.form-group label {
  white-space: nowrap;
}

.balance-panel,
.transaction-panel {
  flex: 1;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  appearance: textfield;
}

input[type='number']:disabled {
  background-color: var(--color-surface);
  color: var(--color-on-surface);
  cursor: not-allowed;
}
