import dotenv from "dotenv";
import { join } from "node:path";
import { z } from "zod";
import { AppType } from "@packages/shared";
// 加载基础环境变量
const cwd = process.cwd();
const isInRootDir = !cwd.endsWith("server");
const envPath = isInRootDir
	? join(cwd, "packages", "server", ".env")
	: join(cwd, ".env");

dotenv.config({ path: envPath });
console.log("CWD:", cwd);
console.log("Loading env from:", envPath);

// 环境变量模式定义
const envSchema = z.object({
	// 只保留关键敏感信息
	ENV_ID: z.string().default("local"),
	DB_PASSWORD: z.string().default(""),
	REDIS_PASSWORD: z.string().default(""),
	MYSQL_PASSWORD: z.string().default(""),
	JWT_SECRET: z.string().default("dev-secret"),
	ENCRYPTION_KEY: z.string().default(""),
	TUSHARE_TOKEN: z.string().default(""),
	NOWAPI_SIGN: z.string().default(""),
	MAIRUI_LICENSE: z.string().default(""),
	EMAIL_PASS: z.string().default(""),
	OPEN_API_KEY: z.string().default(""),
	// 环境特定配置从环境变量读取
	PORT: z.preprocess((val) => (val ? Number(val) : 3000), z.number()),
	TRADING_PLATFORM_ID: z.string().default(""),
	CHANNEL_ID: z.string().default(""),
	NODE_ENV: z.string().default("production"),
	IS_DEMO: z.coerce.boolean().default(false),
	IS_DOCKER_DEV: z.coerce.boolean().default(false),
});

// 解析环境变量
const envVars = envSchema.parse(process.env);

// 定义硬编码的默认配置值（替代原来从baseConfig.json读取的内容）
const defaultConfigValues = {
	DB_HOST: "localhost",
	DB_PORT: 5432,
	USE_PGBOUNCER: true,
	PGBOUNCER_HOST: "localhost",
	PGBOUNCER_PORT: 6432,
	REDIS_HOST: "localhost",
	REDIS_PORT: 6379,
	MYSQL_HOST: "localhost",
	MYSQL_PORT: 3306,
	MYSQL_DATABASE: "ink_dev",
	MYSQL_USER: "root",
	JWT_EXPIRES_IN: "1h",
	REFRESH_TOKEN_EXPIRES_IN: "1d",
	ADMIN_USERNAME: "admin",
	ADMIN_PASSWORD: "admin123",
	UPLOAD_DIR: "/var/uploads",
	EMAIL_HOST: "smtp.yunyou.top",
	EMAIL_PORT: 465,
	EMAIL_SECURE: true,
	EMAIL_USER: "<EMAIL>",
	EMAIL_USER_BACKUP: "<EMAIL>",
	DEVELOPER_EMAIL: "<EMAIL>",
};

// 加载配置
export function loadConfig() {
	// 合并默认配置与环境变量配置
	const config = {
		...defaultConfigValues,
		// 从 envVars 直接获取所有配置
		DB_PASSWORD: envVars.DB_PASSWORD,
		REDIS_PASSWORD: envVars.REDIS_PASSWORD,
		MYSQL_PASSWORD: envVars.MYSQL_PASSWORD,
		JWT_SECRET: envVars.JWT_SECRET,
		ENCRYPTION_KEY: envVars.ENCRYPTION_KEY,
		TUSHARE_TOKEN: envVars.TUSHARE_TOKEN,
		NOWAPI_SIGN: envVars.NOWAPI_SIGN,
		MAIRUI_LICENSE: envVars.MAIRUI_LICENSE,
		EMAIL_PASS: envVars.EMAIL_PASS,
		OPEN_API_KEY: envVars.OPEN_API_KEY,
		PORT: envVars.PORT,
		TRADING_PLATFORM_ID: envVars.TRADING_PLATFORM_ID,
		CHANNEL_ID: envVars.CHANNEL_ID,
		NODE_ENV: envVars.NODE_ENV,
		IS_DEMO: envVars.IS_DEMO,
		IS_DOCKER_DEV: envVars.IS_DOCKER_DEV,
	};

	// 开发环境端口转发绑定前缀 `3` !
	if (config.NODE_ENV === "development") {
		config.DB_PORT = Number(`3${config.DB_PORT}`);
		config.PGBOUNCER_PORT = Number(`3${config.PGBOUNCER_PORT}`);
		config.REDIS_PORT = Number(`3${config.REDIS_PORT}`);
		config.MYSQL_PORT = Number(`3${config.MYSQL_PORT}`);
	}

	// Docker开发环境
	if (config.IS_DOCKER_DEV) {
		config.DB_HOST = "host.docker.internal";
		config.REDIS_HOST = "host.docker.internal";
		config.MYSQL_HOST = "host.docker.internal";
	}

	return config;
}

// 导出当前环境配置
// 建议尽量避免默认导出（ export default ）非类或类实例的变量，特别是配置对象这类全局状态
export const ENV = loadConfig();

// 应用配置
export const APP_CONFIG = {
	// 交易台ID (仅交易台版本使用)
	tradingPlatformId: ENV.TRADING_PLATFORM_ID || "",

	// 通道ID (仅通道版本使用)
	channelId: ENV.CHANNEL_ID || "",
};

// 应用类型
export const APP_TYPE = APP_CONFIG.tradingPlatformId
	? AppType.TRADING_PLATFORM
	: AppType.CHANNEL;

// 判断当前应用是否为交易台版本
export function isTradingPlatform() {
	return APP_CONFIG.tradingPlatformId !== "";
}

// 判断当前应用是否为通道版本
export function isChannel() {
	return APP_CONFIG.channelId !== "";
}
