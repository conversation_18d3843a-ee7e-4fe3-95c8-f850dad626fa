/**
 * Fetch the current stock price.
 * @param ts_code Stock code
 * @returns Current stock price
 */
export async function fetchCurrentPrice(
	ts_code: string,
): Promise<number | undefined> {
	try {
		const s_query = `s_${ts_code.toLowerCase().split(".").reverse().join("")}`;
		const response = await fetch(`https://qt.gtimg.cn/q=${s_query}`);
		const data = await response.text();
		const dataArray = data.split("~");
		return Number.parseFloat(dataArray[3]);
	} catch (error) {
		console.error(`Error fetching current price for ${ts_code}:`, error);
	}
}

export async function fetchCurrentPrices(
	ts_codes: string[],
): Promise<number[]> {
	try {
		// 转换所有股票代码为查询格式并用逗号连接
		const s_query = ts_codes
			.map((code) => `s_${code.toLowerCase().split(".").reverse().join("")}`)
			.join(",");

		const response = await fetch(`https://qt.gtimg.cn/q=${s_query}`);
		const data = await response.text();

		// 分号分隔多条数据
		return (
			data
				.split(";")
				// 过滤掉最后一个空字符串
				.filter((item) => item.trim())
				// 处理每条数据
				.map((item) => Number.parseFloat(item.split("~")[3]))
		);
	} catch (error) {
		console.error("Error fetching current prices:", error);
		return [];
	}
}
