import { Router } from "express";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";

/**
 * 意向客户管理路由
 */
const router = Router();

/**
 * @api {get} /admin/channel/leads 获取意向客户列表
 * @apiName GetLeads
 * @apiGroup ChannelLeads
 * @apiPermission BASIC
 *
 * @apiParam {Number} [page=1] 页码
 * @apiParam {Number} [pageSize=10] 每页条数
 * @apiParam {String} [sortBy=created_at] 排序字段
 * @apiParam {String} [sortOrder=DESC] 排序方向：ASC或DESC
 * @apiParam {Number} [id] 按ID筛选
 * @apiParam {String} [phone_number] 按手机号筛选
 * @apiParam {String} [source] 按来源筛选
 * @apiParam {String} [created_at_start] 创建时间起始（YYYY-MM-DD）
 * @apiParam {String} [created_at_end] 创建时间截止（YYYY-MM-DD）
 *
 * @apiSuccess {Object[]} items 意向客户列表
 * @apiSuccess {Number} total 总记录数
 */
router.get(
	"/",
	wrapAdminRoute(async (req, res) => {
		try {
			// 目前仅返回空数据
			res.json({
				items: [],
				total: 0,
			});
		} catch (error) {
			console.error("Error fetching leads:", error);
			res.status(500).json({ error: "Failed to fetch leads" });
		}
	}),
);

/**
 * @api {get} /admin/channel/lead/:id 获取意向客户详情
 * @apiName GetLeadById
 * @apiGroup ChannelLeads
 * @apiPermission BASIC
 *
 * @apiParam {Number} id 意向客户ID
 *
 * @apiSuccess {Object} lead 意向客户详情
 */
router.get(
	"/:id",
	wrapAdminRoute(async (req, res) => {
		try {
			// 目前仅返回空数据
			res.status(404).json({ error: "Lead not found" });
		} catch (error) {
			console.error("Error fetching lead:", error);
			res.status(500).json({ error: "Failed to fetch lead" });
		}
	}),
);

/**
 * @api {get} /admin/channel/leads/export 导出意向客户数据
 * @apiName ExportLeads
 * @apiGroup ChannelLeads
 * @apiPermission BASIC
 *
 * @apiParam {String} [phone_number] 按手机号筛选
 * @apiParam {String} [source] 按来源筛选
 * @apiParam {String} [created_at_start] 创建时间起始（YYYY-MM-DD）
 * @apiParam {String} [created_at_end] 创建时间截止（YYYY-MM-DD）
 *
 * @apiSuccess {Object[]} leads 意向客户数据
 */
router.get(
	"/export",
	wrapAdminRoute(async (req, res) => {
		try {
			// 目前仅返回空数据
			res.json([]);
		} catch (error) {
			console.error("Error exporting leads:", error);
			res.status(500).json({ error: "Failed to export leads" });
		}
	}),
);

export default router;
