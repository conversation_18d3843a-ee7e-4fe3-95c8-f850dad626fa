import { Router } from "express";
import { wrapUserRoute } from "@/utils/routeWrapper.js";
import * as positionService from "@/services/positionService.js";

const router = Router();

/**
 * Get user positions route: GET /api/position
 */
router.get(
	"/",
	wrapUserRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const isDescending = req.query.isDescending !== "false";
		const { ts_codes } = req.query;

		const result = await positionService.getUserPositions(
			req.jwt.user_id,
			page,
			pageSize,
			isDescending,
			{
				ts_codes: ts_codes ? (ts_codes as string).split(",") : undefined,
			},
		);
		res.status(200).json(result);
	}),
);

export default router;
