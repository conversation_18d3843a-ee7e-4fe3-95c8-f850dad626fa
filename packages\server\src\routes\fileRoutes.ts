import { ENV } from "@/config/configManager.js";
import { Router } from "express";
import { wrapUserRoute } from "@/utils/routeWrapper.js";
import multer from "multer";
import path from "node:path";
import { v4 as uuidv4 } from "uuid";
import fs from "node:fs";
import logger from "@/utils/logger.js";
import { authenticateTokenWithoutQualification } from "@/middlewares/jwtAuth.js";

const router = Router();

// 默认使用 /var/uploads，可通过环境变量覆盖
const uploadDir = ENV.UPLOAD_DIR || "/var/uploads";

// 创建上传目录（如果不存在）
if (!fs.existsSync(uploadDir)) {
	try {
		fs.mkdirSync(uploadDir, { recursive: true });
	} catch (error) {
		logger.error(error, "Failed to create upload directory");
	}
}

// 设置文件大小限制为 50MB，与 app.ts 保持一致
const maxFileSize = 50 * 1024 * 1024; // 50MB in bytes

// 设置存储引擎
const storage = multer.diskStorage({
	destination: (_req, _file, cb) => {
		cb(null, uploadDir);
	},
	filename: (_req, file, cb) => {
		const uniqueSuffix = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
		cb(null, uniqueSuffix);
	},
});

const upload = multer({
	storage,
	limits: {
		fileSize: maxFileSize,
	},
});

// Upload file: POST /api/file/upload
router.post(
	"/upload",
	authenticateTokenWithoutQualification,
	upload.single("file"),
	wrapUserRoute(async (req, res) => {
		if (!req.file) {
			res.status(400).json({ error: "No file uploaded" });
			return;
		}

		// 返回文件的 uid
		res.json({ uid: req.file.filename });
	}),
);

// 管理端下载路由已移到 admin/fileRoutes.ts
// ? 前端路由路径也要同步更改：/api/admin/file/download/:uid
// Serve uploaded files: GET /api/file/download/:uid
// router.use('/download', express.static(uploadDir))

export default router;
