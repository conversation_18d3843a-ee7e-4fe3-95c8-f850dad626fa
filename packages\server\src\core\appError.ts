import { ErrorType, getHttpStatus } from "@packages/shared";
import type { <PERSON>rror<PERSON><PERSON> } from "@packages/shared";

export class AppError extends Error {
	readonly code;
	readonly statusCode;

	private constructor(
		readonly errorKey: <PERSON><PERSON><PERSON><PERSON><PERSON>,
		message: string,
		readonly data?: Record<string, unknown>,
	) {
		super(message);
		this.name = `AppError-${ErrorType[errorKey].name}`;
		this.code = ErrorType[errorKey].code;
		this.statusCode = getHttpStatus(this.code);
	}

	static create(
		errorKey: <PERSON>rror<PERSON><PERSON>,
		message: string,
		data?: Record<string, unknown>,
	) {
		return new AppError(errorKey, message, data);
	}
}
