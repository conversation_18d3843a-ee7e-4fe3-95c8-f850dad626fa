import { ENV } from "@/config/configManager.js";
import { AppError } from "@/core/appError.js";
import logger from "@/utils/logger.js";
import { hashPassword } from "@/utils/encryption.js";
import * as AdminModel from "@/models/admins.js";
import { AdminPermission } from "@packages/shared";

export async function createAdmin(
	username: string,
	password: string,
	name: string,
	permissions: AdminPermission[],
) {
	// 检查用户名是否已存在
	const existingAdmin = await AdminModel.findByUsername(username);
	if (existingAdmin) {
		throw AppError.create("ADMIN_EXISTS", "Admin username already exists");
	}

	// 创建密码哈希
	const passwordHash = await hashPassword(password);

	// 创建管理员账号
	const admin = await AdminModel.create(
		username,
		passwordHash,
		name,
		permissions,
	);

	logger.info(`New admin created: ${username}`);
	return {
		admin_id: admin.admin_id,
		username: admin.username,
		permissions: admin.permissions,
		created_at: admin.created_at,
	};
}

export async function toggleAdminActiveStatus(admin_id: number) {
	// 获取管理员信息
	const admin = await AdminModel.getAdminById(admin_id);
	if (!admin) {
		throw AppError.create("ADMIN_NOT_FOUND", "Admin not found");
	}

	// 检查是否为主管理员（系统中只有一个具有ADMIN权限的账号）
	if (admin.permissions.includes(AdminPermission.ADMIN)) {
		throw AppError.create(
			"ADMIN_PERMISSION_DENIED",
			"Cannot disable the main administrator account",
		);
	}

	await AdminModel.toggleActiveStatus(admin_id);

	return { message: "Admin status toggled successfully" };
}

export async function updateAdminPermissions(
	admin_id: number,
	permissions: AdminPermission[],
) {
	// 获取管理员信息
	const admin = await AdminModel.getAdminById(admin_id);
	if (!admin) {
		throw AppError.create("ADMIN_NOT_FOUND", "Admin not found");
	}

	// 检查是否为主管理员（系统中只有一个具有ADMIN权限的账号）
	if (admin.permissions.includes(AdminPermission.ADMIN)) {
		throw AppError.create(
			"ADMIN_PERMISSION_DENIED",
			"Cannot modify the main administrator permissions",
		);
	}

	await AdminModel.updatePermissions(admin_id, permissions);
	logger.info(`Admin permissions updated for user ID: ${admin_id}`);

	return { message: "Admin permissions updated successfully" };
}

export async function getAllAdmins() {
	const admins = await AdminModel.getAdmins();
	return admins.map((admin) => ({
		admin_id: admin.admin_id,
		username: admin.username,
		permissions: admin.permissions,
		name: admin.name,
		created_at: admin.created_at,
		is_active: admin.is_active,
	}));
}

export async function verifyApiToken(token: string): Promise<boolean> {
	// 使用环境变量中指定的密钥
	return token === ENV.OPEN_API_KEY;
}
