import { computed, ref } from "vue";
import router from "@/router";
import { defineStore } from "pinia";
import { jwtDecode } from "jwt-decode";
import { authApi } from "@/api";
import type {
	BaseAuthResponse,
	LoginRequest,
	UserJWTPayload,
} from "@packages/shared";
import { updateAuth<PERSON>eader } from "@/plugins/axios";
import type { AxiosResponse } from "axios";
import websocketManager from "@/core/websocketManager";

export const useAuthStore = defineStore("auth", () => {
	// computed 读取不到 localStorage 的变化
	const token = ref<string | null>(localStorage.getItem("access_token"));
	const username = ref<string | null>(localStorage.getItem("username"));
	const canTransfer = ref<boolean | null>(
		localStorage.getItem("can_transfer") === "true",
	);

	const isLoggedIn = computed(() => !!token.value);
	const isQualified = computed(() => {
		if (!token.value) return null;
		try {
			const decoded = jwtDecode<UserJWTPayload>(token.value);
			return decoded.is_qualified;
		} catch {
			return null;
		}
	});
	const user_id = computed(() => {
		if (!token.value) return null;
		try {
			const decoded = jwtDecode<UserJWTPayload>(token.value);
			return decoded.user_id;
		} catch {
			return null;
		}
	});

	const app_type = computed(() => {
		if (!token.value) return null;
		try {
			const decoded = jwtDecode<UserJWTPayload>(token.value);
			return decoded.app_type;
		} catch {
			return null;
		}
	});

	// WebSocket 连接管理
	const manageWebSocketConnection = () => {
		if (isLoggedIn.value && user_id.value) {
			websocketManager.connect(user_id.value.toString());
		} else {
			websocketManager.disconnect();
		}
	};

	function setToken(res: AxiosResponse<BaseAuthResponse>) {
		// 从 authorization header 中获取完整的认证头，express 总是使用小写
		const authHeader = res.headers?.authorization;
		if (authHeader?.startsWith("Bearer ")) {
			const newToken = authHeader.slice(7);
			localStorage.setItem("access_token", newToken);
			token.value = newToken;
			updateAuthHeader(authHeader);
		}

		// 从 response body 中获取用户信息
		if (res.data) {
			localStorage.setItem("username", res.data.username);
			localStorage.setItem("can_transfer", res.data.can_transfer.toString());
			username.value = res.data.username;
			canTransfer.value = res.data.can_transfer;
		}

		// 更新 WebSocket 连接
		manageWebSocketConnection();
	}

	function clearToken() {
		localStorage.removeItem("access_token");
		localStorage.removeItem("username");
		localStorage.removeItem("can_transfer");
		token.value = null;
		username.value = null;
		canTransfer.value = null;
		updateAuthHeader(null);

		// 断开 WebSocket 连接
		websocketManager.disconnect();
	}

	async function refreshToken() {
		const res = await authApi.refreshToken();
		setToken(res);
	}

	async function login(loginData: LoginRequest) {
		const res = await authApi.login(loginData);
		setToken(res);
		router.push("/");
	}

	async function logout() {
		await authApi.logout();
		clearToken();
		router.push("/login");
	}

	return {
		user_id,
		isQualified,
		username,
		canTransfer,
		app_type,
		isLoggedIn,
		login,
		logout,
		refreshToken,
	};
});
