import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";
import "./index.scss";
import "./index.css";
import ElementPlus from "element-plus";
import "./assets/styles/global.scss";
import Vue3Signature from "vue3-signature";

const app = createApp(App);
const pinia = createPinia();
app.use(pinia);

app.use(router).use(ElementPlus);

app.use(Vue3Signature);

app.mount("#app");
