# Stage 1: Build stage
FROM node:23-alpine AS builder

# 安装 pnpm
RUN npm install -g pnpm

WORKDIR /app

# 复制根目录和共享包的 package.json 及 pnpm 配置
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/shared/package.json ./packages/shared/

# 复制 admin 包的 package.json
COPY packages/admin/package.json ./packages/admin/

# 安装所有依赖项 (包括 devDependencies 用于构建)
# 使用 --filter 来只安装 admin 及其依赖项，包括 shared
# --prod=false 确保安装 devDependencies
RUN pnpm install --filter @packages/admin... --frozen-lockfile --prod=false

# 复制 shared 包的源代码
COPY packages/shared/ ./packages/shared/

# 复制 admin 包的源代码
COPY packages/admin/ ./packages/admin/

# 构建 admin 应用 (假设构建命令是 build)
RUN pnpm --filter @packages/admin build

# Stage 2: Production stage (using Nginx)
FROM nginx:stable-alpine

# 从 builder 阶段复制构建好的静态文件到 Nginx 的默认 HTML 目录
# 假设 admin 的构建输出目录也是 dist
COPY --from=builder /app/packages/admin/dist /usr/share/nginx/html

# (可选) 复制自定义的 Nginx 配置文件
COPY packages/admin/nginx.conf /etc/nginx/conf.d/default.conf

# 暴露 Nginx 默认端口
EXPOSE 80

# Nginx 镜像会自动启动 Nginx 服务
CMD ["nginx", "-g", "daemon off;"]