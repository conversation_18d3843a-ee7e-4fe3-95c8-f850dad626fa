import { PDFDocument, rgb } from "pdf-lib";
import fontkit from "@pdf-lib/fontkit";
import type { OrderData, SettledOrderData } from "@packages/shared";
import { formatDate } from "./format";
import { useStockStore } from "@/stores/stock";
import tradeTemplate from "@/assets/templates/trade-confirmation.pdf";
import settlementTemplate from "@/assets/templates/settlement-notice.pdf";
import { tradeApi } from "@/api";
import { ElMessage } from "element-plus";
import { useSiteConfigStore } from "@/stores/siteConfig";

const siteConfigStore = useSiteConfigStore();

const { getStockName } = useStockStore();

// 添加字体缓存
let cachedFontPromise: Promise<ArrayBuffer> | null = null;

// 添加字体加载状态
let isFontLoading = false;

// 获取字体的函数
async function getFont(): Promise<ArrayBuffer> {
	if (!cachedFontPromise) {
		isFontLoading = true;

		// 设置2秒后的检查
		setTimeout(() => {
			if (isFontLoading) {
				ElMessage.info("正在下载所需字体文件，请稍候...");

				// 监听加载完成
				cachedFontPromise?.then(() => {
					ElMessage.success("字体文件下载完成");
				});
			}
		}, 2000);

		cachedFontPromise = fetch("/fonts/SimSun.ttf")
			.then((res) => res.arrayBuffer())
			.finally(() => {
				isFontLoading = false;
			});
	}
	return cachedFontPromise;
}

export async function generateTradeConfirmation(
	order: OrderData,
): Promise<Blob> {
	// 加载模板
	const templateBytes = await fetch(tradeTemplate).then((res) =>
		res.arrayBuffer(),
	);
	const pdfDoc = await PDFDocument.load(templateBytes);

	// 注册 fontkit
	pdfDoc.registerFontkit(fontkit);

	// 使用缓存的字体
	const fontBytes = await getFont();
	const font = await pdfDoc.embedFont(fontBytes);

	// 计算部分替换文本
	const userInfo = await tradeApi.getUserInfo();
	const isCall = order.structure.endsWith("C");
	const optionType = `欧式看${isCall ? "涨" : "跌"}期权`;
	const exerciseRatio = Math.round(
		(order.exercise_price / order.entry_price) * 100,
	);
	const settlementPriceText = isCall
		? "Max(0, 到期结算价格-执行价格)/期初价格 × 名义本金"
		: "Max(0, 执行价格-到期结算价格)/期初价格 × 名义本金";

	// 获取第一页
	const page = pdfDoc.getPages()[0];

	// 定义要填充的内容及其位置
	const content = [
		// 甲方
		{ text: siteConfigStore.companyName(), x: 120, y: 727 },
		// 乙方
		{ text: userInfo?.name, x: 120, y: 712 },
		// 交易日期
		{ text: formatDate(order.created_at), x: 217, y: 601 },
		// 期权类型
		{ text: optionType, x: 217, y: 585 },
		// 标的代码
		{ text: order.ts_code, x: 217, y: 569 },
		// 标的名称
		{ text: getStockName(order.ts_code), x: 217, y: 553 },
		// 期初价格
		{ text: `${order.entry_price} CNY`, x: 217, y: 537 },
		// 执行价格比例
		{ text: `${exerciseRatio}%`, x: 217, y: 521 },
		// 执行价格
		{ text: `${Number(order.exercise_price).toFixed(2)} CNY`, x: 217, y: 505 },
		// 名义本金
		{ text: `${order.scale * 10000} CNY`, x: 217, y: 489 },
		// 期权费率
		{ text: `${order.quote}%`, x: 217, y: 473 },
		// 总期权费
		{
			text: `${Math.round(order.quote * order.scale * 100)} CNY`,
			x: 217,
			y: 457,
		},
		// 到期日
		{ text: formatDate(order.expiry_date), x: 217, y: 441 },
		// 到期结算价格
		{ text: settlementPriceText, x: 217, y: 425 },
	];
	// 在指定位置添加文本
	for (const { text, x, y } of content) {
		page.drawText(text || "", {
			x,
			y,
			font,
			size: 10,
			color: rgb(0, 0, 0),
		});
	}
	// 生成最终的 PDF
	const pdfBytes = await pdfDoc.save();
	return new Blob([pdfBytes], { type: "application/pdf" });
}

export async function generateSettlementNotice(
	order: SettledOrderData,
): Promise<Blob> {
	// 加载 PDF 模板
	const templateBytes = await fetch(settlementTemplate).then((res) =>
		res.arrayBuffer(),
	);
	const pdfDoc = await PDFDocument.load(templateBytes);

	// 注册 fontkit
	pdfDoc.registerFontkit(fontkit);

	// 使用缓存的字体
	const fontBytes = await getFont();
	const font = await pdfDoc.embedFont(fontBytes);

	// 获取第一页
	const page = pdfDoc.getPages()[0];

	// 计算一些值
	const userInfo = await tradeApi.getUserInfo();
	const isCall = order.structure.endsWith("C");
	const settleRatio = ((order.settle_price / order.entry_price) * 100).toFixed(
		2,
	);
	const settleProfit = (
		((order.scale *
			Math.max(
				0,
				isCall
					? order.settle_price - order.exercise_price
					: order.exercise_price - order.settle_price,
			)) /
			order.entry_price) *
		10000
	).toFixed(2);

	// 定义要填充的内容及其位置
	const content = [
		// 甲方
		{ text: siteConfigStore.companyName(), x: 120, y: 723 },
		// 乙方
		{ text: userInfo?.name, x: 120, y: 700 },
		// 平仓日
		{ text: formatDate(order.closed_at), x: 300, y: 520 },
		// 标的代码
		{ text: order.ts_code, x: 300, y: 496 },
		// 标的名称
		{ text: getStockName(order.ts_code), x: 300, y: 472 },
		// 期初价格
		{ text: `${order.entry_price} CNY`, x: 300, y: 448 },
		// 终止价格比例
		{ text: `${settleRatio}%`, x: 300, y: 424 },
		// 结算价格
		{ text: `${Number(order.settle_price).toFixed(2)} CNY`, x: 300, y: 400 },
		// 终止名义本金
		{ text: `${order.scale * 10000} CNY`, x: 300, y: 376 },
		// 期权收益结算金额
		{ text: `${settleProfit} CNY`, x: 300, y: 352 },
	];

	// 在指定位置添加文本
	for (const { text, x, y } of content) {
		page.drawText(text || "", {
			x,
			y,
			font,
			size: 10,
			color: rgb(0, 0, 0),
		});
	}

	// 生成最终的 PDF
	const pdfBytes = await pdfDoc.save();
	return new Blob([pdfBytes], { type: "application/pdf" });
}
