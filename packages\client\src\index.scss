/* 重写主题色：#e88234 */
@forward 'element-plus/theme-chalk/src/common/var.scss' with ($colors: ('primary': ('base': #e88234),
  ));

/* 导入所有样式 */
@use 'element-plus/theme-chalk/src/index.scss' as *;
@use 'element-plus/theme-chalk/src/dark/css-vars.scss' as *;

@use 'sass:color';

/* 全局CSS变量定义（可在JS中动态修改） */
:root {
  /* 主题色变量 - 客户端默认使用橙色 */
  --el-color-primary: #e88234;
}

/* ---------------------------------- */