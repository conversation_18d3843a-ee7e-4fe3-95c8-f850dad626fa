<template>
  <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑录单' : '新增录单'" width="360px" class="manual-order-dialog">
    <div class="modal-body">
      <el-form :model="formData" label-position="left" label-width="98px" class="manual-order-form">
        <!-- 标的 -->
        <el-form-item label="标的" required>
          <el-autocomplete v-model="formData.ts_code" :fetch-suggestions="querySearch" placeholder="输入标的代码或名称"
            :trigger-on-focus="false" @select="handleSelect" clearable class="full-width">
            <template #default="{ item }">
              <div>{{ formatSubject(item.value) }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>

        <!-- 结构 -->
        <el-form-item label="结构" label-position="top" class="label-top-form-item" required>
          <el-radio-group v-model="formData.structure" class="structure-group">
            <div class="structure-type-group">
              <div class="structure-type">Call</div>
              <div class="structure-options">
                <el-radio v-for="option in callStructureOptions" :key="option.value" :value="option.value"
                  class="call-option">
                  {{ option.label.split(' ')[0] }}
                </el-radio>
              </div>
            </div>
            <div class="structure-type-group">
              <div class="structure-type">Put</div>
              <div class="structure-options">
                <el-radio v-for="option in putStructureOptions" :key="option.value" :value="option.value"
                  class="put-option">
                  {{ option.label.split(' ')[0] }}
                </el-radio>
              </div>
            </div>
          </el-radio-group>
        </el-form-item>

        <!-- 规模 -->
        <el-form-item label="规模(万)" required>
          <el-input-number v-model="formData.scale" :min="1" :step="1" controls-position="right" class="full-width" />
        </el-form-item>

        <!-- 期限 -->
        <el-form-item label="期限" label-position="top" class="label-top-form-item" required>
          <el-radio-group v-model="formData.term" class="term-group">
            <el-radio :value="14" class="term-radio">2周</el-radio>
            <el-radio :value="1" class="term-radio">1个月</el-radio>
            <el-radio :value="2" class="term-radio">2个月</el-radio>
            <el-radio :value="3" class="term-radio">3个月</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 交易方 -->
        <el-form-item label="交易方" required>
          <el-input v-model="formData.quote_provider" placeholder="交易方名称" />
        </el-form-item>

        <!-- 期权费率 -->
        <el-form-item label="期权费率(%)" required>
          <el-input-number v-model="formData.quote" :min="0" :precision="2" :step="0.01" controls-position="right"
            class="full-width" />
        </el-form-item>

        <!-- 开仓价 -->
        <el-form-item label="开仓价" required>
          <el-input-number v-model="formData.entry_price" :min="0" :precision="2" :step="0.01" controls-position="right"
            class="full-width" />
        </el-form-item>

        <!-- 执行价 -->
        <el-form-item label="执行价" required>
          <el-input-number v-model="formData.exercise_price" :min="0" :precision="2" :step="0.01"
            controls-position="right" class="full-width" />
        </el-form-item>

        <!-- 开仓日 -->
        <el-form-item label="开仓日">
          <el-date-picker v-model="formData.entry_date" type="date" placeholder="选择开仓日期" format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" class="full-width" />
        </el-form-item>

        <!-- 到期日 -->
        <el-form-item label="到期日">
          <el-date-picker v-model="formData.expiry_date" type="date" placeholder="选择到期日期" format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" class="full-width" />
        </el-form-item>

        <!-- 备注 -->
        <el-form-item label="备注">
          <el-input v-model="formData.remarks" type="textarea" :rows="2" placeholder="可选备注信息" />
        </el-form-item>

        <!-- 仅编辑模式显示的字段 -->
        <template v-if="isEdit">
          <!-- 状态 -->
          <el-form-item label="状态">
            <el-select v-model="formData.status" class="full-width">
              <el-option v-for="option in statusOptions" :key="option.value" :label="option.label"
                :value="option.value" />
            </el-select>
          </el-form-item>

          <!-- 结算价，仅当状态为sold时显示 -->
          <el-form-item v-if="formData.status === 'sold'" label="结算价">
            <el-input-number v-model="formData.settle_price" :min="0" :precision="2" :step="0.01"
              controls-position="right" class="full-width" />
          </el-form-item>
        </template>
      </el-form>
    </div>

    <template #footer>
      <div class="button-group">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useStockStore } from "@/stores/stock";
import type { ManualOrderData, ManualOrderRequest, ManualOrderStatus, StructureType } from "@packages/shared";
import { StructureValue } from "@packages/shared";
import { fetchCurrentPrice } from "@/utils/stock";

interface FormData extends Partial<ManualOrderData>, Partial<ManualOrderRequest> {
  ts_code: string;
  structure: StructureType;
  scale: number;
  term: number;
  quote_provider: string;
  quote: number;
  entry_price: number;
  exercise_price: number;
  entry_date?: string;
  expiry_date?: string;
  remarks?: string;
  status?: ManualOrderStatus;
  settle_price?: number;
}

const props = defineProps<{
  modelValue: boolean;
  orderData?: ManualOrderData | ManualOrderRequest | null;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "submit", data: ManualOrderRequest | ManualOrderData): void;
}>();

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const stockStore = useStockStore();
const { formatSubject } = stockStore;

// 计算到期日的辅助函数
const calculateExpiryDate = (term: number, entryDate?: string): string => {
  const baseDate = entryDate ? new Date(entryDate) : new Date();
  if (term === 14) {
    // 2周
    baseDate.setDate(baseDate.getDate() + 14);
  } else {
    // 按月计算
    baseDate.setMonth(baseDate.getMonth() + term);
  }
  return baseDate.toISOString().split('T')[0];
};

// 计算执行价的辅助函数
const calculateExercisePrice = (currentPrice: number, structure: StructureType): number => {
  // 根据结构类型计算执行价
  if (structure.includes('100')) {
    // 100% 行权价等于当前价格
    return currentPrice;
  }
  if (structure.includes('97')) {
    // 97% 行权价
    return currentPrice * 0.97;
  }
  if (structure.includes('95')) {
    // 95% 行权价
    return currentPrice * 0.95;
  }
  if (structure.includes('90')) {
    // 90% 行权价
    return currentPrice * 0.90;
  }
  if (structure.includes('103')) {
    // 103% 行权价
    return currentPrice * 1.03;
  }
  if (structure.includes('105')) {
    // 105% 行权价
    return currentPrice * 1.05;
  }
  if (structure.includes('110')) {
    // 110% 行权价
    return currentPrice * 1.10;
  }
  // 默认使用当前价格
  return currentPrice;
};

// 表单数据
const formData = ref<FormData>({
  ts_code: "",
  structure: "100C", // 默认结构
  scale: 100,
  term: 1,
  quote_provider: "",
  quote: 0,
  entry_price: 0,
  exercise_price: 0,
  entry_date: new Date().toISOString().split('T')[0], // 默认为当天
  expiry_date: calculateExpiryDate(1), // 初始化时就计算到期日
});

// 编辑模式判断
const isEdit = computed(() => !!props.orderData && 'manual_order_id' in props.orderData);

// 监听标的变化，自动获取开仓价
watch(() => formData.value.ts_code, async (newTsCode, oldTsCode) => {
  if (newTsCode && newTsCode !== oldTsCode && !isEdit.value) {
    try {
      const currentPrice = await fetchCurrentPrice(newTsCode);
      if (currentPrice) {
        formData.value.entry_price = Number(currentPrice.toFixed(2));

        // 如果已经有结构信息，同时更新执行价
        if (formData.value.structure) {
          formData.value.exercise_price = Number(calculateExercisePrice(currentPrice, formData.value.structure).toFixed(2));
        }
      }
    } catch (error) {
      console.error('获取股价失败:', error);
    }
  }
});

// 监听结构变化，自动计算执行价
watch(() => formData.value.structure, (newStructure) => {
  if (newStructure && formData.value.entry_price > 0 && !isEdit.value) {
    formData.value.exercise_price = Number(calculateExercisePrice(formData.value.entry_price, newStructure).toFixed(2));
  }
});

// 监听期限变化，自动计算到期日
watch(() => formData.value.term, (newTerm) => {
  if (newTerm && !isEdit.value) {
    formData.value.expiry_date = calculateExpiryDate(newTerm, formData.value.entry_date);
  }
});

// 监听开仓日变化，自动更新到期日
watch(() => formData.value.entry_date, (newEntryDate) => {
  if (newEntryDate && formData.value.term && !isEdit.value) {
    formData.value.expiry_date = calculateExpiryDate(formData.value.term, newEntryDate);
  }
});

// 结构选项，分为看涨和看跌两组
const callStructureOptions = computed(() =>
  Object.entries(StructureValue)
    .filter(([value]) => value.includes("C"))
    .map(([value, label]) => ({
      value: value as StructureType,
      label,
    }))
);

const putStructureOptions = computed(() =>
  Object.entries(StructureValue)
    .filter(([value]) => value.includes("P"))
    .map(([value, label]) => ({
      value: value as StructureType,
      label,
    }))
);

// 标的选项
const subjectOptions = computed(() => {
  if (!stockStore.isInitialized) {
    return [];
  }

  return stockStore.getAllFormattedSubjects();
});

// 状态选项
const statusOptions = [
  { value: "holding", label: "持仓中" },
  { value: "sold", label: "已结算" },
];

// 提交状态
const submitting = ref(false);

// 处理表单提交
const handleSubmit = async () => {
  // 简单验证
  if (
    !formData.value.ts_code ||
    !formData.value.structure ||
    !formData.value.scale ||
    !formData.value.term ||
    !formData.value.quote_provider ||
    formData.value.quote === undefined ||
    formData.value.entry_price === undefined ||
    formData.value.exercise_price === undefined
  ) {
    return;
  }

  submitting.value = true;

  try {
    // 传递编辑中的数据或创建新数据
    if (isEdit.value && props.orderData && 'manual_order_id' in props.orderData) {
      // 编辑模式，保留ID
      const submitData = {
        ...formData.value,
        manual_order_id: props.orderData.manual_order_id,
      };
      emit("submit", submitData as ManualOrderData);
    } else {
      // 创建模式
      emit("submit", formData.value as ManualOrderRequest);
    }
  } finally {
    submitting.value = false;
  }
};

// 当接收到orderData时，填充表单
watch(
  () => props.orderData,
  (newData) => {
    if (newData) {
      // 复制数据到表单
      formData.value = { ...newData } as FormData;

      // 确保开仓日有默认值
      if (!formData.value.entry_date) {
        formData.value.entry_date = new Date().toISOString().split('T')[0];
      }

      // 处理日期格式
      if (formData.value.entry_date && typeof formData.value.entry_date === 'string') {
        formData.value.entry_date = formData.value.entry_date.split('T')[0];
      }

      if (formData.value.expiry_date && typeof formData.value.expiry_date === 'string') {
        formData.value.expiry_date = formData.value.expiry_date.split('T')[0];
      }

      // 如果没有到期日，自动计算
      if (!formData.value.expiry_date && formData.value.term) {
        formData.value.expiry_date = calculateExpiryDate(formData.value.term, formData.value.entry_date);
      }
    } else {
      // 重置表单为默认值
      const today = new Date().toISOString().split('T')[0];
      formData.value = {
        ts_code: "",
        structure: "100C",
        scale: 100,
        term: 1,
        quote_provider: "",
        quote: 0,
        entry_price: 0,
        exercise_price: 0,
        entry_date: today, // 重置时也设为当天
        expiry_date: calculateExpiryDate(1, today), // 重置时也计算到期日
      };
    }
  },
  { immediate: true }
);

// 标的搜索
const querySearch = (queryString: string, cb: (results: { value: string }[]) => void) => {
  const results = subjectOptions.value
    .filter(option =>
      option.value.includes(queryString) ||
      formatSubject(option.value).toLowerCase().includes(queryString.toLowerCase())
    )
    .map(option => ({ value: option.value }));
  cb(results);
};

// 处理选择
const handleSelect = (item: { value: string }) => {
  formData.value.ts_code = item.value;
};
</script>

<style scoped>
.manual-order-form {
  padding: 0 4px;
}

.full-width {
  width: 100%;
}

.label-top-form-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.structure-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}

.structure-type-group {
  display: flex;
  align-items: center;
  gap: 20px;
}

.structure-type {
  width: 40px;
  font-weight: 500;
  font-size: 14px;
}

.structure-options {
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
}

.call-option {
  color: var(--text-color-call);
  margin-right: 0;
}

.put-option {
  color: var(--text-color-put);
  margin-right: 0;
}

.term-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-left: 20px;
}

.term-radio {
  margin-right: 0;
}

@media (max-width: 768px) {
  .structure-type-group {
    gap: 10px;
  }

  .structure-options {
    gap: 10px;
  }

  .term-group {
    gap: 10px;
  }
}
</style>