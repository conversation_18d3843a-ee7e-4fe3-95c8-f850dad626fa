import type { Job } from "bullmq";
import { createWorker, vwapOrdersQueue } from "./index.js";
import logger from "@/utils/logger.js";
import { appRedis } from "@/lib/redis.js";
import {
	getTurnoverAndVolume,
	fetchCurrentPrice,
} from "@/financeUtils/marketData.js";
import { calculateVWAP } from "@/financeUtils/calculator.js";
import * as ConfirmOrder from "@/services/trade/confirmOrder.js";
import * as PendingOrder from "@/models/trade/pendingOrder.js";
import * as NotifService from "@/services/notifService.js";
import { AppError } from "@/core/appError.js";
import { NotificationType, OrderStatus } from "@packages/shared";
import type { PendingOrderData, SellingOrderData } from "@packages/shared";
import { ENV } from "@/config/configManager.js";

// VWAP order constants
const BASE_INTERVAL = 15 * 60 * 1000; // 15 minutes
const VWAP_ORDER_PREFIX = "vwap_order:";
const VWAP_EXPIRY = BASE_INTERVAL + 10 * 1000; // 15 minutes + 10 seconds buffer

// VWAP order job names
export const VWAP_JOBS = {
	INIT_ORDERS: "vwap-init-orders",
	EXECUTE_ORDER: "vwap-execute-order",
};

// Interface for Redis cache
interface VWAPOrderCache {
	pending_id: number;
	ts_code: string;
	start_time: number;
	initial_turnover: number;
	initial_volume: number;
}

// Process VWAP jobs
async function processVwapJob(job: Job) {
	const { name, data } = job;

	logger.info(`Processing VWAP job: ${name}`);

	try {
		switch (name) {
			case VWAP_JOBS.INIT_ORDERS:
				await initializeVwapOrders();
				break;
			case VWAP_JOBS.EXECUTE_ORDER:
				await executeVwapOrder(data);
				break;
			default:
				logger.warn(`Unknown VWAP job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process VWAP job: ${name}`);
		throw error; // Let BullMQ handle the failure
	}
}

// Initialize and find all pending VWAP orders
async function initializeVwapOrders() {
	try {
		logger.info("Initializing pending VWAP orders");
		const pendingOrders = await PendingOrder.findAllVwapOrders();

		for (const order of pendingOrders) {
			const cacheKey = `${VWAP_ORDER_PREFIX}${order.pending_id}`;
			const cachedData = await appRedis.get(cacheKey);

			if (cachedData) {
				const cache: VWAPOrderCache = JSON.parse(cachedData);
				const now = Date.now();
				const timeLeft = cache.start_time + BASE_INTERVAL - now;

				if (timeLeft <= 0) {
					// Time has already passed but within buffer
					logger.info(
						`VWAP order ${order.pending_id} expired, executing immediately`,
					);
					await vwapOrdersQueue.add(
						VWAP_JOBS.EXECUTE_ORDER,
						{
							order,
							startTime: cache.start_time,
							initialTurnover: cache.initial_turnover,
							initialVolume: cache.initial_volume,
							isExpired: true,
						},
						{
							removeOnComplete: true,
							removeOnFail: 1000,
						},
					);
				} else {
					// Schedule for future execution
					logger.info(
						`VWAP order ${order.pending_id} scheduled for execution in ${timeLeft}ms`,
					);
					await vwapOrdersQueue.add(
						VWAP_JOBS.EXECUTE_ORDER,
						{
							order,
							startTime: cache.start_time,
							initialTurnover: cache.initial_turnover,
							initialVolume: cache.initial_volume,
						},
						{
							delay: timeLeft,
							removeOnComplete: true,
							removeOnFail: 1000,
						},
					);
				}
			} else {
				// No cache found, fail the order
				await handleOrderFailure(order, {
					reason:
						"服务器重启后VWAP挂单已超过时限，为确保交易安全，挂单已自动关闭",
				});
			}
		}

		logger.info(
			`Processed ${pendingOrders.length} VWAP orders during initialization`,
		);
	} catch (error) {
		logger.error(error, "Failed to initialize VWAP orders");
		throw error;
	}
}

// Execute a VWAP order
async function executeVwapOrder(data: {
	order: PendingOrderData;
	startTime?: number;
	initialTurnover?: number;
	initialVolume?: number;
	isExpired?: boolean;
}) {
	const { order, startTime, initialTurnover, initialVolume, isExpired } = data;

	try {
		// Double-check if order still exists in database
		const freshOrder = await PendingOrder.findById(order.pending_id);
		if (!freshOrder) {
			logger.info(
				`VWAP order ${order.pending_id} no longer exists, skipping execution`,
			);
			return;
		}

		let vwapPrice: number;

		if (
			startTime &&
			typeof initialTurnover === "number" &&
			typeof initialVolume === "number"
		) {
			// We have historical data, calculate VWAP
			const finalData = await getTurnoverAndVolume(order.ts_code);
			const isSTAR = order.ts_code.split(".")[0].startsWith("68");
			const calculatedPrice = calculateVWAP(
				{
					turnover: initialTurnover,
					volume: initialVolume,
				},
				finalData,
				isSTAR,
			);

			// If VWAP calculation fails, use current price as fallback
			if (calculatedPrice) {
				vwapPrice = calculatedPrice;
			} else {
				const { price } = await fetchCurrentPrice(order.ts_code);
				vwapPrice = price;
			}
		} else {
			// No historical data, use current price
			const { price } = await fetchCurrentPrice(order.ts_code);
			vwapPrice = price;
		}

		// For sell orders, verify price conditions
		if (order.status === OrderStatus.VWAP_SELLING) {
			const isCall = order.structure.endsWith("C");
			const isPriceValid = isCall
				? vwapPrice > order.exercise_price
				: vwapPrice < order.exercise_price;

			if (!isPriceValid) {
				await handleOrderFailure(order, {
					reason: isCall
						? "VWAP均价低于执行价，不满足看涨期权卖出条件"
						: "VWAP均价高于执行价，不满足看跌期权卖出条件",
					vwap_price: vwapPrice,
					exercise_price: order.exercise_price,
				});
				return;
			}
		}

		// Execute the order
		await ConfirmOrder.executeVwap(freshOrder, vwapPrice);

		// Clean up cache
		await appRedis.del(`${VWAP_ORDER_PREFIX}${order.pending_id}`);
	} catch (error) {
		logger.error(error, `Failed to execute VWAP order ${order.pending_id}`);
		await handleOrderFailure(order, {
			reason: error instanceof AppError ? error.name : "",
		});
	}
}

// Start a new VWAP order
export async function startVwapOrder(order: PendingOrderData): Promise<void> {
	try {
		// Get initial market data
		const initialData = await getTurnoverAndVolume(order.ts_code);
		const startTime = Date.now();

		// Cache order data in Redis
		const cacheData: VWAPOrderCache = {
			pending_id: order.pending_id,
			ts_code: order.ts_code,
			start_time: startTime,
			initial_turnover: initialData.turnover,
			initial_volume: initialData.volume,
		};

		await appRedis.setex(
			`${VWAP_ORDER_PREFIX}${order.pending_id}`,
			Math.ceil(VWAP_EXPIRY / 1000), // Redis expiry in seconds
			JSON.stringify(cacheData),
		);

		// Schedule job execution after BASE_INTERVAL
		await vwapOrdersQueue.add(
			VWAP_JOBS.EXECUTE_ORDER,
			{
				order,
				startTime,
				initialTurnover: initialData.turnover,
				initialVolume: initialData.volume,
			},
			{
				delay: BASE_INTERVAL,
				removeOnComplete: true,
				removeOnFail: 1000,
			},
		);

		logger.info(
			`VWAP order ${order.pending_id} scheduled for execution in ${BASE_INTERVAL}ms`,
		);
	} catch (error) {
		logger.error(error, `Failed to start VWAP order ${order.pending_id}`);
		await handleOrderFailure(order, {
			reason: "Failed to initialize VWAP order tracking",
		});
	}
}

// Clean up a VWAP order (e.g., when manually cancelled)
export async function cleanupVwapOrder(orderId: number): Promise<void> {
	try {
		// Remove from Redis
		await appRedis.del(`${VWAP_ORDER_PREFIX}${orderId}`);

		// Remove any pending jobs (excluding active ones)
		const jobs = await vwapOrdersQueue.getJobs([
			"delayed",
			"waiting",
			// "active", // Do not attempt to remove active jobs
		]);
		for (const job of jobs) {
			const data = job.data;
			if (data.order && data.order.pending_id === orderId) {
				try {
					await job.remove();
					logger.info(
						`Removed scheduled job ${job.id} for VWAP order ${orderId}`,
					);
				} catch (removeError: unknown) {
					logger.warn(
						{ error: removeError, jobId: job.id, orderId },
						`Failed to remove job ${job.id} during VWAP cleanup, continuing...`,
					);
				}
			}
		}
	} catch (error) {
		logger.error(error, `Failed to clean up VWAP order ${orderId}`);
		throw error;
	}
}

// Handle order failures
async function handleOrderFailure(
	order: PendingOrderData,
	details?: {
		reason: string;
		message?: string;
		vwap_price?: number;
		exercise_price?: number;
	},
): Promise<void> {
	try {
		// Clean up Redis and close the order
		await appRedis.del(`${VWAP_ORDER_PREFIX}${order.pending_id}`);
		await PendingOrder.close(order.pending_id);
		await PendingOrder.deletePendingOrder(order.pending_id);

		// Build notification content
		let notificationContent = `您的VWAP挂单 ${order.ts_code} 执行失败。`;
		if (details?.reason) {
			notificationContent += `\n原因：${details.reason}`;
		}
		if (details?.message) {
			notificationContent += `\n${details.message}`;
		}

		// Send notification to user
		await NotifService.sendNotification(order.user_id, {
			title: "VWAP挂单执行失败",
			content: notificationContent,
			type: NotificationType.ORDER,
			metadata: {
				type: "pending_failed",
				trade_no: (order as SellingOrderData).trade_no,
				ts_code: order.ts_code,
				status: order.status,
				vwap_price: details?.vwap_price,
				exercise_price: details?.exercise_price,
			},
		});
	} catch (error) {
		logger.error(
			error,
			`Failed to handle failure of VWAP order ${order.pending_id}`,
		);
	}
}

// Create the worker
export const vwapOrderWorker =
	ENV.NODE_ENV === "production"
		? createWorker(vwapOrdersQueue, processVwapJob)
		: undefined;

// Initialize orders on startup
export async function initializeOnStartup() {
	try {
		// 仅在生产环境执行订单恢复
		if (ENV.NODE_ENV === "production") {
			// 先清理可能存在的旧执行任务，避免重复
			try {
				const jobs = await vwapOrdersQueue.getJobs(["waiting", "delayed"]);
				const executeJobs = jobs.filter(
					(job) => job.name === VWAP_JOBS.EXECUTE_ORDER,
				);

				if (executeJobs.length > 0) {
					logger.info(
						`清理 ${executeJobs.length} 个现有VWAP执行任务，避免重复`,
					);
					for (const job of executeJobs) {
						await job.remove();
					}
				}
			} catch (cleanupError) {
				logger.warn(cleanupError, "清理现有VWAP执行任务失败，继续执行初始化");
			}

			// 添加初始化任务
			await vwapOrdersQueue.add(
				VWAP_JOBS.INIT_ORDERS,
				{},
				{
					removeOnComplete: true, // INIT job should be removed once done
					removeOnFail: 100, // Keep fewer failed init jobs
				},
			);
			logger.info("Added VWAP orders initialization job to queue");
		} else {
			// 开发环境下不执行恢复，并给出提示
			logger.info(
				"VwapOrderWorker: Automatic order recovery is disabled in development mode.",
			);
		}
	} catch (error) {
		logger.error(error, "Failed to initialize VWAP orders on startup");
	}
}
