import pino from "pino";
import { join } from "node:path";
import { promises as fs } from "node:fs";
import { ENV } from "@/config/configManager.js";
import { appRedis } from "@/lib/redis.js";

const logDir = "logs";

// 根据环境确定日志配置
const isDevelopment = ENV.NODE_ENV !== "production";

// Redis key for storing last error time
export const LAST_ERROR_TIME_KEY = "error_notification:last_error_time";
// Redis key for storing last error details
export const LAST_ERROR_DETAILS_KEY = "error_notification:last_error_details";

// Redis key for log cleanup lock
const LOG_CLEANUP_LOCK_KEY = "log_cleanup:init_lock";
const LOCK_EXPIRY_SECONDS = 300; // 5分钟锁过期时间

// 更新错误时间的函数
export async function updateLastErrorTime(
	timestamp = Date.now(),
): Promise<void> {
	try {
		await appRedis.set(LAST_ERROR_TIME_KEY, String(timestamp));
	} catch (error) {
		// Log silently - failure to update Redis shouldn't block logging
		console.error("Failed to update error time in Redis:", error);
	}
}

// 更新错误详情的函数
export async function updateLastErrorDetails(
	errorMessage: string,
	timestamp = Date.now(),
): Promise<void> {
	try {
		const errorDetails = {
			message: errorMessage,
			timestamp,
			time: new Date(timestamp).toLocaleString("zh-CN", {
				timeZone: "Asia/Shanghai",
				hour12: false,
			}),
		};
		await appRedis.set(LAST_ERROR_DETAILS_KEY, JSON.stringify(errorDetails));
		// 同时更新错误时间
		await updateLastErrorTime(timestamp);
	} catch (error) {
		// Log silently - failure to update Redis shouldn't block logging
		console.error("Failed to update error details in Redis:", error);
	}
}

// 获取最新的错误时间
export async function getLastErrorTime(): Promise<number> {
	try {
		const redisValue = await appRedis.get(LAST_ERROR_TIME_KEY);
		return redisValue ? Number(redisValue) : 0;
	} catch (error) {
		// If Redis fails, return 0 (same as initial state)
		console.error("Failed to get error time from Redis:", error);
		return 0;
	}
}

// 获取最新的错误详情
export async function getLastErrorDetails(): Promise<{
	message: string;
	timestamp: number;
	time: string;
} | null> {
	try {
		const redisValue = await appRedis.get(LAST_ERROR_DETAILS_KEY);
		return redisValue ? JSON.parse(redisValue) : null;
	} catch (error) {
		// If Redis fails, return null
		console.error("Failed to get error details from Redis:", error);
		return null;
	}
}

// 基础日志配置，自定义配置不默认包含 hostname 字段
const baseOptions = {
	// 可修改的开发环境日志级别
	level: isDevelopment ? "info" : "info",
	base: process.env.pm_id ? { pm_id: process.env.pm_id } : { pid: process.pid },
	timestamp: () =>
		`,"time":"${new Date().toLocaleString("zh-CN", {
			timeZone: "Asia/Shanghai",
			hour12: false,
		})}"`,
	// ? formatters.level 与多个 targets 冲突（不写入），formatters.log 则静默失效
	// 可用 mixin 添加属性
	// * 日志级别
	// * 10: trace, 20: debug, 30: info, 40: warn, 50: error, 60: fatal
	// 控制序列化如何处理错误对象
	serializers: {
		err: pino.stdSerializers.err,
	},
	// 添加钩子，记录最后一次错误时间
	hooks: {
		logMethod(
			this: pino.Logger,
			args: [msg: string, ...args: unknown[]],
			method: pino.LogFn,
			level: number,
		) {
			// 检查是否是错误级别的日志 (直接用 level 参数)
			if (level >= 50) {
				// 50 is error, 60 is fatal
				// 提取错误消息
				let errorMessage = "";

				// 检查args中的内容来提取错误消息
				if (args.length > 0) {
					const firstArg = args[0];
					if (typeof firstArg === "string") {
						// 第一个参数是字符串消息
						errorMessage = firstArg;
					} else if (firstArg && typeof firstArg === "object") {
						// 第一个参数是对象，检查是否有错误信息
						const obj = firstArg as Record<string, unknown>;
						if ("message" in obj && typeof obj.message === "string") {
							errorMessage = obj.message;
						} else if ("error" in obj && obj.error instanceof Error) {
							errorMessage = obj.error.message;
						} else if ("err" in obj && obj.err instanceof Error) {
							errorMessage = obj.err.message;
						} else if (args.length > 1 && typeof args[1] === "string") {
							// 第二个参数是消息
							errorMessage = args[1] as string;
						}
					}
				}

				// 如果没有提取到具体错误消息，使用默认值
				if (!errorMessage) {
					errorMessage = "系统发生错误";
				}

				// 更新最后一次错误详情 (使用新的异步函数，但不等待它)
				updateLastErrorDetails(errorMessage);
			}
			return method.apply(this, args);
		},
	},
};

/**
 * Pino logger 的正确使用方式：
 * 1. 普通消息:
 *    logger.info("message")
 *
 * 2. 对象 + 消息:
 *    logger.info({ context }, "message")
 *
 * 3. 格式化字符串:
 *    logger.info("message %s", value)
 *
 * 4. 对象 + 格式化字符串:
 *    logger.info({ context }, "message %s", value)
 *
 * 注意：当使用对象作为参数时，消息或格式化字符串必须作为第二个参数，
 * 否则对象可能不会被正确显示
 */
let logger: pino.Logger;

if (isDevelopment) {
	// 开发环境：内置使用 pino-pretty 格式化输出
	logger = pino.default({
		...baseOptions,
		// Docker开发环境：不格式化
		...(!ENV.IS_DOCKER_DEV
			? {
					transport: {
						target: "pino-pretty",
						options: {
							colorize: true,
							translateTime: "SYS:standard",
							ignore: "pid,hostname",
						},
					},
				}
			: {}),
	});
} else {
	// 生产环境：使用 pino-roll 进行日志轮转
	const transport = pino.transport({
		targets: [
			{
				target: "pino-roll",
				options: {
					file: join(logDir, "combined.log"),
					frequency: "daily",
					mkdir: true,
					size: "50M",
					limit: {
						count: 10,
						// 移除 size 和 interval，这些参数可能导致冲突
						removeOtherLogFiles: true, // 确保清理其他进程创建的日志文件
					},
				},
				level: "info",
			},
			{
				target: "pino-roll",
				options: {
					file: join(logDir, "error.log"),
					frequency: "daily",
					mkdir: true,
					size: "20M",
					limit: {
						count: 10,
						removeOtherLogFiles: true,
					},
				},
				level: "error",
			},
		],
	});

	logger = pino.default(baseOptions, transport);
}

// 添加优雅关闭功能
let shuttingDown = false;

/**
 * 日志清理功能
 *
 * pino-roll 在某些情况下不能正确清理旧的日志文件，特别是：
 * 1. removeOtherLogFiles 配置不总是生效
 * 2. 应用重启后不会清理之前进程遗留的文件
 *
 * 解决方案：
 * 1. 启动时延迟10秒执行一次清理（补充 pino-roll 的不足）
 * 2. 手动脚本清理：npm run cleanup-logs（立即清理大文件和过期文件）
 * 3. 强制清理函数：forceCleanupLogs()（代码中手动调用）
 *
 * 清理规则：
 * - 超过配置最大天数的文件
 * - 超过配置最大数量的文件（保留最新的）
 * - 手动脚本额外清理大于50MB的文件
 */

// 日志清理配置
interface LogCleanupConfig {
	enabled: boolean;
	maxFiles: number;
	maxAgeDays: number;
}

const LOG_CLEANUP_CONFIG: LogCleanupConfig = {
	enabled: !isDevelopment, // 只在生产环境启用
	maxFiles: Number(process.env.LOG_CLEANUP_MAX_FILES) || 20, // 最大文件数量
	maxAgeDays: Number(process.env.LOG_CLEANUP_MAX_AGE_DAYS) || 7, // 最大保留天数
};

// 日志清理任务状态
let isCleanupRunning = false;
let cleanupTimeout: NodeJS.Timeout | null = null;

/**
 * 简洁的日志清理函数
 * 作为 pino-roll 的补充，处理可能遗留的旧日志文件
 */
async function cleanupOldLogFiles(): Promise<void> {
	if (isCleanupRunning || !LOG_CLEANUP_CONFIG.enabled) {
		return;
	}

	isCleanupRunning = true;

	try {
		const logDirPath = logDir;

		// 读取日志目录
		const files = await fs.readdir(logDirPath);

		// 过滤出日志文件（.log 结尾或包含日期格式）
		const logFiles = files.filter(
			(file) =>
				file.endsWith(".log") ||
				/\.\d{4}-\d{2}-\d{2}\.log$/.test(file) ||
				/\.log\.\d+$/.test(file),
		);

		if (logFiles.length <= LOG_CLEANUP_CONFIG.maxFiles) {
			return; // 文件数量在限制内，无需清理
		}

		// 获取文件详细信息并排序
		const fileStats = await Promise.all(
			logFiles.map(async (file) => {
				const filePath = join(logDirPath, file);
				const stat = await fs.stat(filePath);
				return {
					name: file,
					path: filePath,
					mtime: stat.mtime,
					age: Date.now() - stat.mtime.getTime(),
				};
			}),
		);

		// 按修改时间排序，最旧的在前
		fileStats.sort((a, b) => a.mtime.getTime() - b.mtime.getTime());

		const filesToDelete = fileStats.filter((file, index) => {
			const ageDays = file.age / (24 * 60 * 60 * 1000);

			// 删除条件：超过最大天数 或 超过最大文件数量
			return (
				ageDays > LOG_CLEANUP_CONFIG.maxAgeDays ||
				index < fileStats.length - LOG_CLEANUP_CONFIG.maxFiles
			);
		});

		if (filesToDelete.length === 0) {
			return; // 无需清理
		}

		// 删除旧文件
		let deletedCount = 0;
		for (const file of filesToDelete) {
			try {
				await fs.unlink(file.path);
				deletedCount++;
			} catch (error) {
				// 静默处理删除失败，避免影响主要功能
				logger.debug(error, `无法删除日志文件 ${file.name}`);
			}
		}

		// 只在实际删除了文件时才记录日志
		if (deletedCount > 0) {
			logger.info(`日志清理完成，删除了 ${deletedCount} 个旧文件`);
		}
	} catch (error) {
		// 静默处理清理失败，避免影响主要功能
		logger.debug(error, "日志清理任务执行失败");
	} finally {
		isCleanupRunning = false;
	}
}

/**
 * 启动时执行一次日志清理（使用分布式锁）
 * 延迟10秒执行，避免影响应用启动速度
 */
function scheduleInitialCleanup(): void {
	if (!LOG_CLEANUP_CONFIG.enabled || cleanupTimeout) {
		return;
	}

	cleanupTimeout = setTimeout(async () => {
		// 生成唯一标识符（进程ID + 时间戳）
		const lockIdentifier = `${process.pid}_${Date.now()}`;

		try {
			// 尝试获取分布式锁
			const result = await appRedis.set(
				LOG_CLEANUP_LOCK_KEY,
				lockIdentifier,
				"EX",
				LOCK_EXPIRY_SECONDS,
				"NX",
			);

			if (result !== "OK") {
				logger.debug("其他进程正在执行日志清理任务，跳过本次清理");
				return;
			}

			try {
				await cleanupOldLogFiles();
				logger.info("启动时日志清理完成");
			} catch (error) {
				logger.debug(error, "启动时日志清理失败");
			} finally {
				// 释放分布式锁
				await appRedis.del(LOG_CLEANUP_LOCK_KEY);
			}
		} catch (error) {
			logger.error(error, "日志清理分布式锁操作异常");
		}
	}, 10000); // 延迟10秒执行

	logger.info(
		{
			delay: "10秒后执行",
			maxFiles: LOG_CLEANUP_CONFIG.maxFiles,
			maxAgeDays: LOG_CLEANUP_CONFIG.maxAgeDays,
		},
		"已安排启动时日志清理任务",
	);
}

/**
 * 取消计划的日志清理任务
 */
function cancelScheduledCleanup(): void {
	if (cleanupTimeout) {
		clearTimeout(cleanupTimeout);
		cleanupTimeout = null;
		logger.debug("已取消计划的日志清理任务");
	}
}

// 在生产环境安排启动时清理
if (!isDevelopment) {
	scheduleInitialCleanup();
}

/**
 * Gracefully shutdown the logger - 不再直接监听信号
 */
export function shutdownLogger() {
	if (shuttingDown) return;
	shuttingDown = true;

	logger.info("Logger shutting down...");

	// 取消计划的清理任务
	cancelScheduledCleanup();

	// 确保日志被刷新到磁盘
	return new Promise<void>((resolve) => {
		setTimeout(() => {
			// pino不像winston那样需要显式结束
			logger.info("Logger shut down successfully");
			resolve();
			// 移除 process.exit(0)，让主应用控制退出
		}, 200);
	});
}

// 注: 如需调试日志问题，可添加自动关闭功能
// 在长时间没有新日志时自动关闭日志系统

// 添加子日志记录器支持（为不同模块创建专用日志记录器）
export function getChildLogger(module: string) {
	return logger.child({ module });
}

/**
 * 强制执行日志清理（忽略环境限制）
 * 用于手动清理或紧急情况
 */
async function forceCleanupLogs(): Promise<void> {
	if (isCleanupRunning) {
		logger.warn("日志清理任务正在运行中，跳过强制清理");
		return;
	}

	isCleanupRunning = true;
	logger.info("开始强制清理日志文件...");

	try {
		const logDirPath = logDir;

		// 确保日志目录存在
		try {
			await fs.access(logDirPath);
		} catch {
			logger.info("日志目录不存在，无需清理");
			return;
		}

		// 读取日志目录
		const files = await fs.readdir(logDirPath);

		// 过滤出日志文件（.log 结尾或包含日期格式）
		const logFiles = files.filter(
			(file) =>
				file.endsWith(".log") ||
				/\.\d{4}-\d{2}-\d{2}\.log$/.test(file) ||
				/\.log\.\d+$/.test(file),
		);

		logger.info(`发现 ${logFiles.length} 个日志文件`);

		// 获取文件详细信息并排序
		const fileStats = await Promise.all(
			logFiles.map(async (file) => {
				const filePath = join(logDirPath, file);
				const stat = await fs.stat(filePath);
				const sizeMB = (stat.size / (1024 * 1024)).toFixed(2);
				const ageDays =
					(Date.now() - stat.mtime.getTime()) / (24 * 60 * 60 * 1000);

				return {
					name: file,
					path: filePath,
					mtime: stat.mtime,
					age: Date.now() - stat.mtime.getTime(),
					size: stat.size,
					sizeMB: Number(sizeMB),
					ageDays: ageDays,
				};
			}),
		);

		// 记录文件信息
		for (const file of fileStats) {
			logger.info(
				`文件: ${file.name}, 大小: ${file.sizeMB}MB, 年龄: ${file.ageDays.toFixed(1)}天`,
			);
		}

		// 按修改时间排序，最旧的在前
		fileStats.sort((a, b) => a.mtime.getTime() - b.mtime.getTime());

		const filesToDelete = fileStats.filter((file, index) => {
			const ageDays = file.age / (24 * 60 * 60 * 1000);

			// 删除条件：超过最大天数 或 超过最大文件数量 或 文件过大（超过100MB）
			return (
				ageDays > LOG_CLEANUP_CONFIG.maxAgeDays ||
				index < fileStats.length - LOG_CLEANUP_CONFIG.maxFiles ||
				file.sizeMB > 100
			); // 强制删除超过100MB的文件
		});

		logger.info(`需要删除 ${filesToDelete.length} 个文件`);

		// 删除旧文件
		for (const file of filesToDelete) {
			try {
				await fs.unlink(file.path);
				logger.info(`✅ 已删除: ${file.name} (${file.sizeMB}MB)`);
			} catch (error) {
				logger.warn(error, `❌ 删除失败: ${file.name}`);
			}
		}

		// 统计剩余文件
		const remainingFiles = fileStats.filter((f) => !filesToDelete.includes(f));
		const totalRemainingSize = remainingFiles.reduce(
			(sum, f) => sum + f.sizeMB,
			0,
		);

		logger.info(
			`清理完成！剩余 ${remainingFiles.length} 个文件，总大小: ${totalRemainingSize.toFixed(2)}MB`,
		);
	} catch (error) {
		logger.error(error, "强制清理失败");
		throw error;
	} finally {
		isCleanupRunning = false;
	}
}

// 导出日志清理相关函数（供测试或手动调用）
export { cleanupOldLogFiles, forceCleanupLogs };

export default logger;
