# PgBouncer 配置指南

本文档提供了使用 PgBouncer 作为连接池管理器，解决 PostgreSQL "太多客户端连接" 问题的详细配置步骤。

## 问题背景

当部署多个应用实例共享同一个 PostgreSQL 数据库时，可能会遇到以下错误：

```
致命错误: 对不起, 已经有太多的客户端 (fatal error: sorry, too many clients)
```

这是因为 PostgreSQL 有一个 `max_connections` 参数限制，默认值通常在 100-200 之间。当应用实例数量增加，每个实例都建立多个连接时，很容易超过这个限制。

## 解决方案：PgBouncer

PgBouncer 是一个轻量级的连接池代理，它可以显著减少到 PostgreSQL 服务器的实际连接数量，同时为应用程序提供所需的连接池功能。

## 安装 PgBouncer

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install pgbouncer
```

### CentOS/RHEL
```bash
sudo yum install pgbouncer
```

### 使用 Docker
```bash
docker pull edoburu/pgbouncer
docker run --name pgbouncer -e "DB_USER=dbuser" -e "DB_PASSWORD=dbpass" -e "DB_HOST=dbhost" -e "DB_NAME=dbname" -p 6432:6432 edoburu/pgbouncer
```

## 配置 PgBouncer

### 1. 编辑主配置文件

编辑 `/etc/pgbouncer/pgbouncer.ini`（路径可能因系统而异）：

```ini
[databases]
* = host=<db_host> port=<db_port>

# 或者为不同数据库指定不同配置
# mydb1 = host=<db_host> port=<db_port> dbname=mydb1
# mydb2 = host=<db_host> port=<db_port> dbname=mydb2

[pgbouncer]
# 监听设置
listen_addr = 0.0.0.0
listen_port = 6432

# 认证设置
auth_type = md5
auth_file = /etc/pgbouncer/userlist.txt

# 池化设置
pool_mode = transaction  # 事务池模式，推荐用于大多数应用
max_client_conn = 1000   # 最大客户端连接数
default_pool_size = 20   # 每个用户/数据库的默认池大小
min_pool_size = 0        # 最小连接池大小
reserve_pool_size = 5    # 预留连接池大小

# 日志设置
verbose = 1

# 超时设置（毫秒）
server_reset_query_always = 0
server_check_delay = 30000
server_check_query = select 1
server_lifetime = 3600
server_idle_timeout = 600
```

### 2. 设置用户认证

创建或编辑 `/etc/pgbouncer/userlist.txt`：

```
"postgres" "md5密码哈希"
"应用用户名" "md5密码哈希"
```

获取密码哈希的方法：
```sql
SELECT usename, passwd FROM pg_shadow;
```

### 3. 启动 PgBouncer

```bash
sudo service pgbouncer start
# 或
sudo systemctl start pgbouncer
```

## 配置应用程序

在我们的应用中，已经实现了 PgBouncer 支持。您需要设置以下环境变量：

```
# 启用PgBouncer
USE_PGBOUNCER=true

# PgBouncer连接信息
PGBOUNCER_HOST=localhost  # 或PgBouncer服务器的IP/主机名
PGBOUNCER_PORT=6432       # PgBouncer默认端口
```

## 性能调优建议

1. **池模式选择**:
   - `transaction`: 每个事务使用一个连接，事务结束后归还池。适合大多数Web应用。
   - `session`: 客户端会话期间保持连接。适合需要会话状态的应用。
   - `statement`: 每个语句使用一个连接。最节省连接但可能影响性能。

2. **池大小调整**:
   - `default_pool_size`: 根据应用需求和数据库服务器能力调整。
   - 计算公式: `(应用实例数 * 每实例最大连接数) / 数据库数量`

3. **监控**:
   - 定期使用 `SHOW POOLS` 和 `SHOW STATS` 命令监控 PgBouncer 状态。

## 对多个数据库的支持

PgBouncer 按 `数据库名+用户名+认证方式` 组合创建连接池。如果您有多个实例连接到不同数据库，但使用相同的用户名和认证方式，PgBouncer 将为每个数据库单独维护一个连接池。

所有针对同一个数据库的连接请求都会复用连接池中的连接，从而减少到 PostgreSQL 服务器的实际连接数。

## 故障排除

1. **连接问题**:
   ```bash
   psql -h localhost -p 6432 -U username databasename
   ```

2. **查看 PgBouncer 统计信息**:
   ```bash
   psql -h localhost -p 6432 -U pgbouncer pgbouncer -c "SHOW POOLS;"
   psql -h localhost -p 6432 -U pgbouncer pgbouncer -c "SHOW STATS;"
   psql -h localhost -p 6432 -U pgbouncer pgbouncer -c "SHOW CLIENTS;"
   psql -h localhost -p 6432 -U pgbouncer pgbouncer -c "SHOW SERVERS;"
   ```

3. **日志检查**:
   ```bash
   sudo tail -f /var/log/postgresql/pgbouncer.log
   ```

## 参考资料

- [PgBouncer 官方文档](https://www.pgbouncer.org/usage.html)
- [PostgreSQL 连接池最佳实践](https://www.postgresql.org/docs/current/runtime-config-connection.html) 