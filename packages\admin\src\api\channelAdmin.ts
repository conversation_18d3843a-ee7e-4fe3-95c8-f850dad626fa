import { request } from "./request";
import type {
	ChannelData,
	ChannelBalance,
	ChannelTransactionData,
	ChannelTransactionStatus,
	OrderData,
} from "@packages/shared";

export const channelAdminApi = {
	// 通道相关API
	channel: {
		getAll: () => request.get<ChannelData[]>("/admin/channel/all"),
		getBalance: (channelId: string) =>
			request.get<ChannelBalance>(`/admin/channel/${channelId}/balance`),
		getStatus: () =>
			request.get<
				{
					channel_id: string;
					name: string;
					db_url: string;
					status: string;
				}[]
			>("/admin/channel/status"),
	},

	// 交易相关API
	transaction: {
		getPending: (page = 1, size = 10) =>
			request.get<{ items: ChannelTransactionData[]; total: number }>(
				"/admin/channel/transactions/pending",
				{ params: { page, size } },
			),

		approve: (transactionId: string, review_comment?: string) =>
			request.post<ChannelTransactionData>(
				`/admin/channel/transactions/${transactionId}/approve`,
				{ review_comment },
			),

		reject: (transactionId: string, review_comment?: string) =>
			request.post<ChannelTransactionData>(
				`/admin/channel/transactions/${transactionId}/reject`,
				{ review_comment },
			),

		getAll: (params: {
			channelId?: string;
			status?: ChannelTransactionStatus;
			page?: number;
			size?: number;
		}) =>
			request.get<{ items: ChannelTransactionData[]; total: number }>(
				"/admin/channel/transactions",
				{ params },
			),
	},

	// 通道订单相关API
	orders: {
		getAll: (
			channelId: string,
			params: {
				page?: number;
				pageSize?: number;
				sortBy?: string;
				sortOrder?: "ASC" | "DESC";
				ts_codes?: string[];
				startDate?: string;
				endDate?: string;
				status?: string;
			},
		) =>
			request.get<{ items: OrderData[]; total: number }>(
				`/admin/channel/${channelId}/orders`,
				{ params },
			),

		getSummary: (channelId: string) =>
			request.get<{
				totalOrders: number;
				completedOrders: number;
				totalNotional: number;
				totalPremium: number;
			}>(`/admin/channel/${channelId}/orders/summary`),
	},
};
