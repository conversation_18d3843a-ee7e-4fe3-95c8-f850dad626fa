<template>
  <el-dialog v-model="visible" :title="title" width="90%" :close-on-click-modal="false" class="pdf-preview-dialog">
    <div class="pdf-container">
      <iframe :src="pdfUrl" width="100%" height="100%" frameborder="0"></iframe>
    </div>
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
      <el-button type="primary" @click="handleDownload">下载</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: boolean
  pdfUrl: string
  title: string
  filename: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const visible = ref(props.modelValue)

watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const handleDownload = () => {
  const link = document.createElement('a')
  link.href = props.pdfUrl
  link.download = props.filename
  link.click()
}
</script>

<style>
/* 对话框样式需要是全局的 */
.el-dialog.pdf-preview-dialog {
  height: 94vh;
  display: flex;
  flex-direction: column;
  margin: 3vh auto !important;
}

.pdf-preview-dialog .el-dialog__body {
  flex: 1;
  overflow: hidden;
  padding: 0;
}
</style>

<style scoped>
/* 组件内的其他样式可以保持 scoped */
.pdf-container {
  height: 100%;
}

.pdf-container iframe {
  height: 100%;
}
</style>