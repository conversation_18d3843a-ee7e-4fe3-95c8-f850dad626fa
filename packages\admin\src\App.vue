<script setup lang="ts">
import { onMounted } from "vue";
import { useTheme } from "@/composables/useTheme";
import http from "@/plugins/axios";
import { useStockStore } from "@/stores/stock";
import { useSiteConfigStore } from "@/stores/siteConfig";
import { useSharedConfigStore } from "@/stores/sharedConfig";

// 初始化主题
useTheme();

// 初始化股票数据存储
const stockStore = useStockStore();
const siteConfigStore = useSiteConfigStore();
const sharedConfigStore = useSharedConfigStore();

onMounted(async () => {
  try {
    // 获取初始 CSRF token
    await http.get("/health");

    // 初始化股票数据（非阻塞）
    stockStore.initialize();

    // 加载站点配置
    siteConfigStore.loadSiteConfig();

    // 加载共享配置
    sharedConfigStore.loadSharedConfig();
  } catch (error) {
    console.error("Failed to initialize admin app:", error);
  }
});
</script>

<template>
  <router-view />
</template>

<style>
:root {
  --el-color-primary: #409eff;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

.el-dialog__body {
  padding: 12px;
}

/* 移动端适配的基础样式 */
@media (max-width: 767px) {
  .el-message-box {
    width: 90% !important;
    max-width: 420px;
  }

  .el-dialog {
    width: 90% !important;
    max-width: 460px;
  }

  .el-form-item__label {
    float: none !important;
    display: block !important;
    text-align: left !important;
    margin-bottom: 8px !important;
  }

  .el-form-item__content {
    margin-left: 0 !important;
  }

  .el-table {
    width: 100% !important;
    overflow-x: auto !important;
  }
}
</style>
