<template>
  <div class="transactions-view view">
    <div class="header-container">
      <h2>资金流水</h2>
      <div class="controls">
        <el-button :loading="exporting" @click="exportData">
          <el-icon>
            <Download />
          </el-icon>
          导出数据
        </el-button>
      </div>
    </div>
    <div class="search-bar">
      <el-input v-model="searchQuery" placeholder="输入用户ID搜索" class="search-input" clearable
        @keydown.enter="handleSearch" @clear="handleClear">
        <template #prefix>
          <el-icon>
            <Search />
          </el-icon>
        </template>
      </el-input>
      <el-select v-model="selectedTypes" multiple collapse-tags placeholder="交易类型" class="type-select"
        @change="handleTypeChange">
        <el-option v-for="(label, type) in transactionTypeLabels" :key="type" :label="label" :value="type" />
      </el-select>
      <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
        end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleDateRangeChange" />
    </div>

    <el-table :data="transactions" style="width: 100%" @sort-change="handleSortChange">
      <el-table-column prop="txn_id" label="交易ID" min-width="108" sortable="custom" />
      <el-table-column prop="user_id" label="用户ID" min-width="108" sortable="custom" />
      <el-table-column prop="type" label="类型" min-width="120" sortable="custom">
        <template #default="{ row }">
          <el-tag :type="getTransactionTypeTag(row.type)">
            {{ formatTransactionType(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="signed_amount" label="金额" min-width="120" sortable="custom">
        <template #default="{ row }">
          <span :class="{ 'positive-amount': row.signed_amount > 0, 'negative-amount': row.signed_amount < 0 }">
            {{ formatAmount(row.signed_amount, row.currency) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="currency" label="货币" min-width="100" sortable="custom" />
      <el-table-column prop="trade_no" label="交易编号" min-width="120" sortable="custom" />
      <el-table-column prop="created_at" label="创建时间" min-width="180" sortable="custom">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        :total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { Search, Download } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { financeApi } from "@/api";
import { TransactionType } from "@packages/shared";
import type { TransactionData } from "@packages/shared";
import { formatDate } from "@/utils/format";
import { exportToCsv } from "@/utils/export";

const searchQuery = ref("");
const transactions = ref<TransactionData[]>([]);

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const sortOrder = ref<"DESC" | "ASC">("DESC");
const exporting = ref(false);

const dateRange = ref<[string, string] | null>(null);
const selectedTypes = ref<TransactionType[]>([]);

// 交易类型标签映射
const transactionTypeLabels: Record<string, string> = {
  [TransactionType.DEPOSIT]: "入金",
  [TransactionType.WITHDRAW]: "出金",
  [TransactionType.BUY]: "下单",
  [TransactionType.SELL]: "结算",
  [TransactionType.TRANSFER]: "转账",
  [TransactionType.EXCHANGE]: "换汇",
  [TransactionType.PLATFORM_DEPOSIT]: "平台入金",
};

// 交易类型标签颜色映射
const getTransactionTypeTag = (type: TransactionType): string => {
  const tagMap: Record<TransactionType, string> = {
    [TransactionType.DEPOSIT]: "success",
    [TransactionType.WITHDRAW]: "danger",
    [TransactionType.BUY]: "warning",
    [TransactionType.SELL]: "success",
    [TransactionType.TRANSFER]: "info",
    [TransactionType.EXCHANGE]: "primary",
    [TransactionType.PLATFORM_DEPOSIT]: "success",
  };
  return tagMap[type] || "info";
};

const formatTransactionType = (type: TransactionType): string => {
  return transactionTypeLabels[type] || type;
};

const formatAmount = (amount: number, currency: string): string => {
  return `${amount > 0 ? "+" : ""}${amount.toFixed(2)} ${currency}`;
};

const exportData = async () => {
  exporting.value = true;
  try {
    const response = await financeApi.getTransactions(1, 999999, {
      sortOrder: sortOrder.value,
      filters: getFilters(),
    });

    const headers = [
      "交易ID",
      "用户ID",
      "类型",
      "金额",
      "货币",
      "订单号",
      "创建时间",
    ];
    const csvData =
      response?.items.map((item) => [
        item.txn_id,
        item.user_id,
        formatTransactionType(item.type),
        item.signed_amount,
        item.currency,
        item.trade_no || "",
        formatDate(item.created_at),
      ]) || [];

    exportToCsv(headers, csvData, "transactions");
    ElMessage.success("导出成功");
  } catch (error) {
    console.error("Export failed:", error);
    ElMessage.error("导出失败");
  } finally {
    exporting.value = false;
  }
};

const getFilters = () => {
  const filters: {
    user_id?: number;
    types?: TransactionType[];
    startDate?: string;
    endDate?: string;
  } = {};

  if (searchQuery.value.trim()) {
    const userId = Number.parseInt(searchQuery.value);
    if (!Number.isNaN(userId)) {
      filters.user_id = userId;
    }
  }

  if (selectedTypes.value.length > 0) {
    filters.types = selectedTypes.value;
  }

  if (dateRange.value) {
    filters.startDate = dateRange.value[0];
    filters.endDate = dateRange.value[1];
  }

  return filters;
};

const loadData = async () => {
  try {
    const result = await financeApi.getTransactions(
      currentPage.value,
      pageSize.value,
      {
        sortOrder: sortOrder.value,
        filters: getFilters(),
      },
    );

    transactions.value = result?.items || [];
    total.value = result?.total || 0;
  } catch (error) {
    console.error("Failed to fetch transactions:", error);
    ElMessage.error("获取资金流水数据失败");
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  loadData();
};

const handleClear = () => {
  searchQuery.value = "";
  loadData();
};

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  loadData();
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  loadData();
};

const handleSortChange = ({ order }: { order?: string }) => {
  sortOrder.value = order === "ascending" ? "ASC" : "DESC";
  loadData();
};

const handleDateRangeChange = () => {
  handleSearch();
};

const handleTypeChange = () => {
  handleSearch();
};

onMounted(() => loadData());
</script>

<style scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

h2 {
  margin: 0;
}

.search-bar {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 300px;
}

.type-select {
  width: 200px;
}

.positive-amount {
  color: #67c23a;
  font-weight: bold;
}

.negative-amount {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.el-table) {
  --el-table-bg-color: var(--el-bg-color-overlay);
  --el-table-tr-bg-color: var(--el-bg-color-overlay);
  --el-table-header-bg-color: var(--el-bg-color-overlay);
  --el-table-border-color: var(--el-border-color-lighter);

  background-color: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-overlay);
  color: var(--el-text-color-regular);
}

:deep(.el-table td) {
  color: var(--el-text-color-primary);
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: var(--el-table-row-hover-bg-color);
}

:deep(.el-input__wrapper) {
  background-color: var(--el-bg-color-overlay);
}

:deep(.el-input__inner) {
  color: var(--el-text-color-primary);
}

:deep(.el-input__prefix-icon) {
  color: var(--el-text-color-placeholder);
}

@media (max-width: 768px) {
  .search-bar {
    gap: 12px;
  }

  .search-input {
    width: 100%;
  }

  .type-select {
    width: 100%;
  }
}
</style>