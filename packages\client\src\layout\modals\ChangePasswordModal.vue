<template>
  <el-dialog v-model="visible" title="修改密码" width="360px" :show-close="true" destroy-on-close @close="close"
    class="change-password-dialog">
    <form @submit.prevent="handleSubmit" class="password-form">
      <div class="form-group">
        <el-input v-model="oldPassword" :type="showOldPassword ? 'text' : 'password'" placeholder="旧密码">
          <template #suffix>
            <el-icon class="toggle-password" @click="showOldPassword = !showOldPassword">
              <SvgIcon :name="showOldPassword ? 'eye-rs-flaticon' : 'crossed-eye-rs-flaticon'" />
            </el-icon>
          </template>
        </el-input>
      </div>
      <div class="form-group">
        <el-input v-model="newPassword" :type="showNewPassword ? 'text' : 'password'" placeholder="新密码">
          <template #suffix>
            <el-icon class="toggle-password" @click="showNewPassword = !showNewPassword">
              <SvgIcon :name="showNewPassword ? 'eye-rs-flaticon' : 'crossed-eye-rs-flaticon'" />
            </el-icon>
          </template>
        </el-input>
      </div>
      <div class="form-group">
        <el-input v-model="confirmPassword" :type="showConfirmPassword ? 'text' : 'password'" placeholder="确认密码"
          @keyup.enter="handleSubmit">
          <template #suffix>
            <el-icon class="toggle-password" @click="showConfirmPassword = !showConfirmPassword">
              <SvgIcon :name="showConfirmPassword ? 'eye-rs-flaticon' : 'crossed-eye-rs-flaticon'" />
            </el-icon>
          </template>
        </el-input>
      </div>
      <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
    </form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { authApi } from "@/api";
import { getErrorMessage } from "@packages/shared";
import { useAuthStore } from "@/stores/auth";
import SvgIcon from "@/components/SvgIcon.vue";

const visible = ref(true);
const oldPassword = ref("");
const newPassword = ref("");
const confirmPassword = ref("");
const errorMessage = ref("");

// 添加显示密码的状态
const showOldPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

const emit = defineEmits(["close", "submit"]);

const close = () => {
	visible.value = false;
	emit("close");
};

function checkPasswordStrength(password: string) {
	const minLength = 8;
	const hasLetters = /[a-zA-Z]/.test(password);
	const hasNumbers = /\d/.test(password);
	const hasSpecialChars = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password);

	if (password.length < minLength) return "至少8个字符";
	if (!hasLetters) return "必须包含字母";
	if (!hasNumbers) return "必须包含数字";
	if (!hasSpecialChars) return "必须包含特殊字符";

	return "";
}

const handleSubmit = async () => {
	errorMessage.value = "";

	const strengthCheck = checkPasswordStrength(newPassword.value);
	if (strengthCheck) {
		errorMessage.value = strengthCheck;
		return;
	}

	if (newPassword.value !== confirmPassword.value) {
		errorMessage.value = "两次输入的新密码不一致";
		return;
	}

	try {
		await authApi.changePassword({
			old_password: oldPassword.value,
			new_password: newPassword.value,
		});

		// 清除保存的密码
		localStorage.removeItem("rememberedPassword");

		// 关闭弹窗
		close();

		// 提示用户重新登录
		ElMessage.success("密码修改成功，请重新登录");

		// 登出
		useAuthStore().logout();
	} catch (error) {
		errorMessage.value = getErrorMessage(error) || "发生未知错误";
	}
};
</script>

<style>
.change-password-dialog {
  .el-dialog__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    padding: 0 0 8px;
    margin-top: -4px !important;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .el-dialog__headerbtn {
    position: relative;
  }

  .el-dialog__footer {
    border-top: 1px solid var(--el-border-color-lighter);
  }
}
</style>

<style scoped>
.password-form {
  padding: 20px 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.error-message {
  color: var(--el-color-danger);
  font-size: 14px;
  margin-top: 8px;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.toggle-password {
  cursor: pointer;
}

.toggle-password:hover {
  opacity: 0.8;
}

.toggle-password .svg-icon {
  width: 16px;
  height: 16px;
}
</style>
