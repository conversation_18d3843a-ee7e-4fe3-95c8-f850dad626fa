import logger from "@/utils/logger.js";

/**
 * Calculate the volume-weighted average price (VWAP).
 * @param initialData Initial data.
 * @param finalData Final data.
 * @returns VWAP.
 */
export function calculateVWAP(
	initialData: { volume: number; turnover: number },
	finalData: { volume: number; turnover: number },
	isSTAR: boolean,
): number | undefined {
	const volumeDiff = finalData.volume - initialData.volume;
	const turnoverDiff = finalData.turnover - initialData.turnover;
	// 科创板成交量每手股数为 1
	const result = (turnoverDiff * 10000) / (volumeDiff * (isSTAR ? 1 : 100));
	logger.info({ result, volumeDiff, turnoverDiff }, "VWAP");
	return volumeDiff !== 0 ? result : undefined;
}

/**
 * Calculate the trade amount for buy
 * @param scale Trade scale (10,000)
 * @param quote Quote (%)
 * @returns Trade amount (yuan)
 */
export const calcBuyAmount = (scale: number, quote: number): number => {
	return scale * 10000 * (quote / 100);
};

/**
 * Calculate the trade profit for sell
 * @param scale Trade scale (10,000)
 * @param settle_price Settle price
 * @param entry_price Entry price
 * @param structure Option structure
 * @returns Trade profit (yuan)
 */
export const calcSellProfit = (
	scale: number,
	settle_price: number,
	entry_price: number,
	exercise_price: number,
	structure: string,
): number => {
	const isCall = structure.endsWith("C");
	const priceProfit = isCall
		? settle_price - exercise_price
		: exercise_price - settle_price;
	return priceProfit > 0 ? scale * 10000 * (priceProfit / entry_price) : 0;
};
