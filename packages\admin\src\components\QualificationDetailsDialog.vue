<template>
  <el-dialog v-model="dialogVisible" title="资质详情" width="60%" top="5vh" class="qualification-dialog"
    :destroy-on-close="true">
    <div v-if="data" class="qualification-details">
      <!-- 基本信息 -->
      <section class="details-section">
        <h3>基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名/公司名">{{ data.name }}</el-descriptions-item>
          <el-descriptions-item label="证件号码">{{ data.id_number }}</el-descriptions-item>
          <el-descriptions-item v-if="data.phone_number" label="手机号">{{ data.phone_number }}</el-descriptions-item>
          <el-descriptions-item label="银行名称">{{ data.bank_name }}</el-descriptions-item>
          <el-descriptions-item label="银行编号">{{ data.bank_code }}</el-descriptions-item>
          <el-descriptions-item label="银行账号">{{ data.bank_account }}</el-descriptions-item>
        </el-descriptions>
      </section>

      <!-- 文件资料 -->
      <section class="details-section">
        <h3>文件资料</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="上传文件">
            <div v-if="data.documents?.length" class="file-preview">
              <!-- 遍历所有文件 -->
              <div v-for="file in data.documents" :key="file.uid" class="file-preview">
                <!-- 图片预览 -->
                <el-image v-if="isImage(file.name)" :src="fileUrls[file.uid]" :preview-src-list="[fileUrls[file.uid]]"
                  fit="contain" class="preview-image">
                  <template #error>
                    <div class="image-error">
                      <el-icon>
                        <Picture />
                      </el-icon>
                      <span>图片加载失败</span>
                    </div>
                  </template>
                </el-image>
                <!-- 文件信息和下载按钮 -->
                <div class="file-info">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span>{{ file.name }}</span>
                  <el-button type="primary" link @click="downloadFile(file)">
                    Download
                  </el-button>
                </div>
              </div>
            </div>
          </el-descriptions-item>

          <!-- 签名 -->
          <el-descriptions-item v-if="data.signature" label="签名">
            <div class="file-preview">
              <el-image :src="data.signature" fit="contain" class="preview-image" />
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </section>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from "vue";
import { fileApi } from "@/api";

const props = defineProps<{
  modelValue: boolean;
  data: QualificationData | null;
}>();

// 存储文件的 blob URLs
const fileUrls = ref<Record<string, string>>({});

// 获取文件并创建 blob URL
const getFileUrl = async (uid: string) => {
  try {
    const response = await fileApi.downloadFile(uid);
    if (!response) return "";
    const url = URL.createObjectURL(response as Blob);
    fileUrls.value[uid] = url;
    return url;
  } catch (error) {
    console.error("Failed to load file:", error);
  }
};

// 加载所有需要的文件 URL
const loadFileUrls = async () => {
  if (!props.data) return;

  // 加载所有文档文件
  if (props.data.documents) {
    for (const file of props.data.documents) {
      await getFileUrl(file.uid);
    }
  }
};

// 组件挂载时加载文件
onMounted(loadFileUrls);

// 添加一个标志来追踪组件是否已卸载
const isUnmounted = ref(false);

// 监听 data 的变化
watch(() => props.data, loadFileUrls, { immediate: true });

// 组件卸载时设置标志
// 组件卸载时清理所有创建的 blob URLs
onUnmounted(() => {
  isUnmounted.value = true;
  // 清理 blob URLs
  for (const url of Object.values(fileUrls.value)) {
    URL.revokeObjectURL(url);
  }
});

import { Picture, Document } from "@element-plus/icons-vue";
import type { QualificationData } from "@packages/shared";

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
}>();

const isImage = (filename: string) => {
  const ext = filename.toLowerCase().split(".").pop();
  return ["jpg", "jpeg", "png", "gif", "webp"].includes(ext || "");
};

const downloadFile = async (file: { uid: string; name: string }) => {
  try {
    const response = await fileApi.downloadFile(file.uid);
    if (!response) return "";
    // 创建一个临时的 URL 和下载链接
    const url = URL.createObjectURL(response as Blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    // 清理临时 URL
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Failed to download file:", error);
  }
};

// 使用计算属性来处理对话框的显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});
</script>

<style scoped>
.qualification-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.qualification-details {
  max-height: 70vh;
  overflow-y: auto;
}

.details-section {
  margin-bottom: 24px;
}

.details-section h3 {
  margin-bottom: 16px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.file-preview {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.preview-image {
  height: 120px;
  border-radius: 4px;
}

/* 为签名预览添加特殊样式 */
.file-preview :deep(.el-image) {
  background-color: white;
  border: 1px solid var(--el-border-color-lighter);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
}

.description {
  margin-top: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}
</style>
