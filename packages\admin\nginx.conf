server {
    listen 80;
    server_name _; # 监听所有主机名，因为路由由 Traefik 处理

    root /usr/share/nginx/html;
    index index.html;

    # gzip 压缩 (可选但推荐)
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;

    location / {
        # 关键：尝试直接提供文件，如果找不到则回退到 index.html 以支持 SPA 路由
        try_files $uri $uri/ /index.html;
    }

    # 可以添加对特定静态资源的缓存控制头
    location ~* \.(?:jpg|jpeg|gif|png|ico|css|js|svg|woff|woff2|ttf|eot)$ {
        expires 7d;
        add_header Cache-Control "public";
    }

    # 拒绝访问隐藏文件
    location ~ /\. {
        deny all;
    }
} 