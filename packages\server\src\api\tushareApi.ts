import { ENV } from "@/config/configManager.js";
import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";
import type { StockInfo, TradeCalendarRange } from "@packages/shared";
import type { DailyData, LimitData } from "@/types/api.js";
import axiosWithDNSFailover from "./dnsFailoverAxios.js";
import { dateToCompactString } from "@/utils/dateUtils.js";

/**
 * Fetch the stock list from Tushare API.
 * @returns Stock list
 */
export async function fetchStockListFromAPI(): Promise<StockInfo[]> {
	const response = await axiosWithDNSFailover.post("http://api.tushare.pro", {
		api_name: "stock_basic",
		token: ENV.TUSHARE_TOKEN,
		params: { list_status: "L" },
		fields: "ts_code,name,market",
	});

	return response.data.data.items.map((item: [string, string, string]) => ({
		ts_code: item[0],
		name: item[1],
		market: item[2],
	}));
}

/**
 * Fetch stocks which ex_date is today from Tushare API.
 * @returns Ex_date list
 */
export async function fetchExDateListFromAPI(): Promise<
	Array<{ ts_code: string; ex_date: string }>
> {
	const today = new Date();
	const ex_date = dateToCompactString(today);
	try {
		const response = await axiosWithDNSFailover.post("http://api.tushare.pro", {
			api_name: "dividend",
			token: ENV.TUSHARE_TOKEN,
			params: { ex_date },
			fields: "ts_code, ex_date",
		});

		if (!response?.data?.data?.items) {
			throw AppError.create(
				"INVALID_API_RESPONSE",
				"Invalid today's ex_date list response",
			);
		}

		return response.data.data.items.map((item: Array<string>) => ({
			ts_code: item[0],
			ex_date: item[1],
		}));
	} catch (error) {
		logger.error(error, "Error fetching today's ex_date list from API");
		throw AppError.create(
			"FETCH_EX_DATE_LIST_FAILED",
			"Failed to fetch today's ex_date list from API",
		);
	}
}

/**
 * Fetch the up down limit from Tushare API.
 * @returns Up down limit data
 */
export async function fetchUpDownLimitFromAPI(params: {
	date?: Date;
	ts_code?: string;
}): Promise<LimitData> {
	const trade_date = params.date ? dateToCompactString(params.date) : undefined;
	try {
		const response = await axiosWithDNSFailover.post("http://api.tushare.pro", {
			api_name: "stk_limit",
			token: ENV.TUSHARE_TOKEN,
			params: { trade_date, ts_code: params.ts_code },
			fields: "trade_date,ts_code,pre_close,up_limit,down_limit",
		});

		if (!response?.data?.data?.items) {
			throw AppError.create(
				"INVALID_API_RESPONSE",
				"Invalid trade calendar response",
			);
		}

		return response.data.data.items.map((item: Array<string | number>) => ({
			trade_date: item[0],
			ts_code: item[1],
			pre_close: item[2],
			up_limit: item[3],
			down_limit: item[4],
		}));
	} catch (error) {
		logger.error(error, "Error fetching up down limit from API");
		throw AppError.create(
			"FETCH_UP_DOWN_LIMIT_FAILED",
			"Failed to fetch up down limit from API",
		);
	}
}

/**
 * Fetch the trade calendar from Tushare API.
 * @returns Trade calendar
 */
export async function fetchTradeCalendarRangeFromAPI(
	startDate: Date,
	endDate: Date,
): Promise<TradeCalendarRange[]> {
	const start = dateToCompactString(startDate);
	const end = dateToCompactString(endDate);

	try {
		const response = await axiosWithDNSFailover.post("http://api.tushare.pro", {
			// 默认 exchange：上交所 SSE
			api_name: "trade_cal",
			token: ENV.TUSHARE_TOKEN,
			params: {
				start_date: start,
				end_date: end,
			},
			fields: "cal_date,is_open,pretrade_date",
		});

		if (!response?.data?.data?.items) {
			throw AppError.create(
				"INVALID_API_RESPONSE",
				"Invalid trade calendar response",
			);
		}

		return response.data.data.items.map((item: Array<string | number>) => ({
			cal_date: item[0],
			is_open: item[1],
			pretrade_date: item[2],
		}));
	} catch (error) {
		logger.error(
			error,
			`Error fetching trade calendar from API for ${start} to ${end}`,
		);
		throw error;
	}
}

/**
 * Fetch the close price from Tushare API.
 * Time: 15:00PM~16:00PM update current day data. Will skip the suspended day of stocks.
 * @param date Trade date
 * @param fields Fields to return. The order of fields is fixed.
 * @returns Close price data. e.g. [["000001.SZ", "20240102", 10.01], ...]
 */
export async function fetchDailyDataFromAPI(
	params: {
		date?: Date;
		ts_code?: string;
		limit?: number;
	},
	fields: ["ts_code"?, "trade_date"?, "close"?],
): Promise<DailyData> {
	const trade_date = params.date ? dateToCompactString(params.date) : undefined;
	try {
		const response = await axiosWithDNSFailover.post("http://api.tushare.pro", {
			api_name: "daily",
			token: ENV.TUSHARE_TOKEN,
			params: { trade_date, ts_code: params.ts_code, limit: params.limit },
			fields: fields.join(","),
		});

		if (!response?.data?.data?.items) {
			throw AppError.create(
				"INVALID_API_RESPONSE",
				"Invalid close price response",
			);
		}

		return response.data.data.items;
	} catch (error) {
		logger.error(error, `Error fetching close price for ${trade_date}`);
		throw AppError.create(
			"FETCH_CLOSE_PRICE_FAILED",
			"Failed to fetch close price",
			{ trade_date },
		);
	}
}

/**
 * Fetch suspension data from Tushare API
 * @param params Query parameters
 * @returns Suspension data with type 'S' for suspended or 'R' for resumed
 */
export async function fetchSuspensionFromAPI(params: {
	ts_code?: string;
	trade_date: Date;
}): Promise<
	Array<{ ts_code: string; trade_date: string; suspend_type: string }>
> {
	const trade_date = dateToCompactString(params.trade_date);
	try {
		const response = await axiosWithDNSFailover.post("http://api.tushare.pro", {
			api_name: "suspend_d",
			token: ENV.TUSHARE_TOKEN,
			params: {
				ts_code: params.ts_code,
				trade_date,
			},
			fields: "ts_code,trade_date,suspend_type",
		});

		if (!response?.data?.data?.items) {
			throw AppError.create(
				"INVALID_API_RESPONSE",
				"Invalid suspension data response",
			);
		}

		return response.data.data.items.map((item: Array<string>) => ({
			ts_code: item[0],
			trade_date: item[1],
			suspend_type: item[2],
		}));
	} catch (error) {
		logger.error(error, `Error fetching suspension data for ${params.ts_code}`);
		throw error;
	}
}
