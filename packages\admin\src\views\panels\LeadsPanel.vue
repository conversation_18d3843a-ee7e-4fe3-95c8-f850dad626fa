<template>
	<div class="leads-panel">
		<div class="header-controls">
			<div class="search-bar">
				<el-input v-model="searchQuery" placeholder="搜索手机号" class="search-input" clearable @keydown.enter="handleSearch"
					@clear="handleClear">
					<template #prefix>
						<el-icon>
							<Search />
						</el-icon>
					</template>
				</el-input>
			</div>
			<div class="controls">
				<el-button :loading="exporting" @click="exportData">
					<el-icon>
						<Download />
					</el-icon>
					导出数据
				</el-button>
			</div>
		</div>

		<el-table :data="leads" stripe style="width: 100%;" @sort-change="handleSortChange">
			<el-table-column prop="id" label="ID" min-width="80" sortable="custom" />
			<el-table-column prop="phone_number" label="手机号" min-width="140" />
			<el-table-column prop="source" label="来源" min-width="120" />
			<el-table-column label="状态" min-width="100">
				<template #default="{ row }">
					<el-tag :type="row.is_converted ? 'success' : 'info'">
						{{ row.is_converted ? '已转化' : '未转化' }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="remark" label="备注" min-width="200" />
			<el-table-column prop="created_at" label="创建时间" min-width="170" sortable="custom">
				<template #default="{ row }">
					{{ formatDate(row.created_at) }}
				</template>
			</el-table-column>
		</el-table>

		<div class="pagination-container">
			<el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
				:total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
				@current-change="handleCurrentChange" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue";
import { Search, Download } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { leadsApi } from "@/api/leads";
import type { LeadInfo } from "@packages/shared";
import { formatDate } from "@/utils/format";
import { exportToCsv } from "@/utils/export";

const leads = ref<LeadInfo[]>([]);
const searchQuery = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const sortBy = ref("created_at");
const sortOrder = ref<"ASC" | "DESC">("DESC");
const exporting = ref(false);

const loadLeads = async () => {
	try {
		const filters: Record<string, string | number> = {};

		if (searchQuery.value) {
			filters.phone_number = searchQuery.value;
		}

		const result = await leadsApi.getLeads({
			page: currentPage.value,
			pageSize: pageSize.value,
			sortBy: sortBy.value,
			sortOrder: sortOrder.value,
			filters,
		});

		leads.value = result?.items || [];
		total.value = result?.total || 0;
	} catch (error) {
		console.error("Failed to fetch data:", error);
		ElMessage.error("加载意向客户列表失败");
	}
};

const handleSortChange = ({
	prop,
	order,
}: { prop?: string; order?: string }) => {
	sortBy.value = prop || "created_at";
	sortOrder.value = order === "ascending" ? "ASC" : "DESC";
	loadLeads();
};

const handleSearch = () => {
	currentPage.value = 1; // Reset to first page when searching
	loadLeads();
};

const handleSizeChange = (newSize: number) => {
	pageSize.value = newSize;
	loadLeads();
};

const handleCurrentChange = (newPage: number) => {
	currentPage.value = newPage;
	loadLeads();
};

const handleClear = () => {
	loadLeads();
};

const exportData = async () => {
	try {
		exporting.value = true;
		const filters: Record<string, string | number> = {};

		if (searchQuery.value) {
			filters.phone_number = searchQuery.value;
		}

		const data = await leadsApi.exportLeads({ filters });

		if (data) {
			const headers = ["ID", "手机号", "来源", "状态", "备注", "创建时间"];

			const csvData = data.map((lead) => [
				lead.id,
				lead.phone_number,
				lead.source,
				lead.is_converted ? "已转化" : "未转化",
				lead.remark || "",
				formatDate(lead.created_at),
			]);

			exportToCsv(headers, csvData, "leads");
			ElMessage.success("导出成功");
		} else {
			throw new Error("No data returned");
		}
	} catch (error) {
		console.error("Failed to export data:", error);
		ElMessage.error("导出失败");
	} finally {
		exporting.value = false;
	}
};

// 保存页面状态
const saveState = () => {
	const state = {
		searchQuery: searchQuery.value,
		currentPage: currentPage.value,
		pageSize: pageSize.value,
		sortBy: sortBy.value,
		sortOrder: sortOrder.value,
	};
	sessionStorage.setItem("leadsPanel_state", JSON.stringify(state));
};

// 恢复页面状态
const restoreState = () => {
	const savedState = sessionStorage.getItem("leadsPanel_state");
	if (savedState) {
		try {
			const state = JSON.parse(savedState);
			searchQuery.value = state.searchQuery || "";
			currentPage.value = state.currentPage || 1;
			pageSize.value = state.pageSize || 10;
			sortBy.value = state.sortBy || "created_at";
			sortOrder.value = state.sortOrder || "DESC";
		} catch (e) {
			console.error("Failed to parse saved state:", e);
		}
	}
};

onMounted(() => {
	restoreState();
	loadLeads();
});

onBeforeUnmount(() => {
	saveState();
});
</script>

<style scoped>
.leads-panel {
	width: 100%;
}

.header-controls {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 24px;
	margin-bottom: 20px;
}

.search-bar {
	display: flex;
	gap: 10px;
}

.search-input {
	width: 300px;
}

@media (max-width: 768px) {
	.header-controls {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.search-bar {
		width: 100%;
	}

	.search-input {
		flex: 1;
	}
}
</style>