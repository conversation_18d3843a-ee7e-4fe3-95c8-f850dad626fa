import * as Order from "@/models/trade/order.js";
import { APP_CONFIG } from "@/config/configManager.js";
import type { OrderData } from "@packages/shared";
import logger from "@/utils/logger.js";

/**
 * 获取接口订单数据
 * 返回格式：trade_no~user_id~stock_code~entry_price~exercise_price~settle_price~scale~total_scale~term~quote~quote_diff~structure~status~created_at~closed_at~quote_provider~app_id;...
 * @returns 订单数据
 */
export async function getAllOrdersByApi(): Promise<string> {
	try {
		// 获取所有订单数据
		const orders = await Order.getAll({
			pageSize: 999999, // 获取所有订单
		});

		return orders.items
			.map((order) => {
				// 处理股票代码：移除字母部分
				const stockCode = order.ts_code.replace(/\.[A-Z]+$/, "");

				// 构建数据字符串，用波浪号连接
				const values = [
					order.trade_no || "",
					order.user_id?.toString() || "",
					stockCode || "",
					order.entry_price?.toString() || "",
					order.exercise_price?.toString() || "",
					order.settle_price?.toString() || "",
					order.scale?.toString() || "",
					order.total_scale?.toString() || "",
					order.term?.toString() || "",
					order.quote?.toString() || "",
					order.quote_diff?.toString() || "0",
					order.structure || "",
					order.status || "",
					order.created_at ? order.created_at.split("T")[0] : "",
					order.closed_at ? order.closed_at.split("T")[0] : "",
					order.quote_provider || "INK",
					APP_CONFIG.tradingPlatformId || APP_CONFIG.channelId || "",
				].join("~");

				return values;
			})
			.join(";\n");
	} catch (error) {
		logger.error(error, "获取订单数据失败");
		return "";
	}
}

/**
 * 管理端获取所有订单
 */
export async function getAllOrdersByAdmin(
	page: number,
	pageSize: number,
	options?: {
		sortBy?: string;
		sortOrder?: "ASC" | "DESC";
		user_id?: number;
	},
): Promise<{ total: number; items: OrderData[] }> {
	return Order.getAll({
		page,
		pageSize,
		sortBy: options?.sortBy,
		sortOrder: options?.sortOrder,
		filters: {
			user_id: options?.user_id,
		},
	});
}
