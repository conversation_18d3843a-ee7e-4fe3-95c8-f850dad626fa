import type { Currency } from "./fund.js";
import { TransactionType } from "./fund.js";

// 资金操作类型（用于审核等）
export const FundTransaction = {
	DEPOSIT: TransactionType.DEPOSIT,
	WITHDRAW: TransactionType.WITHDRAW,
	PLATFORM_DEPOSIT: TransactionType.PLATFORM_DEPOSIT,
} as const;

export type FundTransactionType =
	(typeof FundTransaction)[keyof typeof FundTransaction];

// 审核类型
export enum AuditType {
	QUALIFICATION = "qualification",
	FUND = "fund",
}

export enum AuditStatus {
	PENDING = "pending",
	APPROVED = "approved",
	REJECTED = "rejected",
}

// 审核数据
interface BaseAuditData {
	audit_id: number;
	user_id: number;
	status: AuditStatus;
	created_at: Date;
	admin_id: number;
	comment?: string;
}

export interface QualificationAuditData extends BaseAuditData {
	type: AuditType.QUALIFICATION;
	data: QualificationData;
}

export interface FundAuditData extends BaseAuditData {
	type: AuditType.FUND;
	amount: number;
	operation: FundTransactionType;
	currency: Currency;
}

export type AuditData = QualificationAuditData | FundAuditData;

export interface QualificationCriteria {
	portfolio: {
		selected: boolean;
		proofFiles?: Array<{
			uid: string;
			name: string;
		}>;
	};
	experience: {
		selected: boolean;
		proofFiles?: Array<{
			uid: string;
			name: string;
		}>;
	};
}

export interface QualificationData {
	name: string; // 姓名/公司名（英文大写）
	id_number: string; // 身份证号/登记证号
	phone_number: string; // 手机号
	bank_name: string; // 银行名称
	bank_code: string; // 银行编号
	bank_account: string; // 银行账号
	documents: Array<{
		// 上传的证件文件
		uid: string;
		name: string;
	}>;
	declarationAccepted: boolean; // 声明确认状态
	signature: string | null; // 电子签名
}

export type UpdateQualificationInfoData = Omit<
	QualificationData,
	"documents" | "declarationAccepted" | "signature" | "name"
> & {
	name?: string;
};

export interface CreateFundAuditRequest {
	operation: FundTransactionType;
	amount: number;
	currency: Currency;
	password?: string;
}

export interface CreateQualificationAuditRequest {
	data: QualificationData;
	comment?: string;
}

export interface CreateQualificationAuditInput
	extends CreateQualificationAuditRequest {
	user_id: number;
}

export interface CreateFundAuditInput extends CreateFundAuditRequest {
	user_id: number;
}

export interface PlatformDepositRecord {
	audit_id: number;
	user_id: number;
	amount: number;
	status: AuditStatus;
	created_at: string;
	comment?: string;
	admin_id?: number;
	admin_name?: string;
}
