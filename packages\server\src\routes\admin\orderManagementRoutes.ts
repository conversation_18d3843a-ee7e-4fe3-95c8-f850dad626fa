import { Router } from "express";
import * as OrderManagementService from "@/services/admin/orderManagementService.js";
import * as Order from "@/models/trade/order.js";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";

const router = Router();

// 获取订单修改记录 GET /admin/order/modifications
router.get(
	"/modifications",
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt((req.query.page as string) || "1");
		const pageSize = Number.parseInt((req.query.pageSize as string) || "10");
		const filters = req.query.filters as { trade_no?: string };

		const result = await OrderManagementService.getOrderModifications(
			page,
			pageSize,
			filters,
		);
		res.json(result);
	}),
);

// 获取订单详情
router.get(
	"/details",
	wrapAdminRoute<{ trade_no: string }>(async (req, res) => {
		const { trade_no } = req.query;
		const order = await Order.findByTradeNo(trade_no as string);
		res.json(order);
	}),
);

export default router;
