import type { Job } from "bullmq";
import { createWorker, limitOrdersQueue } from "./index.js";
import logger from "@/utils/logger.js";
import { fetchCurrentPrice } from "@/financeUtils/marketData.js";
import * as ConfirmOrder from "@/services/trade/confirmOrder.js";
import * as PendingOrder from "@/models/trade/pendingOrder.js";
import * as NotifService from "@/services/notifService.js";
import { AppError } from "@/core/appError.js";
import { NotificationType, OrderStatus, OrderType } from "@packages/shared";
import type { PendingOrderData, SellingOrderData } from "@packages/shared";
import { ENV } from "@/config/configManager.js";

// Limit order constants
const CHECK_INTERVAL = 5000; // 5 seconds

// Limit order job names
export const LIMIT_JOBS = {
	INIT_ORDERS: "limit-init-orders",
	MONITOR_ORDER: "limit-monitor-order",
	EXECUTE_ORDER: "limit-execute-order",
};

// Process limit order jobs
async function processLimitJob(job: Job) {
	const { name, data } = job;

	logger.info(`Processing limit order job: ${name}`);

	try {
		switch (name) {
			case LIMIT_JOBS.INIT_ORDERS:
				await initializeLimitOrders();
				break;
			case LIMIT_JOBS.MONITOR_ORDER:
				await monitorLimitOrder(data);
				break;
			case LIMIT_JOBS.EXECUTE_ORDER:
				await executeLimitOrder(data.order);
				break;
			default:
				logger.warn(`Unknown limit order job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process limit order job: ${name}`);
		throw error; // Let BullMQ handle the failure
	}
}

// Initialize and find all pending limit orders
async function initializeLimitOrders() {
	try {
		logger.info("Initializing pending limit orders");
		const pendingOrders = await PendingOrder.findAllLimitOrders();

		for (const order of pendingOrders) {
			await startMonitoring(order);
		}

		logger.info(
			`Processed ${pendingOrders.length} limit orders during initialization`,
		);
	} catch (error) {
		logger.error(error, "Failed to initialize limit orders");
		throw error;
	}
}

// Monitor a limit order for price conditions
async function monitorLimitOrder(data: {
	order: PendingOrderData & { limit_price: number };
}) {
	const { order } = data;

	try {
		// Double-check if order still exists in database
		const freshOrder = await PendingOrder.findById(order.pending_id);
		if (!freshOrder) {
			logger.info(
				`Limit order ${order.pending_id} no longer exists, stopping monitoring`,
			);
			return;
		}

		const currentPrice = (await fetchCurrentPrice(order.ts_code)).price;
		let shouldExecute = false;
		const isCall = order.structure.endsWith("C");

		if (order.status === OrderStatus.LIMIT_BUYING) {
			// Buy order:
			// Call option: Current price must be less than or equal to limit price
			// Put option: Current price must be greater than or equal to limit price
			shouldExecute = isCall
				? currentPrice <= order.limit_price
				: currentPrice >= order.limit_price;
		} else {
			// Sell order:
			// Call option: Current price must be greater than or equal to limit price
			// Put option: Current price must be less than or equal to limit price
			shouldExecute = isCall
				? currentPrice >= order.limit_price
				: currentPrice <= order.limit_price;
		}

		logger.info(
			`Limit order ${order.pending_id} check: current price: ${currentPrice}, limit price: ${order.limit_price}, should execute: ${shouldExecute}`,
		);

		if (shouldExecute) {
			// Add job to execute the order
			// Note: Execution jobs might need different cleanup rules depending on whether you want to keep failed execution records
			await limitOrdersQueue.add(
				LIMIT_JOBS.EXECUTE_ORDER,
				{ order },
				{ removeOnFail: 100 },
			); // Keep last 100 failed execution jobs
		} else {
			// Schedule another check - THESE jobs should be cleaned up
			await limitOrdersQueue.add(
				LIMIT_JOBS.MONITOR_ORDER,
				{ order },
				{
					delay: CHECK_INTERVAL,
					removeOnComplete: true, // Automatically remove successful monitor jobs
					removeOnFail: true, // Automatically remove failed monitor jobs
				},
			);
		}
	} catch (error) {
		logger.error(error, `Error monitoring limit order ${order.pending_id}`);

		// If there's an error, still try to continue monitoring but ensure cleanup
		await limitOrdersQueue.add(
			LIMIT_JOBS.MONITOR_ORDER,
			{ order },
			{
				delay: CHECK_INTERVAL,
				removeOnComplete: true, // Also add here for consistency
				removeOnFail: true,
			},
		);
	}
}

// Execute a limit order when conditions are met
async function executeLimitOrder(order: PendingOrderData): Promise<void> {
	try {
		// Double-check if order still exists
		const freshOrder = await PendingOrder.findById(order.pending_id);
		if (!freshOrder) {
			logger.info(
				`Limit order ${order.pending_id} no longer exists, skipping execution`,
			);
			return;
		}

		await ConfirmOrder.executeLimit(order, {
			orderType: OrderType.LIMIT,
		});

		logger.info(`Limit order ${order.pending_id} executed successfully`);
	} catch (error) {
		logger.error(error, `Failed to execute limit order ${order.pending_id}`);

		// Extract specific errors for user-friendly display
		let errorReason = error instanceof AppError ? error.name : "未知错误";
		let errorMessage = "";

		if (
			error instanceof AppError &&
			error.name === "PRICE_VOLATILITY_EXCEEDED"
		) {
			errorReason = "价格波动超限";
			errorMessage = error.message;
		}

		await handleOrderFailure(order, {
			reason: errorReason,
			message: errorMessage,
		});
	}
}

// Start monitoring a limit order
export async function startMonitoring(
	order: PendingOrderData & { limit_price: number },
): Promise<void> {
	try {
		logger.info(`Starting monitoring for limit order ${order.pending_id}`);

		// Add initial monitoring job
		await limitOrdersQueue.add(LIMIT_JOBS.MONITOR_ORDER, { order });
	} catch (error) {
		logger.error(
			error,
			`Failed to start monitoring limit order ${order.pending_id}`,
		);
		await handleOrderFailure(order, {
			reason: "Failed to initialize limit order monitoring",
		});
	}
}

// Clean up a limit order (e.g., when manually cancelled)
export async function cleanupLimitOrder(orderId: number): Promise<void> {
	try {
		// Remove any pending jobs (excluding active ones)
		const jobs = await limitOrdersQueue.getJobs([
			"delayed",
			"waiting",
			// "active", // Do not attempt to remove active jobs
		]);
		for (const job of jobs) {
			const data = job.data;
			if (data.order && data.order.pending_id === orderId) {
				try {
					await job.remove();
					logger.info(`Removed scheduled job for limit order ${orderId}`);
				} catch (removeError: unknown) {
					logger.warn(
						{ error: removeError, jobId: job.id, orderId },
						`Failed to remove job ${job.id} during limit order cleanup, continuing...`,
					);
				}
			}
		}

		logger.info(`Limit order ${orderId} queue jobs cleaned up successfully`);
	} catch (error) {
		logger.error(error, `Failed to clean up limit order ${orderId}`);
		throw error;
	}
}

// Handle order failures
async function handleOrderFailure(
	order: PendingOrderData,
	details?: {
		reason: string;
		message?: string;
		limit_price?: number;
	},
): Promise<void> {
	try {
		// Clean up order
		await cleanupLimitOrder(order.pending_id);

		// Build notification content
		let notificationContent = `您的限价挂单 ${order.ts_code} 执行失败。`;
		if (details?.reason) {
			notificationContent += `\n原因：${details.reason}`;
		}
		if (details?.message) {
			notificationContent += `\n${details.message}`;
		}

		// Notify user
		await NotifService.sendNotification(order.user_id, {
			title: "限价挂单执行失败",
			content: notificationContent,
			type: NotificationType.ORDER,
			metadata: {
				type: "pending_failed",
				trade_no: (order as SellingOrderData).trade_no,
				ts_code: order.ts_code,
				status: order.status,
				limit_price: details?.limit_price,
			},
		});
	} catch (error) {
		logger.error(
			error,
			`Failed to handle failure of limit order ${order.pending_id}`,
		);
	}
}

// Create the worker
export const limitOrderWorker =
	ENV.NODE_ENV === "production"
		? createWorker(limitOrdersQueue, processLimitJob)
		: undefined;

// Initialize orders on startup
export async function initializeOnStartup() {
	try {
		// 仅在生产环境执行订单恢复
		if (ENV.NODE_ENV === "production") {
			// 先清理可能存在的旧监控任务，避免重复
			try {
				const jobs = await limitOrdersQueue.getJobs(["waiting", "delayed"]);
				const monitorJobs = jobs.filter(
					(job) => job.name === LIMIT_JOBS.MONITOR_ORDER,
				);

				if (monitorJobs.length > 0) {
					logger.info(
						`清理 ${monitorJobs.length} 个现有限价监控任务，避免重复`,
					);
					for (const job of monitorJobs) {
						await job.remove();
					}
				}
			} catch (cleanupError) {
				logger.warn(cleanupError, "清理现有限价监控任务失败，继续执行初始化");
			}

			// 添加初始化任务
			await limitOrdersQueue.add(LIMIT_JOBS.INIT_ORDERS, {});
			logger.info("Added limit orders initialization job to queue");
		} else {
			// 开发环境下不执行恢复，并给出提示
			logger.info(
				"LimitOrderWorker: Automatic order recovery is disabled in development mode.",
			);
		}
	} catch (error) {
		logger.error(error, "Failed to initialize limit orders on startup");
	}
}
