// 执行命令：pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\tests\knockoutUtilsTest.ts

import { getKnockoutStocks } from "../financeUtils/knockoutUtils.js";
import logger from "../utils/logger.js";
import { inspect } from "node:util";
import prisma from "../lib/prisma.js";

/**
 * 检查数据库连接
 */
async function checkDatabaseConnection() {
	try {
		await prisma.$queryRaw`SELECT 1 as connected`;
		return true;
	} catch (error) {
		logger.error(error, "数据库连接失败");
		return false;
	}
}

/**
 * 测试强制平仓功能
 */
async function testKnockoutUtils() {
	try {
		logger.info("开始测试强制平仓功能...");

		// 检查数据库连接
		const isConnected = await checkDatabaseConnection();
		if (!isConnected) {
			logger.error("数据库未连接，测试终止");
			return;
		}

		logger.info("数据库连接正常，开始执行测试...");

		// 直接调用函数获取结果
		const knockoutStocks = await getKnockoutStocks();

		// 使用 util.inspect 格式化输出，深度为 null 表示完全展开所有嵌套层级
		logger.info(
			`强制平仓结果：\n${inspect(knockoutStocks, { depth: null, colors: true })}`,
		);

		// 输出统计信息
		logger.info(`总计 ${knockoutStocks.length} 只股票需要强制平仓`);

		// 按市场分类统计
		const marketStats = new Map<string, number>();
		for (const stock of knockoutStocks) {
			// 根据股票代码判断市场
			const market = getMarketFromCode(stock.ts_code);
			marketStats.set(market, (marketStats.get(market) || 0) + 1);
		}

		// 输出市场统计
		for (const [market, count] of marketStats.entries()) {
			logger.info(`${market}: ${count} 只`);
		}

		logger.info("强制平仓测试完成");
	} catch (error) {
		logger.error(error, "测试过程中发生错误");
	} finally {
		// 确保关闭Prisma连接
		await prisma.$disconnect();
	}
}

/**
 * 根据股票代码判断市场
 */
function getMarketFromCode(code: string): string {
	if (code.endsWith(".SZ")) {
		return "深圳交易所";
	}

	if (code.endsWith(".SH")) {
		return "上海交易所";
	}

	if (code.endsWith(".BJ")) {
		return "北京交易所";
	}

	return "其他";
}

// 执行测试
testKnockoutUtils()
	.then(() => {
		logger.info("测试执行完毕");
		// 等待一会儿确保日志写入完成
		setTimeout(() => process.exit(0), 1000);
	})
	.catch((error) => {
		logger.error(error, "测试执行失败");
		process.exit(1);
	});
