import type { Currency } from "./fund.js";

// 通道基础信息
export interface ChannelData {
	channel_id: string; // 通道唯一标识
	name: string; // 通道名称
	description?: string; // 通道描述
	created_at: string; // 创建时间
	updated_at: string; // 更新时间
	status: "active" | "inactive"; // 通道状态
}

export enum ChannelTransactionType {
	DEPOSIT = "deposit", // 通道向交易台存入资金
	WITHDRAW = "withdraw", // 通道从交易台提取资金
	USER_ORDER = "user_order", // 用户下单（通道支出，扣除提价和折扣后的金额）
	USER_EXECUTE = "user_execute", // 用户结算（通道收入，加上盈利分成后的金额）
	EXCHANGE = "exchange", // 货币兑换
	// SETTLE = "settle", // 结算交易
	// ADJUST = "adjust", // 调整余额
}

export enum ChannelTransactionStatus {
	PENDING = "pending", // 待确认
	CONFIRMED = "confirmed", // 已确认
	REJECTED = "rejected", // 已拒绝
	AUTO_CONFIRMED = "auto_confirmed", // 自动处理
}

// 通道资金交易记录
export interface ChannelTransactionData {
	transaction_id: number; // 交易ID
	channel_id: string; // 通道ID
	amount: number; // 金额
	type: ChannelTransactionType; // 交易类型
	status: ChannelTransactionStatus; // 交易状态
	currency: Currency; // 货币类型
	remarks?: string; // 备注
	created_at: string; // 创建时间
	reviewed_at?: string; // 审核时间
	review_comment?: string; // 审核备注
	admin_id?: number; // 审核人ID
}

// 通道余额
export interface ChannelBalance {
	channel_id: string;
	balance_cny: number;
	balance_hkd: number;
	balance_usd: number;
	is_locked: boolean;
}

// 创建通道资金交易请求
export interface CreateChannelTransactionRequest {
	amount: number;
	type: ChannelTransactionType;
	currency: Currency;
	remarks?: string;
}

// 确认/拒绝通道资金交易请求
export interface UpdateChannelTransactionRequest {
	transaction_id: number;
	status: ChannelTransactionStatus;
	remarks?: string;
	review_comment?: string;
	admin_id?: number;
}

// 通道货币交易接口
export interface ChannelExchangeCurrencyData {
	fromCurrency: Currency;
	toCurrency: Currency;
	amount: number;
}

/**
 * 意向客户信息
 */
export interface LeadInfo {
	/**
	 * 意向客户ID
	 */
	id: number;

	/**
	 * 手机号
	 */
	phone_number: string;

	/**
	 * 客户来源
	 */
	source: string;

	/**
	 * 备注
	 */
	remark?: string;

	/**
	 * 已转化为正式用户
	 */
	is_converted: boolean;

	/**
	 * 创建时间
	 */
	created_at: string;

	/**
	 * 更新时间
	 */
	updated_at: string;
}
