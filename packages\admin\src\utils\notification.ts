import { ElMessage, ElButton } from "element-plus";
import { h } from "vue";

const SESSION_KEY_PERMISSION_REQUEST_ATTEMPTED =
	"browser_notification_permission_request_attempted_v1";
const SESSION_KEY_DENIED_PROMPT_SHOWN =
	"browser_notification_denied_prompt_shown_v1";

/**
 * 设置浏览器通知权限。
 * - 如果权限为 'default'，则每会话尝试请求一次。
 * - 如果权限为 'denied'，则每会话提示一次引导用户开启。
 * @returns 是否成功获得通知权限。
 */
export async function setupBrowserNotification(): Promise<boolean> {
	if (!("Notification" in window)) {
		console.warn("This browser does not support notifications");
		return false;
	}

	try {
		if (Notification.permission === "granted") {
			return true;
		}

		if (Notification.permission === "denied") {
			const deniedPromptShown = sessionStorage.getItem(
				SESSION_KEY_DENIED_PROMPT_SHOWN,
			);
			if (!deniedPromptShown) {
				ElMessage.warning({
					message: "为及时获取审核提醒，请在浏览器设置中开启通知权限。",
					duration: 5000,
					showClose: true,
					offset: 60,
				});
				sessionStorage.setItem(SESSION_KEY_DENIED_PROMPT_SHOWN, "true");
			}
			return false;
		}

		// 此时 Notification.permission === "default"
		const permissionRequestedThisSession = sessionStorage.getItem(
			SESSION_KEY_PERMISSION_REQUEST_ATTEMPTED,
		);

		if (!permissionRequestedThisSession) {
			// 使用带有用户交互的消息方式请求权限
			ElMessage({
				message: h(
					"div",
					{
						style:
							"display: flex; align-items: center; justify-content: space-between;",
					},
					[
						h("span", "开启通知权限可及时接收审核提醒"),
						h(
							ElButton,
							{
								type: "primary",
								size: "small",
								onClick: async () => {
									sessionStorage.setItem(
										SESSION_KEY_PERMISSION_REQUEST_ATTEMPTED,
										"true",
									);
									const userChoice = await Notification.requestPermission();

									if (userChoice === "granted") {
										ElMessage.success({
											message: "浏览器通知权限已成功开启！",
											offset: 60,
										});
									} else if (userChoice === "denied") {
										ElMessage.warning({
											message:
												"为及时获取审核提醒，请在浏览器设置中开启通知权限。",
											duration: 5000,
											showClose: true,
											offset: 60,
										});
										sessionStorage.setItem(
											SESSION_KEY_DENIED_PROMPT_SHOWN,
											"true",
										);
									} else {
										// 用户关闭/忽略了原生弹窗，权限仍为 'default'
										ElMessage.info({
											message: "您可以稍后在浏览器设置中手动开启通知权限。",
											duration: 5000,
											showClose: true,
											offset: 60,
										});
									}
								},
							},
							"开启",
						),
					],
				),
				duration: 5000,
				showClose: true,
				offset: 60,
			});

			return false;
		}

		// 本会话已尝试过请求通知权限，且用户未授予，不再主动打扰。
		return false;
	} catch (error) {
		console.error("设置浏览器通知权限时发生错误:", error);
		sessionStorage.setItem(SESSION_KEY_PERMISSION_REQUEST_ATTEMPTED, "true");
		return false;
	}
}

export function showAuditNotification(
	type: "finance" | "qualify",
	count: number,
) {
	if (Notification.permission !== "granted") return;

	const titles = {
		finance: "新的资金审核请求",
		qualify: "新的资质审核请求",
	};

	const bodyMessages = {
		finance: `有 ${count} 条新的资金审核请求待处理。`,
		qualify: `有 ${count} 条新的资质审核请求待处理。`,
	};

	new Notification(titles[type], {
		body: bodyMessages[type],
		icon: "/favicon.ico",
		tag: `audit-${type}`, // 避免重复通知堆积
	});
}
