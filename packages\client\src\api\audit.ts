import { request } from "./request";
import type {
	FundAuditData,
	QualificationData,
	QualificationAuditData,
	CreateFundAuditRequest,
} from "@packages/shared";

// 审核相关 API
export const auditApi = {
	createFundAudit: (data: CreateFundAuditRequest) =>
		request.post<FundAuditData>("/audit/fund", data),

	createQualificationAudit: (data: {
		data: QualificationData;
		comment?: string;
	}) => request.post<QualificationAuditData>("/audit/qualification", data),

	getQualificationStatus: () => request.get("/audit/qualification"),

	getPendingAudits: () =>
		request.get<{
			qualification?: QualificationAuditData;
			fund?: FundAuditData[];
		}>("/audit/pending"),

	// 获取ISDA主协议文件URL
	getIsdaMasterAgreementUrl: () => "/api/public/agreements/isda-master",

	// 获取ISDA补充协议文件URL
	getIsdaSupplementAgreementUrl: () => "/api/public/agreements/isda-supplement",
} as const;
