import * as configService from "@/services/admin/configService.js";
import * as Stock from "@/models/stock.js";
import { AppError } from "@/core/appError.js";
import { StatusChange } from "@packages/shared";

export async function checkSystemAvailable(): Promise<void> {
	const config = await configService.getSystemStatus();
	if (!config.SYSTEM_ENABLED) {
		throw AppError.create("SYSTEM_DISABLED", "System is currently disabled");
	}
}

export async function checkPositionEntryAvailable(): Promise<void> {
	const config = await configService.getSystemStatus();
	if (!config.POSITION_ENTRY_ENABLED) {
		throw AppError.create(
			"POSITION_ENTRY_DISABLED",
			"New position entry is currently disabled",
		);
	}
}

export async function checkStockScaleLimit(
	ts_code: string,
	requestedScale: number,
): Promise<void> {
	const config = await configService.getConfig();
	const currentStock = await Stock.getByCode(ts_code);

	if (currentStock.scale + requestedScale > config.STOCK_SCALE_LIMIT) {
		throw AppError.create(
			"STOCK_SCALE_LIMIT_EXCEEDED",
			"Stock scale limit exceeded",
		);
	}

	const totalScale = await Stock.getTotalScale();
	if (totalScale + requestedScale > config.TOTAL_SCALE_LIMIT) {
		await configService.updateSystemStatus({
			POSITION_ENTRY_ENABLED: false,
			change_type: StatusChange.RISK_CONTROL,
		});
		throw AppError.create(
			"TOTAL_SCALE_LIMIT_EXCEEDED",
			"Total scale limit exceeded",
		);
	}
}

export async function checkInquiryAvailable(): Promise<void> {
	const config = await configService.getSystemStatus();
	if (!config.INQUIRY_ENABLED) {
		throw AppError.create("INQUIRY_DISABLED", "Inquiry is currently disabled");
	}
}
