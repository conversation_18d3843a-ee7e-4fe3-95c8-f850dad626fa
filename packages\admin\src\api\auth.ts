import { request } from "./request";
import http from "@/plugins/axios";
import type { LoginParams, AuthResponse } from "./types";

// 接口定义和实现放在一起
export const authApi = {
	login: (data: LoginParams) =>
		http.post<AuthResponse>("/auth/admin-login", data),

	refreshToken: () => http.post<AuthResponse>("/auth/admin-refresh-token"),

	logout: () => request.post<{ message: string }>("/auth/admin-logout"),

	updatePassword: (oldPassword: string, newPassword: string) =>
		request.post("/admin/auth/password", {
			oldPassword,
			newPassword,
		}),
} as const; // 使用 as const 获得更精确的类型推导
