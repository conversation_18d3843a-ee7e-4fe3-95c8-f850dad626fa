import type { Job } from "bullmq";
import {
	createWorker,
	stockDataQueue,
	systemTasksQueue,
	addRepeatedJob,
} from "./index.js";
import logger from "@/utils/logger.js";
import * as DataSync from "@/services/dataSync.js";
import { coordinationRedis } from "@/lib/redis.js";
import { APP_CONFIG, APP_TYPE } from "@/config/configManager.js";

// Redis锁的配置
const STOCK_SYNC_LOCK_KEY = "stock:sync:lock"; // 锁的键名
const STOCK_SYNC_RECORD_KEY = "stock:sync:last_execution"; // 最后执行记录的键名
const LOCK_EXPIRY = 60 * 15; // 锁过期时间：15分钟，单位秒
const RECENT_EXECUTION_THRESHOLD = 60 * 5; // 最近执行阈值：5分钟，单位秒

// Stock data job types
export const STOCK_DATA_JOBS = {
	SYNC_ALL: "stock-sync-all",
	SYNC_STOCK_LIST: "stock-sync-stock-list",
	SYNC_TRADE_CALENDAR: "stock-sync-trade-calendar",
	SYNC_DAILY_DATA: "stock-sync-daily-data",
	SYNC_UP_DOWN_LIMIT: "stock-sync-up-down-limit",
	SYNC_DIVIDEND: "stock-sync-dividend",
	SYNC_SUSPENSION: "stock-sync-suspension",
};

// System jobs
export const SYSTEM_JOBS = {
	INITIALIZE_STOCK_DATA_SCHEDULES: "system-init-stock-data-schedules",
};

// Process stock data jobs
async function processStockDataJob(job: Job) {
	const { name } = job;

	logger.info(`Processing stock data job: ${name}`);

	try {
		switch (name) {
			case STOCK_DATA_JOBS.SYNC_ALL:
				await DataSync.syncAllData();
				break;
			case STOCK_DATA_JOBS.SYNC_STOCK_LIST:
				await DataSync.syncStockList();
				break;
			case STOCK_DATA_JOBS.SYNC_TRADE_CALENDAR:
				await DataSync.syncTradeCalendar();
				break;
			case STOCK_DATA_JOBS.SYNC_DAILY_DATA:
				await DataSync.syncDailyData();
				break;
			case STOCK_DATA_JOBS.SYNC_UP_DOWN_LIMIT:
				await DataSync.syncUpDownLimit();
				break;
			case STOCK_DATA_JOBS.SYNC_DIVIDEND:
				await DataSync.syncDividend();
				break;
			case STOCK_DATA_JOBS.SYNC_SUSPENSION:
				await DataSync.syncSuspension();
				break;
			default:
				logger.warn(`Unknown stock data job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process stock data job: ${name}`);
		throw error;
	}
}

// 获取应用标识
function getAppIdentifier(): string {
	// 尝试获取交易台ID或通道ID，如果都没有，使用"unknown"加上随机字符串
	const platformId = APP_CONFIG.tradingPlatformId;
	const channelId = APP_CONFIG.channelId;

	if (platformId) return `platform:${platformId}`;
	if (channelId) return `channel:${channelId}`;

	// 生成随机ID作为后备标识
	return `unknown:${Math.random().toString(36).substring(2, 9)}`;
}

// 尝试获取分布式锁
async function tryAcquireSyncLock(): Promise<boolean> {
	const appId = getAppIdentifier();

	// 当前时间戳（秒）
	const now = Math.floor(Date.now() / 1000);

	// 锁的值格式：appId:timestamp
	const lockValue = `${appId}:${now}`;

	// 原子操作：如果锁不存在，则设置锁并返回1，否则返回0
	// 使用SETNX命令确保原子性，即使两个实例同时执行也只有一个能成功
	const result = await coordinationRedis.set(
		STOCK_SYNC_LOCK_KEY,
		lockValue,
		"EX",
		LOCK_EXPIRY,
		"NX",
	);

	// 获取锁成功
	if (result === "OK") {
		logger.info(`Stock data sync lock acquired by ${appId}`);
		return true;
	}

	// 获取锁失败，检查现有锁的信息
	const existingLock = await coordinationRedis.get(STOCK_SYNC_LOCK_KEY);
	if (existingLock) {
		const parts = existingLock.split(":");
		const lockHolderId = parts[0];
		const lockTimestamp = parts.length > 1 ? Number.parseInt(parts[1], 10) : 0;

		logger.info(
			`Stock data sync already locked by ${lockHolderId} at ${new Date(lockTimestamp * 1000).toISOString()}`,
		);
	}

	return false;
}

// 释放分布式锁
async function releaseSyncLock(): Promise<void> {
	const appId = getAppIdentifier();

	// 先获取锁的当前值
	const existingLock = await coordinationRedis.get(STOCK_SYNC_LOCK_KEY);

	// 只有当前应用持有锁时才删除，避免删除其他实例的锁
	if (existingLock?.startsWith(`${appId}:`)) {
		await coordinationRedis.del(STOCK_SYNC_LOCK_KEY);
		logger.info(`Stock data sync lock released by ${appId}`);
	}
}

// 记录同步执行信息
async function recordSyncExecution(): Promise<void> {
	const appId = getAppIdentifier();
	const now = Math.floor(Date.now() / 1000);

	// 记录格式：{appId: string, timestamp: number}
	const recordData = JSON.stringify({
		appId,
		timestamp: now,
		type: APP_TYPE,
		date: new Date().toISOString(),
	});

	// 记录最后执行信息，不设置过期时间，用于长期追踪
	await coordinationRedis.set(STOCK_SYNC_RECORD_KEY, recordData);
	logger.info(`Stock data sync execution recorded by ${appId}`);
}

// 检查是否有最近的执行记录
async function hasRecentExecution(): Promise<boolean> {
	// 获取最近执行记录
	const recordData = await coordinationRedis.get(STOCK_SYNC_RECORD_KEY);
	if (!recordData) return false;

	try {
		const record = JSON.parse(recordData);
		const now = Math.floor(Date.now() / 1000);

		// 检查执行时间是否在阈值范围内
		if (now - record.timestamp < RECENT_EXECUTION_THRESHOLD) {
			logger.info(
				`Recent stock data sync detected at ${new Date(record.timestamp * 1000).toISOString()} by ${record.appId}`,
			);
			return true;
		}

		return false;
	} catch (error) {
		logger.error(error, "Error parsing recent execution record");
		return false;
	}
}

// Process system jobs
async function processSystemJob(job: Job) {
	const { name } = job;

	logger.info(`Processing system job: ${name}`);

	try {
		switch (name) {
			case SYSTEM_JOBS.INITIALIZE_STOCK_DATA_SCHEDULES:
				// 检查是否有最近执行的记录
				if (await hasRecentExecution()) {
					logger.info(
						"Skipping stock data schedule initialization due to recent execution",
					);
					return;
				}

				// 尝试获取分布式锁
				if (await tryAcquireSyncLock()) {
					try {
						// 执行初始化
						await initializeStockDataSchedules();
						// 记录执行信息
						await recordSyncExecution();
					} finally {
						// 完成后释放锁
						await releaseSyncLock();
					}
				} else {
					logger.info(
						"Skipping stock data schedule initialization due to active lock",
					);
				}
				break;
			default:
				logger.warn(`Unknown system job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process system job: ${name}`);
		throw error;
	}
}

// Initialize stock data schedules
async function initializeStockDataSchedules(): Promise<void> {
	try {
		logger.info("Initializing stock data job schedules");

		// Stock data schedules
		await Promise.all([
			// Initial sync
			stockDataQueue.add(STOCK_DATA_JOBS.SYNC_ALL, {}),

			// 0:01 Update stock list
			addRepeatedJob(
				stockDataQueue,
				STOCK_DATA_JOBS.SYNC_STOCK_LIST,
				{},
				"1 0 * * *",
			),

			// 0:30 Update trade calendar
			addRepeatedJob(
				stockDataQueue,
				STOCK_DATA_JOBS.SYNC_TRADE_CALENDAR,
				{},
				"30 0 * * *",
			),

			// 0:30 Update daily closing data
			addRepeatedJob(
				stockDataQueue,
				STOCK_DATA_JOBS.SYNC_DAILY_DATA,
				{},
				"30 0 * * *",
			),

			// 9:00 Update up-down limits
			addRepeatedJob(
				stockDataQueue,
				STOCK_DATA_JOBS.SYNC_UP_DOWN_LIMIT,
				{},
				"0 9 * * *",
			),

			// 9:00 Update dividends
			addRepeatedJob(
				stockDataQueue,
				STOCK_DATA_JOBS.SYNC_DIVIDEND,
				{},
				"0 9 * * *",
			),

			// 9:30 Update suspensions
			addRepeatedJob(
				stockDataQueue,
				STOCK_DATA_JOBS.SYNC_SUSPENSION,
				{},
				"30 9 * * *",
			),
		]);

		logger.info("Stock data job schedules initialized successfully");
	} catch (error) {
		logger.error(error, "Failed to initialize stock data job schedules");
		throw error;
	}
}

// Create workers
export const stockDataWorker = createWorker(
	stockDataQueue,
	processStockDataJob,
);
export const systemWorker = createWorker(systemTasksQueue, processSystemJob);

// Initialize on startup
export async function initializeOnStartup(): Promise<void> {
	try {
		// 初始化定时任务调度 - 使用固定的jobId避免重复添加
		const jobId = "init-stock-data-schedules";
		await systemTasksQueue.add(
			SYSTEM_JOBS.INITIALIZE_STOCK_DATA_SCHEDULES,
			{},
			{ jobId },
		);
		logger.info(
			`Added stock data schedule initialization job to queue with ID: ${jobId}`,
		);

		// 注意：数据为空的检查已在DataSync.syncAllData()中实现，
		// 该函数会自动检查数据库状态并在需要时执行初始化
		// 同样，FORCE_SYNC环境变量的检查也已在DataSync.syncAllData()中处理
	} catch (error) {
		logger.error(error, "Failed to initialize stock data worker on startup");
	}
}
