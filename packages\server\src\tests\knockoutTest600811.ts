// 执行命令：pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\tests\knockoutTest600811.ts

import { getKnockoutStocks } from "../financeUtils/knockoutUtils.js";
import logger from "../utils/logger.js";
import { inspect } from "node:util";
import prisma from "../lib/prisma.js";

/**
 * 检查数据库连接
 */
async function checkDatabaseConnection() {
	try {
		await prisma.$queryRaw`SELECT 1 as connected`;
		return true;
	} catch (error) {
		logger.error(error, "数据库连接失败");
		return false;
	}
}

/**
 * 专门测试 600811.SH 股票的强制平仓
 */
async function test600811KnockoutDetection() {
	try {
		logger.info("开始测试 600811.SH 股票的强制平仓检测...");

		// 检查数据库连接
		const isConnected = await checkDatabaseConnection();
		if (!isConnected) {
			logger.error("数据库未连接，测试终止");
			return;
		}

		logger.info("数据库连接正常，开始执行测试...");

		// 调用强制平仓检测函数
		const knockoutStocks = await getKnockoutStocks();

		// 检查 600811.SH 是否在结果中
		const target = knockoutStocks.find(
			(stock) => stock.ts_code === "600811.SH",
		);

		if (target) {
			logger.info(
				`600811.SH 被成功检测到需要强制平仓，开始日期: ${target.knockoutStartDate}`,
			);
		} else {
			logger.warn("600811.SH 未在强制平仓列表中");
		}

		// 使用 util.inspect 格式化输出完整结果
		logger.info(
			`强制平仓结果：\n${inspect(knockoutStocks, { depth: null, colors: true })}`,
		);

		logger.info(`总计 ${knockoutStocks.length} 只股票需要强制平仓`);
		logger.info("600811.SH 测试完成");
	} catch (error) {
		logger.error(error, "测试过程中发生错误");
	} finally {
		// 确保关闭Prisma连接
		await prisma.$disconnect();
	}
}

// 执行测试
test600811KnockoutDetection()
	.then(() => {
		logger.info("测试执行完毕");
		// 等待一会儿确保日志写入完成
		setTimeout(() => process.exit(0), 1000);
	})
	.catch((error) => {
		logger.error(error, "测试执行失败");
		process.exit(1);
	});
