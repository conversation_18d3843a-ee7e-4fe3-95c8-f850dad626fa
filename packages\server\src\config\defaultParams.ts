import type { BusinessConfig } from "@packages/shared";
import { ENV, APP_CONFIG } from "@/config/configManager.js";

// 默认业务配置
export const DEFAULT_BUSINESS_CONFIG: BusinessConfig = {
	// 限制
	STOCK_SCALE_LIMIT: 500,
	TOTAL_SCALE_LIMIT: 1000000,
	// 参数
	OPTION_MULTIPLIER: 1.35,
	PUT_MULTIPLIER: 1.1,
	MONTH_MULTIPLIER: 1.4,
	TWO_WEEKS_MULTIPLIER: 0.8,
	DISCOUNT_MULTIPLIER: 0.85,
	// 最小报价结构
	MIN_QUOTE_STRUCTURE_100: 3.6,
	MIN_QUOTE_STRUCTURE_103: 3.0,
	MIN_QUOTE_STRUCTURE_105: 2.4,
	MIN_QUOTE_STRUCTURE_110: 1.8,
	// 通道预授权额度
	CHANNEL_CREDIT_LIMIT: 100000,
};

// 提供商折扣配置
export const PROVIDER_DISCOUNT_CONFIG: Record<string, number> = {
	INK: 0.95, // 95%折扣
	// 取消其他提供商折扣
};

// 交易时间段配置
export const TRADING_PERIODS = [
	{ start: 930, end: 1130 },
	{ start: 1300, end: 1457 },
];

// 询价时间段配置
export const INQUIRY_PERIODS = [{ start: 930, end: 1457 }];

/* 定时任务执行时段：
	1. 凌晨 0 点：				清空询价、更新到期日
	2. 凌晨 0:01：				更新股票列表
	3. 凌晨 0:30：				更新交易日历、每日收盘数据
	4. 早上 7:30：				系统健康检查（只读）
	5. 早上 9:00：				更新除权除息、涨跌停限制
	6. 早上 9:15：				调整因子
	7. 早上 9:30：				<ON> 开启交易，更新停复牌数据
	8. 下午 2:57：				<OFF> 关闭交易
	9. 下午 2:58：				关闭挂单
	10. 下午 3 点：				到期订单、临期提醒、强制敲出
*/

// ------------------------------------------------------------

// 默认平台配置
export const DEFAULT_PLATFORM_CONFIG = {
	quote_providers: {
		INK: {
			enabled: true,
			price_adjustment: 0, // 每单加价
			display_name: "INK",
		},
		HAIYING: {
			enabled: true,
			price_adjustment: 0,
			display_name: "HY",
		},
		YINHE_DERUI: {
			enabled: true,
			price_adjustment: 0,
			display_name: "YHDR",
		},
		ZHONGZHENG_ZIBEN: {
			enabled: true,
			price_adjustment: 0,
			display_name: "ZZZB",
		},
		ZHEQI: {
			enabled: true,
			price_adjustment: 0,
			display_name: "ZQ",
		},
		YONGAN: {
			enabled: true,
			price_adjustment: 0,
			display_name: "YA",
		},
		ZHONGJIN: {
			enabled: true,
			price_adjustment: 0,
			display_name: "ZJ",
		},
		GUANGFA: {
			enabled: true,
			price_adjustment: 0,
			display_name: "GF",
		},
		GUOJUNZI: {
			enabled: true,
			price_adjustment: 0,
			display_name: "GJZ",
		},
	},
	profit_sharing: {
		enabled: false,
		percentage: 5, // 默认5%的盈利分成
	},
	// 邮件配置
	email_display_name: "", // 发件人显示名称（默认为空，使用系统默认值）
	qualification_audit_email: [], // 资质审核通知邮箱（默认为空数组）
	fund_audit_email: [], // 资金审核通知邮箱（默认为空数组）
};

type TradingPlatformId = "TEST" | "DEMO" | "INK" | "FM" | "FO";

interface TradingPlatform {
	name: string;
	database_url: string; // 平台数据库URL
	channels?: Partial<Record<ChannelId, Channels>>;
}

type ChannelId =
	| "TEST_CHANNEL"
	| "DEMO_CHANNEL"
	| "FO_CHANNEL"
	| "QIANGSHENG"
	| "HCVANILLA"
	| "CFNB";

interface Channels {
	name: string;
	database_url: string;
}

/**
 * 交易台-通道配置
 * 不写入数据库，仅用于默认常量配置
 * bankAccount 属性包含敏感信息，每一次修改都应该极其谨慎
 * 后续迁移到总控端或使用数据库双向对接
 */
export const TRADING_PLATFORMS: Record<TradingPlatformId, TradingPlatform> = {
	// 开发环境：修改测试端口号
	TEST: {
		name: "TEST",
		database_url: `postgresql://postgres:${encodeURIComponent(
			ENV.DB_PASSWORD,
		)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_test`,
		channels: {
			TEST_CHANNEL: {
				name: "TEST通道",
				database_url: `postgresql://postgres:${encodeURIComponent(
					ENV.DB_PASSWORD,
				)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_test_channel`,
			},
		},
	},
	DEMO: {
		name: "DEMO",
		database_url: `postgresql://postgres:${encodeURIComponent(
			ENV.DB_PASSWORD,
		)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_demo`,
		channels: {
			DEMO_CHANNEL: {
				name: "DEMO通道",
				database_url: `postgresql://postgres:${encodeURIComponent(
					ENV.DB_PASSWORD,
				)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_demo_channel`, // 待创建
			},
		},
	},
	INK: {
		name: "INK",
		database_url: `postgresql://postgres:${encodeURIComponent(
			ENV.DB_PASSWORD,
		)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_ink`,
		// 初始账户信息：谨慎修改！
		// bankAccount: {
		// 	name: "INK LPF",
		// 	bankName: "星展银行，香港分行",
		// 	bankCode: "185",
		// 	accountNumber: "***********",
		// 	accountNumberHKD: "",
		// 	accountNumberUSD: "",
		// 	branchCode: "",
		// },
	},
	FM: {
		name: "FortuneMax",
		database_url: `postgresql://postgres:${encodeURIComponent(
			ENV.DB_PASSWORD,
		)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_fm`,
		// 初始账户信息：谨慎修改！
		// bankAccount: {
		// 	name: "FORTUNE MAX INTERNATIONAL LIMITED",
		// 	bankName: "DBS BANK(HONG KONG) LIMITED",
		// 	bankCode: "016",
		// 	accountNumber: "***********",
		// 	accountNumberHKD: "***********",
		// 	accountNumberUSD: "***********",
		// 	branchCode: "927",
		// },
	},
	FO: {
		name: "FirstOption",
		database_url: `postgresql://postgres:${encodeURIComponent(
			ENV.DB_PASSWORD,
		)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_fo`,
		channels: {
			FO_CHANNEL: {
				name: "FirstOption通道",
				database_url: `postgresql://postgres:${encodeURIComponent(
					ENV.DB_PASSWORD,
				)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_fo_channel`,
			},
			QIANGSHENG: {
				name: "高树衍",
				database_url: `postgresql://postgres:${encodeURIComponent(
					ENV.DB_PASSWORD,
				)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_qiangsheng`,
			},
			HCVANILLA: {
				name: "陈瑞钟",
				database_url: `postgresql://postgres:${encodeURIComponent(
					ENV.DB_PASSWORD,
				)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_hcvanilla`,
			},
			CFNB: {
				name: "肖秀江",
				database_url: `postgresql://postgres:${encodeURIComponent(
					ENV.DB_PASSWORD,
				)}@${ENV.DB_HOST}:${ENV.DB_PORT}/ink_cfnb`,
			},
		},
	},
} as const;

/**
 * 从TRADING_PLATFORMS配置中查找通道名称
 */
export function findChannelNameFromConfig(
	channelId: string,
): string | undefined {
	// 遍历所有平台
	for (const platformId in TRADING_PLATFORMS) {
		const platform =
			TRADING_PLATFORMS[platformId as keyof typeof TRADING_PLATFORMS];
		// 检查平台是否有channels属性
		if (platform.channels) {
			// 查找匹配的通道ID
			const channelKey = Object.keys(platform.channels).find(
				(key) => key === channelId,
			);
			if (
				channelKey &&
				platform.channels[channelKey as keyof typeof platform.channels]
			) {
				return platform.channels[channelKey as keyof typeof platform.channels]
					?.name;
			}
		}
	}
	return undefined;
}

/**
 * 从TRADING_PLATFORMS配置中查找通道数据库URL
 */
export function findChannelDBUrlFromConfig(
	channelId = APP_CONFIG.channelId,
): string | undefined {
	// 遍历所有平台
	for (const platformId in TRADING_PLATFORMS) {
		const platform =
			TRADING_PLATFORMS[platformId as keyof typeof TRADING_PLATFORMS];
		// 检查平台是否有channels属性
		if (platform.channels) {
			// 查找匹配的通道ID
			const channelKey = Object.keys(platform.channels).find(
				(key) => key === channelId,
			);
			if (
				channelKey &&
				platform.channels[channelKey as keyof typeof platform.channels]
			) {
				return platform.channels[channelKey as keyof typeof platform.channels]
					?.database_url;
			}
		}
	}
	return undefined;
}

/**
 * 获取所有配置的通道ID
 * @returns 通道ID数组
 */
export async function getAllConfiguredChannels(): Promise<string[]> {
	// 获取当前平台ID下的所有通道
	const platformId = APP_CONFIG.tradingPlatformId;
	const platformChannels: string[] = [];

	if (platformId && platformId in TRADING_PLATFORMS) {
		const platform =
			TRADING_PLATFORMS[platformId as keyof typeof TRADING_PLATFORMS];
		if (platform.channels) {
			platformChannels.push(...Object.keys(platform.channels));
		}
	}

	return platformChannels;
}

/**
 * 从TRADING_PLATFORMS配置中查找平台数据库URL
 */
export function findPlatformDBUrlFromConfig(): string | undefined {
	if (APP_CONFIG.tradingPlatformId in TRADING_PLATFORMS) {
		const platform =
			TRADING_PLATFORMS[
				APP_CONFIG.tradingPlatformId as keyof typeof TRADING_PLATFORMS
			];
		return platform.database_url;
	}
	return undefined;
}

/**
 * 从TRADING_PLATFORMS配置中查找通道所属交易台的数据库URL
 * @param channelId 通道ID
 * @returns 上游交易台数据库URL
 */
export function findUpstreamDBUrlFromConfig(): string | undefined {
	// 遍历所有平台查找包含此通道的平台
	for (const platformId in TRADING_PLATFORMS) {
		const platform =
			TRADING_PLATFORMS[platformId as keyof typeof TRADING_PLATFORMS];

		// 如果平台有通道配置，并且包含指定通道ID
		if (
			platform.channels &&
			Object.keys(platform.channels).includes(APP_CONFIG.channelId)
		) {
			// 返回平台的数据库URL
			return platform.database_url;
		}
	}

	return undefined;
}

// 获取当前平台或应用的数据库URL
export function getDatabaseUrl(): string | undefined {
	// 如果是交易台环境
	if (APP_CONFIG.tradingPlatformId) {
		const configDbUrl = findPlatformDBUrlFromConfig();
		if (configDbUrl) {
			return configDbUrl;
		}
	}

	// 如果是通道环境
	if (APP_CONFIG.channelId) {
		const configDbUrl = findChannelDBUrlFromConfig();
		if (configDbUrl) {
			return configDbUrl;
		}
	}

	return undefined;
}
