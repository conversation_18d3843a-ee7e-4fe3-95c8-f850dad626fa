import { request } from "./request";
import type {
	AuditStatus,
	FundAuditData,
	PlatformDepositRecord,
	UserInfo,
	TransactionData,
	TransactionType,
} from "@packages/shared";
import type { QueryOptions } from "./types";

// 资金审核、资金流水相关 API
export const financeApi = {
	getFinanceAudits: (status: AuditStatus | undefined, options?: QueryOptions) =>
		request.get<{
			items: FundAuditData[];
			total: number;
		}>("/admin/finance/audits", {
			params: {
				status,
				page: options?.page,
				pageSize: options?.pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
			},
		}),

	processFinanceAudit: (
		auditId: number,
		status: AuditStatus,
		comment?: string,
	) =>
		request.post("/admin/finance/audits", {
			audit_id: auditId,
			status,
			comment,
		}),

	platformDeposit: (userId: number, amount: number, comment?: string) =>
		request.post("/admin/finance/deposit", {
			user_id: userId,
			amount,
			comment,
		}),

	getPlatformDeposits: (options?: QueryOptions) =>
		request.get<{
			items: PlatformDepositRecord[];
			total: number;
		}>("/admin/finance/deposits", {
			params: {
				page: options?.page,
				pageSize: options?.pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
				status: options?.filters?.status,
			},
		}),

	// Transaction API
	getTransactions: (
		page: number,
		pageSize: number,
		options?: {
			sortOrder?: "ASC" | "DESC";
			filters?: {
				user_id?: number;
				types?: TransactionType[];
				startDate?: string;
				endDate?: string;
			};
		},
	) =>
		request.get<{
			items: TransactionData[];
			total: number;
		}>("/admin/finance/transactions", {
			params: {
				page,
				pageSize,
				sortOrder: options?.sortOrder,
				user_id: options?.filters?.user_id,
				types: options?.filters?.types?.join(","),
				startDate: options?.filters?.startDate,
				endDate: options?.filters?.endDate,
			},
		}),
} as const;

// 转账授权相关 API
export const transferAuthApi = {
	getTransferAuthUsers: (options?: QueryOptions) =>
		request.get<{
			items: UserInfo[];
			total: number;
		}>("/admin/finance/transfer-auth", {
			params: {
				page: options?.page,
				pageSize: options?.pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
			},
		}),

	authorizeTransfer: (userId: number) =>
		request.post("/admin/finance/transfer-auth", {
			user_id: userId,
		}),

	revokeTransferAuth: (userId: number) =>
		request.delete(`/admin/finance/transfer-auth/${userId}`),

	unfreezePayment: (userId: number) =>
		request.post("/admin/finance/unfreeze-payment", {
			user_id: userId,
		}),

	clearPaymentPassword: (userId: number) =>
		request.post("/admin/finance/clear-payment-password", {
			user_id: userId,
		}),
};
