<template>
  <div class="login-page">
    <div class="bg-wrapper" :style="{ backgroundImage: `url(${currentBgUrl})` }"
      :class="{ blur: currentBgUrl === placeholderBg }" />

    <div class="login-wrapper">
      <div class="header-wrapper">
        <ThemableLogo v-if="siteConfigStore.logoUrl()" :src="siteConfigStore.logoUrl() || ''" class="logo" />
        <h1>客户交易系统</h1>
      </div>

      <LoginModal v-if="currentModal === 'login'" @switch-to-register="currentModal = 'register'"
        @switch-to-reset="currentModal = 'reset'" />
      <RegisterModal v-else-if="currentModal === 'register'" @close="currentModal = 'login'"
        @success="handleRegisterSuccess" />
      <ResetPasswordModal v-else @close="currentModal = 'login'" @success="handleResetSuccess" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import LoginModal from "./LoginModal.vue";
import RegisterModal from "./RegisterModal.vue";
import ResetPasswordModal from "./ResetPasswordModal.vue";
import { useSiteConfigStore } from "@/stores/siteConfig";
import ThemableLogo from "@/components/ThemableLogo.vue";

const siteConfigStore = useSiteConfigStore();

type ModalType = "login" | "register" | "reset";

const highResBg = new URL("@/assets/images/login-bg.webp", import.meta.url)
	.href;
const placeholderBg = new URL(
	"@/assets/images/login-bg-placeholder.webp",
	import.meta.url,
).href;
const currentBgUrl = ref("");
const currentModal = ref<ModalType>("login");

const loadImage = (src: string): Promise<string> => {
	return new Promise((resolve, reject) => {
		const img = new Image();
		img.onload = () => resolve(src);
		img.onerror = reject;
		img.src = src;
	});
};

// 优雅的加载策略
const loadBackground = async () => {
	try {
		// 同时开始加载两张图片，看哪个先完成
		const bgUrl = await Promise.race([
			loadImage(highResBg),
			loadImage(placeholderBg),
		]);

		// 设置先加载完成的图片
		currentBgUrl.value = bgUrl;

		// 如果是占位图先加载完，继续等待高清图
		if (bgUrl === placeholderBg) {
			const finalUrl = await loadImage(highResBg);
			currentBgUrl.value = finalUrl;
		}
	} catch (error) {
		console.error("Failed to load background:", error);
		currentBgUrl.value = placeholderBg;
	}
};

onMounted(loadBackground);

const handleRegisterSuccess = () => {
	currentModal.value = "login";
};

const handleResetSuccess = () => {
	currentModal.value = "login";
};
</script>

<style scoped>
.login-page {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bg-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-size: cover;
  transition: filter 0.3s ease;
  filter: grayscale(40%);
}

.bg-wrapper.blur {
  filter: blur(10px) grayscale(100%);
}

.login-wrapper {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  padding: 20px;
  background-color: var(--el-mask-color-extra-light);
  backdrop-filter: blur(3px);
  transform: scale(1.1);

  /* 添加响应式宽度 */
  width: 90%;
  max-width: 420px;
  margin: 0 auto;
}

.header-wrapper {
  display: flex;
  align-items: flex-end;
  margin: 16px 0 32px;

  /* 添加移动端样式 */
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 10px;
  }
}

.logo {
  width: 100px;
  height: auto;
  margin-right: 5px;

  /* 添加移动端样式 */
  @media (max-width: 768px) {
    margin-right: 0;
  }
}

h1 {
  font-size: 24px;
  margin-bottom: 0;
  font-family: 'MyCustomFont', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;

  /* 添加移动端样式 */
  @media (max-width: 768px) {
    font-size: 20px;
    margin-bottom: 15px;
  }
}

/* 添加移动端媒体查询 */
@media (max-width: 768px) {
  .login-wrapper {
    transform: scale(1);
    padding: 15px;
  }

  .bg-wrapper.blur {
    filter: blur(15px) grayscale(100%) brightness(0.7);
  }
}

/* 添加横屏模式的适配 */
@media (max-height: 600px) and (orientation: landscape) {
  .login-wrapper {
    transform: scale(0.9);
    padding: 15px 20px;
  }

  .header-wrapper {
    margin-bottom: 10px;
  }

  h1 {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .logo {
    width: 60px;
    height: 60px;
  }
}
</style>
