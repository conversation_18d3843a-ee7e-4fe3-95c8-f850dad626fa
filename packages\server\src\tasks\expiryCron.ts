import cron from "node-cron";
import logger from "@/utils/logger.js";
import {
	isMarketDay,
	getNextTradingDay,
} from "@/financeUtils/marketTimeManager.js";
import * as Position from "@/models/position.js";
import * as TradeService from "@/services/trade/tradeService.js";
import { NotificationType, OrderType, TradeDirection } from "@packages/shared";
import { calculateExpiryDate } from "@packages/shared";
import * as PendingOrder from "@/models/trade/pendingOrder.js";
import { cancelPendingOrder } from "@/services/trade/confirmOrder.js";
import EmailService from "@/utils/email.js";
import * as User from "@/models/user.js";
import { AppError } from "@/core/appError.js";
import * as NotifService from "@/services/notifService.js";
import prisma from "@/lib/prisma.js";
import { formatDate } from "@/utils/format.js";

export class ExpiryCron {
	private static instance: ExpiryCron;

	// typescript will auto privatize constructor

	public static getInstance(): ExpiryCron {
		if (!ExpiryCron.instance) {
			ExpiryCron.instance = new ExpiryCron();
		}
		return ExpiryCron.instance;
	}

	public start(): void {
		try {
			// Process pending orders at 2:58 PM，避免到期结算遇到挂单待沽
			cron.schedule(
				"58 14 * * *",
				async () => {
					await this.processExpiryPendingOrders();
				},
				{
					timezone: "Asia/Shanghai",
				},
			);

			// 每天下午3点执行
			cron.schedule(
				"0 15 * * *",
				async () => {
					await this.processExpiryPositions();
					await this.expiryReminder();
				},
				{
					timezone: "Asia/Shanghai",
				},
			);

			logger.info("Expiry position processing cron job started");
		} catch (error) {
			logger.error(
				error,
				"Failed to start expiry position processing cron job",
			);
			throw error;
		}
	}

	private async processExpiryPositions(): Promise<void> {
		try {
			// 检查是否为交易日，如果非交易日则跳过
			if (!(await isMarketDay())) {
				logger.info("Not a market day, skipping expiry position processing");
				return;
			}

			const startTime = Date.now();
			const positions = await Position.getAll();

			const today = new Date();
			const expiryPositions = positions.filter((position) => {
				const createdAt = new Date(position.created_at);
				const expiryDate = calculateExpiryDate(createdAt, position.term);
				// 只比较日期部分
				today.setHours(0, 0, 0, 0);
				expiryDate.setHours(0, 0, 0, 0);
				return expiryDate <= today;
			});

			// 执行市价卖出
			for (const position of expiryPositions) {
				try {
					await TradeService.placeOrder(
						{
							type: OrderType.MARKET,
							direction: TradeDirection.SELL,
							user_id: position.user_id,
							ts_code: position.ts_code,
							trade_no: position.trade_no,
							scale: position.scale,
							quote_provider: position.quote_provider,
							quote_diff: position.quote_diff,
						},
						{ isExpiry: true },
					);

					await NotifService.sendNotification(
						position.user_id,
						{
							title: "到期通知",
							content: "您的持仓已到期，请注意查看。",
							type: NotificationType.ORDER,
							metadata: {
								type: "expiry",
								ts_code: position.ts_code,
								trade_no: position.trade_no,
							},
						},
						prisma,
					);

					const user = await User.findById(position.user_id);
					if (!user) {
						throw AppError.create("USER_NOT_FOUND", "User not found");
					}
					EmailService.sendEmail(user.email, "EXPIRY_NOTIFICATION", {});

					logger.info(
						`Successfully placed expiry sell order for position ${position.trade_no}`,
					);
				} catch (error) {
					logger.error(
						error,
						`Failed to process expiry position ${position.trade_no}`,
					);
				}
			}

			const duration = Date.now() - startTime;
			logger.info(
				`Expiry position processing completed in ${duration}ms, processed ${expiryPositions.length} positions`,
			);
		} catch (error) {
			logger.error(error, "Error during expiry position processing");
		}
	}

	private async processExpiryPendingOrders(): Promise<void> {
		const pendingOrders = await PendingOrder.findAllPending();
		for (const order of pendingOrders) {
			await cancelPendingOrder(order);
		}

		await prisma.$queryRaw`ALTER SEQUENCE pending_orders_pending_id_seq RESTART WITH 1;`;
	}

	private async expiryReminder(): Promise<void> {
		// 检查是否为交易日，如果非交易日则跳过
		if (!(await isMarketDay())) {
			logger.info("Not a market day, skipping expiry reminder");
			return;
		}

		const positions = await Position.getAll();
		const nextTradingDay = await getNextTradingDay();

		for (const position of positions) {
			const expiryDate = new Date(position.expiry_date);
			if (expiryDate.toDateString() === nextTradingDay.toDateString()) {
				await NotifService.sendNotification(position.user_id, {
					title: "到期日提醒",
					content: `您有一笔持仓将于${formatDate(expiryDate)}到期，请注意查看。`,
					type: NotificationType.ORDER,
					metadata: {
						type: "expiry",
						ts_code: position.ts_code,
						trade_no: position.trade_no,
					},
				});

				const user = await User.findById(position.user_id);
				if (!user) {
					throw AppError.create("USER_NOT_FOUND", "User not found");
				}
				EmailService.sendEmail(user.email, "EXPIRY_REMINDER", {
					date: formatDate(expiryDate),
				});
			}
		}
	}
}

// 导出单例实例
export const expiryCron = ExpiryCron.getInstance();
