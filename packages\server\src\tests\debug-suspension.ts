// 执行命令：pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\tests\debug-suspension.ts

import {
	fetchSuspensionFromAPI,
	fetchTodaySuspension,
	fetchSuspendedStocks,
} from "@/financeUtils/marketData.js";

async function debugSuspension() {
	try {
		// 测试用例1：测试 fetchSuspensionFromAPI
		const result1 = await fetchSuspensionFromAPI({
			ts_code: "000722.SZ",
			trade_date: new Date("2025-03-20"),
		});
		console.log("Test case 1 (fetchSuspensionFromAPI):", result1);

		// 测试用例2：测试 fetchTodaySuspension
		const result2 = await fetchTodaySuspension("000722.SZ");
		console.log("Test case 2 (fetchTodaySuspension):", result2);

		// 测试用例3：测试 fetchSuspendedStocks
		const result3 = await fetchSuspendedStocks(new Date("2025-03-20"));
		console.log("Test case 3 (fetchSuspendedStocks):", result3);
	} catch (error) {
		console.error("Debug error:", error);
	}
}

// 执行调试函数
debugSuspension();
