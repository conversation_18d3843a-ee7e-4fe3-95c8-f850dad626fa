import {
	fetchUpDownLimit,
	fetchDailyData,
	fetchStockList,
	fetchCurrentPrices,
	fetchSuspendedStocks,
} from "./marketData.js";
import logger from "@/utils/logger.js";
import type { StockInfo } from "@packages/shared";
import type { DailyData, LimitData } from "@/types/api.js";
import type { KnockoutResult } from "@/types/api.js";
import { getLastThreeTradingDays } from "./marketTimeManager.js";
import { dateToCompactString, compactStringToDate } from "@/utils/dateUtils.js";

/**
 * ASCII Flow Chart of Knockout Process:
 *
 * Start
 * ┌─────────────────────────┐
 * │ getKnockoutStocks()     │
 * └──────────────┬──────────┘
 *                ▼
 * ┌─────────────────────────┐
 * │ Get Last 3 Trading Days │
 * └──────────────┬──────────┘
 *                ▼
 * ┌─────────────────────────┐
 * │ Fetch Initial Data:     │
 * │ - Daily Data (2 days)   │
 * │ - Limit Data (3 days)   │
 * │ - Current Prices        │
 * └──────────────┬──────────┘
 *                ▼
 * ┌─────────────────────────┐
 * │ Process Each Stock:     │
 * │ ┌───────────────────┐   │
 * │ │ Check Suspension  │   │
 * │ └────────┬──────────┘   │
 * │          ▼              │
 * │ ┌───────────────────┐   │
 * │ │ Calculate Consec. │   │
 * │ │ Limit Days        │   │
 * │ └────────┬──────────┘   │
 * │          ▼              │
 * │ ┌───────────────────┐   │
 * │ │ Compare with      │   │
 * │ │ Market Threshold  │   │
 * │ └───────────────────┘   │
 * └──────────────┬──────────┘
 *                ▼
 * ┌─────────────────────────┐
 * │ Return Knockout Results │
 * └─────────────────────────┘
 */

// 不同市场的强制平仓阈值设置
const KNOCKOUT_THRESHOLDS: Record<string, number> = {
	主板: 3,
	创业板: 2,
	科创板: 2,
	北交所: 1,
} as const;

/**
 * 根据交易所获取强制平仓所需的连续涨跌停天数阈值
 * @param exchange 交易所名称
 * @returns 对应的天数阈值
 */
function getKnockoutThreshold(exchange: string): number {
	return KNOCKOUT_THRESHOLDS[exchange] ?? Number.POSITIVE_INFINITY; // 使用空值合并运算符处理未知交易所
}

/**
 * 主函数：获取需要强制平仓的股票列表
 * 1. 获取最近三个交易日
 * 2. 获取初始数据（日线和涨跌停数据）
 * 3. 处理每只股票的强制平仓检查
 */
export async function getKnockoutStocks(): Promise<KnockoutResult[]> {
	try {
		logger.info("开始检查强制平仓股票");

		const tradingDays = await getLastThreeTradingDays();
		logger.info(
			`获取到交易日: ${tradingDays.map((d) => dateToCompactString(d)).join(", ")}`,
		);

		if (tradingDays.length < 3) {
			logger.warn("Not enough trading days data");
			return [];
		}

		// 获取每个交易日的停牌股票
		const suspendedStocksPromises = tradingDays
			.slice(0, 3)
			.map((date) => fetchSuspendedStocks(date));

		logger.info(
			`正在获取 ${suspendedStocksPromises.length} 天的停牌股票数据...`,
		);

		const [[dailyDataList, limitDataList], suspendedStocksList] =
			await Promise.all([
				fetchInitialData(tradingDays),
				Promise.all(suspendedStocksPromises),
			]);

		logger.info(`获取停牌股票完成，共 ${suspendedStocksList.length} 天的数据`);
		// 记录每天的停牌股票数量
		suspendedStocksList.forEach((stocks, index) => {
			const date = dateToCompactString(tradingDays[index]);
			logger.info(`${date} 停牌股票: ${stocks.length} 只`);
		});

		// 今日停牌的股票
		const todaySuspendedStocks = new Set(suspendedStocksList[0]);
		// 合并所有停牌股票（去重）
		const suspendedStocks = new Set(suspendedStocksList.flat());

		logger.info(
			`今日停牌股票数量: ${todaySuspendedStocks.size}, 所有停牌股票数量: ${suspendedStocks.size}`,
		);

		const stocks = await fetchStockList();
		logger.info(`获取股票列表完成，共 ${stocks.length} 只股票`);

		// 过滤掉今天停牌的股票
		const validStocks = stocks.filter(
			(stock) => !todaySuspendedStocks.has(stock.ts_code),
		);
		logger.info(`过滤后有效股票数量: ${validStocks.length}`);

		return processStocksForKnockout(
			validStocks,
			limitDataList,
			dailyDataList,
			suspendedStocks,
		);
	} catch (error) {
		logger.error(error, "Error in getKnockoutStocks");
		return [];
	}
}

/**
 * 获取初始数据：
 * 1. 获取前两天的日线收盘价数据
 * 2. 获取三天的涨跌停价格数据
 * 3. 获取当日实时收盘价并构造成统一格式
 */
async function fetchInitialData(
	tradingDays: Date[],
): Promise<[DailyData[], LimitData[]]> {
	// 只取前两天的日线数据
	const historicalDays = tradingDays.slice(1);
	const dailyDataPromises = historicalDays.map((date) =>
		fetchDailyData({ date }, ["ts_code", "trade_date", "close"]),
	);

	// 但是涨跌停数据需要取三天的
	const limitDataPromises = tradingDays.map((date) =>
		fetchUpDownLimit({ date }),
	);

	const [dailyData, limitData] = await Promise.all([
		Promise.all(dailyDataPromises),
		Promise.all(limitDataPromises),
	]);

	// 获取当日实时收盘价
	const allStockCodes = limitData[0]
		.map((item) => item.ts_code)
		.filter((code): code is string => code !== undefined);

	const currentPrices = await fetchCurrentPrices(allStockCodes);

	// 构造当日的数据结构
	const today = dateToCompactString(tradingDays[0]);
	const todayData: DailyData = Array.from(currentPrices.entries()).map(
		([ts_code, price]) => [ts_code, today, price],
	);

	// 将当日数据添加到历史数据前面
	return [[todayData, ...dailyData], limitData];
}

/**
 * 处理所有股票的强制平仓检查：
 * 遍历每只股票，检查是否满足强制平仓条件
 */
async function processStocksForKnockout(
	stocks: StockInfo[],
	limitDataList: LimitData[],
	dailyDataList: DailyData[],
	suspendedStocks: Set<string>,
): Promise<KnockoutResult[]> {
	const knockoutStocks: KnockoutResult[] = [];

	logger.info(`开始处理 ${stocks.length} 只股票的强制平仓检查`);
	logger.info(
		`日线数据天数: ${dailyDataList.length}, 涨跌停数据天数: ${limitDataList.length}`,
	);

	for (const stock of stocks) {
		const hasSuspension = suspendedStocks.has(stock.ts_code);

		let stockLimitData = limitDataList;
		let stockDailyData = dailyDataList;

		if (hasSuspension) {
			const [specificLimitData, specificDailyData] = await getStockSpecificData(
				stock.ts_code,
			);

			// 把今日的涨跌停数据和日线数据加在前面
			stockLimitData = [limitDataList[0], ...specificLimitData];
			stockDailyData = [dailyDataList[0], ...specificDailyData];
		}

		const [consecutiveDays, isLimitUp] = calculateConsecutiveLimitDays(
			stock.ts_code,
			stockLimitData,
			stockDailyData,
		);

		const requiredDays = getKnockoutThreshold(stock.market);

		if (consecutiveDays >= requiredDays) {
			const startDate = stockDailyData[requiredDays - 1].find(
				(item) => item[0] === stock.ts_code,
			)?.[1] as string;

			knockoutStocks.push({
				ts_code: stock.ts_code,
				knockoutStartDate: startDate,
				consecutiveDays,
				isLimitUp,
			});
		}
	}

	logger.info(`处理完成，共有 ${knockoutStocks.length} 只股票需要强制平仓`);
	if (knockoutStocks.length > 0) {
		logger.info(
			`强制平仓股票列表: ${knockoutStocks.map((s) => `${s.ts_code}(${s.isLimitUp ? "涨停" : "跌停"}${s.consecutiveDays}天)`).join(", ")}`,
		);
	}

	return knockoutStocks;
}

/**
 * 获取特定股票的涨跌停数据和日线数据：
 * 1. 获取该股票最近2天的日线数据
 * 2. 获取对应的涨跌停数据
 */
async function getStockSpecificData(
	tsCode: string,
): Promise<[LimitData[], DailyData[]]> {
	// 获取排除今天的最近2个不停牌交易日
	const stockDailyData = await fetchDailyData({ ts_code: tsCode, limit: 2 }, [
		"ts_code",
		"trade_date",
		"close",
	]);

	// 因为是按日期并行处理的，所以把 [a, b] 转换成 [[a], [b]]
	const dailyDataList = stockDailyData.map((item) => [item]);

	const stockTradeDates = stockDailyData
		.slice(0, 2)
		.map((item) => item[1] as string);

	// 获取这些日期的涨跌停数据
	const limitDataPromises = stockTradeDates.map((dateStr) => {
		// 从YYYYMMDD格式的日期字符串创建Date对象
		const date = compactStringToDate(dateStr);
		return fetchUpDownLimit({
			ts_code: tsCode,
			date,
		});
	});

	const limitData = await Promise.all(limitDataPromises);

	return [limitData, dailyDataList];
}

/**
 * 计算连续涨跌停天数：
 * 1. 从最新交易日开始向前遍历
 * 2. 比较收盘价与涨跌停价格
 * 3. 统计连续涨停或连续跌停的天数
 * 4. 如果出现方向改变或非涨跌停则终止计算
 */
function calculateConsecutiveLimitDays(
	tsCode: string,
	stockLimitData: LimitData[],
	dailyData: DailyData[],
): [number, boolean] {
	let consecutiveDays = 0;
	let isLimitUp = true;

	for (let i = 0; i < stockLimitData.length; i++) {
		const limitPrice = stockLimitData[i].find(
			(item) => item.ts_code === tsCode,
		);

		if (!limitPrice) {
			continue;
		}

		const closePrice = dailyData[i].find(
			(item) => item[0] === tsCode,
		)?.[2] as number;

		if (!closePrice) {
			continue;
		}

		const { up_limit, down_limit } = limitPrice;

		if (Math.abs(closePrice - (up_limit as number)) < 0.01) {
			if (i === 0 || isLimitUp) {
				consecutiveDays++;
			} else {
				break;
			}
			isLimitUp = true;
		} else if (Math.abs(closePrice - (down_limit as number)) < 0.01) {
			if (i === 0 || !isLimitUp) {
				consecutiveDays++;
			} else {
				break;
			}
			isLimitUp = false;
		} else {
			break;
		}
	}

	return [consecutiveDays, isLimitUp];
}
