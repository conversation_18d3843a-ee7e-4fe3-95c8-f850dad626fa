<template>
  <el-dialog v-model="dialogVisible" title="结算确认" width="360px" class="settle-dialog">
    <div class="modal-body">
      <div class="position-info">
        <div class="info-grid">
          <span class="label">标的：</span>
          <span class="value">{{ formatSubject(position?.ts_code || '') }}</span>

          <span class="label">持仓名本：</span>
          <span class="value">{{ position?.scale + "万" }}
            <span v-if="pendingScale" class="pending-scale">
              (待沽{{ pendingScale }}万)
            </span>
          </span>

          <span class="label">结构：</span>
          <span class="value">{{ position?.structure ? formatStructure(position.structure) : '' }}</span>

          <span class="label">期限：</span>
          <span class="value">{{ position?.term === 14 ? "2周" : position?.term + "个月" }}</span>

          <span class="label">开仓价：</span>
          <span class="value">{{ position?.entry_price }}</span>

          <span class="label">市价：</span>
          <span class="value">{{ currentPrice?.toFixed(2) }}</span>
        </div>
      </div>
      <div class="order-type">
        <div class="form-label">订单类型：</div>
        <div class="radio-group">
          <label :class="{ disabled: !canMarketSettle }">
            <input type="radio" v-model="orderType" value="market" />
            市价
          </label>
          <label>
            <input type="radio" v-model="orderType" value="limit" />
            限价
          </label>
          <label :class="{ disabled: !canVwapOrder }">
            <input type="radio" v-model="orderType" value="vwap" />
            均价
          </label>
        </div>
      </div>
      <div v-if="orderType === 'market' && !canMarketSettle" class="price-warning">{{
        `当前价格${position?.structure.endsWith("C") ? '低' : '高'}于执行价`
      }}
      </div>

      <div v-if="orderType === 'limit' && !canLimitSettle" class="price-warning">
        {{ `限价必须${position?.structure.endsWith("C") ? '高' : '低'}于执行价` }}
      </div>
      <div v-if="orderType === 'vwap' && !canVwapOrder" class="time-warning">当前时间无法创建均价订单</div>
      <div class="sell-scale">
        <div class="form-label">平仓名本：</div>
        <div class="radio-group">
          <template v-if="availableScales.length > 0">
            <label v-for="scale in availableScales" :key="scale">
              <input type="radio" v-model="sellScale" :value="scale" />
              {{ scale + "万" }}
            </label>
          </template>
          <span v-else class="no-scale-hint">暂无可平仓名本</span>
        </div>
      </div>
      <div v-if="orderType === 'limit'" class="limit-price">
        <div class="form-label">限价价格：</div>
        <div class="price-input">
          <el-input-number v-model="limitPrice" :min="0" :precision="2" :step="0.01" controls-position="right"
            class="limit-price-input" placeholder="请输入限价价格" />
          <div class="price-hint">当前股价：{{ currentPrice }}</div>
        </div>
      </div>
      <!-- 二次确认提示 -->
      <div v-if="needConfirmation" class="confirmation-message">
        请确认是否执行{{ orderType === 'market' ? '市价' : orderType === 'limit' ? '限价' : '均价' }}结算，订单成交后不可撤销
      </div>
    </div>

    <template #footer>
      <div class="button-group">
        <el-button class="cancel-button" @click="close">取消</el-button>
        <el-button class="submit-button" @click="handleButtonClick" :disabled="!canSubmit || confirmCountdown > 0"
          :class="{ 'countdown-active': confirmCountdown > 0 }">
          {{ needConfirmation
            ? confirmCountdown > 0
              ? `确认结算 (${confirmCountdown}s)`
              : '确认结算'
            : '结算'
          }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, onUnmounted } from "vue";
import { useStockStore } from "@/stores/stock";
import { formatStructure } from "@/utils/format";
import type { PositionData } from "@packages/shared";
import { tradeApi } from "@/api";
import { validateVwapTime } from "@/utils/validateVwapTime";
const { formatSubject } = useStockStore();

const loading = ref(true);
const pendingScale = ref(0);

const props = defineProps<{
	modelValue: boolean;
	position: PositionData | null;
	currentPrice: number | undefined;
}>();

const emit = defineEmits<{
	(e: "update:modelValue", value: boolean): void;
	(
		e: "confirm",
		orderType: string,
		sellScale: number,
		limitPrice?: number,
	): void;
}>();

// 使用计算属性来处理双向绑定
const dialogVisible = computed({
	get: () => props.modelValue,
	set: (value) => emit("update:modelValue", value),
});

const orderType = ref("market");
const sellScale = ref(props.position?.scale || 0);
const limitPrice = ref(0);

// 添加新的响应式变量
const showConfirmation = ref(false);
const confirmCountdown = ref(0);
let countdownTimer: number | null = null;

// 组件挂载时启动价格更新
onMounted(async () => {
	const pendingOrders = await tradeApi.getPendingOrders(
		props.position?.trade_no || "",
	);
	pendingScale.value =
		pendingOrders?.reduce((sum, order) => sum + order.scale, 0) || 0;
	loading.value = false;
});

const close = () => {
	dialogVisible.value = false;
};

const confirm = () => {
	emit("confirm", orderType.value, sellScale.value, limitPrice.value);
	close();
};

// 修改 watch 监听器，在切换到限价时设置当前价格为默认值
watch(
	() => orderType.value,
	(newType) => {
		if (newType === "limit" && props.currentPrice) {
			limitPrice.value = props.currentPrice;
		}
	},
);

// Add this computed property to generate available scales
const availableScales = computed(() => {
	if (!props.position?.scale) return [];
	const availableTotal = props.position.scale - pendingScale.value; // 减去待沽名本
	const scales: number[] = [];
	for (let i = 100; i <= availableTotal; i += 100) {
		scales.push(i);
	}
	return scales;
});

// 修改计算属性判断是否可以市价结算
const canMarketSettle = computed(() => {
	if (props.currentPrice === undefined || !props.position?.exercise_price)
		return false;

	// 根据结构判断是看涨还是看跌
	const isCall = props.position.structure.endsWith("C");

	if (isCall) {
		// 看涨期权：当市价高于执行价时可以市价结算，等于不让卖
		return props.currentPrice > props.position.exercise_price;
	}

	// 看跌期权：当市价低于执行价时可以市价结算，等于不让卖
	return props.currentPrice < props.position.exercise_price;
});

// 添加新的计算属性来验证限价
const canLimitSettle = computed(() => {
	if (!limitPrice.value || !props.position?.exercise_price) return false;

	// 根据结构判断是看涨还是看跌
	const isCall = props.position.structure.endsWith("C");

	if (isCall) {
		// 看涨期权：限价必须高于执行价
		return limitPrice.value > props.position.exercise_price;
	}

	// 看跌期权：限价必须低于执行价
	return limitPrice.value < props.position.exercise_price;
});

// 修改监听器，只在有实际价格时才进行切换
watch(
	() => props.currentPrice,
	(newPrice) => {
		if (
			newPrice !== undefined &&
			props.position?.exercise_price &&
			!canMarketSettle.value &&
			orderType.value === "market"
		) {
			orderType.value = "limit";
		}
	},
);

// 只检查 VWAP 订单是否可用
const canVwapOrder = computed(() => validateVwapTime().valid);

// 监听 VWAP 可用性变化
watch(canVwapOrder, (newCanVwap) => {
	if (!newCanVwap && orderType.value === "vwap") {
		orderType.value = "market";
	}
});

// 添加新的计算属性检查是否可以提交
const canSubmit = computed(() => {
	// 如果待沽名本等于总持仓，则禁用提交
	if (pendingScale.value >= (props.position?.scale || 0)) {
		return false;
	}

	if (orderType.value === "market" && !canMarketSettle.value) {
		return false;
	}

	// 如果是限价订单且限价无效，则禁用提交
	if (orderType.value === "limit" && !canLimitSettle.value) {
		return false;
	}

	if (orderType.value === "vwap" && !canVwapOrder.value) {
		return false;
	}

	return true;
});

// 修改确认按钮的点击处理
const handleButtonClick = () => {
	if (orderType.value === "vwap" || needConfirmation.value) {
		confirm();
	} else {
		showConfirmationDialog();
	}
};

const showConfirmationDialog = () => {
	showConfirmation.value = true;
	confirmCountdown.value = 2; // 2秒倒计时

	countdownTimer = window.setInterval(() => {
		confirmCountdown.value--;
		if (confirmCountdown.value <= 0) {
			if (countdownTimer) {
				clearInterval(countdownTimer);
				countdownTimer = null;
			}
		}
	}, 1000);
};

// 计算属性
const needConfirmation = computed(
	() => showConfirmation.value && orderType.value !== "vwap",
);

// 监听 show 属性变化
watch(
	() => props.modelValue,
	(newShow) => {
		if (newShow) {
			orderType.value = "market";
			sellScale.value = props.position?.scale || 0;
			showConfirmation.value = false;
			confirmCountdown.value = 0;
			if (countdownTimer) {
				clearInterval(countdownTimer);
				countdownTimer = null;
			}
		}
	},
);

// 在组件卸载时清理定时器
onUnmounted(() => {
	if (countdownTimer) {
		clearInterval(countdownTimer);
	}
});

// 监听订单类型变化时重置确认状态
watch(
	() => orderType.value,
	() => {
		showConfirmation.value = false;
		if (countdownTimer) {
			clearInterval(countdownTimer);
			countdownTimer = null;
		}
		confirmCountdown.value = 0;
	},
);
</script>

<style>
.settle-dialog .el-dialog__header {
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}
</style>

<style scoped>
.modal-body {
  margin: 0 16px;
}

.position-info {
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.info-grid {
  display: grid;
  grid-template-columns: auto auto;
  gap: 8px 24px;
  align-items: center;
  margin-right: 12px;
}

.info-grid .label {
  color: var(--el-text-color-regular);
  justify-self: end;
  white-space: nowrap;
}

.info-grid .value {
  justify-self: start;
}

.pending-scale {
  color: var(--el-text-color-secondary);
  font-size: 0.9em;
  margin-left: 4px;
}

.order-type,
.sell-scale {
  margin: 6px 0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.radio-group {
  display: flex;
  gap: 12px;
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 4px;
  border: 1px solid transparent;
  border-radius: 6px;
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.radio-group label:hover {
  color: var(--el-color-primary-dark-2);
  border-color: var(--el-color-primary-dark-2);
  background: var(--el-fill-color-light);
}

.radio-group input {
  cursor: pointer;
  margin: 3px;
  margin-left: 1px;
}

.radio-group input:checked+span {
  color: var(--el-color-primary);
}

.settle-dialog .button-group {
  display: flex;
  justify-content: flex-end;
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.cancel-button,
.submit-button {
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.cancel-button {
  border: 1px solid var(--el-border-color);
  background-color: var(--color-secondary);
  color: var(--el-text-color-primary);
}

.submit-button {
  border: none;
  background-color: var(--el-color-primary-dark-2);
  color: white;
}

.submit-button:hover {
  background-color: var(--el-color-primary-dark-2);
}

.limit-price {
  display: flex;
  margin-top: 12px;
  gap: 20px;
}

.limit-price-input {
  width: 204px;
}

.price-input {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.form-label {
  margin-top: 2px;
}

.price-input input {
  padding: 5px 10px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  width: 150px;
}

.price-input input:focus,
.scale-input input:focus {
  border-color: var(--el-color-primary-dark-2);
  outline: none;
}

.price-hint {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.price-warning {
  font-size: 12px;
  color: var(--el-color-danger);
  margin: 2px 0;
}

.time-warning {
  font-size: 12px;
  color: var(--el-color-danger);
  margin: 2px 0;
}

.no-scale-hint {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  padding-top: 2px;
}

.confirmation-message {
  color: var(--el-color-warning);
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
}

.submit-button.countdown-active {
  opacity: 0.8;
  cursor: not-allowed;
}

.submit-button {
  transition: opacity 0.3s ease;
}
</style>
