<template>
  <el-dialog v-model="visible" :title="hasPassword ? '请输入支付密码' : '设置支付密码'" width="360px" @close="handleClose"
    @opened="handleOpened">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" :status-icon="true"
      @submit.prevent="handleConfirm">
      <template v-if="!hasPassword">
        <el-form-item label="支付密码" prop="password">
          <el-input v-model="form.password" type="password" show-password placeholder="请输入6位数字密码" maxlength="6"
            :disabled="loading" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="form.confirmPassword" type="password" show-password placeholder="请再次输入密码" maxlength="6"
            :disabled="loading" />
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item label="支付密码" prop="password">
          <el-input ref="passwordInputRef" v-model="form.password" type="password" show-password placeholder="请输入支付密码"
            maxlength="6" :disabled="loading" />
        </el-form-item>
      </template>

      <!-- 显示错误信息 -->
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
    </el-form>

    <template #footer>
      <el-button @click="handleClose" :disabled="loading">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm">
        {{ hasPassword ? '确认' : '设置' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage, ElInput } from "element-plus";
import { paymentApi } from "@/api";
import { getDisplayErrorMessage } from "@/utils/error";

const props = defineProps<{
	modelValue: boolean;
	hasPassword: boolean;
	onRequest: (password: string) => Promise<void>;
}>();

const emit = defineEmits<{
	"update:modelValue": [value: boolean];
	success: [];
	cancel: [];
	"check-password": [];
}>();

const visible = computed({
	get: () => props.modelValue,
	set: (value) => emit("update:modelValue", value),
});

const formRef = ref<FormInstance>();
const loading = ref(false);
const form = ref({
	password: "",
	confirmPassword: "",
});

// 错误信息
const errorMessage = ref("");

// 验证规则
const validatePassword = (
	_rule: unknown,
	value: string,
	callback: (error?: Error) => void,
) => {
	if (!value) {
		callback(new Error("请输入支付密码"));
	} else if (!/^\d{6}$/.test(value)) {
		callback(new Error("支付密码必须是6位数字"));
	} else {
		callback();
	}
};

const validateConfirmPassword = (
	_rule: unknown,
	value: string,
	callback: (error?: Error) => void,
) => {
	if (!value) {
		callback(new Error("请再次输入密码"));
	} else if (value !== form.value.password) {
		callback(new Error("两次输入的密码不一致"));
	} else {
		callback();
	}
};

const rules: FormRules = {
	password: [{ validator: validatePassword, trigger: "blur" }],
	confirmPassword: [{ validator: validateConfirmPassword, trigger: "blur" }],
};

const handleClose = () => {
	resetForm();
	visible.value = false;
	emit("cancel");
};

// 抽取重置表单的逻辑为单独的函数
const resetForm = () => {
	form.value = { password: "", confirmPassword: "" };
	errorMessage.value = "";
	// 重置表单验证
	nextTick(() => {
		formRef.value?.clearValidate();
	});
};

const handleConfirm = async () => {
	if (!formRef.value) return;

	// 先进行表单验证
	try {
		await formRef.value.validate();
	} catch {
		return;
	}

	// 验证通过后，执行业务逻辑
	try {
		loading.value = true;
		errorMessage.value = "";

		if (!props.hasPassword) {
			// 设置支付密码
			await paymentApi.setPaymentPassword(form.value.password);
			ElMessage.success("支付密码设置成功");
			// 重新检查密码状态
			emit("check-password");
		}

		// 执行业务请求
		await props.onRequest(form.value.password);

		// 成功后关闭对话框并触发成功事件
		handleClose();
		emit("success");
	} catch (error) {
		errorMessage.value = getDisplayErrorMessage(error);
		form.value.password = "";
		nextTick(() => {
			passwordInputRef.value?.focus();
		});
	} finally {
		loading.value = false;
	}
};

const passwordInputRef = ref<InstanceType<typeof ElInput>>();

// 对话框完全打开后的处理函数
const handleOpened = () => {
	passwordInputRef.value?.focus();
};

// 修改 watch，处理所有关闭情况
watch(visible, (newVisible) => {
	if (!newVisible) {
		resetForm();
		loading.value = false;
		emit("cancel");
	}
});
</script>

<style scoped>
form {
  margin-top: 12px;
}

.error-message {
  color: var(--el-color-danger);
  font-size: 14px;
  margin-top: -8px;
  margin-bottom: 16px;
  padding: 0 20px;
  text-align: center;
}

:deep(.el-form-item__content) {
  justify-content: center;
}

:deep(.el-input) {
  width: 200px;
}
</style>