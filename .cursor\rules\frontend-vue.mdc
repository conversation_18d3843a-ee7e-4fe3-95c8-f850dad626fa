---
description: 
globs: packages/client/src/**,packages/admin/src/**
alwaysApply: false
---
## Vue 3 Frontend Conventions

*   **Framework:** Always use Vue 3 Composition API with `<script setup lang="ts">`.
*   **UI Library:** Use Element Plus components for UI elements. Import components as needed (e.g., `import { ElButton, ElTable } from 'element-plus';`).
*   **State Management:** Use Pinia for managing application state. Define stores in the `src/stores` directory (e.g., `@/stores/auth.ts`). Access stores using `useMyStore()`.
*   **API Calls:** Make API calls using the dedicated functions defined in `src/api` (e.g., `import { tradeApi } from '@/api';`). Ensure Axios configuration in `src/plugins/axios.ts` is respected.
*   **Routing:** Define routes in `src/router/index.ts` using Vue Router.
*   **Components:**
    *   Place page-level components in `src/views`.
    *   Place reusable components in `src/components`.
    *   Utilize custom composables from `src/composables` where appropriate (e.g., `useTableSettings`).
*   **Types:** Import shared types and enums from the `@packages/shared` package.
*   **Utilities:** Use utility functions from `src/utils` for common tasks like formatting (`@/utils/format.ts`).
*   **Styling:** Follow existing SCSS/CSS patterns.

