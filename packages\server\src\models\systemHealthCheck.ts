import prisma from "@/lib/prisma.js";
import type { JsonValue } from "@prisma/client/runtime/library";
import type { Prisma } from "@prisma/client";

export interface SystemHealthCheckData {
	check_id?: number;
	timestamp?: Date | null;
	test_type: string;
	status: string;
	details?: JsonValue;
	execution_time?: number | null;
	error_message?: string | null;
}

export type HealthCheckStatus = "success" | "failure" | "partial" | "running";

/**
 * Create a new health check record
 */
export async function create(data: SystemHealthCheckData) {
	return prisma.system_health_checks.create({
		data: {
			test_type: data.test_type,
			status: data.status,
			details: data.details ?? {},
			execution_time: data.execution_time,
			error_message: data.error_message,
		},
	});
}

/**
 * Update an existing health check record
 */
export async function update(
	check_id: number,
	data: Partial<Omit<SystemHealthCheckData, "check_id">>,
) {
	// Explicitly create a properly typed update object
	const updateData: Prisma.system_health_checksUpdateInput = {};

	if (data.test_type !== undefined) updateData.test_type = data.test_type;
	if (data.status !== undefined) updateData.status = data.status;
	// Handle JSON field differently
	if (data.details !== undefined) {
		updateData.details =
			data.details === null ? null : JSON.parse(JSON.stringify(data.details));
	}
	if (data.execution_time !== undefined)
		updateData.execution_time = data.execution_time;
	if (data.error_message !== undefined)
		updateData.error_message = data.error_message;
	if (data.timestamp !== undefined) updateData.timestamp = data.timestamp;

	return prisma.system_health_checks.update({
		where: { check_id },
		data: updateData,
	});
}

/**
 * Get health check records with pagination
 */
export async function getAll(
	page = 1,
	pageSize = 20,
): Promise<{
	items: SystemHealthCheckData[];
	total: number;
}> {
	const offset = (page - 1) * pageSize;

	const [total, items] = await Promise.all([
		prisma.system_health_checks.count(),
		prisma.system_health_checks.findMany({
			take: pageSize,
			skip: offset,
			orderBy: { timestamp: "desc" },
		}),
	]);

	// Convert types to match our interface
	const typedItems = items.map(
		(item): SystemHealthCheckData => ({
			...item,
			details: item.details as JsonValue,
		}),
	);

	return { items: typedItems, total };
}

/**
 * Get the most recent health check of a specific type
 */
export async function getLatestByType(
	test_type: string,
): Promise<SystemHealthCheckData | null> {
	const item = await prisma.system_health_checks.findFirst({
		where: { test_type },
		orderBy: { timestamp: "desc" },
	});

	if (!item) return null;

	// Convert type to match our interface
	return {
		...item,
		details: item.details as JsonValue,
	};
}

/**
 * Get the latest records of each test type
 */
export async function getLatestOfEachType(): Promise<SystemHealthCheckData[]> {
	// Get all unique test types
	const testTypes = await prisma.system_health_checks.findMany({
		select: { test_type: true },
		distinct: ["test_type"],
	});

	// For each test type, get the latest record
	const latestChecks = await Promise.all(
		testTypes.map(({ test_type }) => getLatestByType(test_type)),
	);

	// Filter out nulls
	return latestChecks.filter(Boolean) as SystemHealthCheckData[];
}

/**
 * Get all health check records of a specific type with pagination
 */
export async function getByType(
	test_type: string,
	page = 1,
	pageSize = 20,
): Promise<{
	items: SystemHealthCheckData[];
	total: number;
}> {
	const offset = (page - 1) * pageSize;

	const [total, items] = await Promise.all([
		prisma.system_health_checks.count({
			where: { test_type },
		}),
		prisma.system_health_checks.findMany({
			where: { test_type },
			take: pageSize,
			skip: offset,
			orderBy: { timestamp: "desc" },
		}),
	]);

	// Convert types to match our interface
	const typedItems = items.map(
		(item): SystemHealthCheckData => ({
			...item,
			details: item.details as JsonValue,
		}),
	);

	return { items: typedItems, total };
}
