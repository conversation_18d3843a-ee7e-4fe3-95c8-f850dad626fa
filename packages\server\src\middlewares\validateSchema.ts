import type { Request, Response, NextFunction } from "express";
import { z } from "zod";
import { AppError } from "@/core/appError.js";

export const validateSchema = (schema: z.ZodSchema) => {
	return async (req: Request, _res: Response, next: NextFunction) => {
		try {
			const data = req.method === "GET" ? req.query : req.body;
			const validated = await schema.parseAsync(data);

			// 将验证后的数据放回请求对象
			if (req.method === "GET") {
				req.query = validated;
			} else {
				req.body = validated;
			}

			next();
		} catch (error) {
			if (error instanceof z.ZodError) {
				next(
					AppError.create(
						"BAD_REQUEST",
						error.errors.map((e) => e.message).join(", "),
					),
				);
			} else {
				next(error);
			}
		}
	};
};
