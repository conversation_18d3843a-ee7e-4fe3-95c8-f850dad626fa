# Stage 1: Build stage
FROM node:23-alpine AS builder

# 安装 pnpm
RUN npm install -g pnpm

WORKDIR /app

# 复制根目录和共享包的 package.json 及 pnpm 配置
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/shared/package.json ./packages/shared/

# 复制 server 包的 package.json
COPY packages/server/package.json ./packages/server/

# 安装所有依赖项 (包括 devDependencies 用于构建)
# 使用 --filter 来只安装 server 及其依赖项，包括 shared
# --prod=false 确保安装 devDependencies
RUN pnpm install --filter @packages/server... --frozen-lockfile --prod=false

# 复制 shared 包的源代码
COPY packages/shared/ ./packages/shared/

# 复制 server 包的源代码
COPY packages/server/ ./packages/server/

# 生成 prisma client
RUN pnpm --filter @packages/server prisma generate

# 构建 shared 包
RUN pnpm --filter @packages/shared build

# 构建 server 应用
RUN pnpm --filter @packages/server build

# Stage 2: Production stage
FROM node:23-alpine

WORKDIR /app

# 从 builder 阶段复制必要的配置文件和依赖项
COPY --from=builder /app/package.json /app/pnpm-lock.yaml /app/pnpm-workspace.yaml ./
COPY --from=builder /app/packages/shared/package.json ./packages/shared/
COPY --from=builder /app/packages/server/package.json ./packages/server/

# 安装 pnpm
RUN npm install -g pnpm

# 只安装生产依赖
RUN pnpm install --filter @packages/server... --frozen-lockfile --prod

# 从 builder 阶段复制构建好的 server 应用和 shared 包
COPY --from=builder /app/packages/server/dist ./packages/server/dist
COPY --from=builder /app/packages/shared/dist ./packages/shared/dist

# 复制生成的 client
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma

# 复制运行时可能需要的其他文件 (例如 public 目录)
COPY --from=builder /app/packages/server/public ./packages/server/public

# 暴露端口 (根据 app.ts, 默认为 3000)
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000
# 注意：数据库连接字符串等敏感信息应通过 docker-compose 或其他方式注入

# 运行应用
CMD ["node", "packages/server/dist/app.js"]