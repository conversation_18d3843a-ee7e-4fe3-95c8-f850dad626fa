<template>
  <div class="channel-orders-view view">
    <!-- 订单汇总卡片 -->
    <div class="summary-section">
      <div class="header-container">
        <h2>通道订单</h2>
        <div class="header-actions">
          <el-button @click="showStatusModal = true" :icon="Connection">检查通道连接</el-button>
          <el-select v-model="selectedChannelId" placeholder="选择通道" class="channel-selector"
            @change="handleChannelChange">
            <el-option v-for="channel in channels" :key="channel.channel_id" :label="channel.name"
              :value="channel.channel_id"></el-option>
          </el-select>
        </div>
      </div>
      <div class="summary-cards">
        <div class="summary-card" v-loading="summaryLoading">
          <div class="card-icon">
            <el-icon>
              <Document />
            </el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ orderSummary.totalOrders }}</div>
            <div class="card-label">订单总数</div>
          </div>
        </div>

        <div class="summary-card" v-loading="summaryLoading">
          <div class="card-icon">
            <el-icon>
              <Check />
            </el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ orderSummary.completedOrders }}</div>
            <div class="card-label">已结算订单</div>
          </div>
        </div>

        <div class="summary-card" v-loading="summaryLoading">
          <div class="card-icon">
            <el-icon>
              <Money />
            </el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatIntNumber(orderSummary.totalNotional) }}万</div>
            <div class="card-label">总名本</div>
          </div>
        </div>

        <div class="summary-card" v-loading="summaryLoading">
          <div class="card-icon">
            <el-icon>
              <DataAnalysis />
            </el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatNumber(orderSummary.totalPremium) }}万</div>
            <div class="card-label">总期权费</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="orders-table-section">
      <div class="table-card">
        <div class="card-header">
          <h3 class="section-title">通道订单列表</h3>
          <div class="header-controls">
            <!-- 状态筛选下拉框 -->
            <el-select v-model="searchParams.status" placeholder="订单状态" clearable @change="handleStatusChange"
              class="filter-select">
              <el-option label="全部订单" value="all"></el-option>
              <el-option label="持有中" value="holding"></el-option>
              <el-option label="已结算" value="sold"></el-option>
            </el-select>

            <!-- 日期范围选择器 -->
            <el-date-picker v-model="dateRange" type="daterange" class="date-range-picker" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD"
              @change="handleDateChange"></el-date-picker>

            <!-- 刷新按钮 -->
            <el-button type="primary" :icon="Refresh" @click="loadOrders" :loading="ordersLoading">刷新</el-button>
          </div>
        </div>

        <!-- 通道未配置提示 -->
        <div v-if="showNoConfigWarning" class="no-config-warning">
          <el-alert title="通道数据库未配置" type="warning" description="该通道尚未配置数据库连接，请联系管理员在配置文件或环境变量中添加该通道的数据库连接信息。" show-icon
            :closable="false" />
        </div>

        <div v-else class="card-body">
          <div v-if="orders.length === 0 && !ordersLoading" class="empty-data">
            <el-empty description="暂无订单数据" />
          </div>
          <el-table v-else :data="orders" style="width: 100%" v-loading="ordersLoading" @sort-change="handleSortChange">
            <el-table-column prop="trade_no" label="订单编号" min-width="140" sortable="custom"></el-table-column>
            <el-table-column prop="user_id" label="用户ID" min-width="108" sortable="custom"></el-table-column>
            <el-table-column prop="ts_code" label="股票代码" min-width="120" sortable="custom"></el-table-column>
            <el-table-column prop="entry_price" label="开仓价格" min-width="80" sortable="custom">
              <template #default="scope">
                {{ formatNumber(scope.row.entry_price) }}
              </template>
            </el-table-column>
            <el-table-column prop="scale" label="名本" min-width="80" sortable="custom">
              <template #default="scope">
                {{ scope.row.scale }}万
              </template>
            </el-table-column>
            <el-table-column prop="term" label="期限" min-width="80" sortable="custom">
              <template #default="scope">
                {{ scope.row.term === 14 ? '2周' : scope.row.term + '个月' }}
              </template>
            </el-table-column>
            <el-table-column prop="quote" label="报价" min-width="80" sortable="custom">
              <template #default="scope">
                {{ formatNumber(scope.row.quote) }}万
              </template>
            </el-table-column>
            <el-table-column prop="quote_provider" label="交易方" min-width="80">
              <template #default="scope">
                {{ formatProvider(scope.row.quote_provider || 'INK') }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" min-width="80" sortable="custom">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ formatStatus(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" min-width="140" sortable="custom">
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
            <!-- 结算时间列，仅在已结算状态时显示 -->
            <el-table-column v-if="searchParams.status === 'sold'" prop="closed_at" label="结算时间" min-width="140"
              sortable="custom">
              <template #default="scope">
                {{ formatDate(scope.row.closed_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="100">
              <template #default="scope">
                <el-button size="small" @click="viewOrderDetails(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container" v-if="orders.length > 0">
            <el-pagination layout="total, sizes, prev, pager, next" :total="totalOrders" :page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]" :current-page="currentPage" @current-change="handlePageChange"
              @size-change="handleSizeChange" />
          </div>
        </div>
      </div>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" v-model="dialogVisible" width="800px">
      <div v-if="selectedOrder" class="order-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单编号">{{ selectedOrder.trade_no }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ selectedOrder.user_id }}</el-descriptions-item>
          <el-descriptions-item label="股票代码">{{ selectedOrder.ts_code }}</el-descriptions-item>
          <el-descriptions-item label="开仓价格">{{ formatNumber(selectedOrder.entry_price) }}</el-descriptions-item>
          <el-descriptions-item label="执行价格">{{ formatNumber(selectedOrder.exercise_price) }}</el-descriptions-item>
          <el-descriptions-item label="名本">{{ selectedOrder.scale }}万</el-descriptions-item>
          <el-descriptions-item label="期限">{{ selectedOrder.term === 14 ? '2周' : selectedOrder.term + '个月'
          }}</el-descriptions-item>
          <el-descriptions-item label="交易方">{{ formatProvider(selectedOrder.quote_provider || 'INK')
          }}</el-descriptions-item>
          <el-descriptions-item label="报价">{{ formatNumber(selectedOrder.quote) }}%</el-descriptions-item>
          <el-descriptions-item label="渠道价">
            {{ formatNumber(selectedOrder.quote - (selectedOrder.quote_diff || 0)) }}%
            <span v-if="selectedOrder.quote_diff && selectedOrder.quote_diff > 0">
              (提价 {{ formatNumber(selectedOrder.quote_diff) }}%)
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="市价" v-if="selectedOrder.status === 'holding'">
            <span v-if="getCurrentPrice(selectedOrder.ts_code)">{{ formatNumber(getCurrentPrice(selectedOrder.ts_code)!)
            }}</span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item :label="`${selectedOrder.status === 'holding' ? '预估' : ''}收益(抽成前)`">
            {{ calculateProfit(selectedOrder) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedOrder.status)">
              {{ formatStatus(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedOrder.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="到期日" v-if="selectedOrder.expiry_date">{{ formatDate(selectedOrder.expiry_date)
          }}</el-descriptions-item>
          <el-descriptions-item label="结算价格" v-if="selectedOrder.status === 'sold'">{{
            formatNumber(selectedOrder.settle_price) }}</el-descriptions-item>
          <el-descriptions-item label="结算时间" v-if="selectedOrder.status === 'sold'">{{
            formatDate(selectedOrder.closed_at)
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 通道连接状态对话框 -->
    <el-dialog title="通道数据库连接状态" v-model="showStatusModal" width="600px">
      <div v-loading="statusLoading">
        <el-table :data="channelStatus" style="width: 100%">
          <el-table-column prop="name" label="通道名称" width="120"></el-table-column>
          <el-table-column prop="channel_id" label="通道ID" width="140"></el-table-column>
          <el-table-column prop="db_url" label="数据库" width="100"></el-table-column>
          <el-table-column prop="status" label="连接状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === '连接成功' ? 'success' : scope.row.status === '未配置' ? 'info' : 'danger'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <div class="dialog-footer">
          <p class="dialog-hint">如果通道连接状态异常，请联系技术部门进行配置</p>
        </div>
      </div>
      <template #footer>
        <el-button @click="showStatusModal = false">关闭</el-button>
        <el-button type="primary" @click="checkChannelStatus" :loading="statusLoading">刷新</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import {
  Document,
  Check,
  Money,
  DataAnalysis,
  Connection,
  Refresh,
} from "@element-plus/icons-vue";
import { channelAdminApi } from "@/api/channelAdmin";
import type { OrderData, ChannelData } from "@packages/shared";
import { PriceProviderNames } from "@packages/shared";
import { formatNumber, formatIntNumber } from "@/utils/format";
import { fetchCurrentPrices } from "@/utils/stock";
import { useSiteConfigStore } from "@/stores/siteConfig";

// 获取站点配置
const siteConfigStore = useSiteConfigStore();

// 定义价格更新间隔（毫秒）
const PRICE_UPDATE_INTERVAL = 5000;
let priceUpdateTimer: number | null = null;

// 通道列表
const channels = ref<ChannelData[]>([]);
const selectedChannelId = ref<string>("");
const selectedChannelName = ref<string>("");
const showNoConfigWarning = ref(false);

// 订单列表数据
const orders = ref<OrderData[]>([]);
const totalOrders = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const ordersLoading = ref(false);

// 当前市价缓存
const currentPrices = ref<Map<string, number>>(new Map());

// 订单汇总数据
const orderSummary = reactive({
  totalOrders: 0,
  completedOrders: 0,
  totalNotional: 0,
  totalPremium: 0,
});
const summaryLoading = ref(false);

// 详情对话框
const dialogVisible = ref(false);
const selectedOrder = ref<OrderData | null>(null);

// 筛选条件
const dateRange = ref<[Date, Date] | null>(null);
const searchParams = reactive({
  startDate: "",
  endDate: "",
  sortBy: "created_at",
  sortOrder: "DESC" as "ASC" | "DESC",
  status: "all",
});

// 通道连接状态
const showStatusModal = ref(false);
const statusLoading = ref(false);
const channelStatus = ref<
  {
    channel_id: string;
    name: string;
    db_url: string;
    status: string;
  }[]
>([]);

// 格式化状态
const formatStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    holding: "持有中",
    sold: "已结算",
  };
  return statusMap[status] || status;
};

// 状态标签类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    holding: "primary",
    sold: "success",
  };
  return typeMap[status] || "default";
};

// 格式化日期
const formatDate = (dateString: string | undefined | null) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 获取当前价格
const getCurrentPrice = (tsCode: string): number | undefined => {
  return currentPrices.value.get(tsCode);
};

// 更新所有持仓中订单的当前价格
const updateCurrentPrices = async () => {
  // 获取所有持有中订单的股票代码
  const holdingCodes = orders.value
    .filter((order) => order.status === "holding")
    .map((order) => order.ts_code);

  // 如果没有持有中订单，则不更新
  if (holdingCodes.length === 0) return;

  try {
    const prices = await fetchCurrentPrices(holdingCodes);
    // 更新价格缓存
    prices.forEach((price, index) => {
      if (price) {
        currentPrices.value.set(holdingCodes[index], price);
      }
    });
  } catch (error) {
    console.error("更新股票价格失败", error);
  }
};

// 设置价格更新定时器
const setupPricePolling = () => {
  // 清除已有定时器
  clearPricePolling();

  // 立即执行一次
  updateCurrentPrices();

  // 设置定时更新
  priceUpdateTimer = window.setInterval(
    updateCurrentPrices,
    PRICE_UPDATE_INTERVAL,
  );
};

// 清除价格更新定时器
const clearPricePolling = () => {
  if (priceUpdateTimer) {
    clearInterval(priceUpdateTimer);
    priceUpdateTimer = null;
  }
};

// 计算预估收益
const calculateProfit = (order: OrderData) => {
  if (!order) return "-";

  // 获取原始订单信息
  const {
    scale,
    status,
    settle_price,
    ts_code,
    exercise_price,
    entry_price,
    structure,
  } = order;

  // 对于持有中订单但没有市价的情况
  if (status === "holding" && !getCurrentPrice(ts_code)) {
    return "加载中...";
  }

  // 获取价格 - 已结算订单用结算价，持有中订单用市价
  const price = status === "holding" ? getCurrentPrice(ts_code) : settle_price;

  // 如果无法获取价格，返回默认值
  if (!price) return "-";

  // 计算预估收益
  const isCall = structure?.endsWith("C");

  // 计算预估收益
  if (isCall) {
    // 看涨期权：当市价高于执行价时有价值
    if (price <= exercise_price) return "¥0";
    return `¥${formatIntNumber((scale * 10000 * (price - exercise_price)) / entry_price)}`;
  }
  // 看跌期权：当市价低于执行价时有价值
  if (price >= exercise_price) return "¥0";
  return `¥${formatIntNumber((scale * 10000 * (exercise_price - price)) / entry_price)}`;
};

// 加载通道列表
const loadChannels = async () => {
  try {
    const data = await channelAdminApi.channel.getAll();
    if (data) {
      channels.value = data;

      // 默认选择第一个通道
      if (data.length > 0 && !selectedChannelId.value) {
        selectedChannelId.value = data[0].channel_id;
        selectedChannelName.value = data[0].name;
        await loadOrders();
        await loadOrderSummary();
      }
    }
  } catch (error) {
    console.error("加载通道列表失败", error);
    ElMessage.error("加载通道列表失败");
  }
};

// 加载订单列表
const loadOrders = async () => {
  if (!selectedChannelId.value) {
    ElMessage.warning("请先选择通道");
    return;
  }

  ordersLoading.value = true;
  showNoConfigWarning.value = false;

  try {
    const result = await channelAdminApi.orders.getAll(
      selectedChannelId.value,
      {
        page: currentPage.value,
        pageSize: pageSize.value,
        sortBy: searchParams.sortBy,
        sortOrder: searchParams.sortOrder,
        startDate: searchParams.startDate,
        endDate: searchParams.endDate,
        status: searchParams.status === "all" ? undefined : searchParams.status,
      },
    );

    if (result) {
      orders.value = result.items;
      totalOrders.value = result.total;

      // 订单加载后设置价格更新轮询
      setupPricePolling();
    }
  } catch (error: unknown) {
    console.error("加载订单列表失败", error);
    orders.value = [];
    totalOrders.value = 0;

    // 检查是否是数据库配置错误
    const errorMessage = error instanceof Error ? error.message : String(error);

    // 解析响应错误，使用类型断言做更精确的类型检查
    type ApiErrorResponse = {
      response?: {
        data?: {
          message?: string;
        };
      };
    };

    const apiError = error as ApiErrorResponse;
    const responseError = apiError.response?.data?.message;

    if (
      responseError &&
      (responseError.includes("未找到通道") ||
        responseError.includes("数据库配置"))
    ) {
      showNoConfigWarning.value = true;
    } else {
      ElMessage.error(`加载订单列表失败：${responseError || errorMessage}`);
    }
  } finally {
    ordersLoading.value = false;
  }
};

// 加载订单汇总信息
const loadOrderSummary = async () => {
  if (!selectedChannelId.value) {
    return;
  }

  summaryLoading.value = true;
  try {
    const data = await channelAdminApi.orders.getSummary(
      selectedChannelId.value,
    );
    if (data) {
      orderSummary.totalOrders = data.totalOrders;
      orderSummary.completedOrders = data.completedOrders;
      orderSummary.totalNotional = data.totalNotional;
      orderSummary.totalPremium = data.totalPremium;
    }
  } catch (error: unknown) {
    console.error("加载订单汇总信息失败", error);
    // 重置统计数据
    orderSummary.totalOrders = 0;
    orderSummary.completedOrders = 0;
    orderSummary.totalNotional = 0;
    orderSummary.totalPremium = 0;
  } finally {
    summaryLoading.value = false;
  }
};

// 查看订单详情
const viewOrderDetails = (order: OrderData) => {
  selectedOrder.value = order;
  dialogVisible.value = true;
};

// 分页事件处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  loadOrders();
};

// 每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1; // 重置为第一页
  loadOrders();
};

// 通道变化
const handleChannelChange = () => {
  const channel = channels.value.find(
    (c) => c.channel_id === selectedChannelId.value,
  );
  if (channel) {
    selectedChannelName.value = channel.name;
  }

  currentPage.value = 1; // 重置为第一页
  loadOrders();
  loadOrderSummary();
};

// 日期范围变化
const handleDateChange = () => {
  if (dateRange.value) {
    searchParams.startDate = dateRange.value[0].toISOString().split("T")[0];
    searchParams.endDate = dateRange.value[1].toISOString().split("T")[0];
  } else {
    searchParams.startDate = "";
    searchParams.endDate = "";
  }

  currentPage.value = 1; // 重置为第一页
  loadOrders();
};

// 状态筛选变化
const handleStatusChange = () => {
  currentPage.value = 1; // 重置为第一页
  loadOrders();
};

// 排序变化处理
const handleSortChange = ({
  prop,
  order,
}: { prop?: string; order?: string }) => {
  searchParams.sortBy = prop || "created_at";
  searchParams.sortOrder = order === "ascending" ? "ASC" : "DESC";
  currentPage.value = 1; // 重置为第一页
  loadOrders();
};

// 检查通道连接状态
const checkChannelStatus = async () => {
  statusLoading.value = true;
  try {
    const result = await channelAdminApi.channel.getStatus();
    if (result) {
      channelStatus.value = result;
    } else {
      channelStatus.value = [];
    }
  } catch (error) {
    console.error("获取通道状态失败", error);
    ElMessage.error("获取通道状态失败");
    channelStatus.value = [];
  } finally {
    statusLoading.value = false;
  }
};

// 在模态框打开时自动检查状态
watch(showStatusModal, (newVal) => {
  if (newVal) {
    checkChannelStatus();
  }
});

// 格式化交易方显示名称
const formatProvider = (provider: string) => {
  if (provider === "INK") return siteConfigStore.shortName();
  return (
    PriceProviderNames[provider as keyof typeof PriceProviderNames] || provider
  );
};

// 组件挂载时加载数据
onMounted(() => {
  loadChannels();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  clearPricePolling();
});
</script>

<style scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-container h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.channel-selector {
  width: 200px;
}

.section-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  border-left: 3px solid var(--el-color-primary);
  padding-left: 10px;
}

.summary-section {
  margin-bottom: 20px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
}

.summary-card {
  background: var(--el-bg-color-overlay);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-icon {
  background: rgba(64, 158, 255, 0.1);
  color: var(--el-color-primary);
  border-radius: 8px;
  padding: 12px;
  margin-right: 16px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--el-text-color-primary);
}

.card-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.table-card {
  background: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  flex-wrap: wrap;
  gap: 15px;
}

.header-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-select {
  flex-grow: 1;
  width: 160px;
}

:deep(.date-range-picker) {
  flex-grow: 1;
  width: 220px;
}

.card-body {
  padding: 20px;
}

.no-config-warning {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
  display: flex;
  justify-content: center;
}

.order-details {
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-hint {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin-top: 16px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .channel-selector {
    width: 100%;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
    width: 100%;
  }

  .filter-select,
  :deep(.date-range-picker) {
    width: 100%;
    box-sizing: border-box;
  }

  .summary-section {
    margin-bottom: 10px;
  }

  .summary-cards {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}
</style>