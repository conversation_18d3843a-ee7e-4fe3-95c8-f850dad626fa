version: '3.8'

services:
  traefik:
    image: traefik:latest
    container_name: ${COMPOSE_PROJECT_NAME}-traefik # 统一命名
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro # 挂载 Docker socket (只读)
      - traefik-config:/etc/traefik # 挂载配置目录卷
      - traefik-certs:/etc/traefik/certs # 挂载证书存储卷 (acme.json 将在此目录下)
    command:
      - "--api.insecure=true" # 暂时允许不安全的 API/Dashboard 访问 (仅用于调试, 端口 8080)
      - "--providers.docker=true" # 启用 Docker Provider
      - "--providers.docker.exposedByDefault=false" # 默认不暴露服务，需要显式标签
      - "--entrypoints.web.address=:80" # 定义 HTTP 入口点
      # 本地调试下, 注释掉TLS配置
      # - "--entrypoints.websecure.address=:443" # 定义 HTTPS 入口点
      # - "--certificatesresolvers.myresolver.acme.email=<EMAIL>" # !!需要替换为你的邮箱!!
      # - "--certificatesresolvers.myresolver.acme.storage=/etc/traefik/certs/acme.json" # 指定 acme.json 存储路径
      # - "--certificatesresolvers.myresolver.acme.httpchallenge=true"
      # - "--certificatesresolvers.myresolver.acme.httpchallenge.entrypoint=websecure" # 使用 HTTP-01 验证
      # 全局 HTTP 到 HTTPS 重定向 (可选, 但推荐)
      # - "--entrypoints.web.http.redirections.entrypoint.to=websecure"
      # - "--entrypoints.web.http.redirections.entrypoint.scheme=https"
    networks:
      - ink-network

  server:
    build:
      context: .
      dockerfile: ./packages/server/Dockerfile
    container_name: ${COMPOSE_PROJECT_NAME}-server # 使用环境变量定义容器名
    restart: unless-stopped
    # 移除或注释掉 ports 映射，流量将由 Traefik 处理
    # ports:
    #   - "${SERVER_PORT_HOST:-8080}:3000"
    env_file:
      - .env # 从 .env 文件加载环境变量
    environment:
      PORT: 3000 # 容器内服务监听的端口
    labels:
      # 添加 Traefik 标签
      - "traefik.enable=true"
      # --- API 路由 (使用环境变量) ---
      # API 通过主客户端域名下的 /api 路径访问
      - "traefik.http.routers.server-api.rule=Host(`${CLIENT_DOMAIN}`) && PathPrefix(`/api`)"
      - "traefik.http.routers.server-api.entrypoints=web"
      # 本地调试下, 注释掉TLS配置, 并将websecure改为web
      # - "traefik.http.routers.server-api.tls.certresolver=myresolver"
      - "traefik.http.services.server-api.loadbalancer.server.port=3000"
      # --- 如果 API 通过单独子域名访问 (例如 api.${CLIENT_DOMAIN}) ---
      # - "traefik.http.routers.server-api.rule=Host(`api.${CLIENT_DOMAIN}`)"
    networks:
      - ink-network # 加入自定义网络

  client:
    build:
      context: .
      dockerfile: ./packages/client/Dockerfile
    container_name: ${COMPOSE_PROJECT_NAME}-client
    restart: unless-stopped
    # Remove direct port mapping when using Traefik
    # ports:
    #   - "8081:80" # 直接映射端口用于测试
    networks:
      - ink-network
    labels:
      - "traefik.enable=true"
      # --- Client 路由 (使用环境变量) ---
      - "traefik.http.routers.client.rule=Host(`${CLIENT_DOMAIN}`)"
      - "traefik.http.routers.client.entrypoints=web"
      # 本地调试下, 注释掉TLS配置, 并将websecure改为web
      # - "traefik.http.routers.client.tls.certresolver=myresolver"
      - "traefik.http.services.client.loadbalancer.server.port=80" # 指向 Client 容器内 Nginx 的端口

  admin:
    build:
      context: .
      dockerfile: ./packages/admin/Dockerfile
    container_name: ${COMPOSE_PROJECT_NAME}-admin
    restart: unless-stopped
    networks:
      - ink-network
    labels:
      - "traefik.enable=true"
      # --- Admin 路由 (使用环境变量) ---
      - "traefik.http.routers.admin.rule=Host(`${ADMIN_DOMAIN}`)"
      - "traefik.http.routers.admin.entrypoints=web"
      # 本地调试下, 注释掉TLS配置
      # - "traefik.http.routers.admin.tls.certresolver=myresolver"
      - "traefik.http.services.admin.loadbalancer.server.port=80" # 指向 Admin 容器内 Nginx 的端口

networks:
  ink-network:
    driver: bridge # 使用默认的 bridge 网络驱动

volumes:
  traefik-config:
  traefik-certs:
    # 如果需要持久化数据库和 Redis 数据，在这里添加它们的卷定义
    # postgres-data:
    # redis-data:
