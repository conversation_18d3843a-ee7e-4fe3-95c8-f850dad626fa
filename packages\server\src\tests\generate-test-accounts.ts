import bcrypt from "bcryptjs";
import crypto from "node:crypto";
import type { UpdateQualificationInfoData } from "@packages/shared";
import { AppError } from "@/core/appError.js";
import { PrismaClient } from "@prisma/client";
import type { Prisma } from "@prisma/client";
import type { UserInfo } from "@packages/shared";
import { ENV } from "@/config/configManager.js";
import { getDatabaseUrl } from "@/config/defaultParams.js";
async function generateTestAccounts(count = 20, startIndex = 1): Promise<void> {
	console.log(`Starting to generate ${count} test accounts...`);

	const defaultPassword = "test123456";
	const passwordHash = await bcrypt.hash(defaultPassword, 10);

	// Track successful operations
	const createdUsers = [];
	const qualifiedUsers = [];

	for (let i = startIndex; i < startIndex + count; i++) {
		const email = `test${i}@example.com`;
		try {
			// Create the user
			const user = await create(email, passwordHash);
			createdUsers.push(user.user_id);
			console.log(`Created user ${email} with user_id ${user.user_id}`);

			// Update qualification info
			const qualificationData: UpdateQualificationInfoData = {
				name: `Test User ${i}`,
				id_number: `3101${Math.floor(Math.random() * 1000000)
					.toString()
					.padStart(6, "0")}1234`,
				phone_number: `100${i.toString().padStart(3, "0")}`,
				bank_name: "Test Bank",
				bank_code: `TESTBANK${i}`,
				bank_account: `********${Math.floor(Math.random() * ***********).toString()}`,
			};

			await updateQualificationInfo(user.user_id, qualificationData);
			qualifiedUsers.push(user.user_id);
			console.log(`Updated qualification for user ${email}`);
		} catch (error) {
			console.error(`Error creating user ${email}:`, error);
		}
	}

	console.log("\nGeneration complete!");
	console.log(`Successfully created ${createdUsers.length} users`);
	console.log(`Successfully qualified ${qualifiedUsers.length} users`);
	console.log(
		`\nTest users have phone numbers from 100${startIndex.toString().padStart(3, "0")} to 100${(startIndex + count - 1).toString().padStart(3, "0")}`,
	);
	console.log(`Default password for all accounts: ${defaultPassword}`);
}

// Example usage
// Create 50 users starting from 100001
generateTestAccounts(20, 1)
	.then(() => {
		console.log("Script completed successfully");
		process.exit(0);
	})
	.catch((error) => {
		console.error("Script failed:", error);
		process.exit(1);
	});

const ENCRYPTION_KEY = ENV.ENCRYPTION_KEY as string;

// 确定性 (ECB) 加密非高敏感数据 (加密文本: 2N 字符)
// 低于16字节的会被填充到16字节
export function encrypt(text: string): string {
	const cipher = crypto.createCipheriv(
		"aes-256-ecb",
		Buffer.from(ENCRYPTION_KEY),
		null,
	);
	let encrypted = cipher.update(text, "utf8", "hex");
	encrypted += cipher.final("hex");
	return encrypted;
}

export function decrypt(encrypted: string): string {
	const decipher = crypto.createDecipheriv(
		"aes-256-ecb",
		Buffer.from(ENCRYPTION_KEY),
		null,
	);
	let decrypted = decipher.update(encrypted, "hex", "utf8");
	decrypted += decipher.final("utf8");
	return decrypted;
}

const prisma = new PrismaClient({
	// 冗余显式指定数据库 URL
	datasources: {
		db: {
			url: getDatabaseUrl(),
		},
	},
	log: [
		{
			emit: "event",
			level: "query",
		},
		{
			emit: "event",
			level: "error",
		},
		{
			emit: "event",
			level: "info",
		},
		{
			emit: "event",
			level: "warn",
		},
	],
});

// 业务上可能为空，保持数据库灵活性，符合最小约束原则
interface PrismaUser {
	user_id: number;
	email: string;
	password_hash: string;
	payment_password_hash: string | null;

	is_qualified: boolean | null;
	phone_number: string | null;
	balance_cny: string;
	balance_hkd: string;
	balance_usd: string;
	created_at: Date | null;
	updated_at: Date | null;
	contribution: Prisma.Decimal | null;
	name: string | null;
	id_number: string | null;
	bank_name: string | null;
	bank_code: string | null;
	bank_account: string | null;
	premium: Prisma.Decimal | null;
	deposit: Prisma.Decimal | null;
	can_transfer: boolean | null;
}

function transformUserData(prismaUser: PrismaUser): UserInfo {
	const userData = {
		...prismaUser,
		is_qualified: prismaUser.is_qualified ?? false, // 处理 null 值
		phone_number: prismaUser.phone_number ?? "",
		created_at: prismaUser.created_at ?? new Date(),
		updated_at: prismaUser.updated_at ?? new Date(),
		contribution: prismaUser.contribution?.toNumber() ?? 0,
		name: prismaUser.name ?? "",
		id_number: prismaUser.id_number ?? "",
		bank_name: prismaUser.bank_name ?? "",
		bank_code: prismaUser.bank_code ?? "",
		bank_account: prismaUser.bank_account ?? "",
		premium: prismaUser.premium?.toNumber() ?? 0,
		deposit: prismaUser.deposit?.toNumber() ?? 0,
		can_transfer: prismaUser.can_transfer ?? false,
	};

	const { password_hash, ...userInfo } = userData;
	return {
		...userInfo,
		phone_number: decrypt(userInfo.phone_number),
		balance_cny: Number.parseFloat(decrypt(userInfo.balance_cny)),
		balance_hkd: Number.parseFloat(decrypt(userInfo.balance_hkd)),
		balance_usd: Number.parseFloat(decrypt(userInfo.balance_usd)),
		email: userInfo.email ? decrypt(userInfo.email) : "",
		name: userInfo.name || "",
		id_number: userInfo.id_number ? decrypt(userInfo.id_number) : "",
		bank_account: userInfo.bank_account ? decrypt(userInfo.bank_account) : "",
		bank_name: userInfo.bank_name || "",
	};
}

export async function create(
	email: string,
	password_hash: string,
): Promise<UserInfo> {
	const user = await prisma.users.create({
		data: {
			email: encrypt(email),
			password_hash,
			balance_cny: encrypt("0"),
			balance_hkd: encrypt("0"),
			balance_usd: encrypt("0"),
		},
	});

	return transformUserData(user);
}

export async function updateQualificationInfo(
	user_id: number,
	data: UpdateQualificationInfoData,
): Promise<UserInfo> {
	const result = await prisma.users.update({
		where: { user_id },
		data: {
			is_qualified: true,
			name: data.name,
			id_number: encrypt(data.id_number),
			phone_number: encrypt(data.phone_number),
			bank_name: data.bank_name,
			bank_code: data.bank_code,
			bank_account: encrypt(data.bank_account),
			updated_at: new Date(),
		},
	});

	if (!result) {
		throw AppError.create(
			"USER_UPDATE_FAILED",
			`Failed to update qualification info for user ${user_id}`,
		);
	}

	return transformUserData(result);
}
