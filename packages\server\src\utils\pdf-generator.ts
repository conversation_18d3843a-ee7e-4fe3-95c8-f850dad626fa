import { PDFDocument } from "pdf-lib";
import type { PDFPage } from "pdf-lib";
import path from "node:path";
import fs from "node:fs/promises";

// 使用与 env.ts 相同的逻辑确定目录位置
const cwd = process.cwd();
const isInRootDir = !cwd.endsWith("server");
const PUBLIC_DIR = isInRootDir
	? path.join(cwd, "packages", "server", "public")
	: path.join(cwd, "public");

const isdaMasterAgreementTemplate = path.join(
	PUBLIC_DIR,
	"templates/ISDA主协议及附约-中英.pdf",
);
const isdaSupplementTemplate = path.join(
	PUBLIC_DIR,
	"templates/有关于2002主协议的补充协议-中英-v2.pdf",
);

// 获取印章目录下的第一个文件
async function getStampImagePath(): Promise<string> {
	const stampDir = path.join(PUBLIC_DIR, "images/stamp");
	const files = await fs.readdir(stampDir);
	if (files.length === 0) {
		throw new Error("No stamp image found in the stamp directory");
	}
	return path.join(stampDir, files[0]);
}

async function embedStamp(
	pdfDoc: PDFDocument,
	page: PDFPage,
	position: {
		x: number; // 印章中心点的 X 坐标
		y: number; // 印章中心点的 Y 坐标
	},
) {
	const stampPath = await getStampImagePath();
	const stampBytes = await fs.readFile(stampPath);
	const image = await pdfDoc.embedPng(stampBytes);

	// 固定印章参数
	const STAMP_WIDTH = 80; // 印章宽度（点），约 28.22mm
	const STAMP_OPACITY = 0.7;

	// 根据固定宽度计算等比例高度
	const stampHeight = (STAMP_WIDTH * image.height) / image.width;

	// 以中心点为基准计算左下角坐标
	const x = position.x - STAMP_WIDTH / 2;
	const y = position.y - stampHeight / 2;

	page.drawImage(image, {
		x,
		y,
		width: STAMP_WIDTH,
		height: stampHeight,
		opacity: STAMP_OPACITY,
	});
}

// 添加新的辅助函数
async function embedSignature(
	pdfDoc: PDFDocument,
	page: PDFPage,
	position: {
		x: number; // 签名中心点的 X 坐标
		y: number; // 签名中心点的 Y 坐标
	},
	signatureBase64: string,
) {
	// Handle data URL format
	const base64Data = signatureBase64.includes("base64,")
		? signatureBase64.split("base64,")[1]
		: signatureBase64;

	if (!base64Data) {
		throw new Error("Invalid signature data format");
	}

	const signatureBytes = Buffer.from(base64Data, "base64");
	const signatureImage = await pdfDoc.embedPng(signatureBytes);

	// 固定签名参数
	const SIGNATURE_WIDTH = 40;
	const signatureHeight =
		(SIGNATURE_WIDTH * signatureImage.height) / signatureImage.width;

	// 以中心点为基准计算左下角坐标
	const x = position.x - SIGNATURE_WIDTH / 2;
	const y = position.y - signatureHeight / 2;

	page.drawImage(signatureImage, {
		x,
		y,
		width: SIGNATURE_WIDTH,
		height: signatureHeight,
	});
}

export async function generateIsdaMasterAgreement(
	signature: string,
): Promise<Blob> {
	// 使用 fs 替代 fetch
	const templateBytes = await fs.readFile(isdaMasterAgreementTemplate);
	const pdfDoc = await PDFDocument.load(templateBytes);

	// Get pages 37 and 44
	const pages = pdfDoc.getPages();
	const page37 = pages[36]; // 0-based index
	const page44 = pages[43];

	// Define watermark positions
	const stampPositions = [
		{ page: page37, x: 125, y: 725 },
		{ page: page37, x: 355, y: 725 },
		{ page: page44, x: 120, y: 280 },
		{ page: page44, x: 390, y: 280 },
	];

	// Add stamps
	for (const { page, x, y } of stampPositions) {
		await embedStamp(pdfDoc, page, { x, y });
	}

	// Define signature positions for both pages
	const signaturePositions = [
		{ x: 230, y: 725, page: page37 },
		{ x: 460, y: 725, page: page37 },
		{ x: 220, y: 280, page: page44 },
		{ x: 500, y: 280, page: page44 },
	];

	// Add signatures to specified positions
	if (signature) {
		try {
			for (const { x, y, page } of signaturePositions) {
				await embedSignature(pdfDoc, page, { x, y }, signature);
			}
		} catch (error) {
			console.error("Error processing signature:", error);
			throw new Error(
				"Failed to process signature image. Please ensure it is a valid PNG image in base64 format.",
			);
		}
	}

	// 生成最终的 PDF
	const pdfBytes = await pdfDoc.save();
	return new Blob([pdfBytes], { type: "application/pdf" });
}

export async function generateIsdaSupplement(signature: string): Promise<Blob> {
	// 使用 fs 替代 fetch
	const templateBytes = await fs.readFile(isdaSupplementTemplate);
	const pdfDoc = await PDFDocument.load(templateBytes);

	// 获取第4页
	const page = pdfDoc.getPages()[3];

	// 添加印章
	await embedStamp(pdfDoc, page, {
		x: 210,
		y: 570,
	});

	// Define content using signature instead of name
	const signaturePositions = [
		{
			x: 450,
			y: 570,
		},
	];

	// Add signature to specified position
	if (signature) {
		try {
			for (const { x, y } of signaturePositions) {
				await embedSignature(pdfDoc, page, { x, y }, signature);
			}
		} catch (error) {
			console.error("Error processing signature:", error);
			throw new Error(
				"Failed to process signature image. Please ensure it is a valid PNG image in base64 format.",
			);
		}
	}

	// 生成最终的 PDF
	const pdfBytes = await pdfDoc.save();
	return new Blob([pdfBytes], { type: "application/pdf" });
}
