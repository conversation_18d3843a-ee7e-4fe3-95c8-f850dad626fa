import cron from "node-cron";
import { manageMarketStatus } from "@/financeUtils/marketTimeManager.js";
import logger from "@/utils/logger.js";
import { TRADING_PERIODS, INQUIRY_PERIODS } from "@/config/defaultParams.js";
import { broadcast } from "@/utils/notify.js";
import { WebSocketMessageType } from "@packages/shared";

export class MarketStatusCron {
	private static instance: MarketStatusCron;

	// typescript will auto privatize constructor

	public static getInstance(): MarketStatusCron {
		if (!MarketStatusCron.instance) {
			MarketStatusCron.instance = new MarketStatusCron();
		}
		return MarketStatusCron.instance;
	}

	public start(): void {
		try {
			// 合并时间点
			const uniqueTimes = new Set(
				[...TRADING_PERIODS, ...INQUIRY_PERIODS].flatMap((period) => [
					`${period.start % 100} ${Math.floor(period.start / 100)}`,
					`${period.end % 100} ${Math.floor(period.end / 100)}`,
				]),
			);

			// 创建多个独立的 cron 任务
			Array.from(uniqueTimes).map((time) =>
				cron.schedule(
					`${time} * * *`, // 正确的 cron 表达式格式
					async () => {
						await this.updateMarketStatus();
					},
					{
						timezone: "Asia/Shanghai",
					},
				),
			);

			logger.info("Market status cron job started");
		} catch (error) {
			logger.error(error, "Failed to start market status cron job");
			throw error;
		}
	}

	private async updateMarketStatus(): Promise<void> {
		try {
			const startTime = Date.now();
			await manageMarketStatus();
			const duration = Date.now() - startTime;

			logger.info(`Market status check completed in ${duration}ms`);

			await broadcast({
				type: WebSocketMessageType.SYSTEM_AVAILABLE_UPDATE,
			});
		} catch (error) {
			logger.error(error, "Error during market status update");
		}
	}
}

// 导出单例实例
export const marketStatusCron = MarketStatusCron.getInstance();
