import logger from "@/utils/logger.js";
import {
	fetchTodayExDateList,
	fetchTodayPreClose,
	fetchDailyData,
} from "./marketData.js";

interface AdjFactorChange {
	ts_code: string;
	factor: number;
}

/**
 * 获取复权因子发生变化的股票列表
 * @returns 复权因子变化列表，格式为 [{ts_code, old, new}]
 */
export async function getAdjFactorChanges(): Promise<AdjFactorChange[]> {
	try {
		// 获取今日除权除息的股票列表
		const exDateStocks = await fetchTodayExDateList();
		if (!exDateStocks?.length) {
			logger.info("No stocks have ex-dividend date today");
			return [];
		}

		const changes: AdjFactorChange[] = [];

		for (const { ts_code } of exDateStocks) {
			try {
				// 获取今日前收价和昨日收盘价
				const [todayPreClose, yesterdayClose] = await Promise.all([
					fetchTodayPreClose(ts_code as string),
					// 此时（9:15AM）获取到的是昨日的收盘价
					fetchDailyData({ ts_code: ts_code as string, limit: 1 }, [
						"ts_code",
						"trade_date",
						"close",
					]),
				]);

				if (!todayPreClose || !yesterdayClose?.[0]?.[2]) {
					logger.error(`Missing price data for ${ts_code}`);
					continue;
				}

				const preClosePrice = todayPreClose.pre_close;
				const closePrice = yesterdayClose[0][2] as number;

				if (preClosePrice !== closePrice) {
					changes.push({
						ts_code: ts_code as string,
						factor: preClosePrice / closePrice,
					});
				}
			} catch (error) {
				logger.error(error, `Error processing stock ${ts_code}`);
			}
		}

		logger.info(`Found ${changes.length} adjustment factor changes`);
		return changes;
	} catch (error) {
		logger.error(error, "Error checking adjustment factor changes");
		return [];
	}
}
