/**
 * 使用Prisma的ORM方案，连接PostgreSQL处理核心业务数据
 * 通过PgBouncer实现连接池管理，有效控制数据库连接资源
 */
import { PrismaClient } from "@prisma/client";
import logger from "@/utils/logger.js";
import { isChannel } from "@/config/configManager.js";
import {
	getDatabaseUrl,
	findUpstreamDBUrlFromConfig,
} from "@/config/defaultParams.js";
import { ENV } from "@/config/configManager.js";

// 获取上游平台数据库URL（用于通道环境）
function getPlatformDatabaseUrl(): string | undefined {
	// 尝试查找上游平台的数据库URL
	if (isChannel()) {
		return findUpstreamDBUrlFromConfig();
	}

	return undefined;
}

// 构建PgBouncer连接URL，若环境中已配置了PgBouncer则使用环境变量
function getPgBouncerUrl(originalUrl: string): string {
	// 可以通过环境变量配置PgBouncer的主机和端口
	const pgBouncerHost = ENV.PGBOUNCER_HOST || "localhost";
	const pgBouncerPort = ENV.PGBOUNCER_PORT || "6432";

	// 从原始URL解析出数据库名、用户名和密码等信息
	try {
		const url = new URL(originalUrl);
		// 修改主机和端口指向PgBouncer
		url.host = `${pgBouncerHost}:${pgBouncerPort}`;
		// 1.21.0 以下版本 PgBouncer需要添加额外的查询参数
		url.searchParams.append("pgbouncer", "true");
		return url.toString();
	} catch (error) {
		logger.warn(`无法解析数据库URL，将使用原始URL: ${error}`);
		return originalUrl;
	}
}

// 获取数据库URL，可能是直接连接或通过PgBouncer
const mainDbUrl = getDatabaseUrl(); // 获取主数据库URL，假设这个函数总是返回有效的字符串
const dbUrl =
	ENV.USE_PGBOUNCER && mainDbUrl ? getPgBouncerUrl(mainDbUrl) : mainDbUrl;

// 常规数据库连接 - 用于当前应用的数据库
const prisma = new PrismaClient({
	// 显式指定数据库 URL
	datasources: {
		db: {
			url: dbUrl,
		},
	},
	log: [
		{
			emit: "event",
			level: "query",
		},
		{
			emit: "event",
			level: "error",
		},
		{
			emit: "event",
			level: "info",
		},
		{
			emit: "event",
			level: "warn",
		},
	],
});

// 获取平台数据库URL
let platformDbUrl = getPlatformDatabaseUrl();
// 如果使用PgBouncer并且platformDbUrl存在，则转换URL
if (ENV.USE_PGBOUNCER && platformDbUrl) {
	platformDbUrl = getPgBouncerUrl(platformDbUrl);
}

// 仅在通道环境下创建交易台数据库连接
const platformPrisma =
	isChannel() && platformDbUrl
		? new PrismaClient({
				datasources: {
					db: {
						url: platformDbUrl,
					},
				},
				log: [
					{
						emit: "event",
						level: "error",
					},
				],
			})
		: null;

// 添加日志监听
prisma.$on("query", (e) => {
	logger.debug(`Query: ${e.query}`);
	logger.debug(`Duration: ${e.duration}ms`);
});

prisma.$on("error", (e) => {
	logger.error(`Prisma Error: ${e.message}`);
});

prisma.$on("info", (e) => {
	logger.info(`Prisma Info: ${e.message}`);
});

prisma.$on("warn", (e) => {
	logger.warn(`Prisma Warning: ${e.message}`);
});

// 如果platformPrisma存在，添加错误日志监听
if (platformPrisma) {
	platformPrisma.$on("error", (e) => {
		logger.error(`Platform Prisma Error: ${e.message}`);
	});
}

export default prisma;

// 导出交易台数据库客户端，方便在需要时使用
export { platformPrisma };

// 模块被导入时会执行内部代码
/**
 * Test the database connection.
 * This function will execute immediately when the module is loaded.
 */
(async function testConnection() {
	try {
		const dbName = await prisma.$queryRaw`SELECT current_database()`;
		console.log("Connected to database:", dbName);
		console.log(
			"Database connection mode:",
			ENV.USE_PGBOUNCER ? "通过PgBouncer连接" : "直接连接",
		);

		// 测试交易台数据库连接
		if (platformPrisma) {
			const platformDbName =
				await platformPrisma.$queryRaw`SELECT current_database()`;
			console.log("Connected to platform database:", platformDbName);
			console.log(
				"Platform database connection mode:",
				ENV.USE_PGBOUNCER ? "通过PgBouncer连接" : "直接连接",
			);
		}
	} catch (err) {
		logger.error(err, "Database connection error");
	}
})();
