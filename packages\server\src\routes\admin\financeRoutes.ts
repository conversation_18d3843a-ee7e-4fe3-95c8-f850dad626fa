import { Router } from "express";
import * as auditService from "@/services/admin/auditService.js";
import * as userModel from "@/models/user.js";
import { type AuditStatus, AuditType, TransactionType } from "@packages/shared";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import { AppError } from "@/core/appError.js";
import * as auditModel from "@/models/audit.js";
import * as fundService from "@/services/fundService.js";
import * as SharedConfigService from "@/services/admin/sharedConfigService.js";
import { isChannel } from "@/config/configManager.js";

const router = Router();

const validateTransferAuth = async () => {
	if (!isChannel()) return;

	const sharedConfig = await SharedConfigService.getSharedConfig();
	if (!sharedConfig?.channel_management?.enable_transfer_auth) {
		throw AppError.create("TRANSFER_DISABLED", "Transfer auth is disabled");
	}
};

// Create platform deposit for user: POST /api/admin/finance/deposit
router.post(
	"/deposit",
	wrapAdminRoute<AdminDepositBody>(async (req, res) => {
		const { user_id, amount } = req.body;
		const result = await auditService.platformDeposit(user_id, amount);
		res.status(200).json(result);
	}),
);

// Get platform deposits: GET /api/admin/finance/deposits
router.get(
	"/deposits",
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder =
			(req.query.sortOrder as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";
		const status = req.query.status as AuditStatus;

		const result = await auditModel.getAll({
			page,
			pageSize,
			sortBy,
			sortOrder,
			filters: {
				operation: TransactionType.PLATFORM_DEPOSIT,
				status: status,
			},
		});

		res.status(200).json(result);
	}),
);

// Get finance audits: GET /api/admin/finance/audits
router.get(
	"/audits",
	wrapAdminRoute(async (req, res) => {
		const status = req.query.status as AuditStatus;
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder =
			(req.query.sortOrder as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";

		const result = await auditModel.getAll({
			page,
			pageSize,
			sortBy,
			sortOrder,
			filters: {
				status,
				type: AuditType.FUND,
			},
		});

		res.status(200).json(result);
	}),
);

// Process audit request: POST /api/admin/finance/audits
router.post(
	"/audits",
	wrapAdminRoute<{ audit_id: number; status: AuditStatus; comment?: string }>(
		async (req, res) => {
			const { audit_id, status, comment } = req.body;
			const admin_id = req.jwt.admin_id;

			const result = await auditService.processAudit(
				audit_id,
				admin_id,
				status,
				comment,
			);
			res.status(200).json(result);
		},
	),
);

// Get transfer auth users: GET /api/admin/finance/transfer-auth
router.get(
	"/transfer-auth",
	wrapAdminRoute(async (req, res) => {
		await validateTransferAuth();

		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder =
			(req.query.sortOrder as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";

		const result = await userModel.getTransferAuthUsers({
			page,
			pageSize,
			sortBy,
			sortOrder,
			select: ["user_id", "phone_number", "name", "can_transfer"],
		});

		res.status(200).json(result);
	}),
);

// Authorize transfer: POST /api/admin/finance/transfer-auth
router.post(
	"/transfer-auth",
	wrapAdminRoute<{ user_id: number }>(async (req, res) => {
		await validateTransferAuth();

		const { user_id } = req.body;
		const user = await userModel.findById(user_id);
		if (!user) {
			throw AppError.create("USER_NOT_FOUND", "User not found");
		}
		await userModel.updateById(user_id, { can_transfer: true });
		res.status(200).json({ message: "Transfer authorized successfully" });
	}),
);

// Revoke transfer auth: DELETE /api/admin/finance/transfer-auth/:userId
router.delete(
	"/transfer-auth/:userId",
	wrapAdminRoute(async (req, res) => {
		await validateTransferAuth();

		const userId = Number.parseInt(req.params.userId);
		const user = await userModel.findById(userId);
		if (!user) {
			throw AppError.create("USER_NOT_FOUND", "User not found");
		}
		await userModel.updateById(userId, { can_transfer: false });
		res
			.status(200)
			.json({ message: "Transfer authorization revoked successfully" });
	}),
);

// Unfreeze payment: POST /api/admin/finance/unfreeze-payment
router.post(
	"/unfreeze-payment",
	wrapAdminRoute<{ user_id: number }>(async (req, res) => {
		const { user_id } = req.body;
		const user = await userModel.findById(user_id);
		if (!user) {
			throw AppError.create("USER_NOT_FOUND", "User not found");
		}
		await fundService.unfreezePayment(user_id);
		res.status(200).json({ message: "Payment function unfrozen successfully" });
	}),
);

// Clear payment password: POST /api/admin/finance/clear-payment-password
router.post(
	"/clear-payment-password",
	wrapAdminRoute<{ user_id: number }>(async (req, res) => {
		const { user_id } = req.body;
		const user = await userModel.findById(user_id);
		if (!user) {
			throw AppError.create("USER_NOT_FOUND", "User not found");
		}
		await fundService.clearPaymentPassword(user_id);
		res.status(200).json({ message: "Payment password cleared successfully" });
	}),
);

// 获取资金流水记录: GET /api/admin/finance/transactions
router.get(
	"/transactions",
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortOrder =
			(req.query.sortOrder as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";

		// 构建过滤条件
		const filters: {
			user_id?: number;
			types?: TransactionType[];
			startDate?: string;
			endDate?: string;
		} = {};

		if (req.query.user_id) {
			filters.user_id = Number.parseInt(req.query.user_id as string);
		}

		if (req.query.types) {
			filters.types = (req.query.types as string).split(
				",",
			) as TransactionType[];
		}

		if (req.query.startDate) {
			filters.startDate = req.query.startDate as string;
		}

		if (req.query.endDate) {
			filters.endDate = req.query.endDate as string;
		}

		const result = await fundService.getHistory(
			page,
			pageSize,
			sortOrder === "DESC",
			filters,
		);

		res.status(200).json(result);
	}),
);

export default router;

interface AdminDepositBody {
	user_id: number;
	amount: number;
	comment?: string;
}
