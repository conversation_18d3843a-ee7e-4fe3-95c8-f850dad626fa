/**
 * 使用MySQL做金融数据的本地化
 */
import { createPool } from "mysql2/promise";
import { ENV } from "@/config/configManager.js";
import logger from "@/utils/logger.js";

// MySQL连接配置
const pool = createPool({
	host: ENV.MYSQL_HOST,
	port: ENV.MYSQL_PORT,
	user: ENV.MYSQL_USER,
	password: ENV.MYSQL_PASSWORD,
	database: ENV.MYSQL_DATABASE,
	waitForConnections: true,
	connectionLimit: 10,
	queueLimit: 0,
});

// 测试连接
async function testConnection() {
	try {
		const connection = await pool.getConnection();
		await connection.ping();
		connection.release();
		logger.info("MySQL connection established");
	} catch (error) {
		logger.error(error, "Failed to connect to MySQL");
		throw error;
	}
}

// 初始化表结构
async function initializeTables() {
	try {
		// 股票列表
		await pool.query(`
      CREATE TABLE IF NOT EXISTS stock_basic (
        ts_code VARCHAR(20) PRIMARY KEY,
        name VARCHAR(100),
        market VARCHAR(20),
        updated_at DATETIME
      )
    `);

		// 交易日历
		await pool.query(`
      CREATE TABLE IF NOT EXISTS trade_calendar (
        cal_date DATE PRIMARY KEY,
        is_open TINYINT,
        pretrade_date VARCHAR(10),
        updated_at DATETIME
      )
    `);

		// 涨跌停
		await pool.query(`
      CREATE TABLE IF NOT EXISTS stk_limit (
        trade_date DATE,
        ts_code VARCHAR(20),
        pre_close DECIMAL(10,2),
        up_limit DECIMAL(10,2),
        down_limit DECIMAL(10,2),
        updated_at DATETIME,
        PRIMARY KEY (trade_date, ts_code)
      )
    `);

		// 分红，除权除息
		await pool.query(`
      CREATE TABLE IF NOT EXISTS dividend (
        ex_date DATE,
        ts_code VARCHAR(20),
        updated_at DATETIME,
        PRIMARY KEY (ex_date, ts_code)
      )
    `);

		// 日线，收盘价
		await pool.query(`
      CREATE TABLE IF NOT EXISTS daily (
        trade_date DATE,
        ts_code VARCHAR(20),
        close DECIMAL(10,2),
        updated_at DATETIME,
        PRIMARY KEY (trade_date, ts_code)
      )
    `);

		// 停复牌
		await pool.query(`
      CREATE TABLE IF NOT EXISTS suspend_d (
        trade_date DATE,
        ts_code VARCHAR(20),
        suspend_type VARCHAR(10),
        updated_at DATETIME,
        PRIMARY KEY (trade_date, ts_code)
      )
    `);

		logger.info("MySQL tables initialized");
	} catch (error) {
		logger.error(error, "Failed to initialize MySQL tables");
		throw error;
	}
}

// 执行初始化
testConnection()
	.then(initializeTables)
	.catch((err) => process.exit(1));

export { pool };
