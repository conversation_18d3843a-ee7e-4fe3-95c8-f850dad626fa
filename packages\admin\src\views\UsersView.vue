<template>
  <div class="user-management-view view">
    <!-- 通道环境 - 显示多个标签页 -->
    <template v-if="isChannelPlatform">
      <div class="tab-header">
        <div class="tab-item" :class="{ active: activeTab === 'list' }" @click="navigateTo('/users/list')">
          用户列表
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'leads' }" @click="navigateTo('/users/leads')">
          意向客户
        </div>
      </div>
      <div class="tab-content">
        <UsersPanel v-if="activeTab === 'list'" />
        <LeadsPanel v-else-if="activeTab === 'leads'" />
      </div>
    </template>

    <!-- 非通道环境 - 只显示用户列表，使用标题样式 -->
    <template v-else>
      <div class="header-container">
        <h2>用户列表</h2>
      </div>
      <UsersPanel />
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import UsersPanel from "./panels/UsersPanel.vue";
import LeadsPanel from "./panels/LeadsPanel.vue";
import { AppType } from "@packages/shared";
import { useAuthStore } from "@/stores/auth";

const authStore = useAuthStore();

const route = useRoute();
const router = useRouter();
const activeTab = ref("list");
const isChannelPlatform = computed(
  () => authStore.app_type === AppType.CHANNEL,
);

// 导航到指定路由
const navigateTo = (path: string) => {
  console.log("UsersView - Navigating to:", path);
  router.push(path);
};

// 根据路由路径设置当前标签页
const updateTabFromRoute = () => {
  console.log("UsersView - Current route path:", route.path);
  if (route.path.includes("/users/leads")) {
    activeTab.value = "leads";
    console.log("UsersView - Setting tab to leads based on route");
  } else {
    // 默认为用户列表
    activeTab.value = "list";
    console.log("UsersView - Setting tab to list based on route");
  }
};

// 监听路由变化
watch(() => route.path, updateTabFromRoute, { immediate: true });

// 组件挂载时初始化标签页
onMounted(() => {
  updateTabFromRoute();
});
</script>

<style scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

h2 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.tab-header {
  display: flex;
  margin-bottom: 20px;
  gap: 20px;
}

.tab-item {
  font-size: 14px;
  color: var(--el-text-color-regular);
  height: 36px;
  line-height: 36px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s;
}

.tab-item:hover {
  color: var(--el-color-primary);
}

.tab-item.active {
  font-size: 1.5em;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--el-color-primary);
}

/* 适配移动端 */
@media (max-width: 767px) {
  .tab-item.active {
    font-size: 18px;
  }

  .tab-header {
    margin-bottom: 10px;
  }
}
</style>