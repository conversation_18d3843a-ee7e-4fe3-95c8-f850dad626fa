// src/router/index.js
import {
	createRouter,
	createWebHistory,
	type RouteRecordRaw,
} from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { ElMessage } from "element-plus";

const dashboardChildren: RouteRecordRaw[] = [
	{
		path: "",
		redirect: () => ({ path: getDefaultPage() }),
	},
	{
		path: "inquiry",
		component: () => import("@/views/InquiryView.vue"),
	},
	{
		path: "position",
		component: () => import("@/views/PositionsView.vue"),
	},
	{
		path: "order",
		component: () => import("@/views/OrderRecordView.vue"),
	},
	{
		path: "manual-order",
		component: () => import("@/views/ManualOrderView.vue"),
	},
	{
		path: "exercise",
		component: () => import("@/views/ExerciseHistoryView.vue"),
	},
	{
		path: "account",
		component: () => import("@/views/AccountView.vue"),
	},
	{
		path: "transaction",
		component: () => import("@/views/TransactionView.vue"),
	},
	{
		path: "audit",
		component: () => import("@/views/AuditView.vue"),
	},
];

const routes: RouteRecordRaw[] = [
	{
		path: "/",
		component: () => import("@/layout/DashboardLayout.vue"),
		children: dashboardChildren,
		meta: { requiresAuth: true },
	},
	{
		path: "/login",
		component: () => import("@/views/login/LoginPage.vue"),
	},
];

const router = createRouter({
	history: createWebHistory(),
	routes,
});

// 添加路由守卫
router.beforeEach(async (to, _from, next) => {
	const requiresAuth = to.matched.some((record) => record.meta.requiresAuth);
	const authStore = useAuthStore();

	if (requiresAuth && !authStore.isLoggedIn) {
		// ! 		@INFINITE_LOOP
		// ? 		路由守卫在应用初始化阶段工作，`next("/")` 会导致:
		// TODO 1. 无限循环重定向
		// *		2. 应用渲染阻塞
		// 			3. 开发工具无法加载
		next("/login");
		return;
	}

	// 登录后的资质检查，只对需要认证的路由进行检查
	if (requiresAuth && authStore.isLoggedIn) {
		// 如果用户已登录但未认证，且不在审核页面
		if (!authStore.isQualified && to.path !== "/audit") {
			ElMessage.warning("请先完成资质认证");
			next("/audit");
			return;
		}

		// 如果用户已认证且在审核页面，跳转到默认页
		if (authStore.isQualified && to.path === "/audit") {
			next(getDefaultPage());
			return;
		}
	}

	if (to.path === "/login" && authStore.isLoggedIn) {
		next(getDefaultPage());
		return;
	}

	next();
});

export default router;

export const getDefaultPage = (): string => "/inquiry";
