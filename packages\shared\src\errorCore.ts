// 1. 定义错误配置类型
type ErrorConfig = {
	code: number;
	name: string;
};

// 2. 合并的错误定义
export const ErrorType = {
	// HTTP 标准错误 (400-499)
	BAD_REQUEST: { code: 400000, name: "请求参数错误" },
	UNAUTHORIZED: { code: 401000, name: "未授权访问" },
	FORBIDDEN: { code: 403000, name: "禁止访问" },
	NOT_FOUND: { code: 404000, name: "资源不存在" },
	TOO_MANY_REQUESTS: { code: 429000, name: "请求过于频繁" },
	SERVER_ERROR: { code: 500000, name: "服务器错误，请稍后再试" },

	// 认证错误 (401xxx)
	INVALID_TOKEN: { code: 401001, name: "无效的认证令牌" },
	TOKEN_EXPIRED: { code: 401002, name: "认证已过期，请重新登录" },
	INVALID_PASSWORD: { code: 401004, name: "密码错误" },
	FAILED_LOGOUT: { code: 401005, name: "退出登录失败" },
	FAILED_VERIFY_TOKEN_STATUS: { code: 401006, name: "令牌验证失败" },

	// 业务验证错误 (400xxx)
	INVALID_CODE: { code: 400002, name: "验证码错误" },
	CODE_EXPIRED: { code: 400003, name: "验证码已过期" },
	PHONE_EXISTS: { code: 400004, name: "该手机号已被注册" },
	EMAIL_EXISTS: { code: 400013, name: "该邮箱已被注册" },
	REGISTRATION_FAILED: { code: 400005, name: "注册失败，请稍后重试" },
	RESET_FAILED: { code: 400006, name: "重置密码失败，请稍后重试" },
	INVALID_PASSWORD_FORMAT: { code: 400007, name: "密码格式错误" },
	CODE_NOT_REQUESTED: { code: 400008, name: "验证码未请求" },
	INVALID_TRANSFER_AMOUNT: { code: 400010, name: "转账金额无效" },
	INVALID_RECEIVER_INFO: { code: 400011, name: "接收人信息错误" },
	INVALID_TS_CODE: { code: 400012, name: "无效的股票代码" },
	MISSING_REQUIRED_FIELDS: { code: 400014, name: "缺少必要字段" },
	INVALID_REQUEST_DATA: { code: 400015, name: "无效的请求数据" },
	TRANSFER_DISABLED: { code: 400016, name: "转账功能已禁用" },

	// 支付密码错误 (40001xx)
	PAYMENT_PASSWORD_NOT_SET: { code: 4000101, name: "请先设置支付密码" },
	PAYMENT_PASSWORD_EXISTS: { code: 4000102, name: "支付密码已设置" },
	INVALID_PAYMENT_PASSWORD: { code: 4000103, name: "支付密码错误" },
	INVALID_PAYMENT_PASSWORD_FORMAT: {
		code: 4000104,
		name: "支付密码必须是6位数字",
	},
	PAYMENT_PASSWORD_REQUIRED: { code: 4000105, name: "支付密码不能为空" },
	PAYMENT_PASSWORD_LOCKED: { code: 4000106, name: "支付密码已被锁定" },

	// 金融数据错误 (40002xx)
	STOCK_DATA_NOT_FOUND: { code: 4000201, name: "股票数据不存在" },
	INSUFFICIENT_STOCK_DATA: { code: 4000202, name: "股票数据不足" },
	INVALID_STOCK_DATA_FORMAT: { code: 4000203, name: "股票数据格式无效" },
	STOCK_SEARCH_FAILED: { code: 4000205, name: "股票搜索失败" },

	// 订单基础错误 (40003xx)
	ORDER_NOT_FOUND: { code: 400301, name: "订单不存在" },
	ORDER_NOT_OWNED: { code: 400302, name: "订单不属于当前用户" },
	ORDER_ALREADY_PROCESSED: { code: 400303, name: "订单已处理" },
	ORDER_TIME_EXCEEDED: { code: 400304, name: "订单时间超出" },
	INVALID_ORDER_STATUS: { code: 400305, name: "无效的订单状态" },
	ORDER_ALREADY_CLOSED: { code: 400306, name: "订单已结束，无法再次操作" },

	// 交易规则错误 (40004xx)
	INVALID_ORDER_TYPE: { code: 400401, name: "无效的订单类型" },
	INVALID_TRADE_DIRECTION: { code: 400402, name: "无效的交易方向" },
	INVALID_STRATEGY: { code: 400403, name: "无效的交易策略" },
	INVALID_LIMIT_PRICE: { code: 400404, name: "限价价格无效" },
	INVALID_EXERCISE_PRICE: { code: 400405, name: "未达到行权条件" },
	MARKET_CLOSED: { code: 400406, name: "市场已关闭" },
	STRATEGY_EXECUTION_FAILED: { code: 400407, name: "交易策略执行失败" },
	TRADING_RESTRICTION_VIOLATION: {
		code: 400409,
		name: "交易限制违规，未满足T+N要求",
	},
	PRICE_VOLATILITY_EXCEEDED: {
		code: 400410,
		name: "股票价格波动超过限制，报价已失效",
	},

	// 资金限制错误 (40005xx)
	INSUFFICIENT_BALANCE: { code: 400501, name: "余额不足" },
	INSUFFICIENT_POSITION: { code: 400502, name: "持仓不足" },
	INVALID_FUND_AMOUNT: { code: 400503, name: "无效的资金金额" },
	INVALID_ORDER_AMOUNT: { code: 400504, name: "无效的订单金额" },
	INVALID_CURRENCY: { code: 400505, name: "无效的货币类型" },
	CHANNEL_LOCKED: {
		code: 400506,
		name: "通道已被锁定，需先结清欠款",
	},
	CHANNEL_BALANCE_INSUFFICIENT: {
		code: 400507,
		name: "通道余额不足",
	},

	// 规模限制错误 (40006xx)
	INVALID_SCALE: { code: 400601, name: "无效的规模" },
	STOCK_SCALE_LIMIT_EXCEEDED: { code: 400602, name: "单股持仓超出限制" },
	PENDING_ORDERS_INSUFFICIENT_SCALE: {
		code: 400603,
		name: "已有挂单待沽，可结算名本不足",
	},
	ORDER_EXECUTION_FAILED: { code: 400604, name: "订单执行失败" },

	// 管理员错误 (40007xx)
	INVALID_ADMIN_CREDENTIALS: { code: 4000701, name: "无效的管理员凭证" },
	PROTECTED_ENV_VARIABLE: { code: 4000702, name: "受保护的环境变量" },
	INVALID_ADMIN_PASSWORD: { code: 4000703, name: "无效的管理员密码" },
	ADMIN_EXISTS: { code: 4000704, name: "管理员已存在" },
	LAST_ADMIN: { code: 4000705, name: "最后一位管理员" },
	ADMIN_PERMISSION_DENIED: { code: 4000706, name: "管理员权限拒绝" },
	ADMIN_ACCOUNT_DISABLED: { code: 4000707, name: "管理员账号已禁用" },

	// 环境变量错误 (40008xx)
	FAILED_GET_ENV_VARIABLES: { code: 4000801, name: "获取环境变量失败" },
	FAILED_UPDATE_ENV_VARIABLES: { code: 4000802, name: "更新环境变量失败" },
	MODIFY_PROTECTED_VARIABLE: { code: 4000803, name: "修改受保护的变量" },

	// 询价错误 (40009xx)
	INQUIRY_SCALE_LIMIT_EXCEEDED: { code: 4000901, name: "询价规模超出限制" },
	INQUIRY_STOCK_NOT_FOUND: { code: 4000902, name: "股票询价不存在" },
	INVALID_INQUIRY_INFO: { code: 4000903, name: "询价信息错误" },
	INQUIRY_PROCESS_FAILED: { code: 4000904, name: "询价处理失败" },

	// 持仓错误 (40010xx)
	POSITION_UPDATE_FAILED: { code: 4001001, name: "持仓更新失败" },
	POSITION_FETCH_FAILED: { code: 4001002, name: "获取持仓失败" },
	POSITION_NOTIFY_FAILED: { code: 4001003, name: "持仓通知失败" },

	// 通知错误 (40011xx)
	NOTIFICATION_SEND_FAILED: { code: 4001101, name: "通知发送失败" },
	NOTIFICATION_MARK_FAILED: { code: 4001102, name: "通知标记失败" },

	// 配置错误 (40012xx)
	CONFIG_NOT_FOUND: { code: 4001201, name: "配置不存在" },
	CONFIG_VALIDATION_FAILED: { code: 4001202, name: "配置验证失败" },
	CONFIG_UPDATE_FAILED: { code: 4001203, name: "配置更新失败" },
	CONFIG_CACHE_FAILED: { code: 4001204, name: "配置缓存失败" },
	ASSET_RETRIEVAL_FAILED: { code: 4001205, name: "资源获取失败" },
	FILE_SIZE_EXCEEDED: { code: 4001206, name: "文件大小超出限制" },

	// 系统状态错误 (40013xx)
	SYSTEM_DISABLED: { code: 4001301, name: "系统总开关已关闭" },
	POSITION_ENTRY_DISABLED: { code: 4001302, name: "新增持仓功能已关闭" },
	TOTAL_SCALE_LIMIT_EXCEEDED: { code: 4001303, name: "总规模限制超出" },
	STATUS_UPDATE_FAILED: { code: 4001304, name: "系统状态更新失败" },
	INVALID_STATUS_CHANGE: { code: 4001305, name: "无效的状态变更" },
	INQUIRY_DISABLED: { code: 4001306, name: "询价功能已关闭" },

	// 报价验证错误 (40014xx)
	INVALID_QUOTE: { code: 4001401, name: "无效的报价" },
	QUOTE_EXPIRED: { code: 4001402, name: "报价已过期" },
	QUOTE_REJECTED: { code: 4001403, name: "报价已拒绝" },
	QUOTE_MISMATCH: { code: 4001404, name: "报价参数不匹配" },
	EXTERNAL_ORDER_DISABLED: { code: 4001405, name: "外部报价下单功能已禁用" },

	// 用户相关错误 (40015xx)
	UNQUALIFIED: { code: 4001501, name: "未通过资质审核" },
	USER_UPDATE_FAILED: { code: 4001502, name: "用户更新失败" },
	UPDATE_FAILED: { code: 4001504, name: "更新失败" },
	USER_BALANCE_UPDATE_FAILED: { code: 4001505, name: "用户余额更新失败" },

	// 汇率相关错误 (40016xx)
	EXCHANGE_RATE_FETCH_FAILED: { code: 4001601, name: "获取汇率失败" },
	EXCHANGE_RATE_INVALID_RESPONSE: {
		code: 4001602,
		name: "汇率接口返回无效",
	},
	EXCHANGE_RATE_MISSING: { code: 4001603, name: "缺少必需的汇率数据" },
	INVALID_EXCHANGE_CURRENCIES: { code: 4001604, name: "无效的货币兑换组合" },
	EXCHANGE_AMOUNT_TOO_SMALL: { code: 4001605, name: "换汇金额太小" },

	// 手动录单错误 (40017xx)
	MANUAL_ORDER_FAILED: { code: 4001701, name: "手动录单异常" },

	// 限流错误 (429xxx)
	REQUEST_RATE_LIMIT: { code: 429001, name: "请求过于频繁，请稍后再试" },
	LOGIN_REQUEST_RATE_LIMIT: {
		code: 429002,
		name: "登录过于频繁，请15分钟后再试",
	},
	SMS_RATE_LIMIT_IN_MINUTE: { code: 429003, name: "1分钟内短信请求过于频繁" },
	SMS_RATE_LIMIT_IN_HOUR: { code: 429004, name: "1小时内短信请求过于频繁" },
	SMS_RATE_LIMIT_IN_DAY: { code: 429005, name: "24小时内短信请求过于频繁" },

	// 数据库错误 (50001x)
	DB_TRANSACTION_FAILED: { code: 500010, name: "数据库事务失败" },
	DB_CONNECTION_FAILED: { code: 500011, name: "数据库连接失败" },
	DB_QUERY_FAILED: { code: 500012, name: "数据库查询失败" },
	DB_DEADLOCK_DETECTED: { code: 500013, name: "数据库死锁检测" },
	DB_CONSTRAINT_VIOLATION: { code: 500014, name: "数据库约束违反" },
	DB_CONNECTION_ERROR: { code: 500015, name: "数据库连接不可用" },

	// 外部服务错误 (500xxx)
	EXTERNAL_API_ERROR: { code: 500001, name: "外部服务错误" },
	INVALID_API_RESPONSE: { code: 500002, name: "无效的API响应" },
	FETCH_STOCK_LIST_FAILED: { code: 500003, name: "获取股票列表失败" },
	FETCH_STOCK_DATA_FAILED: { code: 500004, name: "获取股票数据失败" },
	FETCH_TRADE_CALENDAR_FAILED: { code: 500005, name: "获取交易日历失败" },
	FETCH_EX_DATE_LIST_FAILED: { code: 500006, name: "获取除权除息列表失败" },
	FETCH_UP_DOWN_LIMIT_FAILED: { code: 500007, name: "获取涨跌幅限制失败" },
	FETCH_CLOSE_PRICE_FAILED: { code: 500008, name: "获取收盘价失败" },

	// 找不到错误 (404xxx)
	USER_NOT_FOUND: { code: 404001, name: "用户不存在" },
	ADMIN_NOT_FOUND: { code: 404002, name: "管理员不存在" },
} as const satisfies Record<string, ErrorConfig>;

// 3. 修改类型定义
export type ErrorKey = keyof typeof ErrorType;
export type ErrorCode = (typeof ErrorType)[ErrorKey]["code"];

// 4. 修改工具函数
export function getHttpStatus(code: ErrorCode): number {
	return code >= 1e6 ? Math.floor(code / 1e4) : Math.floor(code / 1e3);
}

// 从`错误码`获取错误名
export const getErrorName = (code: ErrorCode): ErrorKey | undefined => {
	return Object.entries(ErrorType).find(
		([_, value]) => value.code === code,
	)?.[0] as ErrorKey | undefined;
};

// 从`错误`获取错误名
export const getErrorMessage = (error: unknown): string => {
	if (error && typeof error === "object" && "response" in error) {
		const apiError = error as ApiErrorResponse;
		const errorData = apiError.response?.data;

		// 先尝试从错误码获取预定义消息
		if (errorData?.code) {
			const errorKey = getErrorName(errorData.code);
			return errorKey ? ErrorType[errorKey].name : "";
		}

		// 如果没有预定义消息，尝试使用服务器返回的消息
		if (errorData?.message) {
			return errorData.message;
		}
	}

	return "未知错误";
};

// 统一的错误响应类型
export interface ErrorResponse {
	code: ErrorCode;
	message: string;
	data?: Record<string, unknown>;
}

// API 错误响应类型（用于 axios）
export interface ApiErrorResponse {
	response?: {
		data: ErrorResponse;
		status: number;
	};
}
