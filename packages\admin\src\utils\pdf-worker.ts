import * as pdfjsLib from "pdfjs-dist";

/**
 * 初始化PDF.js Worker - 使用3个可靠的CDN
 *
 * CDN方案：
 * 1. unpkg.com - 官方推荐
 * 2. jsdelivr.com - 另一常用
 *
 * 优势：
 * - 两个CDN均支持最新pdfjs-dist版本
 * - CDN服务商已正确配置.mjs文件的MIME类型
 * - 全球分发网络，提供更好的加载性能
 * - 避免了本地文件配置的复杂性
 */
export const initializePDFWorker = (): void => {
	// 如果已经设置过worker，直接返回
	if (pdfjsLib.GlobalWorkerOptions.workerSrc) {
		return;
	}

	try {
		// 方案1：jsdelivr CDN
		pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.mjs`;
		console.log("PDF.js worker已设置 (jsdelivr CDN)，版本:", pdfjsLib.version);
	} catch (error) {
		console.warn("jsdelivr CDN加载失败，尝试备用方案:", error);

		try {
			// 方案2：unpkg CDN
			pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.mjs`;
			console.log("PDF.js worker已设置 (unpkg CDN)，版本:", pdfjsLib.version);
		} catch (cdnError) {
			console.error("unpkg CDN加载失败，检查网络连接:", cdnError);
		}
	}
};

/**
 * 获取PDF.js配置选项 - 增强错误处理
 */
export const getPDFJSOptions = () => ({
	verbosity: pdfjsLib.VerbosityLevel.ERRORS, // 只显示错误级别的日志
	disableRange: false, // 启用范围请求优化
	disableStream: false, // 启用流式加载
	disableAutoFetch: false, // 启用自动获取
	// 增加超时配置
	httpHeaders: {},
	withCredentials: false,
	// 强制使用标准模式，避免兼容性问题
	standardFontDataUrl: undefined,
	useOnlyCssZoom: false,
	// 禁用一些可能导致问题的特性
	disableFontFace: false,
	useWorkerFetch: false,
});

/**
 * 检查PDF.js worker状态
 */
export const checkWorkerStatus = (): boolean => {
	return !!pdfjsLib.GlobalWorkerOptions.workerSrc;
};

/**
 * 重置PDF.js worker - 用于错误恢复
 */
export const resetPDFWorker = (): void => {
	pdfjsLib.GlobalWorkerOptions.workerSrc = "";
	initializePDFWorker();
};

// 自动初始化
initializePDFWorker();
