import { appRedis } from "@/lib/redis.js";
import logger from "@/utils/logger.js";
import * as UserModel from "@/models/user.js";
import * as OrderModel from "@/models/trade/order.js";
import * as FundModel from "@/models/fund.js";
import prisma from "@/lib/prisma.js";
import * as InquiryModel from "@/models/inquiry.js";
import type {
	UserActivity,
	DashboardStats,
	UserInfo,
	RedisMetrics,
	PostgresMetrics,
} from "@packages/shared";
import { TransactionType } from "@packages/shared";
import { ENV } from "@/config/configManager.js";

// Private helper functions
async function getActiveUsers(): Promise<UserActivity> {
	try {
		const now = Date.now();
		const oneHourAgo = now - 60 * 60 * 1000;
		const oneDayAgo = now - 24 * 60 * 60 * 1000;
		const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000;
		const oneMonthAgo = now - 30 * 24 * 60 * 60 * 1000;

		const [online, daily, weekly, monthly] = await Promise.all([
			appRedis.zcount("user:active", oneHourAgo, "+inf"), // 1小时内活跃 = 在线
			appRedis.zcount("user:active", oneDayAgo, "+inf"), // 24小时内活跃 = 日活
			appRedis.zcount("user:active", oneWeekAgo, "+inf"), // 一周内活跃 = 周活
			appRedis.zcount("user:active", oneMonthAgo, "+inf"), // 一月内活跃 = 月活
		]);

		return {
			online,
			daily,
			weekly,
			monthly,
		};
	} catch (error) {
		logger.error(error, "Error getting user activity stats");
		return { online: 0, daily: 0, weekly: 0, monthly: 0 };
	}
}

async function getRedisMetrics(): Promise<RedisMetrics> {
	const info = await appRedis.info();
	const stats = await appRedis.info("stats");

	// 解析命中和未命中次数
	const hits = Number.parseInt(stats.match(/keyspace_hits:(\d+)/)?.[1] || "0");
	const misses = Number.parseInt(
		stats.match(/keyspace_misses:(\d+)/)?.[1] || "0",
	);

	// 计算命中率
	const hitRate = hits + misses > 0 ? (hits / (hits + misses)) * 100 : 0;

	// 获取最大内存限制
	const maxMemory = Number.parseInt(info.match(/maxmemory:(\d+)/)?.[1] || "0");
	const usedMemory = Number.parseInt(
		info.match(/used_memory:(\d+)/)?.[1] || "0",
	);

	const metrics = {
		usedMemory,
		maxMemory,
		peakMemory: Number.parseInt(
			info.match(/used_memory_peak:(\d+)/)?.[1] || "0",
		),
		keyCount: Number.parseInt(info.match(/keys=(\d+)/)?.[1] || "0"),
		hitRate: Number(hitRate.toFixed(2)),
		connectedClients: Number.parseInt(
			info.match(/connected_clients:(\d+)/)?.[1] || "0",
		),
	};

	return metrics;
}

/**
 * 获取 PostgreSQL 数据库指标
 * 注意: 查询必须在事务中正确处理错误，否则：
 * 1. 查询失败会导致事务中断（错误码：25P02）
 * 2. 如果未正确捕获处理，会触发未处理的 'error' 事件
 * 3. 导致 Node.js 进程崩溃，服务中断
 */
async function getPostgresMetrics(): Promise<PostgresMetrics> {
	try {
		const [connectionsResult, dbSizeResult, latencyResult] = await Promise.all([
			prisma.$queryRaw<
				{ active_connections: number; max_connections: number }[]
			>`
				SELECT 
					count(*) as active_connections,
					(SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections
				FROM pg_stat_activity 
				WHERE state = 'active'
			`,
			prisma.$queryRaw<{ db_size: number }[]>`
				SELECT pg_database_size(current_database()) as db_size
			`,
			prisma.$queryRaw<{ avg_latency: number }[]>`
				SELECT extract(epoch from avg(now() - query_start)) * 1000 as avg_latency
				FROM pg_stat_activity 
				WHERE state = 'active' 
				AND query_start is not null
			`,
		]);

		const transactionsResult = await prisma.$queryRaw<
			{ total_transactions: string }[]
		>`
			SELECT 
				sum(xact_commit + xact_rollback) as total_transactions
			FROM pg_stat_database 
			WHERE datname = current_database()
			AND stats_reset > now() - interval '1 minute'
		`;

		const poolStatus = await prisma.$queryRaw<{ state: string }[]>`
			SELECT state FROM pg_stat_activity
		`;

		const tps = Math.round(
			Number.parseInt(transactionsResult[0]?.total_transactions || "0") / 60,
		);

		return {
			activeConnections: Number(connectionsResult[0]?.active_connections || 0),
			maxConnections: Number(connectionsResult[0]?.max_connections || 0),
			connectionUtilization:
				(Number(connectionsResult[0]?.active_connections || 0) /
					Number(connectionsResult[0]?.max_connections || 1)) *
				100,
			idleConnections: poolStatus.filter((row) => row.state === "idle").length,
			queryLatency: Number(latencyResult[0]?.avg_latency || 0),
			databaseSize: Number(dbSizeResult[0]?.db_size || 0),
			transactionsPerSecond: tps,
		};
	} catch (error) {
		logger.error(error, "Error getting postgres metrics");
		return {
			activeConnections: 0,
			maxConnections: 0,
			connectionUtilization: 0,
			idleConnections: 0,
			queryLatency: 0,
			databaseSize: 0,
			transactionsPerSecond: 0,
		};
	}
}

export async function getAllUsers(): Promise<UserInfo[]> {
	const { items } = await UserModel.getAll();
	return items;
}

export async function getDashboardStats(): Promise<DashboardStats> {
	const now = new Date();
	const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
	const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

	// 并行获取所有需要的数据
	const [
		activeUsers,
		yesterdayActiveUsers,
		todayOrders,
		yesterdayOrders,
		todayVolume,
		yesterdayVolume,
		settleVolume,
		yesterdaySettleVolume,
		// 新增数据查询
		totalUsers,
		verifiedUsers,
		totalInquiries,
		allOrders,
		completedOrders,
		totalTradingVolume,
		depositTotal,
		withdrawalTotal,
	] = await Promise.all([
		getActiveUsers(),
		appRedis.zcount("user:active", yesterday.getTime(), today.getTime()),
		OrderModel.countByDateRange(today, now),
		OrderModel.countByDateRange(yesterday, today),
		OrderModel.getVolumeByDateRange(today, now),
		OrderModel.getVolumeByDateRange(yesterday, today),
		FundModel.getSettleVolumeByDateRange(today, now),
		FundModel.getSettleVolumeByDateRange(yesterday, today),
		// 新增的Promise调用
		UserModel.getTotalCount(),
		UserModel.getVerifiedCount(),
		InquiryModel.getTotalCount(),
		OrderModel.getTotalCount(),
		OrderModel.getCompletedCount(),
		OrderModel.getTotalVolume(),
		FundModel.getTotalByType(TransactionType.DEPOSIT),
		FundModel.getTotalByType(TransactionType.WITHDRAW),
	]);

	// 计算平均订单价值
	const averageOrderValue = allOrders > 0 ? totalTradingVolume / allOrders : 0;

	// 计算利润率 (简化示例，实际计算可能更复杂)
	const profit = totalTradingVolume * 0.05; // 假设利润是交易额的5%
	const profitMargin =
		totalTradingVolume > 0 ? (profit / totalTradingVolume) * 100 : 0;

	// 计算趋势
	const calculateTrend = (current: number, previous: number): number => {
		if (previous === 0) return 0;
		return ((current - previous) / previous) * 100;
	};

	return {
		active_users: activeUsers.daily,
		active_users_trend: calculateTrend(activeUsers.daily, yesterdayActiveUsers),
		todays_orders: todayOrders,
		todays_orders_trend: calculateTrend(todayOrders, yesterdayOrders),
		trading_volume: todayVolume,
		trading_volume_trend: calculateTrend(todayVolume, yesterdayVolume),
		settle_volume: settleVolume,
		settle_volume_trend: calculateTrend(settleVolume, yesterdaySettleVolume),
		total_users: totalUsers,
		verified_users: verifiedUsers,
		verified_rate: totalUsers > 0 ? (verifiedUsers / totalUsers) * 100 : 0,
		total_inquiries: totalInquiries,
		inquiry_conversion_rate:
			totalInquiries > 0 ? (todayOrders / totalInquiries) * 100 : 0,
		total_orders: allOrders,
		completed_orders: completedOrders,
		completion_rate: allOrders > 0 ? (completedOrders / allOrders) * 100 : 0,
		total_trading_volume: totalTradingVolume,
		average_order_value: averageOrderValue,
		deposit_total: depositTotal,
		withdrawal_total: withdrawalTotal,
		profit_margin: profitMargin,
		online_users: activeUsers.online,
		daily_active_users: activeUsers.daily,
		weekly_active_users: activeUsers.weekly,
		monthly_active_users: activeUsers.monthly,
	};
}

// 打印开发环境监控日志
if (ENV.NODE_ENV === "development") {
	setInterval(async () => {
		// 获取所有指标数据
		const memoryUsage = process.memoryUsage();
		const cpuUsage = process.cpuUsage();
		const redisMetrics = await getRedisMetrics();
		const postgresMetrics = await getPostgresMetrics();

		// 格式化内存单位
		const formatBytes = (bytes: number) => {
			if (bytes === 0) return "0 B";
			const units = ["B", "KB", "MB", "GB"];
			const i = Math.floor(Math.log(bytes) / Math.log(1024));
			return `${(bytes / 1024 ** i).toFixed(2)} ${units[i]}`;
		};

		// 格式化为百分比
		const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

		console.log(`
======== 系统性能监控 ========
系统资源:
  • 内存使用:
    - RSS: ${formatBytes(memoryUsage.rss)}
    - 堆内存总量: ${formatBytes(memoryUsage.heapTotal)}
    - 已用堆内存: ${formatBytes(memoryUsage.heapUsed)}
    - 堆外内存: ${formatBytes(memoryUsage.external || 0)}
  • CPU使用:
    - 应用程序占用: ${(cpuUsage.user / 1000).toFixed(2)} ms
    - 系统调用占用: ${(cpuUsage.system / 1000).toFixed(2)} ms
  • 运行时间: ${formatUptime(process.uptime())}

PostgreSQL 数据库:
  • 连接池:
    - 活跃连接: ${postgresMetrics.activeConnections}
    - 空闲连接: ${postgresMetrics.idleConnections}
    - 连接池使用率: ${formatPercentage(postgresMetrics.connectionUtilization)}
  • 性能指标:
    - 查询延迟: ${postgresMetrics.queryLatency.toFixed(2)} ms
    - 每秒事务数: ${postgresMetrics.transactionsPerSecond}
    - 数据库大小: ${formatBytes(postgresMetrics.databaseSize)}

Redis 缓存:
  • 内存使用:
    - 当前使用: ${formatBytes(redisMetrics.usedMemory)}
    - 峰值使用: ${formatBytes(redisMetrics.peakMemory)}
    - 最大内存: ${formatBytes(redisMetrics.maxMemory)}
  • 性能指标:
    - 键总数: ${redisMetrics.keyCount}
    - 连接客户端: ${redisMetrics.connectedClients}
    - 命中率: ${formatPercentage(redisMetrics.hitRate)}
=========================
		`);
	}, 60 * 5000); // 每5分钟打印一次

	// 格式化运行时间辅助函数
	function formatUptime(seconds: number): string {
		const days = Math.floor(seconds / 86400);
		const hours = Math.floor((seconds % 86400) / 3600);
		const minutes = Math.floor((seconds % 3600) / 60);

		const parts = [];
		if (days > 0) parts.push(`${days}d`);
		if (hours > 0) parts.push(`${hours}h`);
		if (minutes > 0) parts.push(`${minutes}m`);

		return parts.join(" ") || "0m";
	}
}
