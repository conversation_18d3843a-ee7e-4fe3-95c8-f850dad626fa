import prisma, { platformPrisma } from "@/lib/prisma.js";
import type { Prisma, PrismaClient } from "@prisma/client";
import type { BankAccount } from "@packages/shared";
import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";

function toJsonValue<T>(data: T): Prisma.InputJsonValue {
	return data as Prisma.InputJsonValue;
}

/**
 * 获取银行账户信息
 * @param usePlatformDb - 是否使用平台数据库
 * @param customPrisma - 可选的自定义Prisma客户端
 */
export async function getBankAccount(
	usePlatformDb: boolean,
	customPrisma?: PrismaClient,
): Promise<BankAccount | null> {
	const prismaClient =
		customPrisma || (usePlatformDb ? platformPrisma : prisma);
	if (!prismaClient) {
		throw AppError.create("NOT_FOUND", "Database client not available");
	}

	const result = await prismaClient.bank_accounts.findFirst({
		orderBy: { config_id: "desc" },
		select: { config: true },
	});
	return result?.config as BankAccount | null;
}

/**
 * 保存银行账户信息
 * @param bankAccount - 银行账户数据
 * @param usePlatformDb - 是否使用平台数据库
 * @param admin_id - 管理员ID，非末位可选参数，不要省略
 * @param customPrisma - 可选的自定义Prisma客户端
 */
export async function saveBankAccount(
	bankAccount: BankAccount,
	usePlatformDb: boolean,
	admin_id?: number,
	customPrisma?: PrismaClient,
): Promise<void> {
	logger.info(bankAccount, "Saving bank account");
	const prismaClient =
		customPrisma || (usePlatformDb ? platformPrisma : prisma);
	if (!prismaClient) {
		throw AppError.create("NOT_FOUND", "Database client not available");
	}

	// 无论是否存在，都创建新记录（保留历史记录）
	await prismaClient.bank_accounts.create({
		data: {
			config: toJsonValue(bankAccount),
			...(admin_id ? { admin_id } : {}),
		},
	});
}

/**
 * 获取银行账户修改历史
 */
export async function getBankAccountHistory(
	page = 1,
	pageSize = 10,
	options: { sortBy?: string; sortOrder?: "ASC" | "DESC" } = {},
): Promise<{
	total: number;
	items: {
		config_id: number;
		config: BankAccount;
		created_at: Date;
		admin_id: number;
	}[];
}> {
	const { sortBy = "created_at", sortOrder = "DESC" } = options;
	const allowedSortFields = ["created_at", "config_id", "admin_id"];
	const orderBy = allowedSortFields.includes(sortBy) ? sortBy : "created_at";

	const [count, items] = await Promise.all([
		prisma.bank_accounts.count(),
		prisma.bank_accounts.findMany({
			skip: (page - 1) * pageSize,
			take: pageSize,
			orderBy: [{ [orderBy]: sortOrder.toLowerCase() }, { created_at: "desc" }],
		}),
	]);

	return {
		total: count,
		items: items as unknown as {
			config_id: number;
			config: BankAccount;
			created_at: Date;
			admin_id: number;
		}[],
	};
}
