import { ENV } from "@/config/configManager.js";
import axios from "axios";
import { coordinationRedis } from "@/lib/redis.js";
import { AppError } from "../core/appError.js";
import type { ExchangeRateResult } from "@packages/shared";

interface ExchangeRate {
	scur: string;
	tcur: string;
	ratenm: string;
	rate: string;
	update: string;
}

interface ExchangeRateResponse {
	success: string;
	result: {
		status: string;
		lists: ExchangeRate[];
	};
}

export class ExchangeRateService {
	private readonly CACHE_KEY = "exchange_rates";
	private readonly CACHE_DURATION = 25; // seconds

	private constructor() {} // Prevent new instances
	private static instance: ExchangeRateService;

	static getInstance(): ExchangeRateService {
		if (!ExchangeRateService.instance) {
			ExchangeRateService.instance = new ExchangeRateService();
		}
		return ExchangeRateService.instance;
	}

	async getCurrentRates(): Promise<ExchangeRateResult> {
		// Try to get from cache first
		const cachedData = await coordinationRedis.get(this.CACHE_KEY);

		if (cachedData) {
			const parsed = JSON.parse(cachedData);
			const updateTime = new Date(parsed.lists[0].update);
			const now = new Date();

			// Check if cache is still valid (within 25s)
			if ((now.getTime() - updateTime.getTime()) / 1000 < this.CACHE_DURATION) {
				return this.formatRates(parsed.lists);
			}
		}

		try {
			const response = await axios.get<ExchangeRateResponse>(
				"https://sapi.k780.com/",
				{
					params: {
						app: "finance.rate",
						scur: "HKD,USD",
						tcur: "CNY",
						appkey: "74476",
						sign: ENV.NOWAPI_SIGN,
					},
				},
			);

			if (response.data.success !== "1") {
				throw AppError.create(
					"EXCHANGE_RATE_FETCH_FAILED",
					"Failed to fetch exchange rates",
				);
			}

			// Cache the result
			await coordinationRedis.setex(
				this.CACHE_KEY,
				this.CACHE_DURATION,
				JSON.stringify(response.data.result),
			);

			return this.formatRates(response.data.result.lists);
		} catch (error) {
			throw AppError.create(
				"EXCHANGE_RATE_INVALID_RESPONSE",
				"Failed to fetch exchange rates",
			);
		}
	}

	private formatRates(lists: ExchangeRate[]): ExchangeRateResult {
		const rates: Partial<ExchangeRateResult> = {};

		for (const item of lists) {
			if (item.scur === "HKD") {
				rates.HKD_CNY = Number.parseFloat(item.rate);
			} else if (item.scur === "USD") {
				rates.USD_CNY = Number.parseFloat(item.rate);
			}
		}

		if (!rates.HKD_CNY || !rates.USD_CNY) {
			throw AppError.create(
				"EXCHANGE_RATE_MISSING",
				"Missing required exchange rates",
			);
		}

		return rates as ExchangeRateResult;
	}
}

export const exchangeRateService = ExchangeRateService.getInstance();
