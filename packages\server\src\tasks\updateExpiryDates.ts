import cron from "node-cron";
import { calculateEstimatedExpiryDate } from "@/financeUtils/marketTimeManager.js";
import * as OrderModel from "@/models/trade/order.js";
import * as PositionModel from "@/models/position.js";
import logger from "@/utils/logger.js";

export class ExpiryDatesCron {
	private static instance: ExpiryDatesCron;

	// typescript will auto privatize constructor

	public static getInstance(): ExpiryDatesCron {
		if (!ExpiryDatesCron.instance) {
			ExpiryDatesCron.instance = new ExpiryDatesCron();
		}
		return ExpiryDatesCron.instance;
	}

	public start(): void {
		try {
			// 每天凌晨0点执行
			cron.schedule(
				"0 0 * * *",
				async () => {
					await this.updateUnconfirmedExpiryDates();
				},
				{
					timezone: "Asia/Shanghai",
				},
			);
			logger.info("Expiry dates update cron job started");
		} catch (error) {
			logger.error(error, "Failed to start expiry dates update cron job");
			throw error;
		}
	}

	private async updateUnconfirmedExpiryDates(): Promise<void> {
		try {
			const orders = await OrderModel.findUnconfirmedExpiryDates();

			for (const order of orders) {
				const { expiryDate, isConfirmed } = await calculateEstimatedExpiryDate(
					new Date(order.created_at),
					order.term,
				);

				if (isConfirmed) {
					await OrderModel.update(order.trade_no, {
						expiry_date: expiryDate.toISOString(),
						expiry_date_confirmed: true,
					});

					await PositionModel.update(order.trade_no, {
						expiry_date: expiryDate.toISOString(),
						expiry_date_confirmed: true,
					});
				}
			}
		} catch (error) {
			logger.error(error, "Failed to update unconfirmed expiry dates");
		}
	}
}

// 导出单例实例
export const expiryDatesCron = ExpiryDatesCron.getInstance();
