import type { Job } from "bullmq";
import {
	createWorker,
	marketStatusTasksQueue,
	addRepeatedJob,
} from "./index.js";
import logger from "@/utils/logger.js";
import { manageMarketStatus } from "@/financeUtils/marketTimeManager.js";
import { broadcast } from "@/utils/notify.js";
import { WebSocketMessageType } from "@packages/shared";
import { TRADING_PERIODS, INQUIRY_PERIODS } from "@/config/defaultParams.js";

// 定义市场状态相关的作业类型
export const MARKET_STATUS_JOBS = {
	UPDATE_MARKET_STATUS: "market-update-status",
};

// 处理市场状态相关的作业
async function processMarketStatusJob(job: Job) {
	const { name } = job;

	logger.info(`Processing market status job: ${name}`);

	try {
		switch (true) {
			case name.startsWith(MARKET_STATUS_JOBS.UPDATE_MARKET_STATUS):
				await updateMarketStatus();
				break;
			default:
				logger.warn(`Unknown market status job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process market status job: ${name}`);
		throw error;
	}
}

// 更新市场状态
async function updateMarketStatus(): Promise<void> {
	try {
		const startTime = Date.now();
		await manageMarketStatus();
		const duration = Date.now() - startTime;

		logger.info(`Market status check completed in ${duration}ms`);

		await broadcast({
			type: WebSocketMessageType.SYSTEM_AVAILABLE_UPDATE,
		});
	} catch (error) {
		logger.error(error, "Error during market status update");
	}
}

// 创建Worker实例
export const marketStatusWorker = createWorker(
	marketStatusTasksQueue,
	processMarketStatusJob,
);

// 初始化定时作业调度
export async function initializeMarketStatusJobs() {
	try {
		// 合并所有交易时段和询价时段的时间点
		const uniqueTimes = new Set(
			[...TRADING_PERIODS, ...INQUIRY_PERIODS].flatMap((period) => [
				{ minute: period.start % 100, hour: Math.floor(period.start / 100) },
				{ minute: period.end % 100, hour: Math.floor(period.end / 100) },
			]),
		);

		// 为每个时间点创建一个定时任务
		for (const time of uniqueTimes) {
			await addRepeatedJob(
				marketStatusTasksQueue,
				`${MARKET_STATUS_JOBS.UPDATE_MARKET_STATUS}-${time.hour}-${time.minute}`,
				{},
				`${time.minute} ${time.hour} * * *`,
			);
		}

		logger.info("Market status jobs scheduled successfully");
	} catch (error) {
		logger.error(error, "Failed to schedule market status jobs");
		throw error;
	}
}

// 用于应用程序启动时初始化所有市场状态任务
export async function initializeOnStartup() {
	await initializeMarketStatusJobs();
	logger.info("Market status worker initialized successfully");
}
