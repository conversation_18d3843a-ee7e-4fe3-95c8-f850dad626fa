import { request } from "./request";
import type {
	SystemStatus,
	StatusHistoryItem,
	PlatformConfig,
	PlatformConfigHistory,
	UserInfo,
} from "@packages/shared";

// 配置相关 API
export const configApi = {
	// System Status API
	getSystemStatus: () =>
		request.get<SystemStatus>("/admin/config/system-status"),

	updateSystemStatus: (updates: Partial<SystemStatus>) =>
		request.post("/admin/config/system-status", updates),

	getSystemStatusHistory: (
		page: number,
		pageSize: number,
		options?: { sortBy?: string; sortOrder?: "ASC" | "DESC" },
	) =>
		request.get<{ total: number; items: StatusHistoryItem[] }>(
			"/admin/config/system-status-history",
			{
				params: { page, pageSize, ...options },
			},
		),

	// 平台配置API
	getPlatformConfig: () =>
		request.get<PlatformConfig>("/admin/config/platform"),

	updatePlatformConfig: (updates: Partial<PlatformConfig>) =>
		request.post("/admin/config/platform", updates),

	getPlatformConfigHistory: () =>
		request.get<PlatformConfigHistory[]>("/admin/config/platform-history"),

	// 用户交易参数API

	// 新增：更新用户交易参数的 API 调用
	updateUserTradeParams: (
		userId: number,
		params: Pick<
			UserInfo,
			"custom_profit_sharing_percentage" | "custom_quote_diffs"
		>,
	) =>
		request.post<{ message: string; user: UserInfo }>(
			`/admin/config/user-trade-params/${userId}`,
			params,
		),
} as const;
