import logger from "./logger.js";

interface ThrottleOptions {
	interval?: number;
}

class NotificationThrottle {
	private notificationTimers: Map<string, number> = new Map();
	private readonly defaultInterval = 60000; // 默认1分钟内不重复发送

	private cleanExpiredRecords(interval: number): void {
		const now = Date.now();
		for (const [key, timestamp] of this.notificationTimers.entries()) {
			if (now - timestamp > interval) {
				this.notificationTimers.delete(key);
			}
		}
	}

	async shouldSend(
		recipient: string,
		type: string,
		templateType: string,
		templateParams: string,
		options: ThrottleOptions = {},
	): Promise<boolean> {
		// 检查是否需要节流
		const shouldThrottle = (type: string, templateType: string) => {
			if (type === "SMS" || type === "EMAIL") {
				// 到期提醒和到期通知始终节流
				if (["EXPIRY_REMINDER", "EXPIRY_NOTIFICATION"].includes(templateType)) {
					return true;
				}

				// 检查是否是在下午3点执行的指令
				if (templateType === "EXERCISE_INSTRUCTION") {
					const now = new Date();
					const hour = now.getHours();
					// 如果是在下午3点执行的，说明是到期执行，需要节流
					return hour === 15;
				}

				return false;
			}
		};

		if (!shouldThrottle(type, templateType)) {
			return true;
		}

		const { interval = this.defaultInterval } = options;
		const key = `${recipient}:${type}:${templateType}:${templateParams}`;
		const now = Date.now();
		const lastTime = this.notificationTimers.get(key) || 0;

		// 定期清理过期记录
		this.cleanExpiredRecords(interval);

		if (now - lastTime > interval) {
			this.notificationTimers.set(key, now);
			return true;
		}

		logger.info(`Notification throttled: ${key}`);
		return false;
	}
}

export const notificationThrottle = new NotificationThrottle();
