import { request } from "./request";
import type { LeadQueryOptions } from "./types";
import type { LeadInfo } from "@packages/shared";

// 意向客户管理 API
export const leadsApi = {
	// 获取意向客户列表
	getLeads: (options?: LeadQueryOptions) =>
		request.get<{
			items: LeadInfo[];
			total: number;
		}>("/admin/leads", {
			params: {
				page: options?.page,
				pageSize: options?.pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
				...options?.filters,
			},
		}),

	// 获取意向客户详情
	getLeadById: (id: number) => request.get<LeadInfo>(`/admin/lead/${id}`),

	// 导出意向客户数据
	exportLeads: (options?: LeadQueryOptions) =>
		request.get<LeadInfo[]>("/admin/leads/export", {
			params: {
				...options?.filters,
			},
		}),
} as const;
