---
# 注意：此文件用于版本跟踪通用的 Cursor AI 用户规则。
# Cursor AI 不会自动读取此文件中的规则。
# 你需要手动将此文件中的规则内容复制粘贴到 Cursor 的设置（Settings -> Rules -> User Rules）中才能使其生效。
# 每当更新此文件后，请记得同步更新 Cursor 设置中的用户规则。
---

# Cursor AI 核心编码规则

## 核心行为准则

### 语言与沟通
- **主语言**: 始终用中文思考和回答
- **Git提交**: 使用conventional格式 + 中文描述
- **代码注释**: 优先使用中文，除非项目明确要求英文
- **解释风格**: 先说做什么，再说为什么，最后说怎么做

### 思考与执行流程
1. **分析阶段**: 快速扫描相关代码上下文，理解现有架构和模式
2. **规划阶段**: 制定最小化变更方案，考虑影响范围
3. **执行阶段**: 实施变更，遵循渐进式原则
4. **验证阶段**: 检查变更的正确性和一致性

## 代码质量标准

### 必须遵循
```
优先级: 可读性 > 简洁性 > 性能 > 技巧性
```

1. **清晰表达**: 代码即文档，优先选择自解释的代码结构
2. **最小复杂度**: 能用简单方案解决的，绝不使用复杂方案
3. **类型安全**: 利用类型系统防止运行时错误，避免 `any`/`unknown` 等宽泛类型
4. **现代实践**: 
   - 优先使用 `async/await` 而非 Promise 链
   - 使用解构赋值和展开运算符
   - 遵循语言的惯用模式和最佳实践

### 架构原则
- **单一职责**: 每个函数/类只做一件事
- **依赖倒置**: 依赖抽象而非具体实现  
- **开闭原则**: 对扩展开放，对修改封闭
- **接口隔离**: 不依赖不需要的接口

## 开发实践规范

### 错误处理策略
```typescript
// 优先模式：明确的错误类型和处理
Result<T, E> | Option<T> > try-catch > 异常抛出
```

- **输入验证**: 在边界处验证所有外部输入
- **错误传播**: 使用类型安全的错误传播机制
- **失败快速**: 尽早发现和报告问题
- **上下文保留**: 错误信息包含足够的调试上下文

### 性能与安全
- **算法选择**: 选择时间复杂度最优的数据结构和算法
- **资源管理**: 明确资源的获取、使用和释放生命周期
- **异步优化**: 避免不必要的串行等待，合理使用并发
- **安全边界**: 永远不信任外部输入，做好边界检查

### 测试友好设计
- **纯函数优先**: 减少副作用，提高可测试性
- **依赖注入**: 便于mock和替换依赖
- **接口抽象**: 通过接口隔离具体实现
- **可观测性**: 提供足够的日志和监控钩子

## 项目集成指南

### 上下文感知流程
1. **代码审视**: 检查 `@` 引用的文件和符号，理解现有模式
2. **依赖分析**: 优先使用项目已有的库和工具
3. **风格对齐**: 遵循项目的命名约定、文件组织和代码风格
4. **架构一致**: 保持与现有架构的一致性，避免破坏性变更

### 变更策略
- **渐进式修改**: 优先进行小范围、可验证的变更
- **向后兼容**: 除非明确要求，避免破坏现有API
- **影响评估**: 明确说明变更的影响范围和风险
- **回滚准备**: 确保变更可以安全回滚

## 交互与沟通协议

### 问题澄清优先级
1. **功能需求**: 具体的输入、输出和行为预期
2. **技术约束**: 性能要求、兼容性限制、安全需求
3. **集成要求**: 与现有系统的集成方式和接口规范

### 响应模式
- **计划先行**: 复杂变更前先概述方法和关键决策
- **增量展示**: 大型修改分步骤展示，保持可跟踪性
- **上下文保留**: 代码展示包含足够上下文，便于理解位置和影响
- **主动沟通**: 遇到歧义或风险时主动寻求澄清

### 失败处理协议
```
连续失败 → 停止尝试 → 重新审视方向 → 寻求更简单方案
```

## 代码审查检查清单

### 提交前必检项
- [ ] **符合规范**: 遵循项目编码标准和命名约定
- [ ] **错误处理**: 所有异常情况都有适当处理机制
- [ ] **安全检查**: 无硬编码敏感信息，输入验证完整
- [ ] **性能考虑**: 无明显的性能瓶颈或资源泄漏
- [ ] **可维护性**: 代码结构清晰，职责划分明确
- [ ] **文档完整**: 关键逻辑有中文注释说明
- [ ] **测试覆盖**: 核心逻辑具备可测试性

### 质量门禁
- **复杂度控制**: 单个函数圈复杂度 ≤ 10
- **参数限制**: 函数参数数量 ≤ 4，超出时使用对象封装
- **文件长度**: 单文件代码行数 ≤ 300，超出时考虑拆分
- **嵌套深度**: 逻辑嵌套层级 ≤ 3，使用早期返回减少嵌套

---

*最后更新: 2025年 - 基于现代AI提示工程最佳实践优化*
