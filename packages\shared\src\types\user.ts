export interface UserData {
	user_id: number;
	email: string;
	phone_number: string;
	password_hash: string;
	is_qualified: boolean;
	balance_cny: number;
	balance_hkd: number;
	balance_usd: number;
	created_at: Date;
	updated_at: Date;
	contribution: number;
	// 新增字段
	name: string;
	id_number: string;
	bank_name: string;
	bank_code: string;
	bank_account: string;
	// 防洗钱
	premium: number;
	deposit: number;
	payment_password_hash: string | null;
	can_transfer: boolean;
	// 自定义报价差额和盈利分成
	custom_quote_diffs?: Record<string, number> | null;
	custom_profit_sharing_percentage?: number | null;
}

// UserInfo type without password_hash
export type UserInfo = Omit<
	UserData,
	"password_hash" | "payment_password_hash"
>;

export interface UserFilters {
	user_id?: number;
	email?: string;
	phone_number?: string;
	name?: string;
	is_qualified?: boolean;
}
