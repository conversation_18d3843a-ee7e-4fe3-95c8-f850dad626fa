import { pool } from "@/lib/mysql.js";
import {
	fetchStockListFromAPI,
	fetchTodayExDateListFromAPI,
	fetchUpDownLimitFromAPI,
	fetchTradeCalendarRangeFromAPI,
	fetchDailyDataFromAPI,
	fetchSuspensionFromAPI,
	countDailyData,
} from "@/financeUtils/marketData.js";
import { getLastNTradingDays } from "@/financeUtils/marketTimeManager.js";
import logger from "@/utils/logger.js";
import { dateToISOString, compactToISOString } from "@/utils/dateUtils.js";

/**
 * MySQL数据同步逻辑
 * 使用ON DUPLICATE KEY UPDATE语句处理重复数据
 * 确保每个表都有适当的主键或唯一索引以防止重复
 */

// 0:01 更新股票列表（缓存6小时更新一次）
export async function syncStockList() {
	try {
		const stocks = await fetchStockListFromAPI();

		// 因为存在已下单标的，不再过滤ST和*ST股票（高风险退市预警股票）

		// 使用事务确保数据一致性
		const connection = await pool.getConnection();
		try {
			await connection.beginTransaction();

			// 清空表并重新插入数据
			await connection.query("TRUNCATE TABLE stock_basic");

			// 批量插入数据
			if (stocks.length > 0) {
				const values = stocks.map((stock) => [
					stock.ts_code,
					stock.name,
					stock.market,
					new Date(),
				]);

				const placeholders = stocks.map(() => "(?, ?, ?, ?)").join(", ");
				await connection.query(
					`INSERT INTO stock_basic (ts_code, name, market, updated_at) VALUES ${placeholders}`,
					values.flat(),
				);
			}

			await connection.commit();
			logger.info(`Stock list synced with ${stocks.length} records`);
		} catch (txError) {
			await connection.rollback();
			throw txError;
		} finally {
			connection.release();
		}
	} catch (error) {
		logger.error(error, "Failed to sync stock list");
		throw error;
	}
}

// 9:15 更新除权除息（每日）
export async function syncDividend() {
	try {
		const data = await fetchTodayExDateListFromAPI();
		if (data.length > 0) {
			const values = data.map((item) => [
				item.ts_code,
				item.ex_date,
				new Date(),
			]);

			const placeholders = data.map(() => "(?, ?, ?)").join(", ");
			await pool.query(
				`INSERT INTO dividend (ts_code, ex_date, updated_at) VALUES ${placeholders} ON DUPLICATE KEY UPDATE ts_code = VALUES(ts_code), ex_date = VALUES(ex_date), updated_at = VALUES(updated_at)`,
				values.flat(),
			);

			logger.info(`Synced ${data.length} dividend records`);
		}
	} catch (error) {
		logger.error(error, "Failed to sync dividend data");
		throw error;
	}
}

// 8:40 更新涨跌停限制（每日）
export async function syncUpDownLimit() {
	try {
		const today = new Date();
		const data = [];

		// 从今天开始，往前15天
		for (let i = 0; i <= 14; i++) {
			const date = new Date(today);
			date.setDate(date.getDate() - i);

			const batchData = await fetchUpDownLimitFromAPI({ date });
			if (batchData.length > 0) {
				data.push(...batchData);
			}
		}

		if (data.length > 0) {
			const values = data.map((item) => {
				// 确保所有数值字段都是数字类型
				return [
					compactToISOString(item.trade_date),
					item.ts_code,
					typeof item.pre_close === "number" ? Number(item.pre_close) : null,
					typeof item.up_limit === "number" ? Number(item.up_limit) : null,
					typeof item.down_limit === "number" ? Number(item.down_limit) : null,
					new Date(),
				];
			});

			// 分批处理，每批1000条
			const batchSize = 1000;
			for (let i = 0; i < values.length; i += batchSize) {
				const batch = values.slice(i, i + batchSize);
				const placeholders = batch.map(() => "(?, ?, ?, ?, ?, ?)").join(", ");
				await pool.query(
					`INSERT INTO stk_limit (trade_date, ts_code, pre_close, up_limit, down_limit, updated_at) VALUES ${placeholders} ON DUPLICATE KEY UPDATE trade_date = VALUES(trade_date), ts_code = VALUES(ts_code), pre_close = VALUES(pre_close), up_limit = VALUES(up_limit), down_limit = VALUES(down_limit), updated_at = VALUES(updated_at)`,
					batch.flat(),
				);
			}

			logger.info(
				`Synced ${data.length} up-down limit records for past 15 days`,
			);
		}
	} catch (error) {
		// 打印更详细的错误信息
		console.error("Data that caused error:", error);
		logger.error(error, "Failed to sync up-down limit");
		throw error;
	}
}

// 每日更新交易日历（提前4个月）
export async function syncTradeCalendar() {
	try {
		const startDate = new Date();
		const endDate = new Date();
		startDate.setMonth(startDate.getMonth() - 1);
		endDate.setMonth(endDate.getMonth() + 4);

		const data = await fetchTradeCalendarRangeFromAPI(startDate, endDate);
		if (data.length > 0) {
			const values = data.map((item) => [
				item.cal_date,
				item.is_open,
				item.pretrade_date,
				new Date(),
			]);

			const placeholders = data.map(() => "(?, ?, ?, ?)").join(", ");
			await pool.query(
				`INSERT INTO trade_calendar (cal_date, is_open, pretrade_date, updated_at) VALUES ${placeholders} ON DUPLICATE KEY UPDATE cal_date = VALUES(cal_date), is_open = VALUES(is_open), pretrade_date = VALUES(pretrade_date), updated_at = VALUES(updated_at)`,
				values.flat(),
			);

			logger.info(`Synced ${data.length} calendar records for next 4 months`);
		}
	} catch (error) {
		logger.error(error, "Failed to sync trade calendar");
		throw error;
	}
}

// 16:30 更新每日收盘数据（每日）
export async function syncDailyData() {
	try {
		// 获取最近五个交易日
		const tradingDays = await getLastNTradingDays(5);
		if (tradingDays.length === 0) {
			logger.warn("No trading days found for daily data sync");
			return;
		}

		// 只同步最近的交易日数据
		const latestTradingDay = tradingDays[0];
		logger.info(`Syncing daily data for ${dateToISOString(latestTradingDay)}`);

		// 从 API 获取数据
		const dailyData = await fetchDailyDataFromAPI({ date: latestTradingDay }, [
			"ts_code",
			"trade_date",
			"close",
		]);

		if (dailyData.length === 0) {
			logger.warn("No daily data returned from API");
			return;
		}

		// 插入到 MySQL
		const values = dailyData
			.map(([ts_code, trade_date, close]) => {
				// 确保 trade_date 存在
				if (!ts_code || !trade_date) {
					logger.warn(
						`Invalid daily data record: ${JSON.stringify([ts_code, trade_date, close])}`,
					);
					return null;
				}

				return [
					ts_code,
					// 将 YYYYMMDD 格式转换为 YYYY-MM-DD
					compactToISOString(trade_date),
					close,
					new Date(),
				];
			})
			.filter(Boolean); // 过滤掉无效记录

		if (values.length > 0) {
			const placeholders = values.map(() => "(?, ?, ?, ?)").join(", ");
			await pool.query(
				`INSERT INTO daily (ts_code, trade_date, close, updated_at) VALUES ${placeholders} ON DUPLICATE KEY UPDATE ts_code = VALUES(ts_code), trade_date = VALUES(trade_date), close = VALUES(close), updated_at = VALUES(updated_at)`,
				values.flat(),
			);
		}

		logger.info(`Successfully synced ${values.length} daily data records`);
	} catch (error) {
		logger.error(error, "Failed to sync daily data");
		throw error;
	}
}

// 同步最近5个交易日的停复牌数据
export async function syncSuspension() {
	try {
		// 获取最近五个交易日
		const tradingDays = await getLastNTradingDays(5);

		// 获取每个交易日的停复牌数据
		for (const date of tradingDays) {
			const data = await fetchSuspensionFromAPI({ trade_date: date });

			if (data.length > 0) {
				const values = data.map((item) => [
					compactToISOString(item.trade_date),
					item.ts_code,
					item.suspend_type,
					new Date(),
				]);

				const placeholders = data.map(() => "(?, ?, ?, ?)").join(", ");
				await pool.query(
					`INSERT INTO suspend_d (trade_date, ts_code, suspend_type, updated_at) VALUES ${placeholders} ON DUPLICATE KEY UPDATE trade_date = VALUES(trade_date), ts_code = VALUES(ts_code), suspend_type = VALUES(suspend_type), updated_at = VALUES(updated_at)`,
					values.flat(),
				);
			}
		}

		logger.info(
			`Successfully synced suspension data for last ${tradingDays.length} trading days`,
		);
	} catch (error) {
		logger.error(error, "Failed to sync suspension data");
		throw error;
	}
}

// MySQL不需要表优化函数，因为它使用ON DUPLICATE KEY UPDATE处理重复数据

// 统一的同步入口
export async function syncAllData() {
	try {
		// 检查是否有数据
		const count = await countDailyData();

		// 待添加本地调试任务

		// 可选强制同步
		if (count === 0 || process.env.FORCE_SYNC === "true") {
			await syncStockList();
			await syncDividend();
			await syncUpDownLimit();
			await syncTradeCalendar();
			await syncDailyData();
			await syncSuspension();

			logger.info(
				`Data sync completed - ${count === 0 ? "initial sync" : "forced by FORCE_SYNC"}`,
			);
		}
	} catch (error) {
		logger.error(error, "Failed to sync all data");
		throw error;
	}
}
