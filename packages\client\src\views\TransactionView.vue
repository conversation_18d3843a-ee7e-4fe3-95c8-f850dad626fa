<template>
  <div class="transaction-view view">
    <!-- 出入金板块 -->
    <div class="two-sections-row transactions-row">
      <!-- 入金板块 -->
      <div class="transaction-panel deposit card">
        <div class="card-header">
          <div class="card-title">入金操作</div>
        </div>
        <!-- 优化公司账户信息布局 -->
        <div class="account-info-grid" v-if="hasBankAccountInfo">
          <div class="info-group">
            <label>账户名：</label>
            <span class="value">{{ companyAccount.name }}</span>
          </div>
          <div class="info-group">
            <label>银行名称：</label>
            <span class="value">{{ companyAccount.bankName }}</span>
          </div>
          <div class="info-group">
            <label>银行编号：</label>
            <span class="value">{{ companyAccount.bankCode }}</span>
          </div>
          <div v-if="companyAccount.branchCode" class="info-group">
            <label>分行号：</label>
            <span class="value">{{ companyAccount.branchCode }}</span>
          </div>
          <div class="info-group">
            <label>{{ depositCurrency === Currency.HKD ? '港币账号：' : depositCurrency === Currency.USD ? '美金账号：' : '收款账户：'
            }}</label>
            <span class="value copy-enabled"
              @click="getAccountNumberForCurrency(depositCurrency) !== '暂无' && copyToClipboard(getAccountNumberForCurrency(depositCurrency))"
              :class="{ 'disabled': getAccountNumberForCurrency(depositCurrency) === '暂无' }">
              {{ getAccountNumberForCurrency(depositCurrency) }}
              <el-icon v-if="getAccountNumberForCurrency(depositCurrency) !== '暂无'">
                <CopyDocument />
              </el-icon>
            </span>
          </div>
        </div>
        <form @submit.prevent="handleDeposit">
          <div class="form-group">
            <label for="depositAmount">入金金额</label>
            <div class="amount-input-group">
              <FormattedNumberInput id="depositAmount" v-model="depositAmount" required placeholder="输入金额" />
              <el-select v-model="depositCurrency" class="currency-select" popper-class="currency-popper">
                <el-option v-for="currency in currencies" :key="currency" :label="currency" :value="currency" />
              </el-select>
            </div>
          </div>
          <button type="submit" :disabled="isSubmitting">确认入金</button>
        </form>
      </div>

      <!-- 出金板块 -->
      <div class="transaction-panel withdrawal card">
        <div class="card-header">
          <div class="card-title">出金操作</div>
        </div>
        <!-- 添加用户银行账户信息 -->
        <div class="account-info-grid">
          <div class="info-group">
            <label>账户名：</label>
            <span class="value">{{ userBankAccount?.name }}</span>
          </div>
          <div class="info-group">
            <label>银行名称：</label>
            <span class="value">{{ userBankAccount?.bank_name }}</span>
          </div>
          <div class="info-group">
            <label>银行编号：</label>
            <span class="value">{{ userBankAccount?.bank_code }}</span>
          </div>
          <div class="info-group">
            <label>银行账户：</label>
            <span class="value copy-enabled"
              @click="userBankAccount?.bank_account && copyToClipboard(userBankAccount.bank_account)">
              {{ userBankAccount?.bank_account }}
              <el-icon>
                <CopyDocument />
              </el-icon>
            </span>
          </div>
        </div>
        <form @submit.prevent="handleWithdrawal">
          <div class="form-group">
            <label for="withdrawalAmount">出金金额</label>
            <div class="amount-input-group">
              <FormattedNumberInput id="withdrawalAmount" v-model="withdrawalAmount" required placeholder="输入金额" />
              <el-select v-model="withdrawalCurrency" class="currency-select" popper-class="currency-popper">
                <el-option v-for="currency in currencies" :key="currency" :label="currency" :value="currency" />
              </el-select>
            </div>
          </div>
          <!-- 添加余额提示 -->
          <div class="balance-hint">
            可用余额：{{ formatNumber(getAvailableBalance(withdrawalCurrency)) }} {{ withdrawalCurrency }}
          </div>
          <button type="submit" :disabled="isSubmitting">确认出金</button>
        </form>
      </div>
    </div>

    <!-- 待审核记录板块 - 仅显示资金相关审核 -->
    <div v-if="hasPendingAudits" class="pending-audits card">
      <div class="card-header">
        <div class="card-title">待确认</div>
      </div>

      <div class="audit-list">
        <!-- 资金审核记录 -->
        <div v-for="audit in pendingFundAudits" :key="audit.audit_id" class="audit-item fund-audit">
          <div class="audit-type">
            {{ getOperationType(audit.operation) }}
          </div>
          <div class="audit-amount">{{ formatNumber(audit.amount) }} {{ audit.currency }}</div>
          <div class="audit-status pending">待审核</div>
          <div class="audit-date">{{ formatDate(audit.created_at) }}</div>
        </div>
      </div>
    </div>

    <!-- 添加支付密码对话框 -->
    <PaymentDialog v-model="showPaymentDialog" :has-password="hasPaymentPassword" :on-request="handleWithdrawalRequest"
      @success="handleWithdrawalSuccess" @cancel="showPaymentDialog = false" @check-password="checkPaymentPassword" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onActivated, reactive } from "vue";
import FormattedNumberInput from "@/components/FormattedNumberInput.vue";
import { ElMessage } from "element-plus";
import { auditApi, tradeApi, fundApi, paymentApi, bankAccountApi } from "@/api";
import {
	getErrorMessage,
	FundTransaction,
	Currency,
	TransactionType,
} from "@packages/shared";
import type { FundAuditData, Balances, BankAccount } from "@packages/shared";
import { useAuthStore } from "@/stores/auth";
import { CopyDocument } from "@element-plus/icons-vue";
import PaymentDialog from "@/components/PaymentDialog.vue";

// Transaction State
const withdrawalAmount = ref("");
const depositAmount = ref("");
const isSubmitting = ref(false);
const isQualified = ref(false);

// 待审核状态
const pendingFundAudits = ref<FundAuditData[]>([]);

const hasPendingAudits = computed(() => pendingFundAudits.value.length > 0);

// 添加余额相关状态
const balances = ref<Balances>({
	balance_cny: 0,
	balance_hkd: 0,
	balance_usd: 0,
});

// 获取指定货币的可用余额
const getAvailableBalance = (currency: Currency) => {
	switch (currency) {
		case Currency.CNY:
			return balances.value.balance_cny;
		case Currency.HKD:
			return balances.value.balance_hkd;
		case Currency.USD:
			return balances.value.balance_usd;
		default:
			return 0;
	}
};

// 添加支付密码相关状态
const showPaymentDialog = ref(false);
const hasPaymentPassword = ref(false);
const pendingWithdrawal = ref<{ amount: number; currency: Currency } | null>(
	null,
);

// 检查支付密码状态
const checkPaymentPassword = async () => {
	try {
		const result = await paymentApi.checkPaymentPassword();
		hasPaymentPassword.value = result?.hasPassword || false;
	} catch (error) {
		console.error("Failed to check payment password:", error);
	}
};

// Methods
const handleWithdrawal = async () => {
	if (!isQualified.value) {
		ElMessage.warning("资格审核未通过");
		return;
	}

	const amount = Number.parseFloat(withdrawalAmount.value);
	const availableBalance = getAvailableBalance(withdrawalCurrency.value);

	if (amount > availableBalance) {
		ElMessage.warning(`${withdrawalCurrency.value}余额不足`);
		return;
	}

	// 保存出金信息并显示支付密码对话框
	pendingWithdrawal.value = {
		amount,
		currency: withdrawalCurrency.value,
	};
	showPaymentDialog.value = true;
};

// 处理出金请求
const handleWithdrawalRequest = async (password: string) => {
	if (!pendingWithdrawal.value) return;

	await auditApi.createFundAudit({
		operation: FundTransaction.WITHDRAW,
		amount: pendingWithdrawal.value.amount,
		currency: pendingWithdrawal.value.currency,
		password,
	});
};

// 处理出金成功
const handleWithdrawalSuccess = async () => {
	ElMessage.success("出金申请已提交");
	withdrawalAmount.value = "";
	pendingWithdrawal.value = null;
	await initializeStatus(); // 刷新待审核列表
};

const handleDeposit = async () => {
	if (!isQualified.value) {
		ElMessage.warning("资格审核未通过");
		return;
	}

	try {
		isSubmitting.value = true;
		await auditApi.createFundAudit({
			operation: FundTransaction.DEPOSIT,
			amount: Number.parseFloat(depositAmount.value),
			currency: depositCurrency.value,
		});
		ElMessage.success("入金申请已提交");
		depositAmount.value = "";
	} catch (error) {
		console.error("Failed to create deposit audit:", error);
		ElMessage.error(`入金申请提交失败：${getErrorMessage(error)}`);
	} finally {
		isSubmitting.value = false;
	}
};

// 公司银行账户信息 - 通过API加载
const companyAccount = reactive<BankAccount>({
	name: "",
	bankName: "",
	bankCode: "",
	accountNumber: "",
	accountNumberHKD: "",
	accountNumberUSD: "",
	branchCode: "",
});

// 加载公司银行账户信息
const loadCompanyAccount = async () => {
	try {
		const data = await bankAccountApi.getBankAccount();
		if (data) {
			// 使用Object.assign更新reactive对象
			Object.assign(companyAccount, data);
		}
	} catch (error) {
		console.error("加载银行账户信息失败", error);
	}
};

// 初始化数据 - 添加银行账户加载
const initializeStatus = async () => {
	isQualified.value = useAuthStore().isQualified || false;
	try {
		const [pendingAudits, userInfo, balancesData] = await Promise.all([
			auditApi.getPendingAudits(),
			tradeApi.getUserInfo(),
			fundApi.getBalances(), // 添加余额获取
			loadCompanyAccount(), // 加载银行账户信息
		]);
		pendingFundAudits.value = pendingAudits?.fund || [];
		userBankAccount.value = {
			name: userInfo?.name || "",
			bank_name: userInfo?.bank_name || "",
			bank_code: userInfo?.bank_code || "",
			bank_account: userInfo?.bank_account || "",
		};
		balances.value = balancesData || {
			balance_cny: 0,
			balance_hkd: 0,
			balance_usd: 0,
		}; // 保存余额数据
	} catch (error) {
		ElMessage.error("获取数据失败");
		console.error("Failed to load status:", error);
	}
};

const formatDate = (date: string | Date) => {
	return new Date(date).toLocaleDateString();
};

const formatNumber = (num: number) => {
	return new Intl.NumberFormat().format(num);
};

// Initialize
onActivated(async () => {
	await initializeStatus();
});

onMounted(() => {
	isQualified.value = useAuthStore().isQualified || false;
	checkPaymentPassword();
	loadCompanyAccount(); // 加载银行账户信息
});

const currencies = [Currency.CNY, Currency.HKD, Currency.USD];
const depositCurrency = ref(Currency.CNY);
const withdrawalCurrency = ref(Currency.CNY);

const userBankAccount = ref<{
	name: string;
	bank_name: string;
	bank_code: string;
	bank_account: string;
} | null>(null);

// 复制到剪贴板功能
const copyToClipboard = async (text: string) => {
	try {
		await navigator.clipboard.writeText(text);
		ElMessage.success("已复制到剪贴板");
	} catch (err) {
		ElMessage.error("复制失败");
	}
};

// Add this function to handle operation type display
const getOperationType = (operation: Omit<TransactionType, "buy" | "sell">) => {
	const operationTypes: Record<string, string> = {
		[TransactionType.DEPOSIT]: "入金",
		[TransactionType.WITHDRAW]: "出金",
		[TransactionType.PLATFORM_DEPOSIT]: "平台入金",
		[TransactionType.EXCHANGE]: "换汇",
	};
	return operationTypes[operation as string] || operation;
};

// 根据货币获取对应的账号
const getAccountNumberForCurrency = (currency: Currency) => {
	switch (currency) {
		case Currency.HKD:
			return (
				companyAccount.accountNumberHKD ||
				companyAccount.accountNumber ||
				"暂无"
			);
		case Currency.USD:
			return (
				companyAccount.accountNumberUSD ||
				companyAccount.accountNumber ||
				"暂无"
			);
		default:
			return companyAccount.accountNumber || "暂无";
	}
};

// Check if bank account has any information to display
const hasBankAccountInfo = computed(() => {
	return !!(
		companyAccount.name ||
		companyAccount.bankName ||
		companyAccount.bankCode ||
		companyAccount.accountNumber ||
		companyAccount.accountNumberHKD ||
		companyAccount.accountNumberUSD ||
		companyAccount.branchCode
	);
});
</script>

<style>
.currency-popper .el-select-dropdown__item {
  width: 76px;
  padding: 0 20px;
}
</style>

<style scoped>
.transactions-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 12px;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.transaction-panel {
  display: flex;
  flex-direction: column;
}

.transaction-panel form {
  justify-content: center;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1; /* 让表单占据剩余空间 */
  
  button {
    margin-top: 16px;

    /* 添加移动端适配 */
    @media (max-width: 768px) {
      width: 100%;
      margin-top: 12px;
    }
  }
}

.transaction-panel.withdrawal form {
  justify-content: center; /* 竖直居中 */
}

.transaction-panel.deposit form {
  margin-top: auto; /* 当账户信息隐藏时，让表单保持在底部 */
}

.form-group label {
  display: block;
  margin-bottom: 2px;
  color: var(--el-text-color-regular);
}

.audit-list {
  padding: 20px;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    padding: 12px;
  }
}

.audit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--el-border-color);
  gap: 20px;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    flex-wrap: wrap;
    padding: 12px;
    gap: 8px;

    .audit-type {
      width: 100%;
      order: 1;
    }

    .audit-amount {
      order: 2;
    }

    .audit-status {
      order: 3;
    }

    .audit-date {
      width: 100%;
      order: 4;
      text-align: right;
    }
  }
}

.audit-item:last-child {
  border-bottom: none;
}

.audit-type {
  font-weight: 500;
}

.audit-amount {
  color: var(--el-color-primary);
}

.audit-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.audit-status.pending {
  background-color: var(--el-color-warning-light);
  color: var(--el-color-warning);
}

.audit-date {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.fund-audit {
  background-color: var(--el-color-warning-lighter);
}

.amount-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
  max-width: 300px;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    max-width: 100%;
  }
}

.currency-select {
  width: 76px;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    width: 70px;
  }
}

.account-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  padding: 20px;
  background-color: var(--el-color-info-light-9);
  border-radius: 4px;
  margin: 16px;
  width: fit-content;
  min-width: 240px;
  max-width: 100%;
  justify-self: center;
  margin-inline: auto;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    padding: 12px;
    margin: auto;
    gap: 12px;
  }
}

.info-group {
  display: grid;
  grid-template-columns: 100px 1fr;
  align-items: center;
  gap: 8px;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    grid-template-columns: 80px 1fr;
    gap: 4px;
  }
}

.info-group label {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  text-align: right;
  white-space: nowrap;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    font-size: 13px;
  }
}

.info-group .value {
  color: var(--el-text-color-primary);
  font-size: 14px;
  text-align: left;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    font-size: 13px;
  }
}

:deep(.el-select__wrapper) {
  @media (max-width: 768px) {
    gap: 0;
  }
}

.copy-enabled {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: var(--el-color-primary);
  transition: opacity 0.2s;
}

.copy-enabled:hover {
  opacity: 0.8;
}

.copy-enabled .el-icon {
  font-size: 14px;
}

.copy-enabled.disabled {
  cursor: default;
  color: var(--el-text-color-secondary);
}

/* 出金面板竖直居中 */
.transaction-panel {
  display: flex;
  flex-direction: column;
}

.transaction-panel.withdrawal form {
  flex: 1;
  justify-content: center;
}

.balance-hint {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: -8px;
  text-align: center;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    font-size: 13px;
    margin-top: 6px;
  }
}

/* 卡片标题的移动端适配 */
.card-header {

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    padding: 12px;

    .card-title {
      font-size: 16px;
    }
  }
}

/* 表单组的移动端适配 */
.form-group {
  margin-bottom: 16px;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    max-width: 100%;
    margin-bottom: 12px;

    label {
      font-size: 14px;
      margin-bottom: 6px;
    }
  }
}
</style>
