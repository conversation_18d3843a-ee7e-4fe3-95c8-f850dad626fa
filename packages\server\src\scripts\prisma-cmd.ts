// 用于执行prisma命令的脚本
import { execSync } from "node:child_process";
import { getDatabaseUrl } from "@/config/defaultParams.js";
import * as readline from "node:readline";
import type { PrismaClient as PrismaClientClass } from "@prisma/client"; // 只为了避免开发时生成后的TS报错
import { fileURLToPath } from "node:url";
import { dirname, resolve } from "node:path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const serverDir = resolve(__dirname, "../..");

/**
 * Prisma Client 生成与 Monorepo 注意事项 (基于 Prisma v6.x)
 *
 * 1. 显式指定 `output` 路径是官方推荐的最佳实践，并将于 Prisma v7 强制要求。
 *    这提供了明确性，避免了依赖 Prisma 自动检测可能带来的问题，并提高了跨环境（如 Docker）的可靠性。
 *    @see https://www.prisma.io/docs/orm/prisma-client/setup-and-configuration/generating-prisma-client#the-location-of-prisma-client
 *
 * 2. 在 pnpm monorepo 中，将 `output` 指向 monorepo 根目录的 `node_modules/.prisma/client`
 *    (`output = "../../../node_modules/.prisma/client"`) 是一个常用且有效的策略。
 *    这能很好地配合 pnpm 的依赖提升 (hoisting) 机制，简化运行时 `@prisma/client` 标准包查找生成代码的路径。
 *
 * 旧注释已移除：不再推荐依赖 Prisma 自动检测输出路径。
 */

// 检查Prisma客户端是否已生成
const checkPrismaClientGenerated = async () => {
	try {
		// 尝试动态导入，这在ESM中是正确的方式
		await import("@prisma/client");
		return true;
	} catch (error) {
		return false;
	}
};

// 生成Prisma客户端
const generatePrismaClient = () => {
	console.log("\x1b[33m检测到Prisma客户端未生成，正在自动生成...\x1b[0m");
	try {
		// 执行生成命令并获取输出
		const output = execSync("pnpx prisma generate", {
			stdio: "pipe", // 捕获输出而不是直接显示
			encoding: "utf-8",
			env: {
				...process.env,
				DATABASE_URL: getDatabaseUrl(),
			},
			cwd: serverDir,
		});

		// 处理输出
		console.log(
			output.replace(
				/Warning: You did not specify an output path for your `generator` in schema\.prisma\. This behavior is deprecated and will no longer be supported in Prisma 7\.0\.0\./g,
				"\x1b[33m警告: 未指定output路径(这是正常的，我们为了pnpm兼容性故意这样做)。这将在Prisma 7.0中不再支持。届时请更新配置。\x1b[0m",
			),
		);

		console.log("\x1b[32mPrisma客户端生成成功！\x1b[0m");
		return true;
	} catch (error) {
		console.error("\x1b[31mPrisma客户端生成失败:\x1b[0m", error);
		return false;
	}
};

// 在导入PrismaClient前确保客户端已生成
(async () => {
	// 获取命令行参数（去掉前两个: node和脚本名称）
	const args = process.argv.slice(2);
	const isGenerateCommand = args.length > 0 && args[0] === "generate";

	// 仅在执行的命令不是 'generate' 时，才检查并尝试自动生成客户端
	if (!isGenerateCommand) {
		if (!(await checkPrismaClientGenerated())) {
			const success = generatePrismaClient();
			if (!success) {
				console.error(
					"\x1b[31m无法使用Prisma命令，请手动运行 'pnpx prisma generate' 命令\x1b[0m",
				);
				process.exit(1);
			}
		}
	}

	// 准备环境变量
	const env = {
		...process.env,
		DATABASE_URL: getDatabaseUrl(),
	};

	// 检查是否为db push命令
	const isDbPushCommand =
		args.length >= 2 && args[0] === "db" && args[1] === "push";

	// 创建确认对话的函数
	const confirmDbPush = () => {
		return new Promise((resolve) => {
			const rl = readline.createInterface({
				input: process.stdin,
				output: process.stdout,
			});

			console.log(
				"\x1b[33m警告: 谨慎使用 Push 命令，以防破坏远程数据库！\x1b[0m",
			);
			rl.question("确定要继续吗? (y/n): ", (answer) => {
				rl.close();
				resolve(answer.toLowerCase() === "y");
			});
		});
	};

	// 显示数据库连接状态
	const showDatabaseStatus = async () => {
		try {
			// 动态导入PrismaClient以避免在未生成时出错
			let prismaClient: PrismaClientClass;
			try {
				// 注意这里的 await 和 import() 函数调用
				const { PrismaClient: Client } = await import("@prisma/client"); // 实际导入生成检查后的PrismaClient
				prismaClient = new Client({
					datasources: {
						db: {
							url: getDatabaseUrl(),
						},
					},
				});
			} catch (error) {
				console.error("无法导入 PrismaClient...", error);
				return;
			}

			console.log("\n\x1b[36m数据库连接信息:\x1b[0m");

			// 测试连接并获取一些基本信息
			try {
				const startTime = Date.now();
				await prismaClient.$queryRaw`SELECT 1`;
				const responseTime = Date.now() - startTime;

				// 提取和显示数据库URL中的基本信息（隐藏密码）
				const dbUrl = getDatabaseUrl() || "";
				const urlParts = dbUrl.split("@");
				let connectionInfo = "未知";

				if (urlParts.length > 1) {
					// 提取主机和数据库名称
					const hostPart = urlParts[1].split("/");
					const dbName = hostPart[1]?.split("?")[0] || "未知";
					connectionInfo = `${hostPart[0]} / ${dbName}`;
				}

				console.log(`\x1b[32m✓ 连接成功\x1b[0m (响应时间: ${responseTime}ms)`);
				console.log(`数据库: ${connectionInfo}`);
			} catch (e) {
				console.log("\x1b[31m✗ 连接失败\x1b[0m");
				console.log(`错误: ${e instanceof Error ? e.message : String(e)}`);
			}

			await prismaClient.$disconnect();
		} catch (e) {
			console.log("\x1b[31m无法初始化数据库客户端\x1b[0m");
		}
	};

	// 显示帮助信息
	const showHelp = async () => {
		console.log(`
\x1b[1mPrisma 数据库工具\x1b[0m

用法:
  pnpm prisma <命令> [选项]

常用命令:
  \x1b[33mdb pull\x1b[0m       从数据库提取 schema 到 Prisma schema
  \x1b[33mgenerate\x1b[0m      根据 schema 生成客户端代码
  \x1b[33mstudio\x1b[0m        启动 Prisma Studio 界面
  \x1b[33mmigrate\x1b[0m       管理数据库迁移

示例:
  pnpm prisma -h   # 显示命令提示
  `);

		// 显示数据库连接状态
		await showDatabaseStatus();
	};

	const executeCommand = async () => {
		try {
			// 如果没有参数，显示帮助信息
			if (args.length === 0) {
				await showHelp();
				return;
			}

			// 如果是db push命令，则需要确认
			if (isDbPushCommand) {
				const confirmed = await confirmDbPush();
				if (!confirmed) {
					console.log("操作已取消");
					process.exit(0);
				}
			}

			// 使用execSync直接执行prisma命令，不要调用pnpm prisma以避免递归
			const command = `pnpx prisma ${args.join(" ")}`;
			console.log("Running command:", command);

			// 执行命令并将输出传递到当前进程
			execSync(command, {
				env,
				stdio: "inherit",
				cwd: serverDir,
			});
		} catch (error) {
			console.error("Failed to execute Prisma command:", error);
			process.exit(1);
		}
	};

	// 执行命令
	await executeCommand();
})();
