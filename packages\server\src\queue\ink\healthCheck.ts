#!/usr/bin/env tsx

/**
 * 运行命令：
 * pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\queue\ink\healthCheck.ts
 */

import { getChildLogger } from "@/utils/logger.js";
import { inkSyncConfig } from "./config.js";
import { lockManager } from "./distributedLockManager.js";
import { retryHandler } from "./retryHandler.js";
import { redisManager } from "./redisManager.js";
import { metricsCollector } from "./metricsCollector.js";
import { fileURLToPath } from "node:url";

/**
 * 直接运行时的注意事项：
 * 1. 确保 Redis 服务已启动且配置正确
 * 2. 运行命令：
 * pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\queue\ink\healthCheck.ts
 */

const logger = getChildLogger("InkHealthCheck");

/**
 * INK数据同步系统健康检查
 * 验证所有优化模块是否正常工作
 */
export class InkHealthChecker {
  /**
   * 执行完整的健康检查
   */
  async performHealthCheck(): Promise<{
    overall: 'healthy' | 'warning' | 'critical';
    modules: Record<string, any>;
    timestamp: number;
  }> {
    logger.info("Starting INK data sync system health check");
    
    const results = {
      overall: 'healthy' as 'healthy' | 'warning' | 'critical',
      modules: {} as Record<string, any>,
      timestamp: Date.now()
    };

    try {
      // 1. 配置模块检查
      results.modules.config = await this.checkConfigModule();
      
      // 2. Redis管理器检查
      results.modules.redis = await this.checkRedisManager();
      
      // 3. 分布式锁管理器检查
      results.modules.lockManager = await this.checkLockManager();
      
      // 4. 重试处理器检查
      results.modules.retryHandler = await this.checkRetryHandler();
      
      // 5. 指标收集器检查
      results.modules.metricsCollector = await this.checkMetricsCollector();

      // 计算整体健康状态
      const moduleStatuses = Object.values(results.modules).map(m => m.status);
      if (moduleStatuses.some(s => s === 'critical')) {
        results.overall = 'critical';
      } else if (moduleStatuses.some(s => s === 'warning')) {
        results.overall = 'warning';
      }

      logger.info(`Health check completed: ${results.overall}`, {
        moduleCount: Object.keys(results.modules).length,
        healthyModules: moduleStatuses.filter(s => s === 'healthy').length
      });

      return results;
    } catch (error) {
      logger.error(error, "Health check failed");
      results.overall = 'critical';
      results.modules.error = {
        status: 'critical',
        error: (error as Error).message
      };
      return results;
    }
  }

  /**
   * 检查配置模块
   */
  private async checkConfigModule(): Promise<any> {
    try {
      const configSummary = inkSyncConfig.getConfigSummary();
      
      // 验证关键配置项
      const requiredConfigs = [
        'lockExpiry', 'tradingStartTime', 'tradingEndTime',
        'highFreqUpdateInterval', 'maxRetryAttempts'
      ];
      
      const missingConfigs = requiredConfigs.filter(key => 
        configSummary[key] === undefined || configSummary[key] === null
      );

      if (missingConfigs.length > 0) {
        return {
          status: 'warning',
          message: `Missing configurations: ${missingConfigs.join(', ')}`,
          config: configSummary
        };
      }

      return {
        status: 'healthy',
        message: 'Configuration module working correctly',
        config: configSummary
      };
    } catch (error) {
      return {
        status: 'critical',
        message: 'Configuration module failed',
        error: (error as Error).message
      };
    }
  }

  /**
   * 检查Redis管理器
   */
  private async checkRedisManager(): Promise<any> {
    try {
      // 健康检查
      const healthResult = await redisManager.healthCheck();
      
      if (!healthResult.healthy) {
        return {
          status: 'critical',
          message: 'Redis connection unhealthy',
          latency: healthResult.latency,
          error: healthResult.error
        };
      }

      // 缓存统计
      const cacheStats = await redisManager.getCacheStats();
      
      // 测试基本操作
      const testKey = 'health-check-test';
      const testData = { timestamp: Date.now(), test: true };
      
      await redisManager.setSwingData({ [testKey]: Date.now() });
      const retrievedData = await redisManager.getSwingData();
      
      if (!retrievedData || !retrievedData[testKey]) {
        return {
          status: 'warning',
          message: 'Redis operations partially working',
          latency: healthResult.latency,
          cacheStats
        };
      }

      return {
        status: 'healthy',
        message: 'Redis manager working correctly',
        latency: healthResult.latency,
        cacheStats
      };
    } catch (error) {
      return {
        status: 'critical',
        message: 'Redis manager failed',
        error: (error as Error).message
      };
    }
  }

  /**
   * 检查分布式锁管理器
   */
  private async checkLockManager(): Promise<any> {
    try {
      const testLockKey = 'health-check-lock';
      
      // 测试锁获取和释放
      const lockAcquired = await lockManager.acquireLockWithRenewal(testLockKey, 30);
      
      if (!lockAcquired) {
        return {
          status: 'warning',
          message: 'Lock acquisition failed (may be normal if lock is held)',
          activeLocks: lockManager.getActiveLockCount()
        };
      }

      // 检查锁是否被当前实例持有
      const isHeld = await lockManager.isLockHeldByCurrentInstance(testLockKey);
      
      if (!isHeld) {
        return {
          status: 'warning',
          message: 'Lock ownership verification failed',
          activeLocks: lockManager.getActiveLockCount()
        };
      }

      // 释放锁
      await lockManager.releaseLock(testLockKey);

      return {
        status: 'healthy',
        message: 'Lock manager working correctly',
        activeLocks: lockManager.getActiveLockCount()
      };
    } catch (error) {
      return {
        status: 'critical',
        message: 'Lock manager failed',
        error: (error as Error).message
      };
    }
  }

  /**
   * 检查重试处理器
   */
  private async checkRetryHandler(): Promise<any> {
    try {
      let attemptCount = 0;
      
      // 测试成功的重试
      const successResult = await retryHandler.executeWithRetry(async () => {
        attemptCount++;
        return 'success';
      }, { maxAttempts: 3 });

      if (successResult !== 'success' || attemptCount !== 1) {
        return {
          status: 'warning',
          message: 'Retry handler behavior unexpected',
          attemptCount
        };
      }

      // 测试失败重试 - 使用自定义重试条件
      attemptCount = 0;
      try {
        await retryHandler.executeWithRetry(async () => {
          attemptCount++;
          if (attemptCount < 3) {
            throw new Error('Test error');
          }
          return 'success after retries';
        }, { 
          maxAttempts: 3,
          retryCondition: (error: Error) => error.message === 'Test error' // 自定义重试条件
        });
      } catch (error) {
        // 预期的错误
      }

      if (attemptCount !== 3) {
        return {
          status: 'warning',
          message: 'Retry mechanism not working as expected',
          attemptCount,
          expectedAttempts: 3
        };
      }

      // 检查断路器状态
      const circuitBreakerStatus = retryHandler.getCircuitBreakerStatus();

      return {
        status: 'healthy',
        message: 'Retry handler working correctly',
        circuitBreakers: Object.keys(circuitBreakerStatus).length
      };
    } catch (error) {
      return {
        status: 'critical',
        message: 'Retry handler failed',
        error: (error as Error).message
      };
    }
  }

  /**
   * 检查指标收集器
   */
  private async checkMetricsCollector(): Promise<any> {
    try {
      // 记录测试作业
      const jobId = metricsCollector.recordJobStart('health-check-test');
      metricsCollector.recordJobComplete('health-check-test', true);
      
      // 记录缓存操作
      metricsCollector.recordCacheHit();
      metricsCollector.recordCacheMiss();
      metricsCollector.recordLockAcquisition();

      // 获取当前指标
      const metrics = await metricsCollector.getCurrentMetrics();
      
      if (!metrics || !metrics.timestamp) {
        return {
          status: 'warning',
          message: 'Metrics collection incomplete'
        };
      }

      // 获取作业历史
      const jobHistory = metricsCollector.getJobHistory('health-check-test', 1);
      
      if (jobHistory.length === 0) {
        return {
          status: 'warning',
          message: 'Job history not being recorded properly'
        };
      }

      // 生成健康报告
      const healthReport = await metricsCollector.generateHealthReport();

      return {
        status: 'healthy',
        message: 'Metrics collector working correctly',
        metricsTimestamp: metrics.timestamp,
        jobHistoryCount: jobHistory.length,
        overallHealth: healthReport.overall
      };
    } catch (error) {
      return {
        status: 'critical',
        message: 'Metrics collector failed',
        error: (error as Error).message
      };
    }
  }

  /**
   * 生成健康检查报告
   */
  async generateReport(): Promise<string> {
    const healthCheck = await this.performHealthCheck();
    
    const report = [
      '=== INK数据同步系统健康检查报告 ===',
      `检查时间: ${new Date(healthCheck.timestamp).toLocaleString('zh-CN')}`,
      `整体状态: ${healthCheck.overall.toUpperCase()}`,
      '',
      '模块状态详情:',
    ];

    for (const [moduleName, moduleResult] of Object.entries(healthCheck.modules)) {
      report.push(`  ${moduleName}: ${moduleResult.status.toUpperCase()}`);
      report.push(`    消息: ${moduleResult.message}`);
      if (moduleResult.error) {
        report.push(`    错误: ${moduleResult.error}`);
      }
      report.push('');
    }

    if (healthCheck.overall !== 'healthy') {
      report.push('建议操作:');
      if (healthCheck.overall === 'critical') {
        report.push('  - 立即检查系统日志');
        report.push('  - 验证Redis连接状态');
        report.push('  - 重启相关服务');
      } else {
        report.push('  - 监控系统状态');
        report.push('  - 检查配置参数');
        report.push('  - 考虑优化性能');
      }
    }

    return report.join('\n');
  }
}

// 导出单例实例
export const inkHealthChecker = new InkHealthChecker();

// 快速健康检查函数
export async function quickHealthCheck(): Promise<boolean> {
  try {
    const result = await inkHealthChecker.performHealthCheck();
    return result.overall !== 'critical';
  } catch (error) {
    logger.error(error, "Quick health check failed");
    return false;
  }
}

// 如果直接运行此脚本
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  inkHealthChecker.generateReport()
    .then(report => {
      console.log(report);
      process.exit(0);
    })
    .catch(error => {
      console.error("健康检查失败:", error);
      process.exit(1);
    });
} 