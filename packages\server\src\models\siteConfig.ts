import prisma from "@/lib/prisma.js";
import type { Prisma } from "@prisma/client";
import type { SiteConfig } from "@packages/shared";
import logger from "@/utils/logger.js";

function toJsonValue<T>(data: T): Prisma.InputJsonValue {
	return data as Prisma.InputJsonValue;
}

/**
 * 获取网站配置信息
 */
export async function getSiteConfig(): Promise<SiteConfig | null> {
	const result = await prisma.site_configs.findFirst({
		orderBy: { config_id: "desc" },
		select: { config: true },
	});
	return result?.config as SiteConfig | null;
}

/**
 * 保存网站配置信息
 * @param siteConfig - 网站配置数据
 * @param admin_id - 管理员ID，非末位可选参数，不要省略
 */
export async function saveSiteConfig(
	siteConfig: SiteConfig,
	admin_id?: number,
): Promise<void> {
	logger.info(siteConfig, "Saving site configuration");

	// 创建新记录以保留历史记录
	await prisma.site_configs.create({
		data: {
			config: toJsonValue(siteConfig),
			...(admin_id ? { admin_id } : {}),
		},
	});
}

/**
 * 获取网站配置修改历史
 */
export async function getSiteConfigHistory(
	page = 1,
	pageSize = 10,
	options: { sortBy?: string; sortOrder?: "ASC" | "DESC" } = {},
): Promise<{
	total: number;
	items: {
		config_id: number;
		config: SiteConfig;
		created_at: Date;
		admin_id: number;
	}[];
}> {
	const { sortBy = "created_at", sortOrder = "DESC" } = options;
	const allowedSortFields = ["created_at", "config_id", "admin_id"];
	const orderBy = allowedSortFields.includes(sortBy) ? sortBy : "created_at";

	const [count, items] = await Promise.all([
		prisma.site_configs.count(),
		prisma.site_configs.findMany({
			skip: (page - 1) * pageSize,
			take: pageSize,
			orderBy: [{ [orderBy]: sortOrder.toLowerCase() }, { created_at: "desc" }],
		}),
	]);

	return {
		total: count,
		items: items as unknown as {
			config_id: number;
			config: SiteConfig;
			created_at: Date;
			admin_id: number;
		}[],
	};
}
