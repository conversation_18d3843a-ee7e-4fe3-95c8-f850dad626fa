{"compilerOptions": {"target": "ESNext", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "baseUrl": ".", "outDir": "./dist", "rootDir": "./src", "paths": {"@/*": ["src/*"], "@packages/shared": ["../shared/src"]}, "types": ["node"], "sourceMap": true, "declaration": true, "removeComments": true, "allowJs": true, "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "../shared"}]}