import * as User from "@/models/user.js";
import { APP_CONFIG } from "@/config/configManager.js";
import { AppError } from "@/core/appError.js";
import EmailService from "@/utils/email.js";
import type { UserInfo } from "@packages/shared";

// 更新用户基本身份和银行信息
export async function updateUserProfile(user: UserInfo) {
	const {
		user_id,
		id_number,
		phone_number,
		bank_name,
		bank_code,
		bank_account,
	} = user;

	// Check for duplicate phone number
	if (phone_number) {
		const existingUserByPhone = await User.findByPhoneNumber(phone_number);
		if (existingUserByPhone && existingUserByPhone.user_id !== user_id) {
			throw AppError.create("PHONE_EXISTS", "该手机号已被其他用户使用");
		}
	}

	const newUser = await User.updateQualificationInfo(user_id, {
		id_number,
		phone_number,
		bank_name,
		bank_code,
		bank_account,
	});

	if (!user) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}
	EmailService.sendEmail(newUser.email, "INFO_UPDATE", {});
}

// 更新用户交易参数
export async function updateUserTradeParams(
	user_id: number,
	params: Pick<
		UserInfo,
		"custom_profit_sharing_percentage" | "custom_quote_diffs"
	>,
	admin_id?: number, // 用于审计日志
): Promise<UserInfo> {
	const { custom_profit_sharing_percentage, custom_quote_diffs } = params;

	const updates: Partial<UserInfo> = {};
	if (custom_profit_sharing_percentage !== undefined) {
		updates.custom_profit_sharing_percentage = custom_profit_sharing_percentage;
	}
	if (custom_quote_diffs !== undefined) {
		// 如果 custom_quote_diffs 是空对象 {}，则表示清空所有自定义加价，将其转为 null
		updates.custom_quote_diffs =
			custom_quote_diffs && Object.keys(custom_quote_diffs).length > 0
				? custom_quote_diffs
				: null;
	}

	if (Object.keys(updates).length === 0) {
		// 如果没有实际参数需要更新，可以提前返回或抛出错误
		const currentUser = await User.findById(user_id);
		if (!currentUser)
			throw AppError.create("USER_NOT_FOUND", `User ${user_id} not found.`);
		return currentUser; // Or throw error "No parameters to update"
	}

	await User.updateById(user_id, updates);

	// 记录管理员操作日志 (示例)
	if (admin_id) {
		// logger.info(`Admin ${admin_id} updated trade parameters for user ${user_id}. Changes: ${JSON.stringify(updates)}`);
	}

	const updatedUser = await User.findById(user_id);
	if (!updatedUser) {
		throw AppError.create(
			"USER_NOT_FOUND",
			`User ${user_id} not found after updating trade params.`,
		);
	}
	// 可以考虑发送一个不同的邮件模板，或不发送邮件
	// EmailService.sendEmail(updatedUser.email, "TRADE_PARAMS_UPDATED", { /* ... */ });
	return updatedUser;
}

// 获取所有用户信息，返回文本格式
// 格式：user_id~phone_number~email~name~bank_name~bank_code~bank_account~contribution~premium~created_at~balance_cny~balance_hkd~balance_usd~is_qualified~app_id;...
export async function getAllTextByApi(): Promise<string> {
	const userInfos = await User.getAllByApi();

	return userInfos
		.map((user) => {
			// 构建数据字符串，用波浪号连接各字段
			const values = [
				user.user_id?.toString() || "",
				user.phone_number || "",
				user.email || "",
				user.name || "",
				user.bank_name || "",
				user.bank_code || "",
				user.bank_account || "",
				user.contribution?.toString() || "0",
				user.premium?.toString() || "0",
				user.created_at?.toISOString() || "",
				user.balance_cny?.toString() || "0",
				user.balance_hkd?.toString() || "0",
				user.balance_usd?.toString() || "0",
				user.is_qualified ? "1" : "0",
				APP_CONFIG.tradingPlatformId || APP_CONFIG.channelId || "",
			].join("~");

			return values;
		})
		.join(";\n");
}
