<template>
  <div class="admin-layout">
    <el-container class="layout-container">
      <el-header>
        <div class="header-content">
          <div class="header-left">
            <el-button link class="menu-toggle-mobile" @click="toggleMobileSidebar">
              <el-icon>
                <Menu />
              </el-icon>
            </el-button>
            <div class="header-title">管理员控制台</div>
          </div>
          <div class="header-actions" v-if="authStore.isLoggedIn">
            <el-button link @click="showChangelog = true">
              <el-icon>
                <Notebook />
              </el-icon>
            </el-button>
            <el-button link @click="toggleTheme">
              <el-icon>
                <Sunny v-if="!isDark" />
                <Moon v-else />
              </el-icon>
            </el-button>
            <el-dropdown trigger="click">
              <el-button link>
                <el-icon>
                  <Setting />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="showChangePassword = true">
                    修改密码
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleLogout" divided>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <el-container class="main-container">
        <transition name="slide-fade">
          <el-aside :width="isCollapsed ? '64px' : '200px'"
            :class="{ 'sidebar-hidden': isMobile && !showMobileSidebar }"
            v-show="!isMobile || (isMobile && showMobileSidebar)">
            <div class="menu-collapse-button" :class="{ collapsed: isCollapsed }" v-if="!isMobile">
              <el-button link @click="toggleCollapse">
                <el-icon>
                  <Fold v-if="!isCollapsed" />
                  <Expand v-else />
                </el-icon>
              </el-button>
            </div>
            <div class="mobile-menu-header" v-if="isMobile">
              <div class="mobile-menu-title">菜单</div>
              <el-button link @click="toggleMobileSidebar">
                <el-icon>
                  <Close />
                </el-icon>
              </el-button>
            </div>
            <el-menu :default-active="activeMenu" router class="menu" :collapse="isCollapsed && !isMobile"
              :background-color="isDark ? '#1f1f1f' : '#ffffff'" :text-color="isDark ? '#ffffff' : '#303133'"
              :active-text-color="'var(--el-color-primary)'">
              <template v-for="(group, groupIndex) in menuGroups" :key="groupIndex">
                <template v-for="item in group.items" :key="item.path">
                  <el-menu-item :index="item.index || (item.computedPath ? item.computedPath() : item.path)"
                    @click="isMobile && toggleMobileSidebar()" :class="{ 'menu-item-disabled': item.label === '接口数据' }">
                    <el-icon>
                      <component :is="item.icon" />
                    </el-icon>
                    <template #title>
                      <span class="menu-item-text" :class="{ 'menu-item-disabled': item.label === '接口数据' }">{{
                        item.label }}</span>
                    </template>
                  </el-menu-item>
                </template>
                <div class="menu-divider" v-if="groupIndex < menuGroups.length - 1"></div>
              </template>
            </el-menu>
          </el-aside>
        </transition>

        <transition name="fade">
          <div class="mobile-overlay" v-if="isMobile && showMobileSidebar" @click="toggleMobileSidebar"></div>
        </transition>

        <el-main>
          <router-view v-if="authStore.isLoggedIn" />
        </el-main>
      </el-container>
    </el-container>

    <!-- 在移动端显示返回顶部按钮 -->
    <back-to-top v-if="isMobile" />
  </div>

  <ChangePasswordModal v-model:visible="showChangePassword" />
  <ChangelogModal v-model="showChangelog" />
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from "vue";
import type { Component } from "vue";
import { useRoute } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import {
  DataLine,
  Setting,
  User,
  Lock,
  Money,
  Monitor,
  Fold,
  Expand,
  PieChart,
  Moon,
  Sunny,
  Connection,
  ChatLineSquare,
  Menu,
  Close,
  Share,
  CreditCard,
  Tickets,
  Brush,
  Finished,
  Files,
  Coin,
  Collection,
  Unlock,
  Notebook,
} from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus";
import { AdminPermission, AppType } from "@packages/shared";
import { useTheme } from "@/composables/useTheme";
import ChangePasswordModal from "./modals/ChangePasswordModal.vue";
import ChangelogModal from "./modals/ChangelogModal.vue";
import BackToTop from "@/components/BackToTop.vue";

const route = useRoute();
const authStore = useAuthStore();
const { isDark, toggleTheme } = useTheme();
const showChangePassword = ref(false);
const showChangelog = ref(false);

// 计算活动菜单路径
const activeMenu = computed(() => {
  // 如果是审核管理的子路由，返回/audits
  if (route.path.startsWith("/audits/")) {
    return "/audits";
  }
  // 如果是用户管理的子路由，返回/users/list
  if (route.path.startsWith("/users/")) {
    return "/users/list";
  }
  return route.path;
});

// 移动端相关状态
// 直接在创建 ref 时就设置正确的初始值，避免初始状态错误导致的动画问题
const isMobile = ref(window.innerWidth < 768);
const showMobileSidebar = ref(false);

// 检测窗口大小变化，判断是否为移动设备
const checkIsMobile = () => {
  const newIsMobile = window.innerWidth < 768;
  if (newIsMobile !== isMobile.value) {
    isMobile.value = newIsMobile;
    if (!newIsMobile) {
      showMobileSidebar.value = false;
    }
  }
};

// 移动端侧边栏显示/隐藏
const toggleMobileSidebar = () => {
  showMobileSidebar.value = !showMobileSidebar.value;

  // 当显示侧边栏时，阻止背景滚动
  if (showMobileSidebar.value) {
    // 延迟一帧，确保动画在开始前应用样式
    requestAnimationFrame(() => {
      document.body.style.overflow = "hidden";
    });
  } else {
    // 使用过渡结束事件监听来处理隐藏后的操作
    // 延迟恢复滚动，等待动画完成
    setTimeout(() => {
      document.body.style.overflow = "";
    }, 300); // 与动画持续时间一致
  }
};

// 监听路由变化，在移动端时关闭侧边栏
watch(
  () => route.path,
  () => {
    if (isMobile.value) {
      showMobileSidebar.value = false;
      document.body.style.overflow = "";
    }
  },
);

// 组件挂载时添加窗口大小变化监听
onMounted(() => {
  checkIsMobile();
  window.addEventListener("resize", checkIsMobile);
});

// 组件卸载时移除窗口大小变化监听
onUnmounted(() => {
  window.removeEventListener("resize", checkIsMobile);
  document.body.style.overflow = "";
});

const handleLogout = async () => {
  await ElMessageBox.confirm("确定要登出吗？", "登出确认", {
    confirmButtonText: "登出",
    cancelButtonText: "取消",
    type: "warning",
  });

  authStore.logout();
};

type MenuItem = {
  path: string;
  icon: Component;
  label: string;
  permission: AdminPermission | AdminPermission[];
  computedPath?: () => string;
  index?: string;
};

type MenuGroup = {
  items: MenuItem[];
};

const menuGroups = computed<MenuGroup[]>(() => {
  const groups = [
    {
      items: [
        {
          path: "/dashboard",
          icon: DataLine,
          label: "仪表盘",
          permission: AdminPermission.BASIC,
        },
        {
          path: "/users/list",
          icon: User,
          label: "用户管理",
          permission: AdminPermission.BASIC,
        },
        {
          path: "/inquiries",
          icon: ChatLineSquare,
          label: "询价管理",
          permission: AdminPermission.BASIC,
        },
      ],
    },
    {
      items: [
        {
          path: "/orders",
          icon: Tickets,
          label: "订单管理",
          permission: AdminPermission.BASIC,
        },
        {
          path: "/audits/qualify",
          icon: Finished,
          label: "审核管理",
          permission: [AdminPermission.QUALIFY, AdminPermission.FINANCE],
          computedPath: () => {
            // 如果用户只有财务权限，则导航到财务审核页面
            if (
              !authStore.permissions.includes(AdminPermission.QUALIFY) &&
              authStore.permissions.includes(AdminPermission.FINANCE)
            ) {
              return "/audits/finance";
            }
            // 默认导航到资质审核页面
            return "/audits/qualify";
          },
          index: "/audits",
        },
        // 临时注释未编写完成的协议管理来部署新站点
        {
          path: "/agreements",
          icon: Collection,
          label: "协议管理",
          permission: AdminPermission.QUALIFY,
        },
      ],
    },
    {
      items: [
        {
          path: "/deposit",
          icon: Money,
          label: "平台出入金",
          permission: AdminPermission.FINANCE,
        },
        {
          path: "/transactions",
          icon: Coin,
          label: "资金流水",
          permission: AdminPermission.FINANCE,
        },
        {
          path: "/bank-account",
          icon: CreditCard,
          label: "银行账户",
          permission: AdminPermission.FINANCE,
        },
        {
          path: "/account-security",
          icon: Unlock,
          label: "账户安全",
          permission: AdminPermission.FINANCE,
        },
      ],
    },
    {
      items: [
        ...(authStore.app_type === AppType.TRADING_PLATFORM
          ? [
            {
              path: "/channel-management",
              icon: Connection,
              label: "通道管理",
              permission: AdminPermission.FINANCE,
            },
            {
              path: "/channel-fund",
              icon: Share,
              label: "通道资金审核",
              permission: AdminPermission.FINANCE,
            },
            {
              path: "/channel-orders",
              icon: Files,
              label: "通道订单查询",
              permission: AdminPermission.FINANCE,
            },
          ]
          : []),
        ...(authStore.app_type === AppType.CHANNEL
          ? [
            {
              path: "/platform-fund",
              icon: PieChart,
              label: "平台资金管理",
              permission: AdminPermission.FINANCE,
            },
          ]
          : []),
      ]
    },
    {
      items: [
        {
          path: "/system-status",
          icon: Monitor,
          label: "系统状态",
          permission: AdminPermission.CONFIG,
        },
        {
          path: "/platform-config",
          icon: Setting,
          label: "平台配置",
          permission: AdminPermission.CONFIG,
        },
        {
          path: "/site-config",
          icon: Brush,
          label: "网站配置",
          permission: AdminPermission.CONFIG,
        },
        {
          path: "/permissions",
          icon: Lock,
          label: "账户权限",
          permission: AdminPermission.ADMIN,
        },
      ],
    },
  ];

  // Filter out items without permission and empty groups
  return groups
    .map((group) => ({
      items: group.items.filter((item) => hasPermission(item.permission)),
    }))
    .filter((group) => group.items.length > 0); // Remove empty groups
});

// 有其中一个权限即可
function hasPermission(
  permission: AdminPermission | AdminPermission[],
): boolean {
  const permissions = authStore.permissions;
  if (!permissions.length) return false;
  if (permission === AdminPermission.BASIC) return true;
  if (Array.isArray(permission)) {
    return permission.some((p) => permissions.includes(p));
  }
  return permissions.includes(permission);
}

const isCollapsed = ref(localStorage.getItem("menuCollapsed") === "true");
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  localStorage.setItem("menuCollapsed", isCollapsed.value.toString());
};
</script>

<style scoped>
.admin-layout {
  height: 100vh;
  background: var(--el-bg-color);
  color: var(--el-text-color-primary);
}

.layout-container {
  height: 100%;
}

.main-container {
  height: calc(100% - 60px);
}

/* Header */
.el-header {
  background: var(--el-bg-color-overlay);
  padding: 0 20px;
  height: 60px;
  line-height: 60px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  position: relative;
  z-index: 1001;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Mobile Menu Toggle */
.menu-toggle-mobile {
  display: none;
  position: relative;
  overflow: hidden;
}

.menu-toggle-mobile::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--el-color-primary-light-8);
  border-radius: 50%;
  opacity: 0;
  transform: scale(0);
  transition: transform 0.3s, opacity 0.3s;
}

.menu-toggle-mobile:active::after {
  opacity: 0.6;
  transform: scale(1);
  transition: 0s;
}

@media (max-width: 767px) {
  .menu-toggle-mobile {
    display: flex;
    height: 32px;
    width: 32px;
    padding: 0;
    align-items: center;
    justify-content: center;
  }

  .menu-toggle-mobile .el-icon {
    font-size: 24px;
  }

  .header-title {
    font-size: 16px;
  }
}

/* Mobile Menu Header */
.mobile-menu-header {
  display: none;
  padding: 0 16px;
  height: 60px;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.mobile-menu-title {
  font-size: 18px;
  font-weight: 500;
}

.mobile-menu-header .el-button {
  position: relative;
  overflow: hidden;
}

.mobile-menu-header .el-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--el-color-primary-light-8);
  border-radius: 50%;
  opacity: 0;
  transform: scale(0);
  transition: transform 0.3s, opacity 0.3s;
}

.mobile-menu-header .el-button:active::after {
  opacity: 0.6;
  transform: scale(1);
  transition: 0s;
}

@media (max-width: 767px) {
  .mobile-menu-header {
    display: flex;
  }
}

/* Mobile Overlay */
.mobile-overlay {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Sidebar */
.el-aside {
  background: var(--el-bg-color-overlay);
  border-right: 1px solid var(--el-border-color-lighter);
  transition: width 0.3s ease-in-out;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-lighter) var(--el-bg-color-overlay);
  position: relative;
  z-index: 1000;
  visibility: visible;
  opacity: 1;
  transform: translateX(0);

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-overlay);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-lighter);
    border-radius: 3px;
  }
}

@media (max-width: 767px) {
  .el-aside {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 80% !important;
    max-width: 300px;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform-origin: left;
  }
}

.el-main {
  background: var(--el-bg-color);
  padding: 20px;
  overflow-x: hidden;
}

@media (max-width: 767px) {
  .el-main {
    padding: 15px;
  }
}

/* 侧边栏过渡动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease-out;
}

.slide-fade-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

/* 遮罩层过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Menu */
.menu {
  border-right: none;
}

.menu-divider {
  margin: 8px 0;
  height: 1px;
  background-color: var(--el-border-color-lighter);
}

.menu-collapse-button {
  padding: 12px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: padding 0.3s ease-in-out;

  &.collapsed {
    padding: 12px 0;
  }

  .el-button {
    color: var(--el-text-color-secondary);
    padding: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: var(--el-text-color-primary);
      background-color: var(--el-menu-hover-bg-color);
    }

    .el-icon {
      font-size: 18px;
      width: var(--el-menu-icon-width);
    }
  }
}

:deep(.el-menu) {
  width: 200px;
  transition: width 0.3s ease-in-out;

  &.el-menu--collapse {
    width: 64px;
  }
}

@media (max-width: 767px) {
  :deep(.el-menu) {
    width: 100% !important;
  }

  :deep(.el-menu-item) {
    width: 100% !important;
    min-width: 100% !important;
    padding-right: 20px !important;
    box-sizing: border-box !important;
  }

  :deep(.el-menu--collapse) {
    width: 100% !important;
  }

  :deep(.menu-item-text) {
    display: inline-block;
    width: calc(100% - 24px);
  }
}

:deep(.el-menu-item.is-disabled) {
  opacity: 0.5;
  cursor: not-allowed;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-button {
    height: 32px;
    width: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-regular);
    transition: all 0.2s ease;

    &:hover {
      color: var(--el-color-primary);
      transform: scale(1.1);
    }

    .el-icon {
      font-size: 16px;
      transition: transform 0.2s ease;
    }

    &:hover .el-icon {
      transform: rotate(5deg);
    }
  }
}

:deep(.el-menu-item) {
  &:hover {
    background-color: var(--el-menu-hover-bg-color);
  }

  &.is-active {
    background-color: var(--el-menu-hover-bg-color);
    color: var(--el-color-primary) !important;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 2px;
      background-color: var(--el-color-primary);
    }
  }
}

:deep(.el-menu--collapse) {
  .el-menu-item {
    &.is-active::before {
      display: none;
    }
  }
}

/* 添加侧边栏隐藏类 */
.sidebar-hidden {
  visibility: hidden;
  opacity: 0;
  transform: translateX(-100%);
}

.menu-item-disabled {
  text-decoration: line-through;
  color: var(--el-text-color-disabled) !important;
}
</style>
