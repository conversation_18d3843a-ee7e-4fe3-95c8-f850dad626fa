import prisma, { platformPrisma } from "@/lib/prisma.js";
import type { Prisma } from "@prisma/client";
import { AppError } from "@/core/appError.js";
import logger from "@/utils/logger.js";

/**
 * 执行数据库事务
 * @param transactionFn - 在事务中执行的函数
 * @param usePlatformDb - 是否使用平台数据库，默认为false表示使用主数据库
 * @returns 事务函数的返回值
 */
export async function withTransaction<T>(
	transactionFn: (tx: Prisma.TransactionClient) => Promise<T>,
	usePlatformDb = false,
): Promise<T> {
	try {
		// 根据参数选择使用的数据库客户端
		const dbClient = usePlatformDb ? platformPrisma : prisma;

		// 检查数据库客户端是否可用
		if (!dbClient) {
			throw AppError.create(
				"DB_CONNECTION_ERROR",
				`${usePlatformDb ? "Platform" : "Main"} database client not available`,
			);
		}

		return await dbClient.$transaction(
			async (tx) => {
				return await transactionFn(tx);
			},
			{
				timeout: 10000, // 可选：设置事务超时时间（毫秒）
			},
		);
	} catch (error) {
		logger.error(
			error instanceof Error ? { stack: error.stack } : {},
			"——事务回滚——",
		);

		// 如果是业务错误（AppError类型），直接抛出，保留原始错误信息
		if (error instanceof AppError) {
			throw error;
		}

		throw AppError.create(
			"DB_TRANSACTION_FAILED",
			`Database transaction failed: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

/**
 * 为什么PostgreSQL和Prisma不支持真正的跨数据库两阶段提交？
 *
 * 两阶段提交协议(2PC)需要特定的数据库支持，主要有以下限制：
 * 1. 需要分布式事务协调器(XA协议)支持，PostgreSQL支持但Prisma不提供此API
 * 2. 需要两个数据库实例能够通信和协调，而独立数据库实例通常没有此能力
 * 3. 即使底层PostgreSQL支持，不同的连接池/客户端也会阻断这种协调
 * 4. 在微服务架构中，跨数据库事务会导致紧耦合，违背了设计初衷
 *
 * 因此，我们使用"先提交再补偿"的模式，这是分布式系统常用的模式：
 * - 当平台数据库操作失败时，自动执行补偿操作回滚主数据库更改
 * - 如果补偿也失败，记录详细日志以便手动介入
 * - 这种方式虽然不保证绝对的原子性，但在大多数场景下足够可靠
 */

/**
 * 执行跨数据库操作，带补偿机制
 * 流程:
 * 1. 执行主数据库事务
 * 2. 如果主事务成功，执行平台数据库操作
 * 3. 如果平台操作失败，自动执行补偿操作回滚主数据库的变更
 *
 * @param mainDbFn - 在主数据库事务中执行的函数
 * @param platformDbFn - 在平台数据库中执行的函数，可接收事务客户端
 * @param rollbackFn - 平台操作失败时回滚主数据库变更的函数
 * @returns 平台数据库函数的返回值
 */
export async function withCrossDbTransaction<T, U>(
	mainDbFn: (tx: Prisma.TransactionClient) => Promise<T>,
	platformDbFn: (
		mainResult: T,
		platformTx?: Prisma.TransactionClient,
	) => Promise<U>,
	rollbackFn: (mainResult: T, error: unknown) => Promise<void>,
): Promise<U> {
	if (!platformPrisma) {
		throw AppError.create("NOT_FOUND", "Platform database not found");
	}

	// 执行主数据库事务
	const mainResult = await withTransaction(mainDbFn);

	// 记录主事务结果，用于错误追踪
	const transactionId = `txn_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
	logger.info(
		{ transactionId, status: "main_completed" },
		"主事务完成，开始执行平台操作",
	);

	// 执行平台数据库操作
	try {
		let result: U;

		// 为平台操作提供事务支持
		if (platformPrisma.$transaction) {
			result = await platformPrisma.$transaction(async (platformTx) => {
				return platformDbFn(mainResult, platformTx);
			});
		} else {
			// 不使用事务
			result = await platformDbFn(mainResult);
		}

		logger.info({ transactionId, status: "completed" }, "跨数据库操作完成");
		return result;
	} catch (error) {
		logger.error(
			{
				transactionId,
				error: error instanceof Error ? error.message : String(error),
				errorStack: error instanceof Error ? error.stack : undefined,
			},
			"平台数据库操作失败，执行回滚操作",
		);

		// 执行回滚函数
		try {
			await rollbackFn(mainResult, error);
			logger.info(
				{ transactionId, status: "rollback_completed" },
				"回滚操作成功完成",
			);
		} catch (rollbackError) {
			// 回滚失败，记录详细信息
			logger.error(
				{
					transactionId,
					mainResult: JSON.stringify(mainResult),
					originalError: error instanceof Error ? error.message : String(error),
					rollbackError:
						rollbackError instanceof Error
							? rollbackError.message
							: String(rollbackError),
					rollbackStack:
						rollbackError instanceof Error ? rollbackError.stack : undefined,
				},
				"严重错误：回滚操作失败，可能导致数据不一致，需要立即手动介入处理！",
			);
		}

		throw AppError.create(
			"DB_TRANSACTION_FAILED",
			`平台数据库操作失败: ${error instanceof Error ? error.message : "未知错误"}`,
		);
	}
}

/**
 * 在单数据库内执行多阶段事务，确保完整性
 *
 * @param operations - 按顺序执行的操作数组
 * @returns 最后一个操作的结果
 */
export async function withMultiStageTransaction<T>(
	operations: ((
		tx: Prisma.TransactionClient,
		prevResult?: unknown,
	) => Promise<unknown>)[],
): Promise<T> {
	return prisma.$transaction(
		async (tx) => {
			let result: unknown;

			for (let i = 0; i < operations.length; i++) {
				result = await operations[i](tx, result);
			}

			return result as T;
		},
		{
			timeout: 15000,
		},
	);
}

/**
 * 执行跨数据库操作 (平台先行模式)
 * 流程:
 * 1. 先执行平台数据库事务
 * 2. 平台操作成功后，再执行主数据库事务
 * 3. 如果平台操作失败，则不执行主数据库事务，直接抛出异常
 * 4. 如果主数据库事务失败，执行回滚函数自动回滚平台数据库的变更
 *
 * 这种方法相比withCrossDbTransaction有以下优势：
 * - 保证数据一致性：当主数据库失败时，通过回滚函数还原平台数据库状态
 * - 符合原子性原则：要么两个数据库都成功，要么不变更
 * - 自动化处理：不需要手动干预处理不一致情况
 *
 * @param platformDbFn - 先在平台数据库事务中执行的函数
 * @param mainDbFn - 平台事务成功后在主数据库事务中执行的函数
 * @param platformRollbackFn - 当主数据库事务失败时，回滚平台数据库变更的函数
 * @returns 主数据库函数的返回值
 */
export async function withCrossDbTransaction2<T, U>(
	platformDbFn: (platformTx: Prisma.TransactionClient) => Promise<T>,
	mainDbFn: (platformResult: T, tx: Prisma.TransactionClient) => Promise<U>,
	platformRollbackFn?: (platformResult: T, error: unknown) => Promise<void>,
): Promise<U> {
	if (!platformPrisma) {
		throw AppError.create("NOT_FOUND", "Platform database not found");
	}

	// 记录事务ID，用于日志追踪
	const transactionId = `txn_${Date.now()}_${Math.floor(Math.random() * 10000)}`;

	// 先执行平台数据库事务
	let platformResult: T;
	try {
		logger.info(
			{ transactionId, status: "platform_started" },
			"开始执行平台数据库事务",
		);

		platformResult = await platformPrisma.$transaction(
			async (platformTx) => {
				return await platformDbFn(platformTx);
			},
			{
				timeout: 10000,
			},
		);

		logger.info(
			{ transactionId, status: "platform_completed" },
			"平台数据库事务完成，即将执行主数据库事务",
		);
	} catch (error) {
		// 平台事务失败，记录错误并抛出异常，无需回滚主数据库
		logger.error(
			{
				transactionId,
				error: error instanceof Error ? error.message : String(error),
				errorStack: error instanceof Error ? error.stack : undefined,
			},
			"平台数据库事务失败，主数据库事务未执行",
		);

		// 如果是业务错误（AppError类型），直接抛出原始错误，而不是包装为DB_TRANSACTION_FAILED
		if (error instanceof AppError) {
			throw error;
		}

		throw AppError.create(
			"DB_TRANSACTION_FAILED",
			`平台数据库操作失败: ${error instanceof Error ? error.message : "未知错误"}`,
		);
	}

	// 平台事务成功后，执行主数据库事务
	try {
		const result = await prisma.$transaction(
			async (tx) => {
				return await mainDbFn(platformResult, tx);
			},
			{
				timeout: 10000,
			},
		);

		logger.info(
			{ transactionId, status: "completed" },
			"跨数据库操作完成：平台和主数据库事务均已成功",
		);

		return result;
	} catch (error) {
		logger.error(
			{
				transactionId,
				error: error instanceof Error ? error.message : String(error),
				errorStack: error instanceof Error ? error.stack : undefined,
			},
			"主数据库事务失败 - 尝试回滚平台数据库事务",
		);

		// 如果提供了回滚函数，尝试回滚平台数据库操作
		if (platformRollbackFn) {
			try {
				await platformRollbackFn(platformResult, error);
				logger.info(
					{ transactionId, status: "platform_rollback_completed" },
					"平台数据库回滚成功",
				);
			} catch (rollbackError) {
				logger.error(
					{
						transactionId,
						originalError:
							error instanceof Error ? error.message : String(error),
						rollbackError:
							rollbackError instanceof Error
								? rollbackError.message
								: String(rollbackError),
						rollbackStack:
							rollbackError instanceof Error ? rollbackError.stack : undefined,
					},
					"平台数据库回滚失败，需要手动处理不一致问题",
				);
			}
		} else {
			logger.warn(
				{ transactionId },
				"主数据库事务失败 - 无回滚函数，平台数据库事务已提交，需要手动处理不一致问题",
			);
		}

		// 如果是业务错误（AppError类型），直接抛出原始错误，而不是包装为DB_TRANSACTION_FAILED
		if (error instanceof AppError) {
			throw error;
		}

		throw AppError.create(
			"DB_TRANSACTION_FAILED",
			`主数据库操作失败: ${error instanceof Error ? error.message : "未知错误"}`,
		);
	}
}

/**
 * 在已有事务环境中执行平台数据库操作
 * 流程:
 * 1. 在已有的主事务环境中，执行平台数据库操作
 * 2. 如果平台操作失败，抛出异常使主事务回滚
 *
 * 这个函数专用于处理已经在事务中的跨数据库操作，通过抛出异常实现原子性
 *
 * @param platformDbFn - 在平台数据库事务中执行的函数
 * @returns 平台操作的结果
 */
export async function withExistingTxPlatformOperation<T>(
	platformDbFn: (platformTx: Prisma.TransactionClient) => Promise<T>,
): Promise<T> {
	if (!platformPrisma) {
		throw AppError.create("NOT_FOUND", "Platform database not found");
	}

	// 记录事务ID，用于日志追踪
	const transactionId = `ptx_${Date.now()}_${Math.floor(Math.random() * 10000)}`;

	try {
		logger.info(
			{ transactionId, status: "platform_started" },
			"在已有事务环境中执行平台数据库操作",
		);

		const result = await platformPrisma.$transaction(
			async (platformTx) => {
				return await platformDbFn(platformTx);
			},
			{
				timeout: 10000,
			},
		);

		logger.info(
			{ transactionId, status: "platform_completed" },
			"平台数据库操作完成，继续主事务",
		);

		return result;
	} catch (error) {
		// 平台事务失败，抛出异常以使主事务回滚
		logger.error(
			{
				transactionId,
				error: error instanceof Error ? error.message : String(error),
				errorStack: error instanceof Error ? error.stack : undefined,
			},
			"平台数据库操作失败，将导致主事务回滚",
		);

		// 如果是业务错误（AppError类型），直接抛出原始错误
		if (error instanceof AppError) {
			throw error;
		}

		throw AppError.create(
			"DB_TRANSACTION_FAILED",
			`平台数据库操作失败，触发主事务回滚: ${error instanceof Error ? error.message : "未知错误"}`,
		);
	}
}
