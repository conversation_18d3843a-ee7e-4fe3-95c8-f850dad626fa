import { ref, computed } from "vue";
import type { Component } from "vue";

export function useModals(modalComponents: Record<string, Component>) {
	const visibilityState = ref<Record<string, boolean>>({});

	const isVisible = (key: string) => visibilityState.value[key] || false;

	const visibleModals = computed(() => {
		return Object.keys(modalComponents).reduce(
			(acc, key) => {
				if (isVisible(key)) {
					acc[key] = modalComponents[key];
				}
				return acc;
			},
			{} as Record<string, Component>,
		);
	});

	const toggleVisibility = (key: string, value?: boolean) => {
		const newValue = value !== undefined ? value : !visibilityState.value[key];

		if (newValue === true) {
			// Close other modals
			for (const k of Object.keys(visibilityState.value)) {
				if (k !== key) visibilityState.value[k] = false;
			}
		}

		visibilityState.value[key] = newValue;
	};

	return {
		isVisible,
		visibleModals,
		toggleVisibility,
	};
}
