<template>
  <div class="reset-modal">
    <form @submit.prevent="handleResetPassword">
      <div class="form-group">
        <label for="email">邮箱</label>
        <input id="email" v-model="email" placeholder="请输入邮箱" />
      </div>

      <div class="form-group">
        <label for="newPassword">新密码</label>
        <div class="password-input-group">
          <input :type="showPassword ? 'text' : 'password'" id="newPassword" v-model="newPassword"
            @focus="handlePasswordFocus" @blur="validatePasswordOnBlur" placeholder="请输入新密码" />
          <button type="button" class="toggle-password" tabindex="-1" @click="showPassword = !showPassword">
            <SvgIcon :name="showPassword ? 'eye-rs-flaticon' : 'crossed-eye-rs-flaticon'" />
          </button>
        </div>
      </div>

      <div class="form-group">
        <label for="confirmPassword">确认密码</label>
        <div class="password-input-group">
          <input :type="showConfirmPassword ? 'text' : 'password'" id="confirmPassword" v-model="confirmPassword"
            placeholder="请确认密码" @blur="validateConfirmPasswordOnBlur" />
          <button type="button" class="toggle-password" tabindex="-1"
            @click="showConfirmPassword = !showConfirmPassword">
            <SvgIcon :name="showConfirmPassword ? 'eye-rs-flaticon' : 'crossed-eye-rs-flaticon'" />
          </button>
        </div>
      </div>

      <div class="form-group">
        <label for="smsCode">验证码</label>
        <div class="sms-input-group">
          <input id="smsCode" v-model="sms_code" placeholder="请输入验证码" />
          <button type="button" class="send-code-btn" :disabled="countdown > 0" @click="handleSendCode">
            {{ countdown > 0 ? `${countdown}s` : '获取' }}
          </button>
        </div>
      </div>

      <div class="reset-btn-container">
        <button class="reset-btn" type="submit">重置密码</button>
        <div class="password-tips-container">
          <SvgIcon name="info-rr-flaticon" class="tips-icon" />
          <div class="password-tips" :class="{ 'show-tips': passwordTipsVisible }">
            <div class="tips-content">
              <p>密码强度要求：</p>
              <ul>
                <li>至少8个字符</li>
                <li>必须包含字母</li>
                <li>必须包含数字</li>
                <li>必须包含特殊字符</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </form>

    <div class="login-link">
      <a @click="$emit('close')">返回登录</a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { authApi } from "@/api";
import SvgIcon from "@/components/SvgIcon.vue";
import { getErrorMessage } from "@packages/shared";
import type {
	ResetPasswordRequest,
	VerifyResetPasswordRequest,
} from "@packages/shared";

const emit = defineEmits(["close", "success"]);

const email = ref("");
const newPassword = ref("");
const sms_code = ref("");
const countdown = ref(0);
const showPassword = ref(false);
const passwordTipsVisible = ref(false);
const hasShownPasswordTips = ref(false);
const confirmPassword = ref("");
const showConfirmPassword = ref(false);

// 发送验证码
const handleSendCode = async () => {
	if (!email.value) {
		ElMessage.error("邮箱不能为空");
		return;
	}

	if (!validateEmail(email.value)) {
		ElMessage.error("邮箱格式无效");
		return;
	}

	// 添加密码验证
	if (!newPassword.value) {
		ElMessage.error("密码不能为空");
		return;
	}

	if (!validatePassword(newPassword.value)) {
		return; // validatePassword 函数已经设置了具体的错误信息
	}

	if (newPassword.value !== confirmPassword.value) {
		ElMessage.error("密码不一致");
		return;
	}

	try {
		const resetData: ResetPasswordRequest = {
			email: email.value,
		};
		await authApi.sendResetCode(resetData);

		// 开始倒计时
		countdown.value = 60;
		const timer = setInterval(() => {
			countdown.value--;
			if (countdown.value <= 0) {
				clearInterval(timer);
			}
		}, 1000);
	} catch (error) {
		ElMessage.error(`发送验证码失败：${getErrorMessage(error)}`);
	}
};

// 重置密码
const handleResetPassword = async () => {
	if (
		!email.value ||
		!newPassword.value ||
		!confirmPassword.value ||
		!sms_code.value
	) {
		ElMessage.error("所有字段均为必填项");
		return;
	}

	if (!validateEmail(email.value)) {
		ElMessage.error("邮箱格式无效");
		return;
	}

	if (!validatePassword(newPassword.value)) {
		return;
	}

	if (newPassword.value !== confirmPassword.value) {
		ElMessage.error("密码不一致");
		return;
	}

	try {
		const verifyData: VerifyResetPasswordRequest = {
			email: email.value,
			new_password: newPassword.value,
			sms_code: sms_code.value,
		};
		await authApi.verifyResetPassword(verifyData);
		ElMessage.success("密码重置成功");
		emit("success");
	} catch (error) {
		ElMessage.error(`重置密码失败：${getErrorMessage(error)}`);
	}
};

// 添加邮箱验证函数
const validateEmail = (email: string): boolean => {
	const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
	return pattern.test(email);
};

// 验证密码强度
const validatePassword = (password: string): boolean => {
	const requirements = [
		{
			regex: /.{8,}/,
			message: "密码强度低：至少8个字符",
		},
		{
			regex: /[a-zA-Z]/,
			message: "密码强度低：必须包含字母",
		},
		{
			regex: /[0-9]/,
			message: "密码强度低：必须包含数字",
		},
		{
			regex: /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,
			message: "密码强度低：必须包含特殊字符",
		},
	];

	for (const requirement of requirements) {
		if (!requirement.regex.test(password)) {
			ElMessage.error(requirement.message);
			return false;
		}
	}
	return true;
};

const handlePasswordFocus = () => {
	if (!hasShownPasswordTips.value) {
		passwordTipsVisible.value = true;
		hasShownPasswordTips.value = true;
		setTimeout(() => {
			passwordTipsVisible.value = false;
		}, 3000);
	}
};

// 密码框失焦时验证密码强度
const validatePasswordOnBlur = () => {
	if (newPassword.value) {
		validatePassword(newPassword.value);
	}
};

// 确认密码框失焦时验证是否一致
const validateConfirmPasswordOnBlur = () => {
	if (confirmPassword.value && newPassword.value) {
		if (newPassword.value !== confirmPassword.value) {
			ElMessage.error("密码不一致");
		}
	}
};
</script>

<style scoped>
.form-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  background-color: var(--el-fill-color-dark);
  border-radius: 5px;
  gap: 8px;
}

.reset-modal input {
  flex: 1;
  min-width: 0;
  padding: 10px 8px;
  border: none;
  border-radius: 5px;
  background-color: unset;
}

.reset-modal label {
  font-size: 15px;
  width: 64px;
  padding: 0 10px;
  text-align-last: justify;
  white-space: nowrap;
  border-right: 1px solid var(--el-border-color-light);
}

.password-input-group,
.sms-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 200px;
}

.toggle-password {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  display: flex;
}

.toggle-password:hover {
  opacity: 0.8;
}

.toggle-password .svg-icon {
  width: 16px;
  height: 16px;
}

.send-code-btn {
  width: 54px;
  white-space: nowrap;
  padding: 7.6px;
  border-radius: 4px;
  background-color: var(--el-color-primary);
  color: var(--el-text-color-primary);
  border: none;
  cursor: pointer;

  &:hover {
    background-color: var(--el-color-primary-light-3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.reset-btn {
  width: 50%;
  padding: 4px 8px;
  border: none;
  border-radius: 5px;
  color: var(--el-text-color-primary);
  cursor: pointer;
  background-color: var(--el-color-primary);

  &:hover {
    background-color: var(--el-color-primary-light-3);
  }
}

.login-link {
  margin: 16px 0 4px;
  text-align: center;
}

.login-link a {
  color: var(--el-text-color-primary);
  text-decoration: underline;
  cursor: pointer;
}

.form-group input:focus {
  outline: none;
  border-color: var(--el-color-primary);
}

.form-group.error input {
  border-color: var(--el-color-danger);
}

/* 隐藏浏览器默认的密码显示按钮 */
input[type='password']::-ms-reveal,
input[type='password']::-ms-clear,
input[type='password']::-webkit-contacts-auto-fill-button,
input[type='password']::-webkit-credentials-auto-fill-button {
  display: none !important;
  pointer-events: none;
  visibility: hidden;
}

/* 添加密码提示样式 */
.reset-btn-container {
  margin-top: 12px;
  position: relative;
}

.password-tips-container {
  position: absolute;
  top: 4px;
  left: 80%;
  display: inline-flex;
  cursor: pointer;
}

.tips-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  color: var(--el-text-color-primary);
}

.password-tips {
  white-space: nowrap;
  position: absolute;
  bottom: -8px;
  left: 0;
  transform: translate(-5px, 100%);
  text-align: left;
  font-size: 14px;
  background-color: var(--el-fill-color-light);
  padding: 8px 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--el-border-radius-base);
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s 0.3s, opacity 0.3s ease-in-out;

  &::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 10px;
    width: 10px;
    height: 10px;
    background-color: var(--el-fill-color-light);
    border-left: 1px solid var(--el-border-color-light);
    border-top: 1px solid var(--el-border-color-light);
    transform: rotate(45deg);
  }
}

.password-tips.show-tips {
  visibility: visible;
  opacity: 0.9;
  transition: visibility 0s, opacity 0.3s ease-in-out;
}

.password-tips-container:hover .password-tips {
  visibility: visible;
  opacity: 0.9;
  transition: visibility 0s, opacity 0.3s ease-in-out;
}

.tips-content {

  p,
  ul {
    margin: 8px 0;
  }

  ul {
    padding-left: 24px;
  }
}

/* 添加移动端适配 */
@media (max-width: 768px) {
  .form-group {
    margin-bottom: 12px;
  }

  .form-group label {
    font-size: 14px;
    width: 56px;
    padding: 0 8px;
  }

  .form-group input {
    padding: 8px;
    font-size: 14px;
  }

  .reset-btn {
    width: 60%;
    margin-top: 8px;
    padding: 8px;
  }

  .login-link {
    margin: 12px 0 4px;
    font-size: 14px;
  }

  .send-code-btn {
    padding: 6px;
    font-size: 13px;
  }

  .password-tips {
    font-size: 13px;
    padding: 6px 12px;
  }
}

/* 添加横屏模式适配 */
@media (max-height: 600px) and (orientation: landscape) {
  .form-group {
    margin-bottom: 8px;
  }

  .reset-btn {
    margin-top: 4px;
    padding: 6px;
  }

  .login-link {
    margin: 8px 0 4px;
  }
}
</style>