import { defineStore } from "pinia";

export interface Article {
	id: string;
	title: string;
	fileUrl: string;
	type: "html" | "image-series";
}

export const useArticlesStore = defineStore("articles", {
	state: () => ({
		articles: [
			{
				id: "1",
				title: "产品介绍",
				fileUrl: "/documents/intro.html",
				type: "html",
			},
			{
				id: "2",
				title: "用户手册",
				fileUrl: Array.from(
					{ length: 18 },
					(_, i) => `/documents/user-guide/user-guide_${i + 1}.webp`,
				),
				type: "image-series",
			},
			{
				id: "3",
				title: "常见问题",
				fileUrl: "/documents/faq.html",
				type: "html",
			},
		] as Article[],
	}),
});
