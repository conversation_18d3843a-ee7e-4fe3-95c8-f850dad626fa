import * as Position from "@/models/position.js";
import * as Order from "@/models/trade/order.js";
import { AppError } from "@/core/appError.js";
import type { Prisma } from "@prisma/client";
import { APP_CONFIG } from "@/config/configManager.js";
import type { PositionData, StructureType } from "@packages/shared";
import logger from "@/utils/logger.js";

export async function getUserPositions(
	user_id: number,
	page: number,
	pageSize: number,
	isDescending: boolean,
	filters?: {
		ts_codes?: string[];
	},
): Promise<{
	items: PositionData[];
	total: number;
	ts_codes: string[];
}> {
	const offset = (page - 1) * pageSize;

	// 现在只需要两个并行查询
	const [positionsResult, ts_codes] = await Promise.all([
		Position.getAllForUser(user_id, offset, pageSize, isDescending, filters),
		Position.getAllTsCodes(user_id),
	]);

	return {
		total: positionsResult.total,
		items: positionsResult.items,
		ts_codes,
	};
}

export async function createOrUpdatePosition(
	user_id: number,
	ts_code: string,
	scale_change: number,
	entry_price: number,
	structure: StructureType,
	exercise_price: number,
	trade_no: string,
	client: Prisma.TransactionClient,
	quote_provider?: string,
	quote_diff?: number,
): Promise<PositionData> {
	const order = await Order.findByTradeNo(trade_no, client);
	const { term, quote, expiry_date, expiry_date_confirmed } = order;

	const updatedPosition = await Position.createOrUpdate(
		{
			trade_no,
			user_id,
			ts_code,
			scale_change,
			entry_price,
			structure,
			exercise_price,
			term,
			quote,
			expiry_date,
			expiry_date_confirmed,
			quote_provider: quote_provider || order.quote_provider,
			quote_diff: quote_diff || order.quote_diff,
		},
		client,
	);

	if (!updatedPosition) {
		throw AppError.create(
			"POSITION_UPDATE_FAILED",
			"Failed to update position",
		);
	}

	logger.info(updatedPosition, "更新持仓");
	return updatedPosition;
}

/**
 * 获取接口持仓数据
 * 返回格式：trade_no~user_id~stock_code~entry_price~scale~term~quote~structure~quote_provider~app_id;...
 * @returns 持仓数据
 */
export async function getAllPositionsByApi(): Promise<string> {
	const positions = await Position.getAllByApi();

	return positions
		.map((position) => {
			// 处理股票代码：移除字母部分
			const stockCode = position.ts_code.replace(/\.[A-Z]+$/, "");

			// 构建数据字符串，用破折号连接
			const values = [
				position.trade_no || "",
				position.user_id || "",
				stockCode || "",
				position.entry_price || "",
				position.scale?.toString() || "",
				position.term?.toString() || "",
				position.quote || "",
				position.structure || "",
				position.quote_provider || "INK",
				APP_CONFIG.tradingPlatformId || APP_CONFIG.channelId || "",
			].join("~");

			return values;
		})
		.join(";\n");
}

export async function getAllPositionsByAdmin(
	page: number,
	pageSize: number,
): Promise<{ total: number; items: PositionData[] }> {
	return Position.getAllByAdmin(page, pageSize);
}

export async function getUserPositionsByAdmin(
	user_id: number,
	page: number,
	pageSize: number,
): Promise<{ total: number; items: PositionData[] }> {
	return Position.getForUserByAdmin(user_id, page, pageSize);
}
