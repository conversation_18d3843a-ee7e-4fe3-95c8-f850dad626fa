import type { Prisma } from "@prisma/client";
import * as NotificationModel from "@/models/notification.js";
import * as UserModel from "@/models/user.js";
import { AppError } from "@/core/appError.js";
import { notifyUser } from "@/utils/notify.js";
import type { NotificationData, NotificationOptions } from "@packages/shared";
import {
	welcomeNotificationOptions,
	WebSocketMessageType,
} from "@packages/shared";

/**
 * 发送通知
 */
export async function sendNotification(
	user_id: number | null,
	options: NotificationOptions,
	tx?: Prisma.TransactionClient,
): Promise<NotificationData> {
	// 系统通知不需要验证用户
	if (user_id !== null) {
		const user = await UserModel.findById(user_id);
		if (!user) {
			throw AppError.create("USER_NOT_FOUND", "User not found");
		}
	}

	const notification = await NotificationModel.create(
		{
			user_id: user_id || undefined,
			target_type: user_id ? "personal" : "system",
			...options,
		},
		tx,
	);

	// 发送 WebSocket 通知
	if (user_id) {
		await notifyUser(user_id, {
			type: WebSocketMessageType.NOTIFICATION_UPDATE,
		});
	}

	return notification;
}

/**
 * 发送欢迎通知
 */
export async function sendWelcomeNotification(
	user_id: number,
): Promise<NotificationData> {
	return sendNotification(user_id, welcomeNotificationOptions);
}

/**
 * 添加一个用于触发页面刷新的函数
 */
export async function notifyPageReload(user_id: number) {
	await notifyUser(user_id, {
		type: WebSocketMessageType.PAGE_RELOAD,
	});
}
