<template>
	<!-- 
    注意：2025-04-05 17:25的版本中包含原有订单修改相关完整代码（人工录单、修改订单、结束订单等）
    这些功能已迁移至总控端通过开放API实现，详见docs/order-management-api.md
    后续优化可考虑将这些功能重新添加回来，但微调为从管理端发起审核请求到总控端的模式
  -->
	<div class="order-management-panel">
		<!-- 搜索区域 -->
		<div class="search-container">
			<el-input v-model="searchTradeNo" placeholder="输入订单号筛选" clearable @clear="handleClearSearch"
				style="width: 250px;">
				<template #append>
					<el-button @click="handleSearch">
						<el-icon>
							<Search />
						</el-icon>
					</el-button>
				</template>
			</el-input>
		</div>

		<!-- 修改记录表格 -->
		<el-table :data="modifications" style="width: 100%">
			<el-table-column prop="created_at" label="修改时间" min-width="100">
				<template #default="{ row }">
					{{ formatDate(row.created_at) }}
				</template>
			</el-table-column>
			<el-table-column prop="type" label="操作类型" min-width="80">
				<template #default="{ row }">
					{{ getModificationType(row.type) }}
				</template>
			</el-table-column>
			<el-table-column prop="trade_no" label="订单号" min-width="100" />
			<el-table-column prop="user_id" label="用户ID" min-width="80" />
			<el-table-column prop="comment" label="修改原因" min-width="100" />
			<el-table-column label="修改详情" min-width="100">
				<template #default="{ row }">
					<el-button type="primary" link @click="showModificationDetails(row)">
						查看详情
					</el-button>
				</template>
			</el-table-column>
		</el-table>

		<!-- 分页 -->
		<div class="pagination-container">
			<el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
				:total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
				@current-change="handleCurrentChange" />
		</div>

		<!-- 修改详情对话框 -->
		<el-dialog v-model="detailsDialogVisible" title="修改详情" width="400px">
			<div class="modification-info">
				<el-descriptions :column="1" border size="small">
					<el-descriptions-item label="订单号">
						{{ selectedModification?.trade_no || '-' }}
					</el-descriptions-item>
					<el-descriptions-item label="用户ID">
						{{ selectedModification?.user_id || '-' }}
					</el-descriptions-item>
					<el-descriptions-item label="修改时间">
						{{ selectedModification ? formatDate(selectedModification.created_at) : '-' }}
					</el-descriptions-item>
					<el-descriptions-item label="修改原因">
						{{ selectedModification?.comment || '-' }}
					</el-descriptions-item>
				</el-descriptions>
			</div>

			<div class="modification-table">
				<div class="section-subtitle">{{
					selectedModification?.type === OrderModificationType.ORDER_CHANGE ? '字段变更' :
						selectedModification?.type === OrderModificationType.MANUAL_CLOSE ? '订单结束' :
							'录单详情'
				}}</div>
				<el-table :data="formatModificationData()" border stripe style="width: 100%">
					<el-table-column prop="field" label="字段" width="120" />
					<el-table-column v-if="selectedModification?.type === OrderModificationType.ORDER_CHANGE ||
						selectedModification?.type === OrderModificationType.MANUAL_CLOSE" prop="oldValue" label="原值" />
					<el-table-column prop="newValue" :label="selectedModification?.type === OrderModificationType.ORDER_CHANGE ||
						selectedModification?.type === OrderModificationType.MANUAL_CLOSE ? '新值' : '值'" />
				</el-table>
			</div>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { formatDate } from "@/utils/format";
import { orderManagementApi } from "@/api";
import type { OrderModification } from "@packages/shared";
import { OrderModificationType } from "@packages/shared";
import { useSiteConfigStore } from "@/stores/siteConfig";

const siteConfigStore = useSiteConfigStore();

// 添加搜索状态
const searchTradeNo = ref("");

// 数据状态
const modifications = ref<OrderModification[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 表单状态
const detailsDialogVisible = ref(false);
const selectedModification = ref<OrderModification | null>(null);

// 加载数据
const loadData = async () => {
	try {
		// 构建筛选条件
		const filters = searchTradeNo.value
			? { trade_no: searchTradeNo.value }
			: undefined;

		const response = await orderManagementApi.getOrderModifications(
			currentPage.value,
			pageSize.value,
			filters,
		);
		modifications.value = response?.items || [];
		total.value = response?.total || 0;
	} catch (error) {
		console.error("Failed to fetch modifications:", error);
		ElMessage.error("获取修改记录失败");
	}
};

// 搜索处理
const handleSearch = () => {
	currentPage.value = 1; // 重置到第一页
	loadData();
};

const handleClearSearch = () => {
	searchTradeNo.value = "";
	loadData();
};

// 类型转换
const getModificationType = (type: OrderModificationType) => {
	const types = {
		[OrderModificationType.MANUAL_CREATE]: "人工录单",
		[OrderModificationType.ORDER_CHANGE]: "修改订单",
		[OrderModificationType.MANUAL_CLOSE]: "结束订单",
	};
	return types[type as keyof typeof types] || type;
};

const showModificationDetails = (modification: OrderModification) => {
	selectedModification.value = modification;
	detailsDialogVisible.value = true;
};

// 分页处理
const handleSizeChange = (newSize: number) => {
	pageSize.value = newSize;
	loadData();
};

const handleCurrentChange = (newPage: number) => {
	currentPage.value = newPage;
	loadData();
};

// 格式化修改数据为表格形式，只显示核心业务字段
const formatModificationData = () => {
	if (!selectedModification.value?.data) return [];

	// 仅显示这些核心业务字段
	const coreFields = [
		"ts_code",
		"entry_price",
		"quote",
		"scale",
		"term",
		"structure",
		"quote_provider",
		"status",
		"settle_price",
	];

	// 如果是修改订单类型且有新旧值对象
	if (
		selectedModification.value.type === OrderModificationType.ORDER_CHANGE ||
		selectedModification.value.type === OrderModificationType.MANUAL_CLOSE
	) {
		const { oldValue, newValue } = selectedModification.value.data;

		// 只返回核心业务字段
		return coreFields
			.filter(
				(field) =>
					// 确保字段存在于数据中
					(oldValue && field in oldValue) || (newValue && field in newValue),
			)
			.map((field) => ({
				field: getFieldName(field),
				oldValue: formatFieldValue(
					field,
					oldValue?.[field as keyof typeof oldValue] ?? "-",
				),
				newValue: formatFieldValue(
					field,
					newValue?.[field as keyof typeof newValue] ?? "-",
				),
			}));
	}
	// 如果是人工录单类型
	return coreFields
		.filter(
			(field) =>
				selectedModification.value?.data.newValue &&
				field in selectedModification.value.data.newValue,
		)
		.map((field) => ({
			field: getFieldName(field),
			newValue: formatFieldValue(
				field,
				selectedModification.value?.data.newValue?.[
				field as keyof typeof selectedModification.value.data.newValue
				],
			),
		}));
};

// 添加格式化字段值的函数
const formatFieldValue = (field: string, value: unknown) => {
	if (field === "quote_provider") {
		if (value === "INK") return siteConfigStore.shortName;

		const providerMap: Record<string, string> = {
			HAIYING: "HY",
			YINHE_DERUI: "YHDR",
			ZHONGZHENG_ZIBEN: "ZZZB",
			ZHEQI: "ZQ",
			YONGAN: "YA",
			ZHONGJIN: "ZJ",
			GUANGFA: "GF",
			GUOJUNZI: "GJZ",
		};

		return typeof value === "string" ? providerMap[value] || value : value;
	}

	if (field === "status") {
		if (typeof value !== "string") return value;
		return getOrderStatusText(value);
	}

	return value;
};

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
	const statusMap: Record<string, string> = {
		holding: "持有中",
		sold: "已结束",
		limit_buying: "限价买入中",
		limit_selling: "限价卖出中",
		vwap_buying: "VWAP买入中",
		vwap_selling: "VWAP卖出中",
	};
	return statusMap[status] || status;
};

const getFieldName = (field: string) => {
	const fieldNames: Record<string, string> = {
		entry_price: "开仓价",
		quote: "期权费率",
		scale: "规模",
		term: "期限",
		structure: "结构",
		ts_code: "标的",
		quote_provider: "报价提供商",
		status: "订单状态",
		settle_price: "结算价",
	};
	return fieldNames[field] || field;
};

// 初始化加载
onMounted(() => {
	loadData();
});
</script>

<style scoped>
.search-container {
	margin-bottom: 20px;
	display: flex;
	align-items: center;
}

.section-title {
	margin: 20px 0;
}

.form-item-hint {
	font-size: 12px;
	color: var(--el-text-color-secondary);
	margin-top: 4px;
	line-height: 1.2;
}

pre {
	white-space: pre-wrap;
	word-wrap: break-word;
	margin: 0;
}

.modification-info {
	margin-bottom: 20px;
}

.section-subtitle {
	font-size: 16px;
	font-weight: 500;
	margin: 16px 0 8px 0;
	color: var(--el-text-color-primary);
}

.modification-table {
	margin-top: 20px;
}
</style>