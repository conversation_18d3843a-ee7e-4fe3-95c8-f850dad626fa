export enum Currency {
	CNY = "CNY",
	HKD = "HKD",
	USD = "USD",
}

export interface Balances {
	balance_cny: number;
	balance_hkd: number;
	balance_usd: number;
}

export enum TransactionType {
	BUY = "buy",
	SELL = "sell",
	DEPOSIT = "deposit",
	WITHDRAW = "withdraw",
	PLATFORM_DEPOSIT = "platform_deposit",
	EXCHANGE = "exchange",
	TRANSFER = "transfer",
}

// 资金操作接口
export interface TransactionData {
	txn_id: number;
	user_id: number;
	/** @invariant signed_amount must be negative (< 0) for sell and withdraw */
	signed_amount: number;
	type: TransactionType;
	currency: Currency;
	trade_no?: string;
	created_at: string;
}

// 转账接口
export interface TransferData {
	receiverName: string;
	receiverPhone: string;
	amount: number;
	password: string;
}

// 货币交易接口
export interface ExchangeCurrencyData {
	fromCurrency: Currency;
	toCurrency: Currency;
	amount: number;
}
