# ink 项目自动化部署指南 (Docker + Traefik)

本文档描述了使用 Docker、Docker Compose 和 Traefik 实现 ink 项目（包括前端、后端和管理端）自动化部署、更新和管理的现代化流程。

## 一、 核心理念与优势

-   **容器化:** 使用 Docker 将应用（前端、后端）及其依赖打包，确保环境一致性。
-   **服务编排:** 使用 Docker Compose 定义和管理应用所需的所有服务（Traefik、后端、前端、数据库、Redis）。
-   **自动反向代理与路由:** 使用 Traefik 自动发现服务、管理路由规则，并处理 HTTPS/SSL 证书。
-   **简化部署:** 通过 `docker compose up` 命令实现一键式部署和更新。
-   **易于管理:** 可结合总控端实现更便捷的多站点管理。

## 二、 准备工作

1.  **服务器环境:**
    *   安装 Docker 和 Docker Compose。
    *   配置好 Git 和 SSH 密钥（如果需要从私有仓库拉取代码）。
    *   确保防火墙允许 80 (HTTP) 和 443 (HTTPS) 端口的入站流量。
    *   **首次设置 Traefik:** 确保 Traefik 容器有权限访问 Docker Socket，并为存储 `acme.json` 的**目录或卷**设置好权限（例如，如果挂载到宿主机目录，确保 Docker 用户可写；如果使用 Docker 卷，通常会自动处理）。Traefik 会在首次成功获取证书时自动创建 `acme.json`。
2.  **域名解析:**
    *   **（唯一的手动步骤）** 将所有需要部署的站点域名（例如 `site1.example.com`, `admin.site1.example.com` 等）解析到服务器的 IP 地址。
3.  **项目代码 (模板准备):**
    *   确保代码仓库中包含 `Dockerfile`（用于后端、前端、管理端）。
    *   准备好**模板 `docker-compose.yml` 文件**，包含所有服务定义（Traefik, backend, frontend, admin, database, redis）以及**带有占位符**的 Traefik 路由规则 (例如 `Host(\`\${SITE_DOMAIN}\`)`)。
    *   准备好**模板 `.env.template` 文件**，列出所有需要的环境变量，并可能包含一些默认值。

## 三、 关键组件配置

### 1. Dockerfile

*   **后端 (`packages/server/Dockerfile`):**
    *   基于 Node.js 镜像。
    *   优化依赖安装（复制 `package.json`, `pnpm-lock.yaml` -> `pnpm install`）。
    *   复制源代码 -> `pnpm build`。
    *   设置启动命令 (`CMD ["node", "dist/app.js"]`)。
*   **前端 (`packages/client/Dockerfile`):**
    *   多阶段构建：
        *   Stage 1: 使用 Node.js 构建静态文件 (`pnpm build`)。
        *   Stage 2: 使用 `nginx:alpine` 或 `caddy` 镜像托管 `dist` 目录下的静态文件，并配置 Nginx/Caddy 处理 SPA 路由。
*   **管理端 (`packages/admin/Dockerfile`):**
    *   类似前端的 Dockerfile。

### 2. Traefik 配置

*   **`traefik.yml` (或 Traefik 启动命令参数):**
    *   定义入口点 `web` (80) 和 `websecure` (443, HTTPS)。
    *   启用 Docker Provider (`providers.docker.exposedByDefault=false`)。
    *   配置 Let's Encrypt Resolver (指定 email, 存储 `acme.json` 的路径)。
    *   设置 HTTP 到 HTTPS 的全局重定向。
*   **`acme.json`:** 用于存储 Let's Encrypt 证书的文件，需要挂载到 Traefik 容器内，并设置正确的文件权限 (`chmod 600 acme.json`)。

### 3. Docker Compose (`docker-compose.yml`)

*   **`version`:** 使用较新的版本，如 '3.8' 或更高。
*   **`networks`:** 定义一个自定义网络（例如 `ink-network`），所有服务都连接到这个网络。
*   **`volumes`:** 定义具名卷用于持久化数据（数据库、Redis、Traefik 证书）。
*   **`services`:**
    *   **`traefik`:**
        *   使用 `traefik:latest` 镜像。
        *   映射端口 `80:80`, `443:443`。
        *   挂载 Docker socket (`/var/run/docker.sock:/var/run/docker.sock:ro`)。
        *   挂载 `traefik.yml` (如果使用文件配置)。
        *   挂载 `acme.json` 卷。
        *   连接到 `ink-network`。
    *   **`database` (PostgreSQL):**
        *   使用 `postgres:alpine` 镜像。
        *   通过 `environment` 从 `.env` 文件读取用户、密码、数据库名。
        *   挂载数据卷到 `/var/lib/postgresql/data`。
        *   连接到 `ink-network`。
        *   **不**需要 `ports` 映射（除非需要从外部直接访问）。
    *   **`redis`:**
        *   使用 `redis:alpine` 镜像。
        *   通过 `environment` 或 `command` 设置密码。
        *   挂载数据卷到 `/data`。
        *   连接到 `ink-network`。
        *   **不**需要 `ports` 映射。
    *   **`backend` (可多个实例，命名区分如 `backend-site1`):**
        *   `build: ./packages/server` 或使用预构建镜像。
        *   `env_file: .env`
        *   连接到 `ink-network`。
        *   **添加 Traefik Labels:**
            ```yaml
            labels:
              - "traefik.enable=true"
              # HTTP Routers
              - "traefik.http.routers.backend-site1-api.rule=Host(`site1.example.com`) && PathPrefix(`/api`)" # 示例路由
              - "traefik.http.routers.backend-site1-api.entrypoints=websecure"
              - "traefik.http.routers.backend-site1-api.tls.certresolver=myresolver" # myresolver 是 Let's Encrypt resolver 名称
              # Services
              - "traefik.http.services.backend-site1-api.loadbalancer.server.port=3000" # 后端容器监听的端口
            ```
    *   **`frontend` (通常一个实例，服务多个域名):**
        *   `build: ./packages/client` 或使用预构建镜像。
        *   连接到 `ink-network`。
        *   **添加 Traefik Labels:** (为每个需要此前端的域名添加 router)
            ```yaml
            labels:
              - "traefik.enable=true"
              # Router for site1.example.com
              - "traefik.http.routers.frontend-site1.rule=Host(`site1.example.com`)"
              - "traefik.http.routers.frontend-site1.entrypoints=websecure"
              - "traefik.http.routers.frontend-site1.tls.certresolver=myresolver"
              # Router for site2.example.com (指向同一个前端服务)
              - "traefik.http.routers.frontend-site2.rule=Host(`site2.example.com`)"
              - "traefik.http.routers.frontend-site2.entrypoints=websecure"
              - "traefik.http.routers.frontend-site2.tls.certresolver=myresolver"
              # Service (指向前端容器的 Nginx/Caddy 端口)
              - "traefik.http.services.frontend.loadbalancer.server.port=80"
            ```
    *   **`admin` (如果部署管理端):**
        *   类似 `frontend` 的配置，但 `Host` 规则使用管理端域名（如 `admin.site1.example.com`）。

### 4. 环境变量 (`.env` 文件)

*   集中管理所有配置，如：
    ```ini
    COMPOSE_PROJECT_NAME=ink_site1 # 用于区分不同站点的容器/网络名称前缀
    # 数据库配置
    POSTGRES_USER=ink_user
    POSTGRES_PASSWORD=your_strong_password
    POSTGRES_DB=ink_site1_db
    DB_PASSWORD=${POSTGRES_PASSWORD} # 确保后端能读取到密码
    # Redis配置
    REDIS_PASSWORD=your_redis_password
    # 后端配置
    JWT_SECRET=your_jwt_secret
    # ... 其他后端需要的环境变量 ...
    # 域名配置 (可用于脚本生成 Traefik 标签或检查)
    SITE_DOMAIN=site1.example.com
    ADMIN_DOMAIN=admin.site1.example.com
    ```

## 四、 总控端自动化部署流程 (一键部署)

当在总控端（Web UI 或 CLI）触发部署新站点或更新站点时，后台应执行以下自动化脚本：

1.  **接收输入:** 从总控端获取站点配置参数（如 `siteDomain=site1.example.com`, `adminDomain=admin.site1.example.com`, `projectName=ink_site1`, `dbName=ink_site1_db`, 以及其他必要的密码/密钥）。
2.  **SSH 连接服务器:** 安全地连接到目标服务器。
3.  **准备站点目录:** 在服务器上创建或进入站点的部署目录 (例如 `/opt/ink-sites/${projectName}` )。
4.  **获取/更新代码:** 执行 `git clone <repo_url> .` 或 `git pull origin <branch>`。
5.  **生成 `.env` 文件:** 读取**模板 `.env.template`**，结合接收到的输入参数，在站点目录中**自动生成**最终的 `.env` 文件。
6.  **生成 `docker-compose.yml` 文件:** 读取**模板 `docker-compose.yml`**，使用接收到的域名参数 ( `siteDomain`, `adminDomain` ) **自动替换** Traefik 标签中的 `Host()` 规则占位符，并将最终的配置文件保存到站点目录。
7.  **确保 Traefik 证书存储可用:** (通常无需操作，由首次服务器设置保证)。
8.  **执行 Docker Compose 启动/更新:** 运行 `docker compose -p ${projectName} --env-file .env up -d --build --remove-orphans`。
    *   `-p ${projectName}`: 使用项目名隔离容器、网络和卷。
    *   `--env-file .env`: 加载生成的环境变量。
    *   `--build`: 确保使用最新的代码构建镜像。
    *   `-d`: 后台运行。
    *   `--remove-orphans`: 清理旧容器。
9.  **执行数据库迁移:** 运行 `docker compose -p ${projectName} --env-file .env exec backend pnpm prisma migrate deploy` (或适用于你项目的迁移命令)。
10. **返回状态:** 向总控端报告部署成功或失败。

## 五、 站点更新流程

与首次部署类似，但通常跳过目录创建步骤，并可能不需要每次都 `--build` (除非代码或 Dockerfile 有更改)。

1.  接收更新请求。
2.  SSH 连接服务器，进入站点目录。
3.  `git pull` 更新代码。
4.  (如果需要) 更新并生成 `.env` 文件。
5.  (如果需要) 更新并生成 `docker-compose.yml` 文件。
6.  `docker compose -p ${projectName} --env-file .env up -d --build --remove-orphans`。
7.  `docker compose -p ${projectName} --env-file .env exec backend pnpm prisma migrate deploy`。
8.  返回状态。

## 六、 站点删除流程

1.  接收删除请求 (需要确认！)。
2.  SSH 连接服务器，进入站点**父**目录。
3.  `docker compose -p ${projectName} down -v` ( **`-v` 会删除数据卷，极其危险，确保有备份或用户确认！**)。
4.  `rm -rf ${projectName}` (删除站点目录)。
5.  返回状态。

## 七、 注意事项

*   **安全性:** SSH 连接、密钥管理、`.env` 文件权限、Docker Socket 访问权限。
*   **错误处理:** 自动化脚本需要健壮的错误处理和状态报告。
*   **资源隔离:** 使用 `docker compose -p <project_name>` 来隔离不同站点的网络和卷。
*   **数据备份:** 自动化流程**不**应取代常规的数据备份策略。
*   **日志管理:** 配置 Docker 日志驱动和集中式日志系统。
