<template>
  <div class="platform-fund-request view">
    <div class="header-container">
      <h2>平台资金管理</h2>
    </div>

    <!-- 余额卡片 -->
    <el-card class="balance-summary-card">
      <template #header>
        <div class="card-header">
          <span>当前余额</span>
          <div class="header-actions">
            <el-button @click="loadBalance" :loading="balanceLoading" :icon="Refresh">
            </el-button>
            <el-button @click="openExchangeDialog" style="margin-left: 0;">
              <el-icon style="margin-right: 2px;">
                <Money />
              </el-icon>
              货币兑换
            </el-button>
          </div>
        </div>
      </template>
      <div v-loading="balanceLoading">
        <div class="balance-item">
          <span class="currency">人民币：</span>
          <span class="amount" :class="{ 'negative': balance.balance_cny < 0 }">{{ formatMoney(balance.balance_cny) }}
            CNY</span>
        </div>
        <div class="balance-item">
          <span class="currency">港币：</span>
          <span class="amount" :class="{ 'negative': balance.balance_hkd < 0 }">{{ formatMoney(balance.balance_hkd) }}
            HKD</span>
        </div>
        <div class="balance-item">
          <span class="currency">美元：</span>
          <span class="amount" :class="{ 'negative': balance.balance_usd < 0 }">{{ formatMoney(balance.balance_usd) }}
            USD</span>
        </div>
        <div class="credit-info">
          <div class="credit-limit">
            <span>预授权额度：</span>
            <span>{{ formatMoney(creditLimit) }}</span>
            <el-popover placement="top-start" :width="300" trigger="hover"
              content="预授权额度是通道专属的人民币(CNY)临时透支额度，请在当日结清透支部分以免次日资金操作受影响。">
              <template #reference>
                <el-icon class="info-icon">
                  <InfoFilled />
                </el-icon>
              </template>
            </el-popover>
          </div>
          <div v-if="balance.is_locked" class="locked-info">
            <el-tag type="danger">通道已锁定</el-tag>
            <el-tooltip content="通道余额为负且已超过当日结算时限，请尽快充值恢复余额为正">
              <el-icon>
                <Warning />
              </el-icon>
            </el-tooltip>
          </div>
        </div>
        <div v-if="hasNegativeBalance && !balance.is_locked" class="warning-info">
          <el-alert title="请注意：通道余额为负" type="warning" :closable="false" show-icon
            description="请在当日结束前充值使余额恢复为正，否则系统将在零点自动锁定通道，锁定后将无法出金。" />
        </div>
      </div>
    </el-card>

    <!-- 银行账户信息 -->
    <div class="accounts-info" v-if="hasChannelAccountInfo || hasUpstreamAccountInfo">
      <div class="two-cards-row">
        <!-- 本地通道银行账户信息 -->
        <el-card class="account-info-card" v-if="hasChannelAccountInfo">
          <template #header>
            <div class="card-header">
              <span>本地银行账户</span>
            </div>
          </template>
          <div class="account-info-grid">
            <div class="info-group">
              <label>账户名：</label>
              <span class="value">{{ channelAccount.name }}</span>
            </div>
            <div class="info-group">
              <label>银行名称：</label>
              <span class="value">{{ channelAccount.bankName }}</span>
            </div>
            <div class="info-group">
              <label>银行编号：</label>
              <span class="value">{{ channelAccount.bankCode }}</span>
            </div>
            <div v-if="channelAccount.branchCode" class="info-group">
              <label>分行号：</label>
              <span class="value">{{ channelAccount.branchCode }}</span>
            </div>
            <div class="info-group">
              <label>{{ transactionForm.currency === Currency.HKD ? '港币账号：' : transactionForm.currency ===
                Currency.USD ?
                '美金账号：' : '人民币账号：' }}</label>
              <span class="value copy-enabled"
                @click="getAccountNumberForCurrency(channelAccount, transactionForm.currency || Currency.CNY) !== '暂无' && copyToClipboard(getAccountNumberForCurrency(channelAccount, transactionForm.currency || Currency.CNY))"
                :class="{ 'disabled': getAccountNumberForCurrency(channelAccount, transactionForm.currency || Currency.CNY) === '暂无' }">
                {{ getAccountNumberForCurrency(channelAccount, transactionForm.currency || Currency.CNY) }}
                <el-icon
                  v-if="getAccountNumberForCurrency(channelAccount, transactionForm.currency || Currency.CNY) !== '暂无'">
                  <CopyDocument />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>

        <!-- 上游交易台银行账户信息 -->
        <el-card class="account-info-card" v-if="hasUpstreamAccountInfo">
          <template #header>
            <div class="card-header">
              <span>交易台银行账户</span>
            </div>
          </template>
          <div class="account-info-grid">
            <div class="info-group">
              <label>账户名：</label>
              <span class="value">{{ upstreamAccount.name }}</span>
            </div>
            <div class="info-group">
              <label>银行名称：</label>
              <span class="value">{{ upstreamAccount.bankName }}</span>
            </div>
            <div class="info-group">
              <label>银行编号：</label>
              <span class="value">{{ upstreamAccount.bankCode }}</span>
            </div>
            <div v-if="upstreamAccount.branchCode" class="info-group">
              <label>分行号：</label>
              <span class="value">{{ upstreamAccount.branchCode }}</span>
            </div>
            <div class="info-group">
              <label>{{ transactionForm.currency === Currency.HKD ? '港币账号：' : transactionForm.currency ===
                Currency.USD ?
                '美金账号：' : '人民币账号：' }}</label>
              <span class="value copy-enabled"
                @click="getAccountNumberForCurrency(upstreamAccount, transactionForm.currency || Currency.CNY) !== '暂无' && copyToClipboard(getAccountNumberForCurrency(upstreamAccount, transactionForm.currency || Currency.CNY))"
                :class="{ 'disabled': getAccountNumberForCurrency(upstreamAccount, transactionForm.currency || Currency.CNY) === '暂无' }">
                {{ getAccountNumberForCurrency(upstreamAccount, transactionForm.currency || Currency.CNY) }}
                <el-icon
                  v-if="getAccountNumberForCurrency(upstreamAccount, transactionForm.currency || Currency.CNY) !== '暂无'">
                  <CopyDocument />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 操作表单 -->
    <el-card class="operation-form-card">
      <template #header>
        <div class="card-header">
          <span>出入金申请</span>
        </div>
      </template>
      <el-form :model="transactionForm" label-width="120px" label-position="left" :rules="formRules"
        ref="transactionFormRef">
        <el-form-item label="操作类型" prop="type">
          <el-select v-model="transactionForm.type" placeholder="请选择操作类型" class="full-width">
            <el-option label="入金" :value="ChannelTransactionType.DEPOSIT"></el-option>
            <el-option label="出金" :value="ChannelTransactionType.WITHDRAW"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="币种" prop="currency">
          <el-select v-model="transactionForm.currency" placeholder="请选择币种" class="full-width">
            <el-option label="人民币 (CNY)" :value="Currency.CNY"></el-option>
            <el-option label="港币 (HKD)" :value="Currency.HKD"></el-option>
            <el-option label="美元 (USD)" :value="Currency.USD"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="金额" prop="amount">
          <el-input-number v-model="transactionForm.amount" :min="0" :precision="0" :step="100"
            class="full-width"></el-input-number>
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input v-model="transactionForm.remarks" type="textarea" :rows="3" placeholder="请输入备注信息"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitTransaction" :loading="submitting">提交申请</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 交易记录 -->
    <el-card class="transaction-history-card">
      <template #header>
        <div class="card-header">
          <span>资金操作记录</span>
          <div class="filters">
            <el-button @click="loadTransactions" :loading="transactionsLoading" :icon="Refresh">
            </el-button>
            <el-select v-model="queryStatus" placeholder="状态" clearable class="filter-select"
              @change="loadTransactions">
              <el-option label="全部" value=""></el-option>
              <el-option label="待确认" :value="ChannelTransactionStatus.PENDING"></el-option>
              <el-option label="已确认" :value="ChannelTransactionStatus.CONFIRMED"></el-option>
              <el-option label="已拒绝" :value="ChannelTransactionStatus.REJECTED"></el-option>
              <el-option label="自动处理" :value="ChannelTransactionStatus.AUTO_CONFIRMED"></el-option>
            </el-select>
          </div>
        </div>
      </template>
      <div v-loading="transactionsLoading">
        <el-table :data="transactions" style="width: 100%">
          <el-table-column prop="created_at" label="时间" min-width="160">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" min-width="100">
            <template #default="scope">
              {{ formatType(scope.row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" min-width="120">
            <template #default="scope">
              <span :class="{ 'amount-positive': scope.row.amount > 0, 'amount-negative': scope.row.amount < 0 }">
                {{ scope.row.amount > 0 ? '+' : '' }}{{ scope.row.amount.toFixed(2) }} {{ scope.row.currency }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="100">
            <template #default="scope">
              <el-tag :type="tagType(scope.row.status)">
                {{ formatStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remarks" label="备注" min-width="200"></el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination layout="total, sizes, prev, pager, next" :total="totalTransactions" :page-size="pageSize"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" @current-change="handlePageChange"
            @size-change="handleSizeChange" />
        </div>
      </div>
    </el-card>

    <!-- 货币兑换弹窗 -->
    <el-dialog v-model="exchangeDialogVisible" title="货币兑换" width="500px" :close-on-click-modal="false"
      destroy-on-close>
      <el-form :model="exchangeForm" label-width="100px" :rules="exchangeRules" ref="exchangeFormRef">
        <el-form-item label="源币种" prop="fromCurrency">
          <el-select v-model="exchangeForm.fromCurrency" placeholder="请选择源币种" class="full-width"
            @change="updateExchangeRate">
            <el-option label="人民币 (CNY)" :value="Currency.CNY"></el-option>
            <el-option label="港币 (HKD)" :value="Currency.HKD"></el-option>
            <el-option label="美元 (USD)" :value="Currency.USD"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="目标币种" prop="toCurrency">
          <el-select v-model="exchangeForm.toCurrency" placeholder="请选择目标币种" class="full-width"
            @change="updateExchangeRate">
            <el-option label="人民币 (CNY)" :value="Currency.CNY"></el-option>
            <el-option label="港币 (HKD)" :value="Currency.HKD"></el-option>
            <el-option label="美元 (USD)" :value="Currency.USD"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="金额" prop="amount">
          <el-input-number v-model="exchangeForm.amount" :min="0" :precision="2" :step="100"
            class="full-width"></el-input-number>
        </el-form-item>

        <!-- 汇率显示 -->
        <div class="exchange-rate-info" v-if="currentRate && exchangeForm.fromCurrency && exchangeForm.toCurrency">
          <div class="rate-display">
            <span>当前汇率：1 {{ exchangeForm.fromCurrency }} = {{ currentRate.toFixed(4) }} {{ exchangeForm.toCurrency
            }}</span>
          </div>
          <div class="exchange-result" v-if="exchangeForm.amount > 0">
            <span>兑换结果：{{ exchangeForm.amount }} {{ exchangeForm.fromCurrency }} = {{ (exchangeForm.amount *
              currentRate).toFixed(2) }} {{ exchangeForm.toCurrency }}</span>
          </div>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="exchangeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitExchange" :loading="exchangeSubmitting">
            确认兑换
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { channelFundApi, bankAccountApi } from "@/api";
import {
  Money,
  Refresh,
  InfoFilled,
  Warning,
  CopyDocument,
} from "@element-plus/icons-vue";
import {
  ChannelTransactionType,
  ChannelTransactionStatus,
  Currency,
  getErrorMessage,
} from "@packages/shared";
import type {
  ChannelBalance,
  ChannelTransactionData,
  CreateChannelTransactionRequest,
  ChannelExchangeCurrencyData,
  ExchangeRateResult,
  BankAccount,
} from "@packages/shared";
import { formatMoney } from "@/utils/format";

// 余额状态
const balance = ref<ChannelBalance>({
  channel_id: "",
  balance_cny: 0,
  balance_hkd: 0,
  balance_usd: 0,
  is_locked: false,
});
// 单独存储信用额度
const creditLimit = ref<number>(0);
const balanceLoading = ref(false);

// 计算属性: 判断是否有负余额
const hasNegativeBalance = computed(() => {
  return (
    balance.value.balance_cny < 0 ||
    balance.value.balance_hkd < 0 ||
    balance.value.balance_usd < 0
  );
});

// 交易表单
const transactionFormRef = ref<FormInstance>();
const transactionForm = reactive<Partial<CreateChannelTransactionRequest>>({
  type: ChannelTransactionType.DEPOSIT,
  currency: Currency.CNY,
  amount: 0,
  remarks: "",
});

// 提交状态
const submitting = ref(false);

// 表单验证规则
const formRules = {
  type: [{ required: true, message: "请选择操作类型", trigger: "change" }],
  currency: [{ required: true, message: "请选择币种", trigger: "change" }],
  amount: [{ required: true, message: "请输入金额", trigger: "change" }],
};

// 货币兑换相关
const exchangeDialogVisible = ref(false);
const exchangeFormRef = ref<FormInstance>();
const exchangeSubmitting = ref(false);

// 兑换表单
const exchangeForm = reactive<ChannelExchangeCurrencyData>({
  fromCurrency: Currency.CNY,
  toCurrency: Currency.HKD,
  amount: 0,
});

// 兑换表单验证规则
const exchangeRules = {
  fromCurrency: [
    { required: true, message: "请选择源币种", trigger: "change" },
  ],
  toCurrency: [
    { required: true, message: "请选择目标币种", trigger: "change" },
  ],
  amount: [{ required: true, message: "请输入金额", trigger: "change" }],
};

// 汇率相关
const currentRate = ref<number | null>(null);
const exchangeRates = ref<ExchangeRateResult | null>(null);
const exchangeRatesLoading = ref(false);

// 交易记录
const transactions = ref<ChannelTransactionData[]>([]);
const transactionsLoading = ref(false);
const totalTransactions = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const queryStatus = ref<ChannelTransactionStatus | "">("");

// 银行账户信息 - 通过bankAccountApi获取
const channelAccount = reactive<BankAccount>({
  name: "",
  bankName: "",
  bankCode: "",
  accountNumber: "",
  accountNumberHKD: "",
  accountNumberUSD: "",
  branchCode: "",
});

// 上游交易台银行账户信息 - 通过API获取
const upstreamAccount = reactive<BankAccount>({
  name: "",
  bankName: "",
  bankCode: "",
  accountNumber: "",
  accountNumberHKD: "",
  accountNumberUSD: "",
  branchCode: "",
});

// 检查是否有通道银行账户信息
const hasChannelAccountInfo = computed(() => {
  return !!(
    channelAccount.name ||
    channelAccount.bankName ||
    channelAccount.bankCode ||
    channelAccount.accountNumber ||
    channelAccount.accountNumberHKD ||
    channelAccount.accountNumberUSD ||
    channelAccount.branchCode
  );
});

// 检查是否有交易台银行账户信息
const hasUpstreamAccountInfo = computed(() => {
  return !!(
    upstreamAccount.name ||
    upstreamAccount.bankName ||
    upstreamAccount.bankCode ||
    upstreamAccount.accountNumber ||
    upstreamAccount.accountNumberHKD ||
    upstreamAccount.accountNumberUSD ||
    upstreamAccount.branchCode
  );
});

// 根据货币获取对应的账号
const getAccountNumberForCurrency = (
  account: {
    accountNumber?: string;
    accountNumberHKD?: string;
    accountNumberUSD?: string;
  },
  currency: Currency,
) => {
  switch (currency) {
    case Currency.HKD:
      return account.accountNumberHKD || account.accountNumber || "暂无";
    case Currency.USD:
      return account.accountNumberUSD || account.accountNumber || "暂无";
    default:
      return account.accountNumber || "暂无";
  }
};

// 打开兑换弹窗
const openExchangeDialog = () => {
  exchangeDialogVisible.value = true;

  // 重置兑换表单
  exchangeForm.amount = 0;
  exchangeForm.fromCurrency = Currency.CNY;
  exchangeForm.toCurrency = Currency.HKD;

  // 加载汇率数据
  loadExchangeRates();
};

// 加载汇率数据
const loadExchangeRates = async () => {
  exchangeRatesLoading.value = true;
  try {
    const rates = await channelFundApi.exchange.getRates();
    exchangeRates.value = rates;
    updateExchangeRate();
  } catch (error) {
    console.error("加载汇率数据失败", error);
    ElMessage.error("加载汇率数据失败，请稍后重试");
  } finally {
    exchangeRatesLoading.value = false;
  }
};

// 计算并更新当前兑换汇率
const updateExchangeRate = () => {
  if (
    !exchangeRates.value ||
    !exchangeForm.fromCurrency ||
    !exchangeForm.toCurrency
  ) {
    currentRate.value = null;
    return;
  }

  // 相同币种不需要兑换
  if (exchangeForm.fromCurrency === exchangeForm.toCurrency) {
    currentRate.value = 1;
    return;
  }

  // 计算汇率
  const rates = exchangeRates.value;

  if (exchangeForm.fromCurrency === Currency.CNY) {
    currentRate.value =
      exchangeForm.toCurrency === Currency.HKD
        ? 1 / rates.HKD_CNY
        : 1 / rates.USD_CNY;
  } else if (exchangeForm.toCurrency === Currency.CNY) {
    currentRate.value =
      exchangeForm.fromCurrency === Currency.HKD
        ? rates.HKD_CNY
        : rates.USD_CNY;
  } else if (
    exchangeForm.fromCurrency === Currency.HKD &&
    exchangeForm.toCurrency === Currency.USD
  ) {
    currentRate.value = rates.HKD_CNY / rates.USD_CNY;
  } else {
    currentRate.value = rates.USD_CNY / rates.HKD_CNY;
  }
};

// 监听兑换表单变化，更新汇率
watch([() => exchangeForm.fromCurrency, () => exchangeForm.toCurrency], () => {
  updateExchangeRate();
});

// 监听状态筛选变化，重新加载交易记录
watch(
  () => queryStatus.value,
  () => {
    currentPage.value = 1; // 重置为第一页
    loadTransactions();
  },
);

// 提交兑换申请
const submitExchange = async () => {
  if (!exchangeFormRef.value) return;

  await exchangeFormRef.value.validate(async (valid) => {
    if (valid) {
      exchangeSubmitting.value = true;
      try {
        // 确保所有必要字段都已填写
        if (
          !exchangeForm.fromCurrency ||
          !exchangeForm.toCurrency ||
          !exchangeForm.amount
        ) {
          ElMessage.error("请填写所有必填字段");
          return;
        }

        // 判断源币种和目标币种是否相同
        if (exchangeForm.fromCurrency === exchangeForm.toCurrency) {
          ElMessage.error("源币种和目标币种不能相同");
          return;
        }

        await channelFundApi.exchange.exchangeCurrency(exchangeForm);
        ElMessage.success("货币兑换成功");

        // 关闭弹窗
        exchangeDialogVisible.value = false;

        // 刷新数据
        loadTransactions();
        loadBalance();
      } catch (error) {
        console.error("兑换申请失败", error);
        ElMessage.error(`兑换申请失败：${getErrorMessage(error)}`);
      } finally {
        exchangeSubmitting.value = false;
      }
    }
  });
};

// 复制到剪贴板功能
const copyToClipboard = async (text: string) => {
  if (!text) return;

  try {
    await navigator.clipboard.writeText(text);
    ElMessage.success("已复制到剪贴板");
  } catch (err) {
    console.error("复制失败", err);
    ElMessage.error("复制失败");
  }
};

// 格式化函数
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const formatType = (type: ChannelTransactionType) => {
  const typeMap: Partial<Record<ChannelTransactionType, string>> = {
    [ChannelTransactionType.DEPOSIT]: "入金",
    [ChannelTransactionType.WITHDRAW]: "出金",
    [ChannelTransactionType.USER_ORDER]: "用户下单",
    [ChannelTransactionType.USER_EXECUTE]: "用户结算",
    [ChannelTransactionType.EXCHANGE]: "货币兑换",
  };
  return typeMap[type] || type;
};

const formatStatus = (status: ChannelTransactionStatus) => {
  const statusMap: Partial<Record<ChannelTransactionStatus, string>> = {
    [ChannelTransactionStatus.PENDING]: "待确认",
    [ChannelTransactionStatus.CONFIRMED]: "已确认",
    [ChannelTransactionStatus.REJECTED]: "已拒绝",
    [ChannelTransactionStatus.AUTO_CONFIRMED]: "自动处理",
  };
  return statusMap[status] || status;
};

const tagType = (status: ChannelTransactionStatus) => {
  const typeMap: Partial<Record<ChannelTransactionStatus, string>> = {
    [ChannelTransactionStatus.PENDING]: "warning",
    [ChannelTransactionStatus.CONFIRMED]: "success",
    [ChannelTransactionStatus.REJECTED]: "danger",
  };
  return typeMap[status] || "info";
};

// 加载余额
const loadBalance = async () => {
  balanceLoading.value = true;
  try {
    const data = await channelFundApi.balance.get();
    if (data) {
      balance.value = data;

      // 获取最新的信用额度 - 单独请求
      try {
        const creditLimitData = await channelFundApi.creditLimit.get();
        if (creditLimitData?.creditLimit) {
          creditLimit.value = creditLimitData.creditLimit;
        }
      } catch (error) {
        console.error("加载预授权额度失败", error);
      }
    }
  } catch (error) {
    console.error("加载余额失败", error);
    ElMessage.error("加载余额失败，请稍后重试");
  } finally {
    balanceLoading.value = false;
  }
};

// 加载交易记录
const loadTransactions = async () => {
  transactionsLoading.value = true;
  try {
    const params: {
      page: number;
      size: number;
      status?: ChannelTransactionStatus;
    } = {
      page: currentPage.value,
      size: pageSize.value,
    };

    if (queryStatus.value) {
      params.status = queryStatus.value;
    }

    const result = await channelFundApi.transaction.getAll(params);
    if (result) {
      transactions.value = result.items;
      totalTransactions.value = result.total;
    }
  } catch (error) {
    console.error("加载交易记录失败", error);
    ElMessage.error("加载交易记录失败，请稍后重试");
  } finally {
    transactionsLoading.value = false;
  }
};

// 提交交易申请
const submitTransaction = async () => {
  if (!transactionFormRef.value) return;

  await transactionFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true;
      try {
        // 确保所有必要字段都已填写
        if (
          !transactionForm.type ||
          !transactionForm.currency ||
          !transactionForm.amount
        ) {
          ElMessage.error("请填写所有必填字段");
          return;
        }

        const createData: CreateChannelTransactionRequest = {
          type: transactionForm.type,
          currency: transactionForm.currency,
          amount: transactionForm.amount,
          remarks: transactionForm.remarks,
        };

        await channelFundApi.transaction.create(createData);
        ElMessage.success("申请提交成功，等待审核");
        resetForm();
        loadTransactions(); // 刷新交易列表
        loadBalance(); // 刷新余额信息
      } catch (error) {
        console.error("提交申请失败", error);
        ElMessage.error(`提交申请失败：${getErrorMessage(error)}`);
      } finally {
        submitting.value = false;
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  if (transactionFormRef.value) {
    transactionFormRef.value.resetFields();
  }
};

// 切换页码
const handlePageChange = (page: number) => {
  currentPage.value = page;
  loadTransactions();
};

// 新增：切换每页条数
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadTransactions();
};

// 加载通道银行账户信息
const loadChannelAccount = async () => {
  try {
    const data = await bankAccountApi.getBankAccount();
    if (data) {
      // 使用Object.assign更新reactive对象
      Object.assign(channelAccount, data);
    }
  } catch (error) {
    console.error("加载银行账户信息失败", error);
    ElMessage.error("加载银行账户信息失败");
  }
};

// 加载交易台银行账户信息
const loadPlatformAccount = async () => {
  try {
    const data = await bankAccountApi.getPlatformBankAccount();
    if (data) {
      // 使用Object.assign更新reactive对象
      Object.assign(upstreamAccount, data);
    }
  } catch (error) {
    console.error("加载交易台银行账户信息失败", error);
    ElMessage.error("加载交易台银行账户信息失败");
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadBalance();
  loadTransactions();
  loadExchangeRates();
  loadChannelAccount();
  loadPlatformAccount();
});
</script>

<style scoped>
.header-container h2 {
  margin: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.balance-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 16px;
}

.balance-item .currency {
  color: var(--el-text-color-secondary);
}

.balance-item .amount {
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.balance-item .amount.negative {
  color: var(--el-color-danger);
}

.full-width {
  width: 100%;
}

.filters {
  display: flex;
  gap: 10px;
}

.filter-select {
  min-width: 150px;
}

:deep(.el-select .el-input__wrapper) {
  background-color: var(--el-bg-color-overlay);
}

/* 银行账户信息样式 */
.two-cards-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.account-info-card {
  flex: 1;
}

.account-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-top: 10px;
}

.info-group {
  display: grid;
  grid-template-columns: 120px 1fr;
  align-items: center;
}

.info-group label {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.info-group .value {
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.copy-enabled {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: var(--el-color-primary);
  transition: opacity 0.2s;
}

.copy-enabled:hover {
  opacity: 0.8;
}

.copy-enabled.disabled {
  cursor: default;
  color: var(--el-text-color-disabled);
}

.credit-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed var(--el-border-color-lighter);
}

.credit-limit {
  color: var(--el-text-color-secondary);
}

.info-icon {
  margin-left: 10px;
  font-size: 16px;
  color: var(--el-color-info);
  cursor: help;
  vertical-align: middle;
  position: relative;
  top: -1px;
}

.locked-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.warning-info {
  margin-top: 15px;
}

.exchange-rate-info {
  margin: 15px 0;
  padding: 15px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px dashed var(--el-border-color);
  text-align: center;
}

.rate-display {
  font-size: 16px;
  color: var(--el-text-color-primary);
  margin-bottom: 10px;
}

.exchange-result {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  font-weight: bold;
}

.amount-positive {
  color: var(--el-color-success);
  font-weight: bold;
}

.amount-negative {
  color: var(--el-color-danger);
  font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .two-cards-row {
    grid-template-columns: 1fr;
  }

  .info-group {
    grid-template-columns: 100px 1fr;
  }
}
</style>