import { ENV } from "@/config/configManager.js";
import crypto from "node:crypto";
import bcrypt from "bcryptjs";

const ENCRYPTION_KEY = ENV.ENCRYPTION_KEY as string;
const SALT_ROUNDS = 12;

// 密码哈希 (60字符)
export async function hashPassword(password: string): Promise<string> {
	return bcrypt.hash(password, SALT_ROUNDS);
}

export async function verifyPassword(
	password: string,
	hash: string,
): Promise<boolean> {
	return bcrypt.compare(password, hash);
}

// 确定性 (ECB) 加密非高敏感数据 (加密文本: 2N 字符)
// 低于16字节的会被填充到16字节
export function encrypt(text: string): string {
	const cipher = crypto.createCipheriv(
		"aes-256-ecb",
		Buffer.from(ENCRYPTION_KEY),
		null,
	);
	let encrypted = cipher.update(text, "utf8", "hex");
	encrypted += cipher.final("hex");
	return encrypted;
}

export function decrypt(encrypted: string): string {
	const decipher = crypto.createDecipheriv(
		"aes-256-ecb",
		Buffer.from(ENCRYPTION_KEY),
		null,
	);
	let decrypted = decipher.update(encrypted, "hex", "utf8");
	decrypted += decipher.final("utf8");
	return decrypted;
}
