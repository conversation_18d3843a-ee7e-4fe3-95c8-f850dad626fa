import { defineConfig } from "@rsbuild/core";
import { pluginVue } from "@rsbuild/plugin-vue";
import { pluginSass } from "@rsbuild/plugin-sass";
export default defineConfig({
	plugins: [pluginVue(), pluginSass()],
	html: {
		template: "./static/index.html",
		title: "交易平台",
		favicon: "./src/assets/favicon/default.svg",
		mountId: "app",
	},
	dev: {
		hmr: true,
	},
	server: {
		port: 3001, // 让出服务器端口
		proxy: {
			"/api": {
				target: "http://localhost:3000",
				changeOrigin: true,
			},
			"/ws": {
				target: "ws://localhost:3000",
				changeOrigin: true,
				ws: true,
			},
		},
	},
	performance: {
		removeConsole: true,
		preload: {
			include: [/vendors\..*\.js$/], // 只预加载 vendors js 文件（约2MB）
		},
	},
	tools: {
		bundlerChain: (chain) => {
			chain.module
				.rule("svg")
				.test(/\.svg$/)
				.type("asset/source") // 使用 asset/source 来获取原始内容
				.exclude.add(/node_modules/);

			chain.optimization.splitChunks({
				// 应用结构使得大部分依赖在初始加载时就会被使用
				cacheGroups: {
					vendor: {
						test: /[\\/]node_modules[\\/]/,
						name: "vendors", // 将所有第三方依赖打包到单一文件中便于预加载，减少HTTP请求
						chunks: "all",
						priority: 10,
						reuseExistingChunk: true,
					},
				},
				minSize: 20000,
				minChunks: 1,
			});
		},
	},
	output: {
		minify: true, // 使用 Rsbuild 内置的压缩配置
		filenameHash: true, // 确保文件名包含内容哈希，在代码变动时更新缓存
	},
	source: {
		assetsInclude: /\.pdf$/,
	},
});
