import { Router } from "express";
import { wrapRoute } from "@/utils/routeWrapper.js";
import * as siteConfigService from "@/services/admin/siteConfigService.js";

const router = Router();

/**
 * @api {get} /api/site-config 获取网站配置
 * @apiName GetSiteConfig
 * @apiGroup SiteConfig
 * @apiDescription 获取网站基本配置信息
 */
router.get(
	"/",
	wrapRoute(async (_req, res) => {
		try {
			const config = await siteConfigService.getSiteConfig();

			// 只返回客户端需要的配置信息
			const clientConfig = {
				clientSiteName: config.clientSiteName,
				companyLegalName: config.companyLegalName,
				companyShortName: config.companyShortName,
				faviconId: config.faviconId,
				logoId: config.logoId,
				clientPrimaryColor: config.clientPrimaryColor,
				adminPrimaryColor: config.adminPrimaryColor,
			};

			res.status(200).json(clientConfig);
		} catch (error) {
			console.error("Failed to get site config:", error);
			res.status(500).json({ error: "Failed to get site configuration" });
		}
	}),
);

/**
 * @api {get} /api/site-config/asset/:assetId 获取资源
 * @apiName GetAsset
 * @apiGroup SiteConfig
 * @apiDescription 获取图标、logo等资源
 */
router.get(
	"/asset/:assetId",
	wrapRoute(async (req, res) => {
		try {
			const assetId = req.params.assetId;
			const asset = await siteConfigService.getAsset(assetId);

			// 设置合适的内容类型和缓存头
			res.set("Content-Type", asset.mimeType);
			res.set("Cache-Control", "public, max-age=86400"); // 24小时缓存

			// 返回资源内容
			res.send(asset.content);
		} catch (error) {
			console.error("Error getting asset:", error);
			res.status(404).send("Resource not found");
		}
	}),
);

export default router;
