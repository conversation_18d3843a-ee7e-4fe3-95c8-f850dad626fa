#!/usr/bin/env tsx

/**
 * INK数据同步系统优化功能验证脚本
 * 验证2025年5月的主要优化改进
 * 运行命令：
 * pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\queue\ink\test-optimizations.ts
 */

import { inkSyncConfig } from './config.js';
import { redisManager } from './redisManager.js';
import { lockManager } from './distributedLockManager.js';
import { TRADING_TIME } from './constants.js';
import { fileURLToPath } from 'node:url';

// 颜色输出函数
const colors = {
  green: (text: string) => `\x1b[32m${text}\x1b[0m`,
  red: (text: string) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text: string) => `\x1b[33m${text}\x1b[0m`,
  blue: (text: string) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text: string) => `\x1b[36m${text}\x1b[0m`,
};

function logSuccess(message: string) {
  console.log(`${colors.green('✅')} ${message}`);
}

function logError(message: string) {
  console.log(`${colors.red('❌')} ${message}`);
}

function logInfo(message: string) {
  console.log(`${colors.blue('ℹ️')} ${message}`);
}

function logWarning(message: string) {
  console.log(`${colors.yellow('⚠️')} ${message}`);
}

/**
 * 测试交易时间优化
 */
function testTradingTimeOptimizations() {
  console.log(colors.cyan('\n=== 测试交易时间优化 ==='));
  
  try {
    // 测试配置是否正确加载
    const morningStart = inkSyncConfig.morningTradingStart;
    const morningEnd = inkSyncConfig.morningTradingEnd;
    const afternoonStart = inkSyncConfig.afternoonTradingStart;
    const afternoonEnd = inkSyncConfig.afternoonTradingEnd;
    
    logInfo(`上午交易时段: ${morningStart} - ${morningEnd}`);
    logInfo(`下午交易时段: ${afternoonStart} - ${afternoonEnd}`);
    
    // 验证午休时间被正确排除
    if (morningEnd === 1130 && afternoonStart === 1300) {
      logSuccess('午休时间(11:30-13:00)被正确排除');
    } else {
      logError('午休时间配置不正确');
    }
    
    // 测试不同时间点的判断
    const testTimes = [
      { time: 900, period: '盘前', expected: false },
      { time: 1000, period: '上午交易', expected: true },
      { time: 1200, period: '午休', expected: false },
      { time: 1400, period: '下午交易', expected: true },
      { time: 1600, period: '盘后', expected: false },
    ];
    
    for (const test of testTimes) {
      const morningTrading = test.time >= morningStart && test.time < morningEnd;
      const afternoonTrading = test.time >= afternoonStart && test.time < afternoonEnd;
      const isTrading = morningTrading || afternoonTrading;
      
      if (isTrading === test.expected) {
        logSuccess(`${test.period}(${test.time}): 判断正确`);
      } else {
        logError(`${test.period}(${test.time}): 判断错误，期望${test.expected}，实际${isTrading}`);
      }
    }
    
  } catch (error) {
    logError(`交易时间测试失败: ${(error as Error).message}`);
  }
}

/**
 * 测试执行记录功能
 */
async function testExecutionRecords() {
  console.log(colors.cyan('\n=== 测试执行记录功能 ==='));
  
  try {
    // 测试记录执行
    await redisManager.recordExecution('test-task', {
      testData: 'optimization-test',
      timestamp: Date.now()
    });
    logSuccess('执行记录创建成功');
    
    // 测试查询执行历史
    const history = await redisManager.getExecutionHistory('test-task', 5);
    if (history.length > 0) {
      logSuccess(`执行历史查询成功，找到${history.length}条记录`);
      logInfo(`最近执行: ${history[0].date}`);
    } else {
      logWarning('未找到执行历史记录');
    }
    
    // 测试最近执行检查
    const hasRecent = await redisManager.hasRecentExecution('test-task');
    if (hasRecent) {
      logSuccess('最近执行检查功能正常');
    } else {
      logWarning('最近执行检查未检测到刚才的记录');
    }
    
  } catch (error) {
    logError(`执行记录测试失败: ${(error as Error).message}`);
  }
}

/**
 * 测试分布式锁优化
 */
async function testDistributedLockOptimizations() {
  console.log(colors.cyan('\n=== 测试分布式锁优化 ==='));
  
  try {
    const testLockKey = 'test-optimization-lock';
    
    // 测试获取锁
    const acquired = await lockManager.acquireLockWithRenewal(testLockKey, 60, 1000);
    if (acquired) {
      logSuccess('分布式锁获取成功');
      
      // 测试锁计数
      const lockCount = lockManager.getActiveLockCount();
      logInfo(`当前活跃锁数量: ${lockCount}`);
      
      // 测试释放锁
      await lockManager.releaseLock(testLockKey);
      logSuccess('分布式锁释放成功');
      
      const newLockCount = lockManager.getActiveLockCount();
      if (newLockCount < lockCount) {
        logSuccess('锁计数更新正确');
      } else {
        logWarning('锁计数可能未正确更新');
      }
      
    } else {
      logError('分布式锁获取失败');
    }
    
  } catch (error) {
    logError(`分布式锁测试失败: ${(error as Error).message}`);
  }
}

/**
 * 测试常量优化
 */
function testConstantOptimizations() {
  console.log(colors.cyan('\n=== 测试常量优化 ==='));
  
  try {
    // 验证新的交易时间常量
    logInfo(`上午交易时段: ${TRADING_TIME.MORNING_START} - ${TRADING_TIME.MORNING_END}`);
    logInfo(`下午交易时段: ${TRADING_TIME.AFTERNOON_START} - ${TRADING_TIME.AFTERNOON_END}`);
    logInfo(`盘前时段: ${TRADING_TIME.PREMARKET_START} - ${TRADING_TIME.PREMARKET_END}`);
    
    // 验证兼容性常量
    if (TRADING_TIME.TRADING_START === 925 && TRADING_TIME.TRADING_END === 1500) {
      logSuccess('兼容性常量保持正确');
    } else {
      logError('兼容性常量配置错误');
    }
    
    logSuccess('常量配置验证完成');
    
  } catch (error) {
    logError(`常量测试失败: ${(error as Error).message}`);
  }
}

/**
 * 测试配置管理优化
 */
function testConfigurationOptimizations() {
  console.log(colors.cyan('\n=== 测试配置管理优化 ==='));
  
  try {
    // 测试配置摘要
    const configSummary = inkSyncConfig.getConfigSummary();
    logInfo('配置摘要获取成功:');
    console.log(JSON.stringify(configSummary, null, 2));
    
    // 测试新的交易时间配置
    const expectedKeys = [
      'morningTradingStart',
      'morningTradingEnd', 
      'afternoonTradingStart',
      'afternoonTradingEnd'
    ];
    
    let hasAllKeys = true;
    for (const key of expectedKeys) {
      if (!(key in configSummary)) {
        logError(`缺少配置项: ${key}`);
        hasAllKeys = false;
      }
    }
    
    if (hasAllKeys) {
      logSuccess('所有新配置项都已正确加载');
    }
    
  } catch (error) {
    logError(`配置管理测试失败: ${(error as Error).message}`);
  }
}

/**
 * 主测试函数
 */
async function runOptimizationTests() {
  console.log(colors.cyan('🚀 INK数据同步系统优化功能验证开始...\n'));
  
  try {
    // 1. 测试交易时间优化
    testTradingTimeOptimizations();
    
    // 2. 测试执行记录功能
    await testExecutionRecords();
    
    // 3. 测试分布式锁优化
    await testDistributedLockOptimizations();
    
    // 4. 测试常量优化
    testConstantOptimizations();
    
    // 5. 测试配置管理优化
    testConfigurationOptimizations();
    
    console.log(colors.cyan('\n🎉 优化功能验证完成!'));
    console.log(colors.green('\n主要优化改进验证通过:'));
    console.log(colors.green('✓ 精确交易时间判断（排除午休）'));
    console.log(colors.green('✓ 执行记录追踪机制'));
    console.log(colors.green('✓ 分布式锁保护优化'));
    console.log(colors.green('✓ 配置管理增强'));
    console.log(colors.green('✓ 常量结构优化'));
    
  } catch (error) {
    logError(`验证过程出错: ${(error as Error).message}`);
    process.exit(1);
  }
}

// 运行测试
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  runOptimizationTests().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
} 