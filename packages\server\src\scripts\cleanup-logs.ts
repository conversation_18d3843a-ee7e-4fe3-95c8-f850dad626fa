#!/usr/bin/env tsx

/**
 * 临时日志清理脚本
 * 可以立即执行，清理大于指定大小的日志文件
 */

import { promises as fs } from "node:fs";
import { join } from "node:path";

// 使用与项目中其他地方相同的逻辑确定目录位置
const cwd = process.cwd();
const isInRootDir = !cwd.endsWith("server");

// 保持与 logger.ts 中 logDir = "logs" 的一致性
const LOG_DIR = isInRootDir 
	? join(cwd, "logs")  // 在根目录执行时，直接使用 logs 文件夹
	: join(cwd, "../../logs");  // 在 server 目录执行时，向上两级到根目录的 logs

const MAX_FILE_SIZE_MB = 50; // 超过50MB的文件将被删除
const MAX_FILES = 10; // 最多保留10个文件
const MAX_AGE_DAYS = 7; // 超过7天的文件将被删除

interface FileInfo {
	name: string;
	path: string;
	mtime: Date;
	sizeMB: number;
	ageDays: number;
}

async function cleanupLogs(): Promise<void> {
	console.log("🧹 开始清理日志文件...");
	console.log(`📁 日志目录: ${LOG_DIR}`);
	console.log(`📍 当前工作目录: ${cwd} (${isInRootDir ? '根目录' : 'server目录'})`);
	
	try {
		// 检查日志目录是否存在
		try {
			await fs.access(LOG_DIR);
		} catch {
			console.log("❌ 日志目录不存在");
			return;
		}

		// 读取目录中的所有文件
		const files = await fs.readdir(LOG_DIR);
		
		// 过滤出日志文件
		const logFiles = files.filter(file => 
			file.endsWith('.log') || 
			/\.\d{4}-\d{2}-\d{2}\.log$/.test(file) ||
			/\.log\.\d+$/.test(file)
		);

		if (logFiles.length === 0) {
			console.log("✅ 没有找到日志文件");
			return;
		}

		console.log(`📋 发现 ${logFiles.length} 个日志文件`);

		// 获取文件统计信息
		const fileStats: FileInfo[] = await Promise.all(
			logFiles.map(async (file): Promise<FileInfo> => {
				const filePath = join(LOG_DIR, file);
				const stat = await fs.stat(filePath);
				const sizeMB = stat.size / (1024 * 1024);
				const ageDays = (Date.now() - stat.mtime.getTime()) / (24 * 60 * 60 * 1000);
				
				return {
					name: file,
					path: filePath,
					mtime: stat.mtime,
					sizeMB: sizeMB,
					ageDays: ageDays,
				};
			})
		);

		// 显示文件信息
		console.log("\n📊 文件统计:");
		let totalSize = 0;
		fileStats.forEach(file => {
			totalSize += file.sizeMB;
			console.log(`  ${file.name}: ${file.sizeMB.toFixed(2)}MB (${file.ageDays.toFixed(1)}天前)`);
		});
		console.log(`📦 总大小: ${totalSize.toFixed(2)}MB\n`);

		// 按修改时间排序，最旧的在前
		fileStats.sort((a, b) => a.mtime.getTime() - b.mtime.getTime());

		// 确定要删除的文件
		const filesToDelete = fileStats.filter((file, index) => {
			// 删除条件：
			// 1. 文件大小超过限制
			// 2. 文件年龄超过限制
			// 3. 文件数量超过限制（保留最新的）
			return file.sizeMB > MAX_FILE_SIZE_MB || 
				   file.ageDays > MAX_AGE_DAYS ||
				   index < (fileStats.length - MAX_FILES);
		});

		if (filesToDelete.length === 0) {
			console.log("✅ 所有文件都在合理范围内，无需清理");
			return;
		}

		console.log(`🗑️  需要删除 ${filesToDelete.length} 个文件:`);
		filesToDelete.forEach(file => {
			const reason = file.sizeMB > MAX_FILE_SIZE_MB ? "过大" : 
						  file.ageDays > MAX_AGE_DAYS ? "过旧" : "超量";
			console.log(`  ❌ ${file.name} (${file.sizeMB.toFixed(2)}MB, ${reason})`);
		});

		// 执行删除
		let deletedCount = 0;
		let freedSpace = 0;
		
		for (const file of filesToDelete) {
			try {
				await fs.unlink(file.path);
				deletedCount++;
				freedSpace += file.sizeMB;
				console.log(`  ✅ 已删除: ${file.name}`);
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : String(error);
				console.log(`  ❌ 删除失败: ${file.name} - ${errorMessage}`);
			}
		}

		console.log(`\n🎉 清理完成!`);
		console.log(`📊 删除了 ${deletedCount} 个文件`);
		console.log(`💾 释放了 ${freedSpace.toFixed(2)}MB 空间`);
		
		// 显示剩余文件
		const remainingFiles = fileStats.filter(f => !filesToDelete.includes(f));
		const remainingSize = remainingFiles.reduce((sum, f) => sum + f.sizeMB, 0);
		console.log(`📁 剩余 ${remainingFiles.length} 个文件，总大小: ${remainingSize.toFixed(2)}MB`);

	} catch (error) {
		console.error("❌ 清理过程中发生错误:", error);
		process.exit(1);
	}
}

// 执行清理
cleanupLogs().then(() => {
	console.log("🏁 脚本执行完成");
	process.exit(0);
}).catch((error) => {
	console.error("💥 脚本执行失败:", error);
	process.exit(1);
}); 