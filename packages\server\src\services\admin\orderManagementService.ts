import * as Order from "@/models/trade/order.js";
import * as Position from "@/models/position.js";
import * as User from "@/models/user.js";
import { withTransaction } from "@/core/dbTxnManager.js";
import { fetchStockList } from "@/financeUtils/marketData.js";
import { AppError } from "@/core/appError.js";
import type {
	OrderData,
	OrderModification,
	BuyRequest,
	StructureType,
} from "@packages/shared";
import {
	TradeDirection,
	OrderType,
	OrderModificationType,
	OrderStatus,
} from "@packages/shared";
import { executeOrder } from "@/services/trade/executeOrder.js";
import logger from "@/utils/logger.js";

export async function getOrderModifications(
	page: number,
	pageSize: number,
	filters?: {
		trade_no?: string;
	},
): Promise<{ items: OrderModification[]; total: number }> {
	return Order.getModifications(page, pageSize, filters);
}

export async function createOrderManually(
	data: {
		user_id: number;
		ts_code: string;
		entry_price: number;
		scale: number;
		term: number;
		quote: number;
		structure: StructureType;
		comment: string;
		quote_provider: string;
		quote_diff: number;
	},
	admin_id: number,
): Promise<OrderData> {
	// 验证 ts_code 是否存在
	const stockList = await fetchStockList();
	const stockExists = stockList.some((stock) => stock.ts_code === data.ts_code);
	if (!stockExists) {
		throw AppError.create(
			"INVALID_TS_CODE",
			`Invalid ts_code: ${data.ts_code}`,
		);
	}

	const user = await User.findById(data.user_id);
	if (!user) {
		throw AppError.create("USER_NOT_FOUND", `User not found: ${data.user_id}`);
	}

	// 构造买入请求
	const buyRequest: BuyRequest = {
		type: OrderType.MARKET,
		direction: TradeDirection.BUY,
		user_id: data.user_id,
		ts_code: data.ts_code,
		scale: data.scale,
		quote: data.quote,
		structure: data.structure,
		term: data.term,
		entry_price: data.entry_price,
		quote_provider: data.quote_provider,
		quote_diff: data.quote_diff,
	};

	// 执行订单，使用管理员输入的价格
	const order = await executeOrder(buyRequest, {
		vwapPrice: data.entry_price,
	});

	// 记录修改历史
	try {
		await Order.createModification({
			trade_no: order.trade_no,
			user_id: data.user_id,
			type: OrderModificationType.MANUAL_CREATE,
			data: {
				newValue: data,
			},
			comment: data.comment,
			admin_id,
		});
	} catch (error) {
		logger.error(
			`Failed to create order modification: ${error instanceof Error ? error.message : String(error)}`,
		);
	}

	return order;
}

export async function updateOrderManually(
	data: {
		trade_no: string;
		newValue: Partial<OrderData>;
		comment: string;
	},
	admin_id: number,
): Promise<OrderData> {
	return withTransaction(async (tx) => {
		// 获取原始订单数据
		const originalOrder = await Order.findByTradeNo(data.trade_no, tx);

		// 构造oldValue，只包含将被修改的字段的原始值
		const oldValueRaw: Record<string, unknown> = {};
		for (const key of Object.keys(data.newValue)) {
			const k = key as keyof OrderData;
			oldValueRaw[key] = originalOrder[k];
		}
		const oldValue = oldValueRaw as Partial<OrderData>;

		// 更新订单
		const updatedOrder = await Order.update(data.trade_no, data.newValue, tx);

		// 更新持仓
		await Position.updateByTradeNo(updatedOrder.trade_no, data.newValue, tx);

		// 记录修改历史
		await Order.createModification(
			{
				trade_no: data.trade_no,
				user_id: originalOrder.user_id,
				type: OrderModificationType.ORDER_CHANGE,
				data: {
					oldValue,
					newValue: { ...originalOrder, ...data.newValue },
				},
				comment: data.comment,
				admin_id,
			},
			tx,
		);

		return updatedOrder;
	});
}

export async function closeOrderManually(
	data: {
		trade_no: string;
		settle_price: number;
		comment: string;
	},
	admin_id: number,
): Promise<OrderData> {
	return withTransaction(async (tx) => {
		// 获取原始订单数据
		const originalOrder = await Order.findByTradeNo(data.trade_no, tx);

		// 检查订单是否已经结束
		if (originalOrder.status === OrderStatus.SOLD) {
			throw AppError.create("ORDER_ALREADY_CLOSED", "Order is already closed");
		}

		// 构造更新数据
		const updateData: Partial<OrderData> = {
			status: OrderStatus.SOLD,
			settle_price: data.settle_price,
		};

		// 更新订单
		const updatedOrder = await Order.update(data.trade_no, updateData, tx);

		// 更新持仓
		await Position.updateByTradeNo(updatedOrder.trade_no, updateData, tx);

		// 记录修改历史
		await Order.createModification(
			{
				trade_no: data.trade_no,
				user_id: originalOrder.user_id,
				type: OrderModificationType.MANUAL_CLOSE,
				data: {
					oldValue: {
						status: originalOrder.status,
						settle_price: originalOrder.settle_price,
					},
					newValue: {
						status: OrderStatus.SOLD,
						settle_price: data.settle_price,
					},
				},
				comment: data.comment,
				admin_id,
			},
			tx,
		);

		return updatedOrder;
	});
}
