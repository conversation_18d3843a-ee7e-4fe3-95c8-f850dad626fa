import type { StructureType } from "./inquiry.js";

export interface PositionData {
	trade_no: string;
	user_id: number;
	ts_code: string;
	entry_price: number;
	exercise_price: number;
	structure: StructureType;
	scale: number;
	term: number;
	quote: number;
	quote_provider: string;
	quote_diff: number;
	expiry_date: string;
	expiry_date_confirmed: boolean;
	created_at: string;
}

export interface PositionChangeData {
	user_id: number;
	trade_no: string;
	ts_code: string;
	scale_change: number;
	structure: StructureType;
	entry_price: number;
	exercise_price: number;
	term: number;
	quote: number;
	expiry_date: string;
	expiry_date_confirmed: boolean;
	quote_provider?: string;
	quote_diff?: number;
}
