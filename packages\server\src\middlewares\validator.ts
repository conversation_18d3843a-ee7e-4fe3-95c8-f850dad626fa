import { AppError } from "@/core/appError.js";
import type { Request, Response, NextFunction } from "express";
import { TradeDirection, OrderType, Currency } from "@packages/shared";
import type { BuyRequest, SellRequest } from "@packages/shared";

export function validateOrderRequest(
	req: Request,
	_res: Response,
	next: NextFunction,
) {
	const order = req.body;

	// "Missing required fields" -> 可以使用 BAD_REQUEST
	if (!order.direction || !order.type || !order.ts_code) {
		return next(AppError.create("BAD_REQUEST", "Missing required fields"));
	}

	// "Invalid direction" -> 可以使用 INVALID_TRADE_DIRECTION
	if (!Object.values(TradeDirection).includes(order.direction)) {
		return next(
			AppError.create("INVALID_TRADE_DIRECTION", "Invalid direction"),
		);
	}

	// "Invalid order type" -> 需要新增 INVALID_ORDER_TYPE
	if (!Object.values(OrderType).includes(order.type)) {
		return next(AppError.create("INVALID_ORDER_TYPE", "Invalid order type"));
	}

	// "Invalid buy/sell order format" -> 可以使用 INVALID_ORDER_AMOUNT
	if (order.direction === TradeDirection.BUY) {
		if (
			!validateBuyOrder(order) ||
			!order.structure ||
			!order.scale ||
			!order.term ||
			!order.quote
		) {
			return next(
				AppError.create("INVALID_ORDER_AMOUNT", "Invalid buy order format"),
			);
		}
	} else if (order.direction === TradeDirection.SELL) {
		if (!validateSellOrder(order)) {
			return next(
				AppError.create("INVALID_ORDER_AMOUNT", "Invalid sell order format"),
			);
		}
	}

	next();
}

function validateBuyOrder(order: BuyRequest): boolean {
	return (
		typeof order.scale === "number" &&
		order.scale > 0 &&
		typeof order.term === "number" &&
		order.term > 0 &&
		typeof order.quote === "number" &&
		order.quote > 0 &&
		typeof order.structure === "string" &&
		(!order.entry_price || typeof order.entry_price === "number") &&
		(!order.limit_price || typeof order.limit_price === "number")
	);
}

function validateSellOrder(order: SellRequest): boolean {
	return typeof order.trade_no === "string";
}

export function validateTransactionRequest(
	req: Request,
	_res: Response,
	next: NextFunction,
) {
	const { amount, currency } = req.body;

	// "Invalid amount" -> 可以使用 INVALID_ORDER_AMOUNT
	if (typeof amount !== "number" || amount <= 0) {
		return next(AppError.create("INVALID_ORDER_AMOUNT", "Invalid amount"));
	}

	// "Invalid currency" -> 需要新增 INVALID_CURRENCY
	if (
		typeof currency !== "string" ||
		!Object.values(Currency).includes(currency as Currency)
	) {
		return next(AppError.create("INVALID_CURRENCY", "Invalid currency"));
	}

	next();
}

export function validateLoginRequest(
	req: Request,
	_res: Response,
	next: NextFunction,
) {
	const { phone_number, email, password } = req.body;

	// 至少需要提供手机号或邮箱之一
	if (
		(!phone_number || phone_number.trim() === "") &&
		(!email || email.trim() === "")
	) {
		return next(AppError.create("BAD_REQUEST", "请提供手机号或邮箱"));
	}

	// "Invalid password" -> 可以使用 INVALID_PASSWORD_FORMAT
	if (!password || typeof password !== "string" || password.trim() === "") {
		return next(AppError.create("INVALID_PASSWORD_FORMAT", "Invalid password"));
	}

	// 清理输入数据
	if (phone_number) {
		req.body.phone_number = phone_number.trim();
	}
	if (email) {
		req.body.email = email.trim();
	}
	req.body.password = password.trim();

	next();
}
