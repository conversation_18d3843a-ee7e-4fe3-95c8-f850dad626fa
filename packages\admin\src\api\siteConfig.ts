import { request } from "./request";
import type { SiteConfig } from "@packages/shared";

export const siteConfigApi = {
	// 获取网站配置
	getSiteConfig: () => request.get<SiteConfig>("/admin-site-config"),

	// 更新网站配置
	updateSiteConfig: (data: Partial<SiteConfig>) =>
		request.post<{ message: string }>("/admin-site-config", data),

	// 获取历史记录
	getSiteConfigHistory: (
		page = 1,
		pageSize = 10,
		options?: {
			sortBy?: string;
			sortOrder?: "ASC" | "DESC";
		},
	) =>
		request.get<{
			total: number;
			items: {
				config_id: number;
				config: SiteConfig;
				created_at: string;
				admin_id: number;
			}[];
		}>("/admin-site-config/history", {
			params: {
				page,
				pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
			},
		}),

	// 上传图标
	uploadIcon: (file: File, type: "favicon" | "logo") => {
		const formData = new FormData();
		formData.append("file", file);

		return request.post<{ message: string; assetId: string }>(
			`/admin-site-config/upload-icon?type=${type}`,
			formData,
			{
				headers: {
					"Content-Type": "multipart/form-data",
				},
			},
		);
	},

	// 获取资源URL
	getAssetUrl: (assetId: string) => `/api/admin-site-config/asset/${assetId}`,
} as const;
