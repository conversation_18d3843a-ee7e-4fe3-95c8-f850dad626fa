import { Router } from "express";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import * as sharedConfigService from "@/services/admin/sharedConfigService.js";

const router = Router();

// 获取共享配置: GET /api/shared-config
router.get(
	"/",
	wrapAdminRoute(async (_, res) => {
		const config = await sharedConfigService.getSharedConfig();
		res.status(200).json(config);
	}),
);

export default router;
