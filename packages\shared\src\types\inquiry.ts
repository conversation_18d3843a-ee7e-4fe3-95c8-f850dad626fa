export const StructureValue = {
	"100C": "100% call",
	"103C": "103% call",
	"105C": "105% call",
	"110C": "110% call",
	"100P": "100% put",
	"97P": "97% put",
	"95P": "95% put",
	"90P": "90% put",
} as const;

export type StructureType = keyof typeof StructureValue;

export enum InquiryStatus {
	PROCESSING = "processing",
	APPROVED = "approved",
	REJECTED = "rejected",
	EXPIRED = "expired",
}

export const QUOTE_REJECTED = -1;

// redis setex 的过期时间，单位为秒
export const INQUIRY_EXPIRATION_TIME_IN_SECONDS = 30 * 60; // 30分钟

export interface StockInfo {
	ts_code: string;
	name: string;
	market: string;
}

export interface SearchStocksRequest {
	query?: string;
	offset?: number;
}

export interface SearchStocksResponse {
	data: StockInfo[];
	total: number;
	hasMore: boolean;
}

export interface MarketData {
	high: number;
	low: number;
	pre_close: number;
	swing: number;
	amount: number;
}

export interface QuoteRequest {
	user_id?: number; // cookie 中获取
	ts_code: string; // 股票代码
	scale: number; // 名义本金
	structure: StructureType; // 期权结构(如 "100C")
	term: number; // 期限(月)
	currentDayData?: MarketData; // 批报价服务添加
}

export interface BatchQuoteRequest {
	quotes: QuoteRequest[];
}

export interface QuoteResponse {
	inquiry_id: number;
	user_id: number;
	ts_code: string;
	structure: StructureType;
	scale: number;
	term: number;
	quote: number;
	status: InquiryStatus;
	created_at: string;
	external_quotes?: Record<ExternalQuoteProvider | string, number | null>; // 使用ExternalQuoteProvider类型
	quote_diffs?: Record<ExternalQuoteProvider | string, number | null>; // 使用ExternalQuoteProvider类型
}

export type InquiryData = QuoteResponse;

export interface InquiryItem extends InquiryData {
	subject: string;
}

export interface TradeCalendarRange {
	cal_date: Date;
	is_open: number;
	pretrade_date: string;
}

// 外部报价提供商显示名映射
export const PriceProviderNames = {
	HAIYING: "HY",
	YINHE_DERUI: "YHDR",
	ZHONGZHENG_ZIBEN: "ZZZB",
	ZHEQI: "ZQ",
	YONGAN: "YA",
	ZHONGJIN: "ZJ",
	GUANGFA: "GF",
	GUOJUNZI: "GJZ",
} as const;

export type ExternalQuoteProvider = keyof typeof PriceProviderNames;

// T+N trading restrictions by provider
export const ProviderTradingRestrictions = {
	INK: 0, // T+0
	ZHONGZHENG_ZIBEN: 1, // T+1
	ZHONGJIN: 5, // T+5
	GUANGFA: 5, // T+5
	HAIYING: 1, // T+1
	YONGAN: 1, // T+1
	YINHE_DERUI: 1, // T+1
	GUOJUNZI: 5, // T+5
	ZHEQI: 1, // T+1
} as const;

export type TradingRestrictionDays =
	(typeof ProviderTradingRestrictions)[keyof typeof ProviderTradingRestrictions];
