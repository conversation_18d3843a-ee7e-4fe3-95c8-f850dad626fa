<template>
  <div class="exercise-record-view view">
    <div class="pending-orders card">
      <div class="card-header clickable" @click="isPendingCollapsed = !isPendingCollapsed">
        <div class="card-title">挂单卖出</div>
        <el-icon class="collapse-icon">
          <component :is="isPendingCollapsed ? ArrowDown : ArrowUp" />
        </el-icon>
      </div>

      <transition name="fade">
        <div v-show="!isPendingCollapsed">
          <TableWrapper v-model:page-size="pendingPageSize" v-model:current-page="pendingCurrentPage"
            v-model:is-descending="pendingIsDescending" :total-pages="pendingTotalPages">
            <LoadingState :loading="isPendingLoading" :has-data="pendingOrders.length > 0" :icon="DataLine" />
            <template v-if="!isPendingLoading && pendingOrders.length">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>标的</th>
                    <th>开仓日期</th>
                    <th>开仓价</th>
                    <th>平仓名本</th>
                    <th>结构</th>
                    <th>到期日</th>
                    <th>期权费</th>
                    <th>订单类型</th>
                    <th>限价</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="order in paginatedPendingOrders" :key="order.pending_id">
                    <td>{{ formatSubject(order.ts_code) }}</td>
                    <td>{{ formatDate(order.created_at!) }}</td>
                    <td>{{ formatNumber(order.entry_price) }}</td>
                    <td>{{ order.scale + "万" }}</td>
                    <td>{{ formatStructure(order.structure) }}</td>
                    <td>{{ formatDate(order.expiry_date) }}</td>
                    <td>{{ formatNumber(order.quote) }}</td>
                    <td>{{ formatOrderType(order.status) }}</td>
                    <td>{{ order.limit_price || '-' }}</td>
                    <td>{{ formatStatus(order.status) }}</td>
                    <td>
                      <button class="action-button" @click="handleCancelOrder(order)">取消</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </template>
          </TableWrapper>
        </div>
      </transition>
    </div>

    <div class="exercise-record card">
      <div class="card-header">
        <div class="card-title">行权记录</div>
      </div>

      <div class="filter-row">
        <div class="form-group">
          <label for="exercise-dateRange">行权日期：</label>
          <DateRangePicker id="exercise-dateRange" v-model:startDate="startDate" v-model:endDate="endDate"
            placeholder="所有" />
        </div>
        <div class="form-group">
          <label for="subject">选择标的：</label>
          <MySelect id="subject" v-model="selectedSubject" :options="subjectOptions" placeholder="全部" :page-size="20" />
        </div>
      </div>

      <TableWrapper v-model:page-size="pageSize" v-model:current-page="currentPage" v-model:is-descending="isDescending"
        :total-pages="totalPages">
        <LoadingState v-if="!orderRecords?.length" :loading="isLoading" :has-data="orderRecords?.length > 0"
          :icon="DataLine" />
        <template v-else>
          <div class="table-container">
            <LoadingState :loading="isLoading" :has-data="orderRecords?.length > 0" :icon="DataLine"
              :class="['loading-overlay', { 'visible': isLoading }]" />
            <table class="data-table">
              <thead>
                <tr>
                  <th>标的</th>
                  <th>行权日期</th>
                  <th>平仓名本</th>
                  <th class="desktop-only">开仓价</th>
                  <th class="desktop-only">结构</th>
                  <th class="desktop-only">执行价</th>
                  <th class="desktop-only">结算价</th>
                  <th>收益</th>
                  <!-- <th class="desktop-only">结算通知书</th> -->
                </tr>
              </thead>
              <tbody>
                <template v-for="order in orderRecords" :key="order.trade_no">
                  <tr class="order-row" :class="{ 'selected': selectedOrder?.trade_no === order.trade_no }"
                    @click="toggleOrderDetails(order)">
                    <td>{{ formatSubject(order.ts_code) }}</td>
                    <td>{{ formatDate(order.closed_at!) }}</td>
                    <td>{{ order.scale + "万" }}</td>
                    <td class="desktop-only">{{ formatNumber(order.entry_price) }}</td>
                    <td class="desktop-only">{{ formatStructure(order.structure) }}</td>
                    <td class="desktop-only">{{ formatNumber(order.exercise_price) }}</td>
                    <td class="desktop-only">{{ formatNumber(order.settle_price) }}</td>
                    <td :class="getProfitClass(calculateProfit(order))">
                      {{ formatNumber(calculateProfit(order)) }}
                    </td>
                    <!-- <td class="desktop-only">
                      <el-button class="action-button" @click.stop="handleOrder(order)">查看</el-button>
                    </td> -->
                  </tr>
                  <template v-if="selectedOrder?.trade_no === order.trade_no">
                    <tr class="mobile-only details-row">
                      <td colspan="4">
                        <div class="order-details details-transition">
                          <div class="detail-item">
                            <span class="label">开仓价：</span>
                            <span class="value">{{ formatNumber(order.entry_price) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">结构：</span>
                            <span class="value">{{ formatStructure(order.structure) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">执行价：</span>
                            <span class="value">{{ formatNumber(order.exercise_price) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">结算价：</span>
                            <span class="value">{{ formatNumber(order.settle_price) }}</span>
                          </div>
                          <!-- <div class="detail-item">
                            <el-button class="action-button" @click="handleOrder(order)">查看结算通知书</el-button>
                          </div> -->
                        </div>
                      </td>
                    </tr>
                    <tr class="dummy-row"></tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </template>
      </TableWrapper>
    </div>

    <PdfPreview v-model="showPdfPreview" :pdf-url="pdfUrl" :title="pdfTitle" :filename="pdfFilename" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onActivated, onUnmounted } from "vue";
import { tradeApi } from "@/api";
import { useStockStore } from "@/stores/stock";
import MySelect from "@/components/MySelect.vue";
import DateRangePicker from "@/components/DateRangePicker.vue";
import TableWrapper from "@/components/TableWrapper.vue";
import LoadingState from "@/components/LoadingState.vue";
import type { SettledOrderData, SellingOrderData } from "@packages/shared";
import { OrderStatus } from "@packages/shared";
import { ElMessage } from "element-plus";
import { DataLine, ArrowDown, ArrowUp } from "@element-plus/icons-vue";
import { useTableSettings } from "@/composables/useTableSettings";
// import { generateSettlementNotice } from "@/utils/pdf-generator";
import { formatStructure } from "@/utils/format";
import { eventBus } from "@/utils/eventBus.js";
import PdfPreview from "@/components/PdfPreview.vue";

const { formatSubject } = useStockStore();

// 状态管理
const orderRecords = ref<SettledOrderData[]>([]);
const startDate = ref("");
const endDate = ref("");
const selectedSubject = ref<string[]>([]);
const isLoading = ref(false);

// 添加挂单相关的状态
const pendingOrders = ref<SellingOrderData[]>([]);
const isPendingLoading = ref(false);

// Table Settings
const { initSettings, pageSize, isDescending, currentPage, totalPages } =
  useTableSettings();

// 添加挂单的分页设置
const pendingPageSize = ref(10);
const pendingCurrentPage = ref(1);
const pendingIsDescending = ref(true);
const pendingTotalPages = computed(() =>
  Math.ceil(pendingOrders.value.length / pendingPageSize.value),
);

// 添加挂单的分页计算属性
const paginatedPendingOrders = computed(() => {
  const start = (pendingCurrentPage.value - 1) * pendingPageSize.value;
  const end = start + pendingPageSize.value;
  return pendingOrders.value.slice(start, end);
});

// 添加新的响应式变量存储所有可用的标的代码
const availableTsCodes = ref<string[]>([]);

// 修改标的选项计算属性
const subjectOptions = computed(() => {
  if (!availableTsCodes.value) return [];

  return availableTsCodes.value.map((ts_code) => ({
    value: ts_code,
    label: formatSubject(ts_code),
  }));
});

// 方法
const fetchOrderRecords = async () => {
  try {
    isLoading.value = true;
    const response = await tradeApi.getSettleHistory(
      currentPage.value,
      pageSize.value,
      isDescending.value,
      {
        ts_codes: selectedSubject.value,
        startDate: startDate.value,
        endDate: endDate.value,
      },
    );
    orderRecords.value = response?.items || [];
    totalPages.value = Math.ceil((response?.total || 1) / pageSize.value);
    // 更新可用标的列表
    availableTsCodes.value = response?.ts_codes || [];
  } catch (error) {
    console.error("Failed to fetch order records:", error);
    ElMessage.error("加载订单记录失败");
  } finally {
    isLoading.value = false;
  }
};

// 添加获取挂单记录的方法，只获取卖出挂单
const fetchPendingOrders = async () => {
  isPendingLoading.value = true;
  try {
    const sellOrders = await tradeApi.getPendingSellHistory();
    pendingOrders.value =
      sellOrders?.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      ) || [];
    // 如果列表为空，自动折叠面板
    if (pendingOrders.value.length === 0) {
      isPendingCollapsed.value = true;
    }
  } catch (error) {
    console.error("Failed to fetch pending sell orders:", error);
    ElMessage.error("加载挂单记录失败");
  } finally {
    isPendingLoading.value = false;
  }
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString();
};

const formatNumber = (num: number) => {
  return new Intl.NumberFormat("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(num);
};

const formatStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    [OrderStatus.LIMIT_SELLING]: "限价待沽",
    [OrderStatus.VWAP_SELLING]: "均价待沽",
  };
  return statusMap[status] || status;
};

const formatOrderType = (status: string) => {
  if (status === OrderStatus.LIMIT_SELLING) {
    return "限价单";
  }
  return "均价单";
};

const calculateProfit = (order: SettledOrderData): number => {
  const isCall = order.structure.endsWith("C");
  const priceProfit = isCall
    ? order.settle_price - order.exercise_price
    : order.exercise_price - order.settle_price;
  return priceProfit >= 0
    ? (order.scale * 10000 * priceProfit) / order.entry_price
    : 0;
};

const getProfitClass = (value: number): string => {
  return value > 0 ? "profit-positive" : "";
};

// 添加选中订单的状态
const selectedOrder = ref<SettledOrderData | null>(null);

// 切换订单详情的显示
const toggleOrderDetails = (order: SettledOrderData) => {
  if (selectedOrder.value?.trade_no === order.trade_no) {
    selectedOrder.value = null;
  } else {
    selectedOrder.value = order;
  }
};

// 添加移动端检测
// const isMobile = () => {
// 	return window.innerWidth <= 768;
// };

// 查看结算通知书
// const handleOrder = async (order: SettledOrderData) => {
// 	try {
// 		const pdfBlob = await generateSettlementNotice(order);
// 		const url = URL.createObjectURL(pdfBlob);

// 		if (isMobile()) {
// 			// 移动端：新标签页打开
// 			window.open(url, "_blank");
// 			// 延迟释放 URL
// 			setTimeout(() => {
// 				URL.revokeObjectURL(url);
// 			}, 100);
// 		} else {
// 			// 桌面端：使用对话框预览
// 			pdfUrl.value = url;
// 			pdfTitle.value = "结算通知书";
// 			pdfFilename.value = `结算通知_${order.trade_no}.pdf`;
// 			showPdfPreview.value = true;
// 		}
// 	} catch (error) {
// 		console.error("Failed to generate settlement notice:", error);
// 		ElMessage.error("生成结算通知书失败");
// 	}
// };

const handleCancelOrder = async (order: SellingOrderData) => {
  try {
    await tradeApi.cancelPendingOrder({ pending_id: order.pending_id });
    ElMessage.success("成功取消挂单");
  } catch (error) {
    console.error("Failed to cancel order:", error);
    ElMessage.error("取消挂单失败");
  }
};

// 添加 activated 钩子
onActivated(async () => {
  // 只在数据为空时加载数据
  if (orderRecords.value.length === 0) {
    await Promise.all([fetchOrderRecords(), fetchPendingOrders()]);
  }
});

onMounted(() => {
  // 只初始化设置，不加载数据
  initSettings();
  // 添加事件监听
  eventBus.on("order-updated", fetchOrderRecords);
  eventBus.on("order-updated", fetchPendingOrders);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  eventBus.off("order-updated", fetchOrderRecords);
  eventBus.off("order-updated", fetchPendingOrders);
});

// 监听筛选变化
watch([selectedSubject, startDate, endDate], () => {
  currentPage.value = 1;
  fetchOrderRecords();
});

const isPendingCollapsed = ref(true);

watch(pendingOrders, (newOrders) => {
  if (newOrders.length > 0) {
    isPendingCollapsed.value = false;
  }
});

// 修改监听分页和排序变化
watch([currentPage, pageSize, isDescending], () => {
  fetchOrderRecords();
});

// 在 setup 中添加状态
const showPdfPreview = ref(false);
const pdfUrl = ref("");
const pdfTitle = ref("");
const pdfFilename = ref("");

// 在组件卸载时清理
onUnmounted(() => {
  if (pdfUrl.value) {
    URL.revokeObjectURL(pdfUrl.value);
  }
});
</script>

<style scoped>
.card-header.clickable {
  cursor: pointer;
  user-select: none;
}

.collapse-icon {
  cursor: pointer;
  transition: transform 0.3s;
  padding: 4px;
  border-radius: 4px;
}

.collapse-icon:hover {
  background-color: var(--el-fill-color-light);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.action-button {
  width: 58px;
}

.profit-positive {
  color: var(--el-color-error);
}

.profit-negative {
  color: var(--el-color-success);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .data-table {
    min-width: unset;
    width: 100%;
  }

  .order-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .order-row:hover {
    background-color: var(--el-fill-color-light);
  }

  .order-row.selected {
    background-color: var(--el-fill-color);
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }

  .order-details {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px 12px;
    font-size: 14px;
  }

  .detail-item {
    display: flex;
    padding: 6px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .detail-item:nth-last-child(2),
  .detail-item:last-child {
    border-bottom: none;
  }

  .detail-item .label {
    color: var(--el-text-color-secondary);
    width: 70px;
    flex-shrink: 0;
  }

  .detail-item .value {
    flex-grow: 1;
  }

  /* 最后一列单独占一行 */
  /* .detail-item:last-child {
    grid-column: 1 / -1;
    justify-content: center;
    padding: 0;
  } */

  .detail-item .action-button {
    width: 140px;
  }

  /* 隐藏空行但保持其对奇偶计数的影响 */
  tr.dummy-row {
    display: none;
  }

  /* 详情展开动画 */
  .details-transition {
    animation: expand 0.25s ease-in-out forwards;
  }

  @keyframes expand {
    from {
      max-height: 91px;
      opacity: 0.5;
    }

    to {
      max-height: 400px;
      opacity: 1;
    }
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }

  /* 筛选器的移动端间隔 */
  .filter-row {
    gap: 8px;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
}
</style>
