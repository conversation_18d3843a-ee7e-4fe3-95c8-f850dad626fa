import { appRedis } from "@/lib/redis.js";
import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";
import * as ConfigModel from "@/models/config.js";
import {
	DEFAULT_BUSINESS_CONFIG,
	DEFAULT_PLATFORM_CONFIG,
} from "@/config/defaultParams.js";
import { ENV } from "@/config/configManager.js";
import type {
	BaseSystemStatus,
	BusinessConfig,
	PlatformConfig,
	PlatformConfigHistory,
} from "@packages/shared";
import type { StatusHistoryItem, SystemStatus } from "@packages/shared";
import * as MarketTimeManager from "@/financeUtils/marketTimeManager.js";

const CACHE_KEY = "config:business";
const STATUS_CACHE_KEY = "config:system_status";
const PLATFORM_CACHE_KEY = "config:platform";
const CACHE_TTL = 300; // 5分钟缓存

async function setCache(config: BusinessConfig): Promise<void> {
	try {
		await appRedis.setex(CACHE_KEY, CACHE_TTL, JSON.stringify(config));
	} catch (error) {
		logger.error(error, "Failed to set config cache");
		throw AppError.create(
			"CONFIG_CACHE_FAILED",
			"Failed to cache configuration",
		);
	}
}

async function setStatusCache(status: SystemStatus): Promise<void> {
	try {
		await appRedis.setex(STATUS_CACHE_KEY, CACHE_TTL, JSON.stringify(status));
	} catch (error) {
		logger.error(error, "Failed to set status cache");
		throw AppError.create(
			"CONFIG_CACHE_FAILED",
			"Failed to cache system status",
		);
	}
}

function validateConfig(config: Partial<BusinessConfig>): void {
	const validationErrors: string[] = [];

	if (config.OPTION_MULTIPLIER !== undefined) {
		if (config.OPTION_MULTIPLIER <= 1) {
			validationErrors.push("OPTION_MULTIPLIER must be greater than 1");
		}
	}

	if (config.PUT_MULTIPLIER !== undefined) {
		if (config.PUT_MULTIPLIER <= 1) {
			validationErrors.push("PUT_MULTIPLIER must be greater than 1");
		}
	}

	if (config.MONTH_MULTIPLIER !== undefined) {
		if (config.MONTH_MULTIPLIER <= 1) {
			validationErrors.push("MONTH_MULTIPLIER must be greater than 1");
		}
	}

	if (validationErrors.length > 0) {
		throw AppError.create(
			"CONFIG_VALIDATION_FAILED",
			validationErrors.join("; "),
		);
	}
}

export async function getConfig(): Promise<BusinessConfig> {
	try {
		const cached = await appRedis.get(CACHE_KEY);
		if (cached) return JSON.parse(cached);

		const config = await ConfigModel.getConfig();

		if (!config) {
			await ConfigModel.saveConfig(DEFAULT_BUSINESS_CONFIG);
		}

		const usedConfig = config || DEFAULT_BUSINESS_CONFIG;

		// If in demo mode, multiply scale limits by 100
		if (ENV.IS_DEMO) {
			usedConfig.STOCK_SCALE_LIMIT *= 100;
			usedConfig.TOTAL_SCALE_LIMIT *= 100;
		}

		await setCache(usedConfig);
		return usedConfig;
	} catch (error) {
		logger.error(error, "Failed to get business config");
		throw AppError.create(
			"CONFIG_NOT_FOUND",
			"Failed to get business configuration",
		);
	}
}

export async function updateConfig(
	updates: Partial<BusinessConfig>,
	admin_id?: number,
) {
	try {
		validateConfig(updates);
		const currentConfig = await getConfig();
		const newConfig = { ...currentConfig, ...updates };

		await ConfigModel.saveConfig(newConfig, admin_id);
		await appRedis.del(CACHE_KEY);

		return { message: "Business config updated successfully" };
	} catch (error) {
		logger.error(error, "Failed to update business config");
		throw AppError.create(
			"CONFIG_UPDATE_FAILED",
			"Failed to update business configuration",
		);
	}
}

export async function getSystemStatus(): Promise<SystemStatus> {
	try {
		// Try cache first
		const cached = await appRedis.get(STATUS_CACHE_KEY);
		if (cached) return JSON.parse(cached);

		// Get from database
		const status = await ConfigModel.getSystemStatus();
		if (!status) {
			const defaultStatus = await MarketTimeManager.getDefaultStatus();
			await ConfigModel.saveSystemStatus(defaultStatus);
			await setStatusCache(defaultStatus);
			return defaultStatus;
		}

		await setStatusCache(status);
		return status;
	} catch (error) {
		logger.error(error, "Failed to get system status");
		throw AppError.create("CONFIG_NOT_FOUND", "Failed to get system status");
	}
}

export async function updateSystemStatus(
	updates: Partial<SystemStatus>,
	admin_id?: number,
) {
	try {
		const currentStatus = await getSystemStatus();

		const { auto_manage_enabled: currentAuto, ...currentBase } = currentStatus;
		const { auto_manage_enabled: updatesAuto, ...updatesBase } = updates;

		// 计算真正的变化
		const actualUpdatesBase = Object.entries(
			updatesBase as BaseSystemStatus,
		).reduce(
			(acc, [key, value]) => {
				if (currentBase[key as keyof BaseSystemStatus] !== value) {
					acc[key as keyof BaseSystemStatus] = value;
				}
				return acc;
			},
			{} as Partial<BaseSystemStatus>,
		);

		const hasAutoChanges =
			updatesAuto && !updatesAuto.every((v, i) => v === currentAuto[i]);

		// 修改验证逻辑，考虑到同时更新的情况
		if (
			actualUpdatesBase.POSITION_ENTRY_ENABLED &&
			!currentBase.SYSTEM_ENABLED &&
			actualUpdatesBase.SYSTEM_ENABLED !== true
		) {
			throw AppError.create(
				"INVALID_STATUS_CHANGE",
				"Cannot enable position entry while system is disabled",
			);
		}

		// 如果系统被禁用，同时禁用持仓入场
		if (actualUpdatesBase.SYSTEM_ENABLED === false) {
			actualUpdatesBase.POSITION_ENTRY_ENABLED = false;
		}

		const newStatus: BaseSystemStatus = {
			...currentBase,
			...actualUpdatesBase,
		};

		// 分别保存真正变化的部分
		if (hasAutoChanges && updatesAuto) {
			await ConfigModel.saveAutoManageConfig(updatesAuto);
		}
		if (Object.keys(actualUpdatesBase).length > 0) {
			await ConfigModel.saveSystemStatus(newStatus, admin_id);
		}

		await appRedis.del(STATUS_CACHE_KEY);
		return { message: "System status updated successfully" };
	} catch (error) {
		logger.error(error, "Failed to update system status");
		throw AppError.create(
			"STATUS_UPDATE_FAILED",
			"Failed to update system status",
		);
	}
}

export async function getSystemStatusHistory(
	page: number,
	pageSize: number,
	options: { sortBy?: string; sortOrder?: "ASC" | "DESC" } = {},
): Promise<{ total: number; items: StatusHistoryItem[] }> {
	return await ConfigModel.getSystemStatusHistory(page, pageSize, options);
}

async function setPlatformCache(config: PlatformConfig): Promise<void> {
	try {
		await appRedis.setex(PLATFORM_CACHE_KEY, CACHE_TTL, JSON.stringify(config));
	} catch (error) {
		logger.error(error, "Failed to set platform config cache");
		throw AppError.create(
			"CONFIG_CACHE_FAILED",
			"Failed to cache platform configuration",
		);
	}
}

function validatePlatformConfig(config: Partial<PlatformConfig>): void {
	const validationErrors: string[] = [];

	if (config.quote_providers) {
		for (const [provider, settings] of Object.entries(config.quote_providers)) {
			if (
				settings.price_adjustment !== undefined &&
				settings.price_adjustment < 0
			) {
				validationErrors.push(
					`${provider} price adjustment must be greater than 0`,
				);
			}

			// 验证加价金额是否为100的整数倍
			if (
				settings.price_adjustment !== undefined &&
				settings.price_adjustment % 100 !== 0
			) {
				validationErrors.push(
					`${provider} price adjustment must be a multiple of 100`,
				);
			}
		}
	}

	// 验证盈利分成百分比
	if (config.profit_sharing) {
		if (
			config.profit_sharing.percentage !== undefined &&
			(config.profit_sharing.percentage < 0 ||
				config.profit_sharing.percentage > 100)
		) {
			validationErrors.push(
				"Profit sharing percentage must be between 0 and 100",
			);
		}

		// 验证百分比是否为整数
		if (
			config.profit_sharing.percentage !== undefined &&
			!Number.isInteger(config.profit_sharing.percentage)
		) {
			validationErrors.push("Profit sharing percentage must be an integer");
		}
	}

	// 验证邮箱显示名
	if (config.email_display_name && config.email_display_name.length > 100) {
		validationErrors.push(
			"Email display name must be less than 100 characters",
		);
	}

	// 验证邮箱格式和数量
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	const MAX_EMAILS = 5;

	// 验证资质审核邮箱
	if (config.qualification_audit_email) {
		// 验证是否为数组
		if (!Array.isArray(config.qualification_audit_email)) {
			validationErrors.push("Qualification audit email must be an array");
		} else {
			// 验证数组长度
			if (config.qualification_audit_email.length > MAX_EMAILS) {
				validationErrors.push(
					`Qualification audit email can have at most ${MAX_EMAILS} addresses`,
				);
			}

			// 验证每个邮箱的格式
			for (const email of config.qualification_audit_email) {
				if (!emailRegex.test(email)) {
					validationErrors.push(`Invalid email format: ${email}`);
				}
			}
		}
	}

	// 验证资金审核邮箱
	if (config.fund_audit_email) {
		// 验证是否为数组
		if (!Array.isArray(config.fund_audit_email)) {
			validationErrors.push("Fund audit email must be an array");
		} else {
			// 验证数组长度
			if (config.fund_audit_email.length > MAX_EMAILS) {
				validationErrors.push(
					`Fund audit email can have at most ${MAX_EMAILS} addresses`,
				);
			}

			// 验证每个邮箱的格式
			for (const email of config.fund_audit_email) {
				if (!emailRegex.test(email)) {
					validationErrors.push(`Invalid email format: ${email}`);
				}
			}
		}
	}

	if (validationErrors.length > 0) {
		throw AppError.create(
			"CONFIG_VALIDATION_FAILED",
			validationErrors.join("; "),
		);
	}
}

export async function getPlatformConfig(): Promise<PlatformConfig> {
	try {
		const cached = await appRedis.get(PLATFORM_CACHE_KEY);
		if (cached) return JSON.parse(cached);

		const config = await ConfigModel.getPlatformConfig();
		if (!config) {
			await ConfigModel.savePlatformConfig(DEFAULT_PLATFORM_CONFIG);
			await setPlatformCache(DEFAULT_PLATFORM_CONFIG);
			return DEFAULT_PLATFORM_CONFIG;
		}

		await setPlatformCache(config);
		return config;
	} catch (error) {
		logger.error(error, "Failed to get platform config");
		throw AppError.create(
			"CONFIG_NOT_FOUND",
			"Failed to get platform configuration",
		);
	}
}

export async function updatePlatformConfig(
	updates: Partial<PlatformConfig>,
	admin_id?: number,
) {
	try {
		validatePlatformConfig(updates);
		const currentConfig = await getPlatformConfig();

		// 深度合并更新，以保持嵌套结构
		const newConfig = {
			...currentConfig,
			...updates,
			quote_providers: {
				...currentConfig.quote_providers,
				...(updates.quote_providers || {}),
			},
		};

		await ConfigModel.savePlatformConfig(newConfig, admin_id);
		await appRedis.del(PLATFORM_CACHE_KEY);

		return { message: "Platform config updated successfully" };
	} catch (error) {
		logger.error(error, "Failed to update platform config");
		throw AppError.create(
			"CONFIG_UPDATE_FAILED",
			"Failed to update platform configuration",
		);
	}
}

export async function getPlatformConfigHistory(): Promise<
	PlatformConfigHistory[]
> {
	return await ConfigModel.getPlatformConfigHistory();
}
