/**
 * 通道数据库连接管理器
 * 用于管理交易台连接到多个通道数据库的连接池
 */
import { PrismaClient } from "@prisma/client";
import logger from "@/utils/logger.js";
import { findChannelDBUrlFromConfig } from "@/config/defaultParams.js";
import { AppError } from "@/core/appError.js";

// 通道数据库连接池
const channelDbConnections = new Map<string, PrismaClient>();

/**
 * 获取通道数据库连接
 * @param channelId 通道ID
 * @returns 通道数据库Prisma客户端
 */
export async function getChannelDbConnection(
	channelId: string,
): Promise<PrismaClient> {
	// 检查是否已有连接缓存
	if (channelDbConnections.has(channelId)) {
		const connection = channelDbConnections.get(channelId);
		if (connection) {
			return connection;
		}
	}

	// 从配置中获取数据库连接信息
	const dbUrl = getChannelDatabaseUrl(channelId);
	if (!dbUrl) {
		throw AppError.create("NOT_FOUND", `未找到通道 ${channelId} 的数据库配置`);
	}

	try {
		// 创建新的数据库连接
		const prismaClient = new PrismaClient({
			datasources: {
				db: { url: dbUrl },
			},
			log: [{ emit: "event", level: "error" }],
		});

		// 添加错误监听
		prismaClient.$on("error", (e) => {
			logger.error(`Channel DB (${channelId}) Error: ${e.message}`);
		});

		// 测试连接
		await testConnection(prismaClient, channelId);

		// 缓存连接
		channelDbConnections.set(channelId, prismaClient);
		return prismaClient;
	} catch (error) {
		logger.error(
			`Failed to connect to channel database for ${channelId}: ${error}`,
		);
		throw AppError.create(
			"SERVER_ERROR",
			`无法连接到通道 ${channelId} 的数据库`,
		);
	}
}

/**
 * 获取通道数据库URL
 */
function getChannelDatabaseUrl(channelId: string): string | undefined {
	// 试从TRADING_PLATFORMS配置中查找
	const configDbUrl = findChannelDBUrlFromConfig(channelId);
	if (configDbUrl) {
		return configDbUrl;
	}
}

/**
 * 测试数据库连接
 */
async function testConnection(
	prisma: PrismaClient,
	channelId: string,
): Promise<void> {
	try {
		const dbName = await prisma.$queryRaw`SELECT current_database()`;
		logger.info(`Connected to channel ${channelId} database: ${dbName}`);
	} catch (err) {
		logger.error(
			`Connection test failed for channel ${channelId}: ${err instanceof Error ? err.message : String(err)}`,
		);
		throw err;
	}
}

/**
 * 检查通道数据库连接是否已配置
 * @param channelId 通道ID
 * @returns 是否已配置
 */
export function isChannelDbConfigured(channelId: string): boolean {
	return Boolean(getChannelDatabaseUrl(channelId));
}

/**
 * 清理数据库连接
 */
export async function disconnectAll(): Promise<void> {
	for (const [channelId, client] of channelDbConnections.entries()) {
		try {
			await client.$disconnect();
			logger.info(`Disconnected from channel ${channelId} database`);
		} catch (error) {
			logger.error(
				`Error disconnecting from channel ${channelId} database: ${error}`,
			);
		}
	}
	channelDbConnections.clear();
}
