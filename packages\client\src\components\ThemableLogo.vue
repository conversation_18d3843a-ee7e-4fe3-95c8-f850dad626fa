<template>
  <div class="themable-logo-container" :class="classes" ref="container">
    <img v-if="isSvg === false || loadError" :src="props.src" class="fallback-image" :alt="props.alt" />
    <!-- 当isSvg为null时显示加载占位符 -->
    <div v-if="isSvg === null" class="loading-placeholder"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";

interface Props {
	src: string;
	alt?: string;
	class?: string | Record<string, boolean> | string[];
}

const props = withDefaults(defineProps<Props>(), {
	alt: "logo",
	class: "",
});

const container = ref<HTMLElement | null>(null);
// 初始设置为null，表示未知是否为SVG
const isSvg = ref<boolean | null>(null);
const loadError = ref(false);

// 支持多种类型的class
const classes = computed(() => {
	if (typeof props.class === "string") {
		return props.class.split(" ");
	}

	if (Array.isArray(props.class)) {
		return props.class;
	}

	if (props.class && typeof props.class === "object") {
		return Object.entries(props.class)
			.filter(([_, value]) => value)
			.map(([key]) => key);
	}

	return [];
});

async function loadSvg() {
	if (!props.src || !container.value) {
		loadError.value = false;
		isSvg.value = false;
		return;
	}

	try {
		// 清除当前内容
		if (container.value.querySelector("svg")) {
			container.value.innerHTML = "";
		}

		// 获取资源内容
		const response = await fetch(props.src);
		if (!response.ok)
			throw new Error(`Failed to load resource: ${response.status}`);

		// 检查Content-Type是否为SVG
		const contentType = response.headers.get("content-type") || "";
		const isSvgContent =
			contentType.includes("image/svg+xml") ||
			props.src.toLowerCase().endsWith(".svg");

		isSvg.value = isSvgContent;

		if (!isSvgContent) {
			// 不是SVG内容，使用img标签回退
			loadError.value = false;
			return;
		}

		const svgText = await response.text();

		// 简单的安全检查 (生产环境建议使用DOMPurify等库进行更严格的清理)
		if (/<script/i.test(svgText)) {
			throw new Error("SVG contains script tags");
		}

		// 注入SVG
		container.value.innerHTML = svgText;

		// 处理内联SVG
		const svg = container.value.querySelector("svg");
		if (svg) {
			svg.setAttribute("class", "inline-svg");
			svg.setAttribute("aria-label", props.alt);

			// 确保SVG能够正确缩放
			if (
				!svg.getAttribute("viewBox") &&
				svg.getAttribute("width") &&
				svg.getAttribute("height")
			) {
				const width = svg.getAttribute("width") || "100%";
				const height = svg.getAttribute("height") || "100%";
				svg.setAttribute("viewBox", `0 0 ${width} ${height}`);
			}

			svg.setAttribute("width", "100%");
			svg.setAttribute("height", "100%");
		}

		loadError.value = false;
	} catch (error) {
		console.warn("SVG加载失败，使用图片回退", error);
		loadError.value = true;
		isSvg.value = false;
	}
}

// 监听src变化重新加载
watch(() => props.src, loadSvg);

// 组件挂载时加载
onMounted(loadSvg);
</script>

<style scoped>
.themable-logo-container {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	/* 主页和登录页应用不同尺寸，避免宽度样式污染 */
	/* width: 100%; */
	/* height: 100%; */
}

.fallback-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.loading-placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--color-surface, #eee);
  border-radius: 4px;
  opacity: 0.6;
}

:deep(.inline-svg) {
	width: 100%;
	height: 100%;

	color: var(--theme-logo-color, currentColor);
}

/* 你可以在全局CSS中定义CSS变量: */
/* :root { --theme-logo-color: #333; } */
/* html.dark { --theme-logo-color: #fff; } */
</style> 