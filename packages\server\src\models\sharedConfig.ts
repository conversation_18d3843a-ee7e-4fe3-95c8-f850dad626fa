import prisma, { platformPrisma } from "@/lib/prisma.js";
import { AppError } from "@/core/appError.js";
import { isChannel } from "@/config/configManager.js";
import logger from "@/utils/logger.js";
import type { Prisma } from "@prisma/client";
import type { SharedConfig } from "@packages/shared";

function toJsonValue<T>(data: T): Prisma.InputJsonValue {
	return data as Prisma.InputJsonValue;
}

/**
 * 获取共享配置
 * @returns 共享配置数据
 */
export async function getSharedConfig(): Promise<SharedConfig | null> {
	// 通道环境从交易台数据库获取
	const client = isChannel() && platformPrisma ? platformPrisma : prisma;

	if (!client) {
		throw AppError.create("NOT_FOUND", "Database client not available");
	}

	const result = await client.shared_configs.findFirst({
		orderBy: { config_id: "desc" },
		select: { config: true },
	});

	// 临时禁用外部报价下单功能
	const config = result?.config as SharedConfig | null;

	const modifiedConfig = config
		? {
				...config,
				channel_management: {
					...config?.channel_management,
					enable_external_order: false,
				},
			}
		: null;

	return modifiedConfig;
}

/**
 * 保存共享配置
 * @param configData 配置数据
 * @param admin_id 管理员ID
 */
export async function saveSharedConfig(
	configData: SharedConfig,
	admin_id?: number,
): Promise<void> {
	// 通道环境不能修改共享配置
	if (isChannel()) {
		throw AppError.create(
			"FORBIDDEN",
			"Channel environment cannot modify shared configurations",
		);
	}

	logger.info(configData, "Saving shared config");

	// 创建新记录以保留历史记录
	await prisma.shared_configs.create({
		data: {
			config: toJsonValue(configData),
			...(admin_id ? { admin_id } : {}),
		},
	});
}
