<template>
  <el-dialog v-model="isDialogVisible" title="用户详情" :width="dialogWidth" top="10vh" class="user-details-dialog">
    <div v-if="userData" class="user-details">
      <el-descriptions :column="isMobile ? 1 : 2" border>
        <el-descriptions-item label="用户ID">{{ userData.user_id }}</el-descriptions-item>
        <el-descriptions-item v-if="userData.phone_number" label="手机号">{{ userData.phone_number
        }}</el-descriptions-item>
        <el-descriptions-item v-if="userData.email" label="邮箱">{{ userData.email
        }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="userData.is_qualified ? 'success' : 'info'">
            {{ userData.is_qualified ? '已认证' : '未认证' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="姓名">{{ userData.name || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="证件号码">{{ userData.id_number || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="银行名称">
          <span v-if="userData.bank_name" class="copy-enabled" @click="copyToClipboard(userData.bank_name)">
            {{ userData.bank_name }}
            <el-icon>
              <CopyDocument />
            </el-icon>
          </span>
          <span v-else>N/A</span>
        </el-descriptions-item>
        <el-descriptions-item label="银行编号">
          <span v-if="userData.bank_code" class="copy-enabled" @click="copyToClipboard(userData.bank_code.toString())">
            {{ userData.bank_code }}
            <el-icon>
              <CopyDocument />
            </el-icon>
          </span>
          <span v-else>N/A</span>
        </el-descriptions-item>
        <el-descriptions-item label="银行账号">
          <span v-if="userData.bank_account" class="copy-enabled" @click="copyToClipboard(userData.bank_account)">
            {{ userData.bank_account }}
            <el-icon>
              <CopyDocument />
            </el-icon>
          </span>
          <span v-else>N/A</span>
        </el-descriptions-item>
      </el-descriptions>

      <div class="balance-section">
        <h4>账户余额</h4>
        <div class="balance-tags">
          <el-tag>人民币: {{ formatMoney(userData.balance_cny) }}</el-tag>
          <el-tag type="success">港币: {{ formatMoney(userData.balance_hkd) }}</el-tag>
          <el-tag type="warning">美元: {{ formatMoney(userData.balance_usd) }}</el-tag>
        </div>
      </div>

      <div class="dates-section">
        <p>创建时间: {{ formatDate(userData.created_at) }}</p>
        <p>更新时间: {{ formatDate(userData.updated_at) }}</p>
      </div>
    </div>

    <template #footer>
      <slot name="footer">
        <div class="dialog-footer">
          <el-button @click="isDialogVisible = false">关闭</el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import type { UserInfo } from "@packages/shared";
import { formatMoney, formatDate } from "@/utils/format";
import { basicApi } from "@/api";
import { ElMessage } from "element-plus";
import { CopyDocument } from "@element-plus/icons-vue";

const props = defineProps<{
  visible: boolean;
  userId?: number;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const loading = ref(false);
const userData = ref<UserInfo | null>(null);

const isDialogVisible = computed({
  get: () => props.visible && !loading.value,
  set: (value) => {
    emit("update:visible", value);
  },
});

// 添加移动端检测
const isMobile = ref(window.innerWidth < 768);
const dialogWidth = computed(() => (isMobile.value ? "100%" : "60%"));

// 监听窗口大小变化
window.addEventListener("resize", () => {
  isMobile.value = window.innerWidth < 768;
});

watch(
  () => props.userId,
  (newId) => {
    if (newId) {
      loadUserData(newId);
    }
  },
);

// 加载用户数据
const loadUserData = async (userId: number) => {
  loading.value = true;
  try {
    const data = await basicApi.getUserById(userId);
    userData.value = data;
  } catch (error) {
    console.error("Failed to load user details:", error);
    ElMessage.error("获取用户信息失败");
  } finally {
    loading.value = false;
  }
};

// Add copyToClipboard function
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    ElMessage.success("Copied to clipboard");
  } catch (err) {
    ElMessage.error("Failed to copy");
  }
};
</script>

<style scoped>
.user-details {
  padding: 20px;
}

.balance-section {
  margin-top: 20px;
}

.balance-tags {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.dates-section {
  margin-top: 20px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.dates-section p {
  margin: 5px 0;
}

.copy-enabled {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: var(--el-color-primary);
  transition: opacity 0.2s;
}

.copy-enabled:hover {
  opacity: 0.8;
}

.copy-enabled .el-icon {
  font-size: 14px;
}
</style>