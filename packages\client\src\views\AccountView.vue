<template>
  <div class="account-view view">
    <!-- 余额板块 -->
    <div class="balance-section card">
      <div class="card-header">
        <div class="card-title">可用余额</div>
        <div class="button-group">
          <el-button v-if="hasTransferPermission" type="primary" @click="showTransferDialog">转账</el-button>
          <el-button type="primary" @click="showExchangeDialog">换汇</el-button>
        </div>
      </div>

      <el-row :gutter="20" class="balance-row">
        <el-col :span="8">
          <el-statistic :value="balances.balance_cny" :precision="2">
            <template #title>
              <div class="currency-label">CNY</div>
            </template>
            <template #prefix>
              <el-icon>
                <Money />
              </el-icon>
            </template>
          </el-statistic>
        </el-col>

        <el-col :span="8">
          <el-statistic :value="balances.balance_hkd" :precision="2">
            <template #title>
              <div class="currency-label">HKD</div>
            </template>
            <template #prefix>
              <el-icon>
                <Money />
              </el-icon>
            </template>
          </el-statistic>
        </el-col>

        <el-col :span="8">
          <el-statistic :value="balances.balance_usd" :precision="2">
            <template #title>
              <div class="currency-label">USD</div>
            </template>
            <template #prefix>
              <el-icon>
                <Money />
              </el-icon>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </div>

    <!-- 转账对话框 -->
    <el-dialog v-model="transferDialogVisible" title="转账" width="360px" class="transfer-dialog"
      @opened="handleTransferDialogOpened">
      <el-form ref="transferFormRef" :model="transferForm" :rules="transferRules" label-width="88px">
        <el-form-item label="收款人" prop="receiverName">
          <el-input v-model="transferForm.receiverName" placeholder="请输入收款人姓名" />
        </el-form-item>

        <el-form-item label="手机号" prop="receiverPhone">
          <el-input v-model="transferForm.receiverPhone" placeholder="请输入收款人手机号" />
        </el-form-item>

        <el-form-item label="转账金额" prop="amount">
          <el-input-number v-model="transferForm.amount" :min="0" :precision="2" :step="100" class="w-full"
            @keyup.enter="handleTransfer" />
        </el-form-item>

        <div class="balance-hint">
          可用余额：{{ formatNumber(balances.balance_cny) }} CNY
        </div>
      </el-form>

      <template #footer>
        <el-button @click="transferDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="transferLoading" @click="handleTransfer">确认转账</el-button>
      </template>
    </el-dialog>

    <!-- 换汇对话框 -->
    <el-dialog v-model="exchangeDialogVisible" title="货币兑换" width="360px">
      <el-form ref="exchangeFormRef" :model="exchangeForm" :rules="exchangeRules" label-width="88px">
        <el-form-item label="源货币" prop="fromCurrency">
          <el-select v-model="exchangeForm.fromCurrency" class="w-full">
            <el-option v-for="currency in currencies" :key="currency" :label="currency" :value="currency" />
          </el-select>
        </el-form-item>

        <el-form-item label="目标货币" prop="toCurrency">
          <el-select v-model="exchangeForm.toCurrency" class="w-full">
            <el-option v-for="currency in availableToCurrencies" :key="currency" :label="currency" :value="currency" />
          </el-select>
        </el-form-item>

        <el-form-item label="兑换金额" prop="amount">
          <el-input-number v-model="exchangeForm.amount" :min="0" :precision="2" :step="100" class="w-full" />
        </el-form-item>

        <div class="balance-hint">
          可用余额：
          {{ formatNumber(getAvailableBalance(exchangeForm.fromCurrency)) }}
          {{ exchangeForm.fromCurrency }}
        </div>

        <!-- 汇率预览 -->
        <div v-if="exchangeForm.fromCurrency && exchangeForm.toCurrency" class="exchange-preview">
          <div class="rate-info">
            当前汇率：
            1 {{ exchangeForm.fromCurrency }} = {{ formatNumber(exchangeRate) }} {{ exchangeForm.toCurrency }}
          </div>
          <div v-if="exchangeForm.amount > 0">
            <div class="amount-preview">
              预计收到：
              {{ calculatedAmount }} {{ exchangeForm.toCurrency }}
            </div>
          </div>
        </div>

        <div class="exchange-notice">
          平台换汇无手续费，仅作为平台内部便利结算，<br />
          不可换汇后直接出金。
        </div>
      </el-form>

      <template #footer>
        <el-button @click="exchangeDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="exchangeLoading" @click="handleExchange">确认兑换</el-button>
      </template>
    </el-dialog>

    <!-- 资金变动记录板块 -->
    <div class="transaction-history card">
      <div class="card-header">
        <div class="card-title">资金变动记录</div>
      </div>
      <div class="filter-row">
        <div class="form-group">
          <label for="transaction-dateRange">日期范围：</label>
          <DateRangePicker id="transaction-dateRange" v-model:startDate="startDate" v-model:endDate="endDate"
            placeholder="所有" />
        </div>
        <div class="form-group">
          <label for="reason">变动原因：</label>
          <MySelect id="reason" v-model="selectedReasons" :options="reasonOptions" placeholder="全部" :page-size="20" />
        </div>
      </div>

      <TableWrapper v-model:page-size="pageSize" v-model:current-page="currentPage" v-model:is-descending="isDescending"
        :total-pages="totalPages">
        <LoadingState v-if="!transactions.length" :loading="loading" :has-data="transactions.length > 0"
          :icon="DataLine" />
        <template v-else>
          <div class="table-container">
            <LoadingState :loading="loading" :has-data="transactions.length > 0" :icon="DataLine"
              :class="['loading-overlay', { 'visible': loading }]" />
            <table class="data-table">
              <thead>
                <tr>
                  <th>变动时间</th>
                  <th class="desktop-only">订单号</th>
                  <th>变动原因</th>
                  <th>变动金额</th>
                  <th class="desktop-only">币种</th>
                </tr>
              </thead>
              <tbody>
                <template v-for="transaction in transactions" :key="transaction.txn_id">
                  <tr class="order-row" :class="{ 'selected': selectedTransaction?.txn_id === transaction.txn_id }"
                    @click="toggleTransactionDetails(transaction)">
                    <td>{{ formatDateTime(transaction.created_at) }}</td>
                    <td class="desktop-only">{{ transaction.trade_no || '-' }}</td>
                    <td>{{ formatReason(transaction.type) }}</td>
                    <td :class="getAmountClass(transaction.signed_amount)">
                      {{ formatAmount(transaction.signed_amount) }}
                      <span class="mobile-only currency-suffix">{{ transaction.currency }}</span>
                    </td>
                    <td class="desktop-only">{{ transaction.currency }}</td>
                  </tr>
                  <template v-if="selectedTransaction?.txn_id === transaction.txn_id">
                    <tr class="mobile-only details-row">
                      <td colspan="4">
                        <div class="order-details details-transition">
                          <div class="detail-item">
                            <span class="label">订单号：</span>
                            <span class="value">{{ transaction.trade_no || '-' }}</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr class="dummy-row"></tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </template>
      </TableWrapper>
    </div>
  </div>

  <PaymentDialog v-model="showPaymentDialog" :has-password="hasPaymentPassword" :on-request="handleTransferRequest"
    @success="handleTransferSuccess" @cancel="showPaymentDialog = false" @check-password="checkPaymentPassword" />
</template>

<script setup lang="ts">
import {
	ref,
	onMounted,
	watch,
	computed,
	onActivated,
	onUnmounted,
	nextTick,
} from "vue";
import MySelect from "@/components/MySelect.vue";
import DateRangePicker from "@/components/DateRangePicker.vue";
import { Money } from "@element-plus/icons-vue";
import { fundApi, paymentApi } from "@/api";
import type { FormInstance, FormRules } from "element-plus"; // 添加 FormInstance 导入
import {
	TransactionType,
	type Balances,
	type TransactionData,
	Currency,
} from "@packages/shared";
import { ElMessage } from "element-plus";
import { formatDateTime } from "@/utils/format";
import { eventBus } from "@/utils/eventBus.js";
import { useAuthStore } from "@/stores/auth";
import PaymentDialog from "@/components/PaymentDialog.vue";

// 表格组件
import TableWrapper from "@/components/TableWrapper.vue";
import LoadingState from "@/components/LoadingState.vue";
import { DataLine } from "@element-plus/icons-vue";
import { useTableSettings } from "@/composables/useTableSettings";
const { initSettings, pageSize, isDescending, currentPage, totalPages } =
	useTableSettings();

const balances = ref<Balances>({
	balance_cny: 0,
	balance_hkd: 0,
	balance_usd: 0,
});

const startDate = ref("");
const endDate = ref("");
const selectedReasons = ref<TransactionType[]>([]);
const transactions = ref<TransactionData[]>([]);
const loading = ref(false);

const reasonOptions = [
	{ value: TransactionType.BUY, label: "下单" },
	{ value: TransactionType.SELL, label: "平仓" },
	{ value: TransactionType.DEPOSIT, label: "入金" },
	{ value: TransactionType.WITHDRAW, label: "出金" },
	{ value: TransactionType.PLATFORM_DEPOSIT, label: "平台入金" },
	{ value: TransactionType.EXCHANGE, label: "换汇" },
	{ value: TransactionType.TRANSFER, label: "转账" },
];

const formatReason = (type: TransactionType) => {
	return reasonOptions.find((option) => option.value === type)?.label || type;
};

const getAmountClass = (signed_amount: number) => {
	return signed_amount >= 0 ? "positive" : "negative";
};

const formatAmount = (signed_amount: number) => {
	const formatter = new Intl.NumberFormat("zh-CN", {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	});

	return `${signed_amount >= 0 ? "+" : ""}${formatter.format(signed_amount)}`;
};

// 获取余额
const fetchBalances = async () => {
	try {
		const response = await fundApi.getBalances();
		balances.value = response || {
			balance_cny: 0,
			balance_hkd: 0,
			balance_usd: 0,
		};
	} catch (error) {
		console.error("Failed to fetch balances:", error);
		ElMessage.error("获取余额失败");
	}
};

// 获取交易记录
const fetchTransactions = async () => {
	loading.value = true;
	try {
		const response = await fundApi.getTransactionHistory(
			currentPage.value,
			pageSize.value,
			isDescending.value,
			{
				types: selectedReasons.value,
				startDate: startDate.value,
				endDate: endDate.value,
			},
		);

		transactions.value = response?.items || [];
		totalPages.value = Math.ceil((response?.total || 1) / pageSize.value);
	} catch (error) {
		console.error("Failed to fetch transactions:", error);
		ElMessage.error("获取资金记录失败");
	} finally {
		loading.value = false;
	}
};

// 监听筛选条件变化
watch([selectedReasons, startDate, endDate], () => {
	currentPage.value = 1; // 重置到第一页
	fetchTransactions(); // 重新获取数据
});

// 监听分页和排序变化
watch([currentPage, pageSize, isDescending], () => {
	fetchTransactions();
});

// 添加 formatNumber 工具函数
const formatNumber = (num: number): string => {
	return new Intl.NumberFormat().format(num);
};

// 换汇相关的状态
const exchangeDialogVisible = ref(false);
const exchangeLoading = ref(false);
const exchangeFormRef = ref<FormInstance | null>(null);
const exchangeForm = ref({
	fromCurrency: Currency.CNY,
	toCurrency: Currency.HKD,
	amount: 0,
});

// 可用货币列表
const currencies = [Currency.CNY, Currency.HKD, Currency.USD];

// 计算可用的目标货币
const availableToCurrencies = computed(() =>
	currencies.filter((c) => c !== exchangeForm.value.fromCurrency),
);

// 修改源货币选择时，如果目标货币相同则自动切换
watch(
	() => exchangeForm.value.fromCurrency,
	(newCurrency) => {
		if (newCurrency === exchangeForm.value.toCurrency) {
			// 自动选择第一个可用的其他货币
			exchangeForm.value.toCurrency = availableToCurrencies.value[0];
		}
	},
);

// 修改验证规则的类型定义
const exchangeRules: FormRules = {
	fromCurrency: [{ required: true, message: "请选择源货币" }],
	toCurrency: [{ required: true, message: "请选择目标货币" }],
	amount: [
		{ required: true, message: "请输入兑换金额" },
		{
			validator: (
				_: unknown,
				value: number,
				callback: (error?: Error) => void,
			) => {
				if (value <= 0) {
					callback(new Error("请输入大于0的金额"));
				} else if (
					value > getAvailableBalance(exchangeForm.value.fromCurrency)
				) {
					callback(new Error("余额不足"));
				} else {
					callback();
				}
			},
			trigger: "change",
		},
	],
};

// 获取指定货币的可用余额
const getAvailableBalance = (currency: Currency) => {
	switch (currency) {
		case Currency.CNY:
			return balances.value.balance_cny;
		case Currency.HKD:
			return balances.value.balance_hkd;
		case Currency.USD:
			return balances.value.balance_usd;
		default:
			return 0;
	}
};

// 处理换汇操作
const handleExchange = async () => {
	if (!exchangeFormRef.value) return;

	await exchangeFormRef.value.validate(async (valid) => {
		if (valid) {
			exchangeLoading.value = true;
			try {
				await fundApi.exchangeCurrency(exchangeForm.value);
				ElMessage.success("兑换成功");
				exchangeDialogVisible.value = false;
				// 刷新余额和交易记录
				await fetchBalances();
				await fetchTransactions();
			} catch (error) {
				console.error("Exchange failed:", error);
				ElMessage.error("兑换失败");
			} finally {
				exchangeLoading.value = false;
			}
		}
	});
};

// 汇率相关状态
const currentRates = ref<{ USD_CNY: number; HKD_CNY: number } | null>(null);
const exchangeRate = computed(() => {
	if (
		!currentRates.value ||
		!exchangeForm.value.fromCurrency ||
		!exchangeForm.value.toCurrency
	) {
		return 0;
	}

	const { fromCurrency, toCurrency } = exchangeForm.value;
	const rates = currentRates.value;

	if (fromCurrency === Currency.CNY) {
		return toCurrency === Currency.HKD ? 1 / rates.HKD_CNY : 1 / rates.USD_CNY;
	}
	if (toCurrency === Currency.CNY) {
		return fromCurrency === Currency.HKD ? rates.HKD_CNY : rates.USD_CNY;
	}
	if (fromCurrency === Currency.HKD && toCurrency === Currency.USD) {
		return rates.HKD_CNY / rates.USD_CNY;
	}
	return rates.USD_CNY / rates.HKD_CNY; // USD to HKD
});

// 计算最终兑换所得
const calculatedAmount = computed(() => {
	if (!exchangeRate.value || !exchangeForm.value.amount) return 0;

	const amount = exchangeForm.value.amount * exchangeRate.value;
	return amount.toFixed(2);
});

// 获取当前汇率
const fetchRates = async () => {
	try {
		currentRates.value = await fundApi.getExchangeRates();
	} catch (error) {
		console.error("Failed to fetch rates:", error);
		ElMessage.error("获取汇率失败");
	}
};

// 监听对话框显示状态，显示时获取汇率
watch(exchangeDialogVisible, async (visible) => {
	if (visible) {
		await fetchRates();
	}
});

// 添加 activated 钩子
onActivated(async () => {
	// 余额需要每次都刷新，因为可能在其他页面发生变化
	await fetchBalances();

	// 交易记录只在为空时加载
	if (transactions.value.length === 0) {
		await fetchTransactions();
	}

	// 汇率需要每次都刷新
	await fetchRates();
});

onMounted(() => {
	// 只初始化设置，不加载数据
	initSettings();
	// 检查支付密码状态
	checkPaymentPassword();
	// 添加事件监听
	eventBus.on("order-updated", async () => {
		// 刷新余额和交易记录
		await fetchBalances();
		await fetchTransactions();
	});
});

// 组件卸载时移除事件监听
onUnmounted(() => {
	eventBus.off("order-updated", async () => {
		await fetchBalances();
		await fetchTransactions();
	});
});

// 转账相关的状态
const transferDialogVisible = ref(false);
const transferLoading = ref(false);
const transferFormRef = ref<FormInstance | null>(null);
const transferForm = ref({
	receiverName: "",
	receiverPhone: "",
	amount: 0,
});

// 转账表单验证规则
const transferRules: FormRules = {
	receiverName: [
		{ required: true, message: "请输入收款人姓名", trigger: "blur" },
		{ min: 2, max: 20, message: "姓名长度在2-20个字符之间", trigger: "blur" },
	],
	receiverPhone: [
		{ required: true, message: "请输入手机号", trigger: "blur" },
		{
			pattern: /^1[3-9]\d{9}$/,
			message: "请输入正确的手机号",
			trigger: "blur",
		},
	],
	amount: [
		{ required: true, message: "请输入转账金额", trigger: "blur" },
		{
			validator: (
				_: unknown,
				value: number,
				callback: (error?: Error) => void,
			) => {
				if (value <= 0) {
					callback(new Error("请输入大于0的金额"));
				} else if (value > balances.value.balance_cny) {
					callback(new Error("余额不足"));
				} else {
					callback();
				}
			},
			trigger: "blur",
		},
	],
};

// 转账权限控制
const hasTransferPermission = computed(() => useAuthStore().canTransfer);

// 处理转账对话框打开完成事件
const handleTransferDialogOpened = () => {
	const firstInput = document.querySelector(
		".transfer-dialog input",
	) as HTMLElement;
	firstInput?.focus();
};

// 处理转账操作
const handleTransfer = async () => {
	if (!transferFormRef.value) return;

	await transferFormRef.value.validate(async (valid) => {
		if (valid) {
			showPaymentDialog.value = true;
		}
	});
};

// 准备转账请求
const handleTransferRequest = async (password: string) => {
	// 发送转账请求
	await paymentApi.transfer({
		...transferForm.value,
		password,
	});
};

// 处理转账成功
const handleTransferSuccess = async () => {
	ElMessage.success("转账成功");
	transferDialogVisible.value = false;
	transferForm.value = { receiverName: "", receiverPhone: "", amount: 0 };
	await Promise.all([fetchBalances(), fetchTransactions()]);
};

// 添加支付密码相关状态
const showPaymentDialog = ref(false);
const hasPaymentPassword = ref(false);

// 检查支付密码状态
const checkPaymentPassword = async () => {
	try {
		const result = await paymentApi.checkPaymentPassword();
		hasPaymentPassword.value = result?.hasPassword || false;
	} catch (error) {
		console.error("Failed to check payment password:", error);
	}
};

// 重置转账表单
const resetTransferForm = () => {
	transferForm.value = {
		receiverName: "",
		receiverPhone: "",
		amount: 0,
	};
	nextTick(() => {
		transferFormRef.value?.clearValidate();
	});
};

// 重置换汇表单
const resetExchangeForm = () => {
	exchangeForm.value = {
		fromCurrency: Currency.CNY,
		toCurrency: Currency.HKD,
		amount: 0,
	};
	nextTick(() => {
		exchangeFormRef.value?.clearValidate();
	});
};

// 监听对话框显示状态
watch(transferDialogVisible, (newVisible) => {
	if (!newVisible) {
		resetTransferForm();
	}
});

watch(exchangeDialogVisible, (newVisible) => {
	if (!newVisible) {
		resetExchangeForm();
	}
});

// 显示转账对话框
const showTransferDialog = () => {
	transferDialogVisible.value = true;
	resetTransferForm();
};

// 显示换汇对话框
const showExchangeDialog = () => {
	exchangeDialogVisible.value = true;
	resetExchangeForm();
};

// 添加选中交易记录的状态
const selectedTransaction = ref<TransactionData | null>(null);

// 切换交易记录详情的显示
const toggleTransactionDetails = (transaction: TransactionData) => {
	if (selectedTransaction.value?.txn_id === transaction.txn_id) {
		selectedTransaction.value = null;
	} else {
		selectedTransaction.value = transaction;
	}
};
</script>

<style scoped>
.balance-row {
  padding: 20px;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    padding: 12px;
  }
}

.currency-label {
  font-size: 20px;
  font-weight: 500;
  color: var(--text-color-secondary);
  margin-bottom: 8px;

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    font-size: 14px;
    margin-bottom: 4px;
  }
}

:deep(.el-statistic__number) {
  font-size: 24px;
  color: var(--el-color-primary);

  /* 添加移动端适配 */
  @media (max-width: 768px) {
    font-size: 18px;
  }
}

:deep(.el-statistic__prefix) {
  margin-right: 8px;
  color: var(--text-color-secondary);
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.form-group {
  display: flex;
  align-items: center;
  margin: 5px;
}

.positive {
  color: var(--el-color-error);
}

.negative {
  color: var(--el-color-success);
}

.action-button {
  width: 58px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button-group {
  display: flex;
  gap: 8px;
}

.button-group .el-button {
  margin-left: 0;
}

.w-full {
  width: 100%;
}

.balance-hint {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: -10px;
  margin-bottom: 10px;
}

.exchange-preview {
  margin-top: 16px;
  padding: 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.rate-info {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-bottom: 8px;
}

.amount-preview {
  font-size: 16px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.amount-info {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-bottom: 8px;
}

.exchange-notice {
  margin-top: 12px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
  line-height: 1.5;
}

/* 在小屏幕下调整列布局 */
@media (max-width: 768px) {
  :deep(.el-col-8) {
    width: 100%;

    /* 最后一个不需要底部间距 */
    &:last-child {
      margin-bottom: 0;
    }
  }

  /* 调整卡片标题和按钮的布局 */
  .card-header {
    padding: 12px;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;

    .card-title {
      text-align: center;
    }

    .button-group {
      flex-direction: column;
      gap: 8px;
    }
  }

  .filter-row {
    gap: 8px;
  }
}

/* 添加平板布局支持 */
@media (min-width: 769px) and (max-width: 1024px) {
  :deep(.el-col-8) {
    width: 50%;
    margin-bottom: 16px;

    /* 最后一个货币单独占一行且居中 */
    &:last-child {
      width: 100%;
      padding: 0 25%;
    }
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .mobile-only {
    display: revert;
  }

  .data-table {
    min-width: unset;
    width: 100%;
  }

  .order-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .order-row:hover {
    background-color: var(--el-fill-color-light);
  }

  .order-row.selected {
    background-color: var(--el-fill-color);
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }

  .order-details {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 8px;
    font-size: 14px;
  }

  .detail-item {
    display: flex;
    padding: 6px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .detail-item:last-child {
    border-bottom: none;
  }

  .detail-item .label {
    color: var(--el-text-color-secondary);
    width: 70px;
    flex-shrink: 0;
  }

  .detail-item .value {
    flex-grow: 1;
  }

  /* 隐藏空行但保持其对奇偶计数的影响 */
  tr.dummy-row {
    display: none;
  }

  /* 详情展开动画 */
  .details-transition {
    animation: expand 0.25s ease-in-out forwards;
  }

  @keyframes expand {
    from {
      max-height: 0;
      opacity: 0.5;
    }

    to {
      max-height: 400px;
      opacity: 1;
    }
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }

  /* 货币后缀 */
  .currency-suffix {
    color: var(--el-text-color-regular);
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
}
</style>
