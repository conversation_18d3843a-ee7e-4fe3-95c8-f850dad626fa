## Auto-Deploy 补充方案文档：后续步骤与配置总结

**目标:** 实现 ink 项目（多站点实例）基于 Docker, Docker Compose 和 Traefik 的自动化部署、更新与管理。

**已选定技术栈:**

*   **容器化与编排:** Docker & Docker Compose
*   **反向代理与路由:** Traefik (自动服务发现、路由、HTTPS/SSL)

**核心部署策略:**

1.  **资源共享:**
    *   **前端 (Client/Admin):** 所有站点实例共享同一套前端构建产物（Docker 镜像）。Traefik 根据不同的域名 (`CLIENT_DOMAIN`, `ADMIN_DOMAIN`) 将请求路由到对应的共享前端服务容器（如果前端服务设计为可配置或无状态）。如果前端需要特定于站点的配置，则可能需要为每个站点部署独立的前端容器，但这会增加资源消耗。**当前倾向于共享前端容器。**
    *   **数据库 (PostgreSQL):** 使用**外部共享**的 PostgreSQL 实例。每个站点实例在此共享实例上拥有**独立的数据库**。
    *   **Redis:** 使用**外部共享**的 Redis 实例（如果项目需要 Redis）。
    *   **后端 (Server):** **每个站点实例独立部署**一个后端服务容器。
2.  **配置管理:**
    *   **环境变量:** 使用项目**根目录**下的 `.env` 文件作为**唯一**的环境变量来源。此文件由总控端的自动化部署脚本根据站点参数**动态生成**。
    *   `docker-compose.yml`: 作为模板文件，使用环境变量进行服务配置（如域名、项目名）。
3.  **自动化:** 部署、更新、删除操作由**总控端**发起，通过 SSH 连接目标服务器执行预定义的自动化脚本。

**后续关键步骤与配置：**

1.  **最终确定根 `.env` 文件模板 (`example.env` 或 `.env.template`):**
    *   在项目根目录创建/确认此模板文件。
    *   **必需包含的核心变量:**
        *   **实例标识符 (`APP_ID`):**
            *   `TRADING_PLATFORM_ID`: 交易台实例的唯一标识符 (如果部署的是交易台)。
            *   `CHANNEL_ID`: 通道实例的唯一标识符 (如果部署的是通道)。
            *   *注：`TRADING_PLATFORM_ID` 或 `CHANNEL_ID` 其中一个**必须被设置**。在本文档后续部分，我们将使用 **`APP_ID`** 作为此唯一标识符的统称。*
        *   `COMPOSE_PROJECT_NAME=ink_${APP_ID}`: 定义 Docker Compose 项目名称 (脚本生成时根据实例类型设置)。
        *   `CLIENT_DOMAIN`: 客户端访问域名。
        *   `ADMIN_DOMAIN`: 管理端访问域名。
        *   `NODE_ENV=production`: 指定生产环境。
        *   `IS_DOCKER_ENV=false`: (可选, 主要用于开发环境 Docker 测试, 生产环境通常保持 false)。
        *   `PORT=3000`: 后端容器内部监听端口。
        *   **数据库连接 (指向外部共享 PG):**
            *   `DB_HOST`: 共享数据库主机名/IP。
            *   `DB_PORT`: 共享数据库端口。
            *   `DB_USER`: 数据库用户名。
            *   `DB_PASSWORD`: 数据库密码。
            *   `DATABASE_NAME=ink_${APP_ID}`: **实例特定的数据库名称** (脚本生成时根据实例类型设置)。
        *   **Redis 连接 (指向外部共享 Redis):**
            *   `REDIS_HOST`: 共享 Redis 主机名/IP。
            *   `REDIS_PORT`: 共享 Redis 端口。
            *   `REDIS_PASSWORD`: Redis 密码 (如果有)。
        *   **安全密钥:** `JWT_SECRET`, `ENCRYPTION_KEY` (应为每个站点生成独立的强密钥)。
        *   `IS_DEMO`: (根据实例类型)。
        *   **其他 API 密钥/凭据:** `TUSHARE_TOKEN`, `NOWAPI_SIGN`, `MAIRUI_LICENSE`, `EMAIL_PASS`, `OPEN_API_KEY`, `MYSQL_PASSWORD` (如果需要，确保安全管理)。
        *   **邮箱配置:** (根据附文，需要明确如何在 `.env` 中配置，例如 `EMAIL_HOST`, `EMAIL_PORT`, `EMAIL_USER`, `EMAIL_PASS` 以及用户上传的通知邮箱地址变量)。
        *   **端口号约定:** (根据附文，需要明确是哪个服务的端口，例如后端服务映射到宿主机的端口 `SERVER_PORT_HOST`，格式如 `前缀3`?)
    *   **移除不再需要的变量:** 如 `IS_DOCKER_DEV`。

2.  **开发总控端自动化部署脚本:**
    *   **触发:** 接收总控端的部署请求，包含参数（如 `appId`，`clientDomain`, `adminDomain`, 数据库用户/密码，密钥等）。
    *   **执行步骤 (在目标服务器上):**
        1.  **SSH 连接:** 安全连接到目标服务器。
        2.  **准备目录:** 创建或进入站点部署目录 (例如 `/opt/ink-sites/ink_${APP_ID}` )。
        3.  **获取/更新代码:** `git clone` 或 `git pull`。
        4.  **生成 `.env` 文件:** 根据接收的参数和模板，创建 `.env` 文件。
        5.  **数据库准备:**
            *   **检查/创建数据库:** 使用提供的数据库凭据连接到**共享 PostgreSQL 实例**，执行 `CREATE DATABASE ink_${APP_ID}` (如果数据库不存在)。需要确保脚本有足够的权限执行此操作，或者由DBA预先创建。
            *   **数据库迁移:** 在站点目录中执行 `docker compose -p ink_${APP_ID} --env-file .env run --rm server pnpm prisma migrate deploy` (确保 `server` 服务在 compose 文件中定义了正确的数据库连接)。这将把 `prisma.schema` 中定义的结构应用到新数据库。
            *   **(按要求) 清理迁移目录:** 执行 `rm -rf packages/server/prisma/migrations`。
                *   **原因说明:** 此步骤是为了确保每次部署时，都是将 `schema.prisma` 的当前状态直接应用到（可能）新创建的数据库上，并避免将自动生成的 SQL 迁移文件纳入版本控制。 (注意：这不同于使用 `migrate dev` 进行迭代开发和依赖迁移历史的典型工作流)。
        6.  **启动服务:** 执行 `docker compose -p ink_${APP_ID} --env-file .env up -d --build --remove-orphans`。
        7.  **返回状态:** 向总控端报告成功或失败。

3.  **调整后端配置以适应部署模型:**
    *   **数据库连接:** 确认 `packages/server/src/config/configManager.ts` 和 `packages/server/src/config/defaultParams.ts` (或相关配置代码) 正确读取 `.env` 文件中的 `DB_HOST`, `DB_PORT`, `DATABASE_NAME=ink_${APP_ID}`, `DB_USER`, `DB_PASSWORD` 来连接**特定站点**的数据库。移除任何硬编码的连接或基于 `IS_DOCKER_DEV` 的逻辑。
    *   **Redis 连接:** 确认配置代码正确读取 `.env` 文件中的 `REDIS_HOST`, `REDIS_PORT`, `REDIS_PASSWORD` 连接共享 Redis。
    *   **上下游关系:**
        *   **管理方式:** 采用**总控端维护关系表**的方案。总控端数据库需要有一个表（例如 `site_relationships`）存储类似 `app_id`, `name`, `type ('platform'/'channel')`, `upstream_app_id` (对于 channel) 的信息。
        *   **后端获取:** 后端服务启动时或需要时，应能根据自身的 `APP_ID` 通过 API (如果总控端提供) 或其他机制 (如共享配置数据库) 查询其上下游关系信息，而不是依赖 `defaultParams.ts` 中的硬编码。**需要修改 `defaultParams.ts` 和依赖此文件的代码 (如 `channelFund.ts`)，移除硬编码的平台/通道配置和数据库 URL，改为动态获取或依赖注入。** 例如，`findChannelDBUrlFromConfig` 等函数需要重构。

4.  **调整 `docker-compose.yml` 模板:**
    *   **移除数据库和 Redis 服务定义** (因为它们是外部共享的)。
    *   确保 `server` 服务的 `env_file: .env` 指令存在。
    *   确保 `traefik`, `client`, `admin`, `server` 服务都使用环境变量（如 `${CLIENT_DOMAIN}`, `${ADMIN_DOMAIN}`, `${COMPOSE_PROJECT_NAME}`）进行配置。
    *   确保所有服务连接到同一个自定义网络 (如 `ink-network`)。

5.  **测试:**
    *   在 Staging 环境中完整测试总控端触发的自动化部署流程。
    *   验证数据库是否正确创建和迁移。
    *   验证站点是否能通过域名访问，功能是否正常，特别是涉及数据库和 Redis 的操作。
    *   验证后端服务是否能正确识别并连接其特定的数据库 (`ink_${APP_ID}`) 和共享 Redis。
    *   验证上下游关系是否能正确建立（如果相关功能已调整）。
    *   验证 Traefik 路由和 HTTPS 证书。

---

这个更新后的文档应该更准确地反映了你设想的自动化部署方案。请仔细检查环境变量、自动化脚本步骤以及后端代码需要进行的调整，特别是关于上下游关系管理的重构。 