import http from "../plugins/axios";
import type { AxiosRequestConfig } from "axios";

// 将通用请求方法抽离出来
export const request = {
	get: <T>(url: string, config?: AxiosRequestConfig) =>
		http.get<T>(url, config).then((res) => res?.data || null),

	post: <T, D = unknown>(url: string, data?: D, config?: AxiosRequestConfig) =>
		http.post<T>(url, data, config).then((res) => res?.data || null),

	put: <T, D = unknown>(url: string, data?: D, config?: AxiosRequestConfig) =>
		http.put<T>(url, data, config).then((res) => res?.data || null),

	delete: <T>(url: string, config?: AxiosRequestConfig) =>
		http.delete<T>(url, config).then((res) => res?.data || null),
};
