import { appRedis } from "@/lib/redis.js";
import * as SharedConfigModel from "@/models/sharedConfig.js";
import { AppError } from "@/core/appError.js";
import { isChannel } from "@/config/configManager.js";
import logger from "@/utils/logger.js";
import type { SharedConfig } from "@packages/shared";

// 为不同类型配置定义缓存键前缀
const SHARED_CONFIG_CACHE_KEY = "config:shared";
const CACHE_TTL = 3600; // 1小时缓存

/**
 * 设置共享配置缓存
 */
async function setSharedConfigCache(configData: SharedConfig): Promise<void> {
	try {
		await appRedis.setex(
			SHARED_CONFIG_CACHE_KEY,
			CACHE_TTL,
			JSON.stringify(configData),
		);
	} catch (error) {
		logger.error(
			`Failed to set shared config cache: ${error instanceof Error ? error.message : String(error)}`,
		);
		// 不抛出异常，仅记录日志，缓存失败不影响业务
	}
}

/**
 * 清除共享配置缓存
 */
async function clearSharedConfigCache(): Promise<void> {
	try {
		await appRedis.del(SHARED_CONFIG_CACHE_KEY);
	} catch (error) {
		logger.error(
			`Failed to clear shared config cache: ${error instanceof Error ? error.message : String(error)}`,
		);
		// 不抛出异常，仅记录日志
	}
}

/**
 * 获取共享配置
 * @returns 共享配置数据
 */
export async function getSharedConfig(): Promise<SharedConfig> {
	try {
		// 通道端直接从数据库获取最新配置，避免使用交易台修改后的过期缓存
		if (isChannel()) {
			const allConfigsFromDb = await SharedConfigModel.getSharedConfig();
			return allConfigsFromDb || ({} as SharedConfig);
		}

		// 优先从缓存获取
		const cached = await appRedis.get(SHARED_CONFIG_CACHE_KEY);

		if (cached) {
			return JSON.parse(cached) as SharedConfig;
		}

		// 从数据库获取完整的共享配置
		const allConfigsFromDb = await SharedConfigModel.getSharedConfig();

		const configData = allConfigsFromDb || ({} as SharedConfig);

		// 缓存配置数据
		await setSharedConfigCache(configData);
		return configData;
	} catch (error) {
		logger.error(
			`Failed to get shared config: ${error instanceof Error ? error.message : String(error)}`,
		);
		throw error instanceof AppError
			? error
			: AppError.create("NOT_FOUND", "Failed to get shared configuration");
	}
}

/**
 * 更新共享配置
 * @param config 配置数据
 * @param admin_id 管理员ID
 */
export async function updateSharedConfig(
	config: SharedConfig,
	admin_id?: number,
): Promise<{ message: string }> {
	try {
		// 获取当前完整的共享配置
		const currentFullConfig = await SharedConfigModel.getSharedConfig();

		// 创建更新后的完整配置对象
		// 如果当前没有配置，则初始化为空对象
		const updatedFullConfig: SharedConfig = {
			...(currentFullConfig || {}),
			...config,
		};

		// 保存到数据库 (整个配置对象)
		await SharedConfigModel.saveSharedConfig(updatedFullConfig, admin_id);

		// 清除缓存
		await clearSharedConfigCache();

		return { message: "Shared configuration updated successfully" };
	} catch (error) {
		logger.error(
			`Failed to update shared config: ${error instanceof Error ? error.message : String(error)}`,
		);
		throw error instanceof AppError
			? error
			: AppError.create("BAD_REQUEST", "Failed to update shared configuration");
	}
}
