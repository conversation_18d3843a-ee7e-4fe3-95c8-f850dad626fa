import { Router } from "express";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import * as adminAuthService from "@/services/admin/authService.js";

const router = Router();

// Update admin password: POST /api/admin/auth/password
router.post(
	"/password",
	wrapAdminRoute<UpdatePasswordBody>(async (req, res) => {
		const { oldPassword, newPassword } = req.body;
		await adminAuthService.updatePassword(
			req.jwt.admin_id,
			oldPassword,
			newPassword,
		);
		res.status(200).json({ message: "Password updated successfully" });
	}),
);

export default router;

interface UpdatePasswordBody {
	oldPassword: string;
	newPassword: string;
}
