import axios from "axios";
import dns from "node:dns";
import { promisify } from "node:util";
import http from "node:http";
import https from "node:https";
import logger from "@/utils/logger.js";
import { Mutex, Semaphore } from "async-mutex";

/**
 * !!! 重要安全警告 !!!
 *
 * 本DNS故障转移系统存在严重的并发问题风险。如果处理不当，会导致:
 * 1. 并发查询冲突 - 当多个请求同时尝试修改DNS服务器列表时，c-ares库会抛出
 *    "c-ares failed to set servers: There are pending queries"错误
 * 2. 无限循环风险 - 所有DNS服务器被标记为不可用后，系统重置所有服务器，
 *    若立即再次因冲突失败，将导致无限重置-失败循环
 * 3. 磁盘I/O爆炸 - 大量错误日志写入，造成磁盘I/O饱和(可达数百次/秒)
 * 4. 系统不可用 - 所有外部API请求无法解析域名而失败
 *
 * 根本原因是：
 * Node.js的DNS解析使用c-ares库，该库不允许在有未完成查询时修改DNS服务器列表。
 * 当一个DNS查询正在进行时，如果尝试修改同一个resolver实例的服务器列表（通过setServers方法）
 * ，c-ares会抛出"There are pending queries"错误，因为这可能导致查询状态不一致。
 *
 * c-ares库的内部状态：
 * c-ares库在内部维护了一个查询状态机
 * 当有查询正在进行时，这个状态机处于活跃状态
 * 直接修改服务器列表会破坏这个状态机的完整性
 *
 * 并发冲突：
 *    时间轴 - 未创建新实例的情况下：
 *    t1: 请求A开始DNS查询
 *    t2: 请求B尝试修改DNS服务器列表
 *    t3: 请求A的查询仍在进行中
 *    t4: 修改服务器列表导致请求A的查询状态被破坏
 *
 * 并发报错无限循环:
 *    时间轴 - 未使用新建实例和锁机制的情况：
 *    t1: 请求B正在查询中，使用DNS服务器1
 *    t2: 请求A查询过程检测到DNS服务器1有问题，尝试切换到DNS服务器2
 *    t3: 由于请求B的查询仍在进行中，c-ares抛出"There are pending queries"错误
 *    t4: 系统错误地认为DNS服务器2有问题，将其标记为不可用
 *    t5: 请求A尝试使用DNS服务器3
 *    t6: 同样由于请求A的查询仍在进行，c-ares再次抛出错误
 *    t7: DNS服务器3被错误标记为不可用
 *    t8: 所有DNS服务器最终被错误标记为不可用
 *    t9: 系统重置所有DNS服务器状态
 *    t10: 新的请求开始，循环回到t1...
 *
 * 设计原则和注意事项:
 * 1. DNS解析器状态必须有并发控制机制，防止多个请求同时修改服务器列表
 * 2. 应实现请求去重，避免对同一域名重复发起DNS查询
 * 3. 失败后的恢复策略应具有退避机制，避免立即重试导致的级联失败
 * 4. 在高并发情况下应限制最大并发DNS请求数量
 * 5. 应设置查询超时，防止查询挂起
 * 6. 不应在错误状态下重置所有DNS服务器，而应采用部分重置策略
 *
 * 此类错误在系统重启后通常会暂时消失，因为所有状态被重置，
 * 但根本原因若不修复，问题会在高负载时再次出现。
 */

// 配置
const CONFIG = {
	DNS_SERVERS: [
		"119.29.29.29", // 腾讯 DNS
		"119.28.28.28", // 腾讯 DNS 备用
		"1.1.1.1", // Cloudflare DNS
		"1.0.0.1", // Cloudflare DNS 备用
		"8.8.8.8", // Google DNS
		"8.8.4.4", // Google DNS 备用
		"114.114.114.114", // 114 DNS
		"114.114.115.115", // 114 DNS 备用
		"223.5.5.5", // 阿里 DNS
		"223.6.6.6", // 阿里 DNS 备用
	],
	DNS_TIMEOUT: 2000, // DNS 解析超时时间
	HTTP_TIMEOUT: 5000, // HTTP 请求超时时间
	RECOVERY_TIME: 24 * 60 * 60 * 1000, // DNS 服务器恢复时间 (24小时)
} as const;

// DNS 服务器状态管理
export class DNSServerManager {
	private static instance: DNSServerManager;
	private resolver: dns.Resolver;
	private serverStatus: Map<
		string,
		{ isAvailable: boolean; lastFailure: number | null }
	>;
	private currentServer: string | null = null;

	/**
	 * 以下属性用于解决并发问题:
	 * - pendingQueries: 跟踪正在进行的查询，避免对同一域名重复查询
	 */
	private pendingQueries = new Map<string, Promise<string>>();
	private semaphore = new Semaphore(5); // 最大并发数
	private mutex = new Mutex();

	private constructor() {
		this.resolver = new dns.Resolver();
		this.serverStatus = new Map(
			CONFIG.DNS_SERVERS.map((server) => [
				server,
				{ isAvailable: true, lastFailure: null },
			]),
		);
	}

	public static getInstance(): DNSServerManager {
		if (!DNSServerManager.instance) {
			DNSServerManager.instance = new DNSServerManager();
		}
		return DNSServerManager.instance;
	}

	public getConfiguredServers(): string[] {
		return [...this.serverStatus.keys()];
	}

	public getCurrentServer(): string | null {
		return this.currentServer;
	}

	public getServerStatusMap(): Map<
		string,
		{ isAvailable: boolean; lastFailure: number | null }
	> {
		return new Map(this.serverStatus);
	}

	private getAvailableServers(): string[] {
		const now = Date.now();
		const available: string[] = [];

		// 检查并恢复超时的服务器
		for (const [server, status] of this.serverStatus.entries()) {
			if (
				!status.isAvailable &&
				status.lastFailure &&
				now - status.lastFailure > CONFIG.RECOVERY_TIME
			) {
				status.isAvailable = true;
				status.lastFailure = null;
				logger.info(`DNS server ${server} restored after timeout`);
			}
			if (status.isAvailable) {
				available.push(server);
			}
		}

		// 如果没有可用的服务器，智能重置部分服务器
		if (available.length === 0) {
			logger.warn("No available DNS servers, resetting some servers");
			// 随机选择3个服务器重置，避免全部重置引发新的风暴，遵循"渐进式退避和恢复"的原则
			const servers = Array.from(this.serverStatus.keys());
			const serversToReset = servers
				.sort(() => 0.5 - Math.random())
				.slice(0, Math.min(3, servers.length));

			for (const server of serversToReset) {
				const status = this.serverStatus.get(server);
				if (status) {
					status.isAvailable = true;
					status.lastFailure = null;
					available.push(server);
					logger.info(`Reset DNS server ${server} to available state`);
				}
			}
		}

		return available;
	}

	private markServerFailed(server: string, reason?: string): void {
		const status = this.serverStatus.get(server);
		if (status) {
			status.isAvailable = false;
			status.lastFailure = Date.now();
			// 只在没有提供原因时记录简单警告
			if (!reason) {
				logger.warn(`DNS server ${server} marked as unavailable`);
			} else {
				logger.warn(`DNS server ${server} marked as unavailable: ${reason}`);
			}
		}
	}

	/**
	 * 安全地修改DNS服务器
	 * 通过锁机制确保同一时间只有一个请求可以修改服务器设置
	 * 防止"c-ares failed to set servers: There are pending queries"错误
	 */
	private async safeSetServer(server: string): Promise<void> {
		if (this.currentServer === server) return;

		const release = await this.mutex.acquire();
		try {
			const newResolver = new dns.Resolver();
			newResolver.setServers([server]);
			this.resolver = newResolver;
			this.currentServer = server;
		} finally {
			release();
		}
	}

	public async resolve(hostname: string): Promise<string> {
		// 仍然需要检查和利用pendingQueries
		const pendingQuery = this.pendingQueries.get(hostname);
		if (pendingQuery) return pendingQuery;

		// 创建新的查询Promise并立即存储
		const queryPromise = (async () => {
			// 使用信号量控制并发
			const [permits, release] = await this.semaphore.acquire();
			try {
				return await this._doResolve(hostname);
			} finally {
				release();
			}
		})();

		// 存储promise并设置自清理
		this.pendingQueries.set(hostname, queryPromise);
		queryPromise.finally(() => {
			this.pendingQueries.delete(hostname);
		});

		return queryPromise;
	}

	// 将原来的resolve逻辑移到这个私有方法
	private async _doResolve(hostname: string): Promise<string> {
		// 检查是否已经是IP地址，如果是则直接返回
		if (/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/.test(hostname)) {
			return hostname;
		}

		const availableServers = this.getAvailableServers();
		const resolve4Async = promisify(this.resolver.resolve4.bind(this.resolver));

		// 记录尝试次数
		let attempts = 0;
		const maxAttempts = availableServers.length * 2; // 每个服务器最多尝试两次

		while (attempts < maxAttempts) {
			for (const server of availableServers) {
				try {
					// 如果服务器改变，重新设置
					await this.safeSetServer(server);

					// 使用 Promise.race 添加超时控制
					const addresses = await Promise.race([
						resolve4Async(hostname),
						new Promise<string[]>((_, reject) =>
							setTimeout(
								() =>
									reject(
										new Error(
											`DNS resolution timeout for ${hostname} using ${server}`,
										),
									),
								CONFIG.DNS_TIMEOUT,
							),
						),
					]);

					if (
						!addresses?.[0] ||
						!/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/.test(addresses[0])
					) {
						throw new Error(
							`Invalid IP address resolved for ${hostname}: ${addresses?.[0]}`,
						);
					}

					return addresses[0];
				} catch (error) {
					// 直接将错误原因传递给markServerFailed，减少重复日志
					this.markServerFailed(
						server,
						`${hostname}解析失败: ${(error as Error).message}`,
					);
					attempts++;

					// 在切换到下一个服务器之前等待一小段时间
					await new Promise((resolve) => setTimeout(resolve, 200));
				}
			}
		}

		throw new Error(
			`DNS resolution failed for ${hostname} using all available servers after ${attempts} attempts`,
		);
	}

	public markServerFailedForTesting(server: string, reason: string): void {
		this.markServerFailed(server, reason);
	}
}

// 创建 DNS 管理器实例
const dnsManager = DNSServerManager.getInstance();

// DNS 查找函数 - 改进类型安全性
const dnsLookup = (
	hostname: string,
	optionsOrCallback:
		| dns.LookupOptions
		| number
		| ((
				err: NodeJS.ErrnoException | null,
				address: string,
				family: number,
		  ) => void),
	maybeCallback?: (
		err: NodeJS.ErrnoException | null,
		address: string,
		family: number,
	) => void,
): void => {
	// 初始化变量，避免重新赋值参数
	let actualOptions: dns.LookupOptions;
	let actualCallback: (
		err: NodeJS.ErrnoException | null,
		address: string,
		family: number,
	) => void;

	// 处理不同形式的参数
	if (typeof optionsOrCallback === "function") {
		actualCallback = optionsOrCallback;
		actualOptions = { family: 0 };
	} else if (typeof optionsOrCallback === "number") {
		actualOptions = { family: optionsOrCallback };
		actualCallback = maybeCallback || (() => {});
	} else {
		actualOptions = optionsOrCallback;
		actualCallback = maybeCallback || (() => {});
	}

	if (typeof actualCallback !== "function") {
		throw new Error("Callback function is required");
	}

	if (!hostname) {
		const err = new Error("Invalid hostname") as NodeJS.ErrnoException;
		err.code = "ENOTFOUND";
		err.errno = -2;
		err.syscall = "getaddrinfo";
		actualCallback(err, "", 4);
		return;
	}

	// 使用我们的DNS管理器
	dnsManager
		.resolve(hostname)
		.then((address) => {
			if (!address) {
				const err = new Error(
					`DNS resolution failed for ${hostname}`,
				) as NodeJS.ErrnoException;
				err.code = "ENOTFOUND";
				err.errno = -2;
				err.syscall = "getaddrinfo";
				actualCallback(err, "", 4);
				return;
			}

			logger.debug(`DNS管理器成功解析 ${hostname} 到 ${address}`);
			actualCallback(null, address, 4);
		})
		.catch((error) => {
			logger.warn(`DNS解析 ${hostname} 失败: ${(error as Error).message}`);

			// 直接返回错误，不使用系统DNS作为后备
			const err = new Error(
				`DNS解析失败: ${(error as Error).message}`,
			) as NodeJS.ErrnoException;
			err.code = "ENOTFOUND";
			err.errno = -2;
			err.syscall = "getaddrinfo";
			actualCallback(err, "", 4);
		});
};

// 使用内置Agent - 配置为使用我们的DNS查找功能
const httpAgent = new http.Agent({
	keepAlive: true,
	keepAliveMsecs: 3000,
	maxSockets: 100,
	timeout: CONFIG.HTTP_TIMEOUT,
	lookup: dnsLookup, // 使用我们的DNS故障转移系统
});

const httpsAgent = new https.Agent({
	keepAlive: true,
	keepAliveMsecs: 3000,
	maxSockets: 100,
	timeout: CONFIG.HTTP_TIMEOUT,
	lookup: dnsLookup, // 使用我们的DNS故障转移系统
});

// 创建 axios 实例
const axiosWithDNSFailover = axios.create({
	httpAgent,
	httpsAgent,
	timeout: CONFIG.HTTP_TIMEOUT,
	maxRedirects: 5,
	validateStatus: (status) => status >= 200 && status < 300,
	headers: {
		"User-Agent":
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		Accept:
			"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
		"Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
		Connection: "keep-alive",
	},
});

// 请求拦截器 - 修改为直接使用IP地址
axiosWithDNSFailover.interceptors.request.use(
	async (config) => {
		config.headers = config.headers || {};
		// 保持默认请求头
		config.headers["User-Agent"] =
			config.headers["User-Agent"] ||
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";

		try {
			// 解析原始URL
			const originalUrl = new URL(config.url || "");

			// 使用我们的DNS管理器获取IP
			const ip = await dnsManager.resolve(originalUrl.hostname);
			logger.debug(`成功解析 ${originalUrl.hostname} 到 ${ip}`);

			// 修改请求配置，将URL中的主机名替换为IP
			const protocol = originalUrl.protocol;
			const port = originalUrl.port ? `:${originalUrl.port}` : "";
			const path = originalUrl.pathname + originalUrl.search;

			// 替换为IP地址
			const ipUrl = `${protocol}//${ip}${port}${path}`;
			config.url = ipUrl;

			// 设置Host头，确保服务器正确处理请求
			config.headers.Host = originalUrl.hostname;
		} catch (error) {
			logger.warn(
				`DNS解析失败 ${config.url}: ${error instanceof Error ? error.message : String(error)}`,
			);
			// 保留原始URL
		}

		return config;
	},
	(error) => Promise.reject(error),
);

// 响应拦截器
axiosWithDNSFailover.interceptors.response.use(
	(response) => {
		// 记录成功信息和响应状态
		const url = response.config.url || "";
		return response;
	},
	async (error) => {
		const config = error.config;

		// 增强错误日志
		if (config) {
			const url = config.url || "未知URL";
			logger.warn(
				`请求失败: ${url}, 错误: ${error.message}, 代码: ${error.code}`,
			);
		}

		// 处理IP地址无效错误 - 记录更多信息以便调试
		if (error.code === "ERR_INVALID_IP_ADDRESS") {
			try {
				if (!config?.url) {
					throw new Error("Missing URL in config");
				}

				const url = new URL(config.url);
				logger.error(`发现IP地址无效错误: ${url.href}`);
				logger.error(`错误详情: ${error.stack || error.message}`);

				// 不使用回退，让错误正常传播以便调试
				return Promise.reject(error);
			} catch (innerError) {
				logger.error(innerError, "处理IP地址无效错误时出错");
			}
		}

		// 处理 DNS 相关错误
		if (
			error.code === "ENOTFOUND" ||
			error.code === "ECONNREFUSED" ||
			error.code === "ETIMEDOUT" ||
			error.code === "ECONNABORTED"
		) {
			try {
				if (!config?.url) {
					throw new Error("Missing URL in config");
				}

				// 检查重试次数，防止无限重试
				const retryAttempt = config.headers?.["X-Retry-Attempt"]
					? Number.parseInt(config.headers["X-Retry-Attempt"] as string, 10)
					: 0;
				if (retryAttempt >= 2) {
					logger.warn(
						`已达到最大重试次数(${retryAttempt})，不再重试: ${config.url}`,
					);
					return Promise.reject(error);
				}

				const url = new URL(config.url);

				// 获取原始域名（保存在Host头中）
				const originalHostname = config.headers?.Host;

				// 如果没有原始域名，无法进行重试
				if (!originalHostname) {
					logger.warn(`无法找到原始域名，无法重试请求: ${url.hostname}`);
					return Promise.reject(error);
				}

				// 只有当originalHostname不是IP地址时才进行DNS解析
				logger.info(`尝试重新解析域名: ${originalHostname}`);
				let ip: string;
				if (/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/.test(originalHostname)) {
					// 如果原始主机名是IP地址，直接使用它
					logger.info(
						`originalHostname已经是IP地址: ${originalHostname}，跳过解析`,
					);
					ip = originalHostname;
				} else {
					// 只有当不是IP地址时才进行解析
					ip = await dnsManager.resolve(originalHostname);
					logger.info(`重新解析成功，IP: ${ip}, 准备重试请求`);
				}

				// 更新配置
				config.headers = config.headers || {};
				config.headers.Host = originalHostname;
				config.headers["X-Retry-Attempt"] = (retryAttempt + 1).toString();

				// 重建URL，使用新解析的IP地址
				const protocol = url.protocol;
				const port = url.port ? `:${url.port}` : "";
				const path = url.pathname + url.search;
				config.url = `${protocol}//${ip}${port}${path}`;

				// 增加超时时间
				config.timeout = CONFIG.HTTP_TIMEOUT * 2;

				return axiosWithDNSFailover(config);
			} catch (dnsError) {
				logger.error(dnsError, "DNS resolution failed after retry");
			}
		}

		return Promise.reject(error);
	},
);

export default axiosWithDNSFailover;

// 公开的测试方法，用于手动调试DNS连接问题
export async function testDNSConnection(
	domains: string[] = [
		"www.baidu.com",
		"www.qq.com",
		"www.aliyun.com",
		"qt.gtimg.cn",
		"api.tushare.pro",
	],
): Promise<Record<string, string | Error>> {
	const results: Record<string, string | Error> = {};

	logger.info("开始DNS连接测试...");

	// 显示DNS服务器配置
	logger.info("=== DNS服务器配置 ===");
	logger.info(`已配置的DNS服务器: ${CONFIG.DNS_SERVERS.join(", ")}`);

	// 显示DNS服务器状态
	const serverStatus = dnsManager.getServerStatusMap();
	logger.info("DNS服务器状态:");
	for (const [server, status] of serverStatus.entries()) {
		logger.info(
			`  - ${server}: ${status.isAvailable ? "可用" : "不可用"}${!status.isAvailable && status.lastFailure ? ` (故障时间: ${new Date(status.lastFailure).toISOString()})` : ""}`,
		);
	}

	const currentServer = dnsManager.getCurrentServer();
	if (currentServer) {
		logger.info(`当前使用的DNS服务器: ${currentServer}`);
	} else {
		logger.info("当前未选定DNS服务器");
	}

	logger.info("=== 开始DNS解析测试 ===");
	for (const domain of domains) {
		try {
			logger.info(`尝试解析 ${domain}...`);
			const ip = await dnsManager.resolve(domain);
			logger.info(`✅ ${domain} 解析成功: ${ip}`);

			// 测试HTTP请求 - 根据域名选择协议
			const protocol = domain === "api.tushare.pro" ? "http" : "https";
			try {
				logger.info(`尝试${protocol.toUpperCase()}请求 ${domain}...`);
				const startTime = Date.now();

				// 使用DNS增强版本发起请求
				const response = await axiosWithDNSFailover.get(
					`${protocol}://${domain}`,
					{
						// 设置更宽松的超时
						timeout: 10000,
					},
				);

				const responseTime = Date.now() - startTime;
				logger.info(
					`✅ ${protocol.toUpperCase()}请求 ${domain} 成功，响应时间: ${responseTime}ms，状态码: ${response.status}`,
				);
				results[domain] =
					`${ip} (HTTP响应: ${response.status}, ${responseTime}ms)`;
			} catch (httpError) {
				// 记录更详细的错误信息
				const error = httpError as Error;
				const axiosError = httpError as import("axios").AxiosError;
				const errorCode = axiosError.code || "UNKNOWN";
				const errorMessage = error.message || "Unknown error";
				const responseStatus = axiosError.response?.status || "No status";

				logger.error(
					`❌ ${protocol.toUpperCase()}请求 ${domain} 失败: [${errorCode}] ${errorMessage}`,
				);

				// 记录更多调试信息
				if (errorCode === "ERR_INVALID_IP_ADDRESS") {
					logger.error(`  IP地址无效错误。域名: ${domain}, IP: ${ip}`);
					if (error.stack) {
						logger.error(`  错误堆栈: ${error.stack}`);
					}
				}

				if (axiosError.response) {
					logger.error(
						axiosError.response.data,
						`  响应状态: ${responseStatus}`,
					);
				}

				if (axiosError.request) {
					logger.error("  请求已发送但无响应");
				}

				results[domain] =
					`${ip} (${protocol.toUpperCase()}请求失败: [${errorCode}] ${errorMessage})`;
			}
		} catch (err) {
			logger.error(`❌ ${domain} 解析失败: ${(err as Error).message}`);
			results[domain] = err as Error;
		}
	}

	logger.info(results, "DNS连接测试完成");
	return results;
}
