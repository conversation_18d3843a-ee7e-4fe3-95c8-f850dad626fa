import { Router } from "express";
import * as fundService from "@/services/fundService.js";
import { wrapUserRoute } from "@/utils/routeWrapper.js";
import * as userModel from "@/models/user.js";
import type {
	TransactionType,
	TransferData,
	ExchangeCurrencyData,
} from "@packages/shared";
import { exchangeRateService } from "@/utils/exchangeRate.js";
const router = Router();

/**
 * Get user's account balance
 * @route GET /api/fund/balance
 * @returns {Promise<ServiceResponse>} Response containing user's balance
 */
router.get(
	"/balances",
	wrapUserRoute(async (req, res) => {
		const balances = await userModel.getBalances(req.jwt.user_id);
		res.status(200).json(balances);
	}),
);

/**
 * Get transaction history
 * @route GET /api/fund/history
 */
router.get(
	"/history",
	wrapUserRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const isDescending = req.query.isDescending !== "false";
		const { types, startDate, endDate } = req.query;

		const response = await fundService.getHistory(
			page,
			pageSize,
			isDescending,
			{
				user_id: req.jwt.user_id,
				types: types
					? ((types as string).split(",") as TransactionType[])
					: undefined,
				startDate: startDate as string,
				endDate: endDate as string,
			},
		);
		res.status(200).json(response);
	}),
);

/**
 * Exchange currency
 * @route POST /api/fund/exchange
 */
router.post(
	"/exchange",
	wrapUserRoute<ExchangeCurrencyData>(async (req, res) => {
		const { fromCurrency, toCurrency, amount } = req.body;
		await fundService.exchangeCurrency(
			req.jwt.user_id,
			fromCurrency,
			toCurrency,
			amount,
		);
		res.status(200).json({ message: "Currency exchanged successfully" });
	}),
);

/**
 * Get current exchange rates
 * @route GET /api/fund/rates
 */
router.get(
	"/rates",
	wrapUserRoute(async (_req, res) => {
		const rates = await exchangeRateService.getCurrentRates();
		res.status(200).json(rates);
	}),
);

/**
 * Transfer money to another user
 * @route POST /api/fund/transfer
 */
router.post(
	"/transfer",
	wrapUserRoute<TransferData>(async (req, res) => {
		const { receiverName, receiverPhone, amount, password } = req.body;
		const user_id = req.jwt.user_id;

		await fundService.transfer(
			user_id,
			receiverName,
			receiverPhone,
			amount,
			password,
		);
		res.status(200).json({ message: "Transfer completed successfully" });
	}),
);

/**
 * Check payment password
 * @route GET /api/fund/has-password
 */
router.get(
	"/has-password",
	wrapUserRoute(async (req, res) => {
		const hasPassword = await fundService.hasPaymentPassword(req.jwt.user_id);
		res.status(200).json({ hasPassword });
	}),
);

/**
 * Set payment password
 * @route POST /api/fund/set-password
 */
router.post(
	"/set-password",
	wrapUserRoute<{ password: string }>(async (req, res) => {
		const { password } = req.body;
		await fundService.setPaymentPassword(req.jwt.user_id, password);
		res.status(200).json({ message: "Payment password set successfully" });
	}),
);

export default router;
