import { getChildLogger } from "@/utils/logger.js";
import { inkSyncConfig } from "./config.js";

const logger = getChildLogger("RetryHandler");

/**
 * 重试配置接口
 *
 * 定义重试行为的各种参数，支持灵活的重试策略配置
 */
export interface RetryOptions {
	maxAttempts?: number; // 最大重试次数
	delayMs?: number; // 初始延迟时间（毫秒）
	backoffMultiplier?: number; // 退避倍数（指数退避）
	maxDelayMs?: number; // 最大延迟时间（毫秒）
	retryCondition?: (error: Error) => boolean; // 重试条件判断函数
}

/**
 * 断路器状态枚举
 *
 * 断路器模式的三种状态：
 * - CLOSED: 正常状态，允许请求通过
 * - OPEN: 断开状态，直接拒绝请求
 * - HALF_OPEN: 半开状态，允许少量请求测试服务恢复情况
 */
enum CircuitState {
	CLOSED = "CLOSED", // 闭合状态：正常工作
	OPEN = "OPEN", // 打开状态：服务异常，拒绝请求
	HALF_OPEN = "HALF_OPEN", // 半开状态：尝试恢复
}

/**
 * 断路器类
 *
 * 实现断路器模式，防止级联故障：
 * - 监控失败率，超过阈值时打开断路器
 * - 在打开状态下直接拒绝请求，避免无效调用
 * - 定期尝试恢复，检测服务是否已修复
 *
 * 设计特点：
 * - 自动故障检测和恢复
 * - 快速失败，减少资源浪费
 * - 渐进式恢复，避免服务过载
 */
export class CircuitBreaker {
	private state = CircuitState.CLOSED; // 当前状态
	private failureCount = 0; // 失败计数
	private lastFailureTime = 0; // 最后失败时间
	private successCount = 0; // 成功计数（半开状态下）
	private readonly logger = getChildLogger("CircuitBreaker");

	constructor(
		private name: string, // 断路器名称
		private threshold = inkSyncConfig.circuitBreakerThreshold, // 失败阈值
		private timeout = 60000, // 超时时间（毫秒）
		private halfOpenMaxCalls = 3, // 半开状态最大调用次数
	) {}

	/**
	 * 执行受保护的操作
	 *
	 * 根据断路器状态决定是否执行操作：
	 * - CLOSED: 直接执行
	 * - OPEN: 检查超时，决定是否转为HALF_OPEN
	 * - HALF_OPEN: 限制调用次数，根据结果决定状态转换
	 *
	 * @param fn 要执行的异步函数
	 * @returns 执行结果
	 */
	async execute<T>(fn: () => Promise<T>): Promise<T> {
		if (this.state === CircuitState.OPEN) {
			// 检查是否超过超时时间，可以尝试恢复
			if (Date.now() - this.lastFailureTime < this.timeout) {
				throw new Error(`Circuit breaker is OPEN for ${this.name}`);
			}
			// 转为半开状态，尝试恢复
			this.state = CircuitState.HALF_OPEN;
			this.successCount = 0;
			this.logger.info(`Circuit breaker ${this.name} moved to HALF_OPEN`);
		}

		try {
			const result = await fn();
			this.onSuccess();
			return result;
		} catch (error) {
			this.onFailure();
			throw error;
		}
	}

	/**
	 * 处理成功情况
	 *
	 * 成功时的状态转换逻辑：
	 * - CLOSED: 重置失败计数
	 * - HALF_OPEN: 增加成功计数，达到阈值后转为CLOSED
	 */
	private onSuccess(): void {
		this.failureCount = 0;

		if (this.state === CircuitState.HALF_OPEN) {
			this.successCount++;
			if (this.successCount >= this.halfOpenMaxCalls) {
				this.state = CircuitState.CLOSED;
				this.logger.info(`Circuit breaker ${this.name} moved to CLOSED`);
			}
		}
	}

	/**
	 * 处理失败情况
	 *
	 * 失败时的状态转换逻辑：
	 * - 增加失败计数
	 * - 记录失败时间
	 * - 检查是否需要打开断路器
	 */
	private onFailure(): void {
		this.failureCount++;
		this.lastFailureTime = Date.now();

		if (this.state === CircuitState.HALF_OPEN) {
			// 半开状态下失败，直接转为打开状态
			this.state = CircuitState.OPEN;
			this.logger.warn(
				`Circuit breaker ${this.name} moved to OPEN (from HALF_OPEN)`,
			);
		} else if (this.failureCount >= this.threshold) {
			// 失败次数达到阈值，打开断路器
			this.state = CircuitState.OPEN;
			this.logger.warn(
				`Circuit breaker ${this.name} moved to OPEN (threshold reached)`,
			);
		}
	}

	/**
	 * 获取断路器状态
	 *
	 * @returns 状态信息对象
	 */
	getStatus() {
		return {
			state: this.state,
			failureCount: this.failureCount,
			lastFailureTime: this.lastFailureTime,
			successCount: this.successCount,
		};
	}
}

/**
 * 带重试和断路器的执行器
 *
 * 核心功能：
 * - 智能重试机制：支持指数退避、自定义重试条件
 * - 断路器保护：防止级联故障
 * - 灵活配置：支持运行时配置调整
 * - 全面监控：记录重试和断路器状态
 *
 * 使用场景：
 * - 外部API调用
 * - 数据库操作
 * - 网络请求
 * - 任何可能失败的异步操作
 */
export class RetryHandler {
	private circuitBreakers = new Map<string, CircuitBreaker>();

	/**
	 * 执行带重试逻辑的函数
	 *
	 * 重试策略：
	 * 1. 指数退避：每次重试延迟时间递增
	 * 2. 最大延迟限制：防止延迟时间过长
	 * 3. 智能重试条件：只对特定错误重试
	 * 4. 断路器保护：可选的断路器机制
	 *
	 * @param fn 要执行的异步函数
	 * @param options 重试配置选项
	 * @param circuitBreakerKey 断路器键名（可选）
	 * @returns 执行结果
	 */
	async executeWithRetry<T>(
		fn: () => Promise<T>,
		options: RetryOptions = {},
		circuitBreakerKey?: string,
	): Promise<T> {
		const {
			maxAttempts = inkSyncConfig.maxRetryAttempts,
			delayMs = inkSyncConfig.retryDelayMs,
			backoffMultiplier = 2,
			maxDelayMs = 30000,
			retryCondition = this.defaultRetryCondition,
		} = options;

		// 如果指定了断路器，先检查断路器状态
		if (circuitBreakerKey) {
			const circuitBreaker = this.getCircuitBreaker(circuitBreakerKey);
			return circuitBreaker.execute(async () => {
				return this.performRetries(fn, {
					maxAttempts,
					delayMs,
					backoffMultiplier,
					maxDelayMs,
					retryCondition,
				});
			});
		}

		return this.performRetries(fn, {
			maxAttempts,
			delayMs,
			backoffMultiplier,
			maxDelayMs,
			retryCondition,
		});
	}

	/**
	 * 执行重试逻辑
	 *
	 * 重试流程：
	 * 1. 尝试执行函数
	 * 2. 成功则返回结果
	 * 3. 失败则检查重试条件
	 * 4. 满足条件则等待后重试
	 * 5. 不满足条件或达到最大次数则抛出错误
	 *
	 * @param fn 要执行的函数
	 * @param options 重试配置（必需参数）
	 * @returns 执行结果
	 */
	private async performRetries<T>(
		fn: () => Promise<T>,
		options: Required<RetryOptions>,
	): Promise<T> {
		let lastError: Error | undefined;
		let currentDelay = options.delayMs;

		for (let attempt = 1; attempt <= options.maxAttempts; attempt++) {
			try {
				const result = await fn();
				if (attempt > 1) {
					logger.info(`Operation succeeded after ${attempt} attempts`);
				}
				return result;
			} catch (error) {
				lastError = error as Error;

				// 检查是否应该重试
				if (
					attempt === options.maxAttempts ||
					!options.retryCondition(lastError)
				) {
					logger.error(
						lastError,
						`Operation failed after ${attempt} attempts (max: ${options.maxAttempts})`,
					);
					throw lastError;
				}

				logger.warn(
					lastError,
					`Attempt ${attempt}/${options.maxAttempts} failed, retrying in ${currentDelay}ms`,
				);

				// 等待后重试
				await this.sleep(currentDelay);

				// 指数退避：下次延迟时间增加
				currentDelay = Math.min(
					currentDelay * options.backoffMultiplier,
					options.maxDelayMs,
				);
			}
		}

		if (lastError) {
			throw lastError;
		}
		throw new Error("Unexpected error: no attempts made");
	}

	/**
	 * 默认重试条件
	 *
	 * 可重试的错误类型：
	 * - 网络相关错误（超时、连接重置、DNS解析失败等）
	 * - Redis连接错误
	 * - HTTP 5xx 服务器错误
	 *
	 * 不可重试的错误：
	 * - 4xx 客户端错误（参数错误、认证失败等）
	 * - 业务逻辑错误
	 *
	 * @param error 错误对象
	 * @returns 是否应该重试
	 */
	private defaultRetryCondition(error: Error): boolean {
		// 网络相关错误可以重试
		if (
			error.message.includes("timeout") ||
			error.message.includes("ECONNRESET") ||
			error.message.includes("ENOTFOUND") ||
			error.message.includes("socket hang up")
		) {
			return true;
		}

		// Redis连接错误可以重试
		if (
			error.message.includes("Redis") ||
			error.message.includes("Connection closed")
		) {
			return true;
		}

		// HTTP 5xx 错误可以重试
		if (error.message.includes("50") && error.message.includes("status")) {
			return true;
		}

		return false;
	}

	/**
	 * 获取或创建断路器
	 *
	 * 使用懒加载模式，只在需要时创建断路器实例
	 *
	 * @param key 断路器键名
	 * @returns 断路器实例
	 */
	private getCircuitBreaker(key: string): CircuitBreaker {
		if (!this.circuitBreakers.has(key)) {
			this.circuitBreakers.set(key, new CircuitBreaker(key));
		}
		const breaker = this.circuitBreakers.get(key);
		if (!breaker) {
			throw new Error(`Failed to create circuit breaker for key: ${key}`);
		}
		return breaker;
	}

	/**
	 * 获取所有断路器状态
	 *
	 * 用于监控和调试，了解各个断路器的运行状态
	 *
	 * @returns 断路器状态映射
	 */
	getCircuitBreakerStatus(): Record<
		string,
		{
			state: string;
			failureCount: number;
			lastFailureTime: number;
			successCount: number;
		}
	> {
		const status: Record<
			string,
			{
				state: string;
				failureCount: number;
				lastFailureTime: number;
				successCount: number;
			}
		> = {};
		for (const [key, breaker] of this.circuitBreakers.entries()) {
			status[key] = breaker.getStatus();
		}
		return status;
	}

	/**
	 * 重置断路器
	 *
	 * 强制重置指定断路器到初始状态，用于故障恢复
	 *
	 * @param key 断路器键名
	 */
	resetCircuitBreaker(key: string): void {
		this.circuitBreakers.delete(key);
		logger.info(`Circuit breaker ${key} reset`);
	}

	/**
	 * 睡眠函数
	 *
	 * 用于重试间隔的延迟等待
	 *
	 * @param ms 延迟时间（毫秒）
	 * @returns Promise
	 */
	private sleep(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}
}

// 导出单例实例
export const retryHandler = new RetryHandler();
