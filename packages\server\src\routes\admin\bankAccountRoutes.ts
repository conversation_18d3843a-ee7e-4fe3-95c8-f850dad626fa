import { Router } from "express";
import { AppError } from "@/core/appError.js";
import * as bankAccountService from "@/services/admin/bankAccountService.js";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import { isChannel } from "@/config/configManager.js";
import type { BankAccount } from "@packages/shared";

const router = Router();

/**
 * 验证交易台环境
 */
function ValidateChannel() {
	if (!isChannel()) {
		throw AppError.create("FORBIDDEN", "只有通道环境才能访问该资源");
	}
}

/**
 * @api {get} /admin/bank-account Get bank account
 * @apiName GetBankAccount
 * @apiGroup Admin/BankAccount
 * @apiDescription Get bank account information
 */
router.get(
	"/",
	wrapAdminRoute(async (req, res) => {
		const bankAccount = await bankAccountService.getBankAccount(false);
		res.status(200).json(bankAccount);
	}),
);

/**
 * @api {get} /admin/bank-account/platform Get platform bank account
 * @apiName GetPlatformBankAccount
 * @apiGroup Admin/BankAccount
 * @apiDescription Get platform bank account information
 */
router.get(
	"/platform",
	wrapAdminRoute(async (req, res) => {
		ValidateChannel();

		const bankAccount = await bankAccountService.getBankAccount(true);
		res.status(200).json(bankAccount);
	}),
);

/**
 * @api {get} /admin/bank-account/channel/:channelId Get channel bank account
 * @apiName GetChannelBankAccount
 * @apiGroup Admin/BankAccount
 * @apiDescription Get bank account information for a specific channel
 */
router.get(
	"/channel/:channelId",
	wrapAdminRoute(async (req, res) => {
		const { channelId } = req.params;
		if (!channelId) {
			throw AppError.create("BAD_REQUEST", "Channel ID is required");
		}

		const bankAccount =
			await bankAccountService.getChannelBankAccount(channelId);
		res.status(200).json(bankAccount);
	}),
);

/**
 * @api {post} /admin/bank-account Update bank account
 * @apiName UpdateBankAccount
 * @apiGroup Admin/BankAccount
 * @apiDescription Update bank account information
 */
router.post(
	"/",
	wrapAdminRoute<BankAccount>(async (req, res) => {
		const updates = req.body;

		const result = await bankAccountService.updateBankAccount(
			updates,
			req.jwt.admin_id,
		);
		res.status(200).json(result);
	}),
);

/**
 * @api {get} /admin/bank-account/history Get bank account history
 * @apiName GetBankAccountHistory
 * @apiGroup Admin/BankAccount
 * @apiDescription Get history of bank account changes with pagination
 */
router.get(
	"/history",
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder = req.query.sortOrder as "ASC" | "DESC";

		const history = await bankAccountService.getBankAccountHistory(
			page,
			pageSize,
			{
				sortBy,
				sortOrder,
			},
		);
		res.status(200).json(history);
	}),
);

export default router;
