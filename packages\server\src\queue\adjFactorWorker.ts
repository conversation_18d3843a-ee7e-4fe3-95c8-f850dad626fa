import type { Job } from "bullmq";
import { createW<PERSON>ker, adjFactorTasksQueue, addRepeatedJob } from "./index.js";
import logger from "@/utils/logger.js";
import { isMarketDay } from "@/financeUtils/marketTimeManager.js";
import { getAdjFactorChanges } from "@/financeUtils/adjFactorManager.js";
import * as Position from "@/models/position.js";
import * as Order from "@/models/trade/order.js";
import { withTransaction } from "@/core/dbTxnManager.js";
import type { Prisma } from "@prisma/client";

// 定义除权因子相关的作业类型
export const ADJ_FACTOR_JOBS = {
	PROCESS_ADJ_FACTOR_CHANGES: "adjfactor-process-changes",
};

// 处理除权因子相关的作业
async function processAdjFactorJob(job: Job) {
	const { name } = job;

	logger.info(`Processing adjustment factor job: ${name}`);

	try {
		switch (name) {
			case ADJ_FACTOR_JOBS.PROCESS_ADJ_FACTOR_CHANGES:
				await processAdjFactorChanges();
				break;
			default:
				logger.warn(`Unknown adjustment factor job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process adjustment factor job: ${name}`);
		throw error;
	}
}

// 处理除权因子变化
async function processAdjFactorChanges(): Promise<void> {
	try {
		// 检查是否为交易日
		if (!(await isMarketDay())) {
			logger.info("Not a market day, skipping adjustment factor processing");
			return;
		}

		const startTime = Date.now();
		const changes = await getAdjFactorChanges();

		if (!changes.length) {
			logger.info("No adjustment factor changes found");
			return;
		}

		// 处理每个发生变化的股票
		for (const change of changes) {
			try {
				await updatePrices(change.ts_code, change.factor);
				logger.info(
					`Successfully processed adjustment factor change for ${change.ts_code}`,
				);
			} catch (error) {
				logger.error(
					error,
					`Failed to process adjustment factor change for ${change.ts_code}`,
				);
			}
		}

		const duration = Date.now() - startTime;
		logger.info(
			`Adjustment factor processing completed in ${duration}ms, processed ${changes.length} stocks`,
		);
	} catch (error) {
		logger.error(error, "Error during adjustment factor processing");
	}
}

// 更新价格
async function updatePrices(ts_code: string, factor: number): Promise<void> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		// 使用模型方法更新价格
		await Promise.all([
			Position.updatePricesByStock(ts_code, factor, tx),
			Order.updatePricesByStock(ts_code, factor, tx),
		]);

		logger.info(`Updated prices for ${ts_code} with factor ${factor}`);
	};

	await withTransaction(updateFn);
}


/*
	弃用邮件群发功能，避免触发邮箱服务商的反垃圾邮件机制
	问题：被标记为"疑似发送垃圾邮件"，需要更改密码后才能继续发送
	EmailService.sendToAllUsers("HOLIDAY_NOTIFICATION", {
		date1: formatDate(holiday.start),
		date2: formatDate(holiday.end),
	});
*/

// 创建Worker实例
export const adjFactorWorker = createWorker(
	adjFactorTasksQueue,
	processAdjFactorJob,
);

// 初始化定时作业调度
export async function initializeAdjFactorJobs() {
	try {
		// 每天早上9点15分执行（盘前获取gtimg的当日数据）
		await addRepeatedJob(
			adjFactorTasksQueue,
			ADJ_FACTOR_JOBS.PROCESS_ADJ_FACTOR_CHANGES,
			{},
			"15 9 * * *", // cron pattern
			// { removeOnFail: 50 } // 可选：如果需要覆盖默认的 removeOnFail: 100
		);

		logger.info(
			"Holiday reminder job temporarily disabled to avoid triggering spam filters",
		);

		logger.info("Adjustment factor jobs scheduled successfully");
	} catch (error) {
		logger.error(error, "Failed to schedule adjustment factor jobs");
		throw error;
	}
}

// 用于应用程序启动时初始化所有除权因子任务
export async function initializeOnStartup() {
	await initializeAdjFactorJobs();
	logger.info("Adjustment factor worker initialized successfully");
}
