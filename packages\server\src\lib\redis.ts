import { ENV, APP_CONFIG } from "../config/configManager.js";
import { Redis } from "ioredis";

// 获取应用ID，不区分通道与交易台，使用统一前缀
const APP_ID = APP_CONFIG.tradingPlatformId || APP_CONFIG.channelId || "";

// ========================
// Redis 实例初始化
// ========================
const redis = new Redis({
	host: ENV.REDIS_HOST || "localhost",
	port: Number(ENV.REDIS_PORT),
	password: ENV.REDIS_PASSWORD,
	// 统一使用默认db:0，通过key前缀区分不同应用
	// db: 0, // 可省略，默认就是0
	retryStrategy: (times: number) => {
		const delay = Math.min(times * 50, 2000);
		return delay;
	},
	// 添加更多的连接选项
	connectTimeout: 10000, // 连接超时时间
	maxRetriesPerRequest: 3, // 每个请求的最大重试次数
	enableReadyCheck: true, // 启用就绪检查
});

// ========================
// Redis 事件监听与日志
// ========================
console.log("Redis Configuration:", {
	host: ENV.REDIS_HOST,
	port: ENV.REDIS_PORT,
	// 不要记录实际密码，只记录是否存在
	hasPassword: !!ENV.REDIS_PASSWORD,
});

redis.on("error", (error: Error) => {
	console.error("Redis connection error:", error);
});
redis.on("connect", () => {
	console.log("Redis connected successfully");
});
redis.on("ready", () => {
	console.log("Redis is ready to accept commands");
});
redis.on("authError", (error: Error) => {
	console.error("Redis authentication failed:", error);
	console.error("Current Redis config:", {
		host: ENV.REDIS_HOST,
		port: ENV.REDIS_PORT,
		hasPassword: !!ENV.REDIS_PASSWORD,
	});
});

// ========================
// Redis 客户端包装器（带前缀）
// ========================
const NON_KEY_COMMANDS = [
	"subscribe",
	"psubscribe",
	"unsubscribe",
	"punsubscribe",
	"quit",
	"info",
	"ping",
];

// 创建一个Redis客户端包装器，自动添加前缀
export function createPrefixedRedis(
	prefix: string,
	redisClient: Redis = redis,
): Redis {
	interface RedisLike {
		[key: string]: (...args: unknown[]) => unknown;
	}

	const handler = {
		get(target: object, prop: string | symbol): unknown {
			const propertyKey = prop.toString();

			// 如果属性在Redis实例上不存在，返回undefined
			if (!(propertyKey in redisClient)) {
				return undefined;
			}

			const originalProperty = redisClient[propertyKey as keyof Redis];

			// 如果是函数，包装它以添加前缀
			if (typeof originalProperty === "function") {
				// 使用箭头函数并处理参数
				return (...args: unknown[]): unknown => {
					// 如果第一个参数是键名，添加前缀
					if (
						args.length > 0 &&
						typeof args[0] === "string" &&
						!NON_KEY_COMMANDS.includes(propertyKey)
					) {
						args[0] = `${prefix}:${args[0]}`;
					}
					// 直接调用方法但使用类型安全的方式
					return (redisClient as unknown as RedisLike)[propertyKey](...args);
				};
			}

			// 对于非函数属性，直接返回原值
			return originalProperty;
		},
	};

	// 创建代理
	return new Proxy<Redis>({} as Redis, handler);
}

// ========================
// 主要导出（推荐使用）
// ========================

/**
 * 应用业务Redis客户端（自动带前缀）
 * 用于：应用内部的业务数据、缓存、锁等，以应用为单位隔离
 */
export const appRedis = APP_ID
	? createPrefixedRedis(`app:${APP_ID}`)
	: createPrefixedRedis("global");

/**
 * 分布式协调/全局用途Redis客户端（不带前缀，等同于基础redis）
 * 用于：跨应用的全局协调、系统级监控数据等
 */
export const coordinationRedis = redis;

/**
 * Redis key 常量
 */
export const REDIS_KEYS = {
	// 使用 sorted set 存储黑名单，score 为过期时间戳，避免独立 key 导致的性能问题
	TOKEN_BLACKLIST: "token:blacklist",
} as const;

// ========================
// 低频使用导出
// ========================

/**
 * 基础Redis客户端（不带前缀，建议仅特殊场景使用）
 */
export default redis;
