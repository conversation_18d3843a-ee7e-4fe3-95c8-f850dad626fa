/**
 * Deeply compares two values.
 * @param value1 1
 * @param value2 2
 * @returns Whether they are equal
 */
export function deepEqual(value1: unknown, value2: unknown): boolean {
  if (!isObject(value1) || !isObject(value2)) {
    return deepEqualPrimitive(value1, value2);
  }
  return deepEqualObjects(value1 as Record<string, unknown>, value2 as Record<string, unknown>);
}

/**
 * Compares two primitive values.
 * @param value1 1
 * @param value2 2
 * @returns Whether they are equal
 */
function deepEqualPrimitive(value1: unknown, value2: unknown): boolean {
  return value1 === value2;
}

/**
 * Deeply compares two objects.
 * @param obj1 1
 * @param obj2 2
 * @returns Whether they are equal
 */
function deepEqualObjects(obj1: Record<string, unknown>, obj2: Record<string, unknown>): boolean {
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    const val1 = obj1[key];
    const val2 = obj2[key];
    if (!deepEqual(val1, val2)) {
      return false;
    }
  }

  return true;
}

/**
 * Determines if a value is an object.
 * @param value The value to check
 * @returns Whether it is an object
 */
function isObject(value: unknown): boolean {
  return value != null && typeof value === 'object';
}
