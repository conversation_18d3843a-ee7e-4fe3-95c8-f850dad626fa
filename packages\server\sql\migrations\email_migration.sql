-- 邮箱迁移脚本
-- 该脚本将users表中的email字段设为非空且唯一，处理NULL值和重复邮箱

-- 注意：运行此脚本前，请确保先运行db-field-crypto脚本解密email和phone_number字段

-- 事务开始
BEGIN;

-- Step 1: 创建函数，为没有邮箱的用户生成唯一邮箱（手机号@ink.hk.cn）
CREATE OR REPLACE FUNCTION generate_unique_email(phone VARCHAR) RETURNS VARCHAR AS $$
BEGIN
    RETURN phone || '@ink.hk.cn';
END;
$$ LANGUAGE plpgsql;

-- Step 2: 处理NULL邮箱，基于手机号设置占位邮箱
UPDATE users 
SET email = generate_unique_email(phone_number)
WHERE email IS NULL AND phone_number IS NOT NULL;

-- Step 3: 查找重复邮箱并使其唯一（使用"手机号.邮箱"的格式）
UPDATE users u1
SET email = u1.phone_number || '.' || u1.email
FROM (
    SELECT email, COUNT(*) as cnt
    FROM users
    WHERE email IS NOT NULL
    GROUP BY email
    HAVING COUNT(*) > 1
) u2
WHERE u1.email = u2.email
AND u1.email NOT LIKE '%@ink.hk.cn'
AND u1.phone_number IS NOT NULL;

-- Step 3.1: 处理同时有重复邮箱但没有手机号的情况（使用用户ID作为唯一标识）
UPDATE users u1
SET email = u1.email || '.' || u1.user_id || '@ink.hk.cn'
FROM (
    SELECT email, COUNT(*) as cnt
    FROM users
    WHERE email IS NOT NULL AND phone_number IS NULL
    GROUP BY email
    HAVING COUNT(*) > 1
) u2
WHERE u1.email = u2.email
AND u1.phone_number IS NULL;

-- Step 4: 处理任何剩余的NULL邮箱（如果手机号也为NULL的情况）
UPDATE users
SET email = 'user.' || user_id || '@ink.hk.cn'
WHERE email IS NULL;

-- 检查是否还有空邮箱或重复邮箱
SELECT COUNT(*) as null_emails FROM users WHERE email IS NULL;
SELECT email, COUNT(*) as count FROM users GROUP BY email HAVING COUNT(*) > 1;

-- Step 5: 现在可以安全地添加约束
ALTER TABLE users 
   ALTER COLUMN email SET NOT NULL,
   ADD CONSTRAINT users_email_key UNIQUE (email);
   
-- Step 6: 让phone_number变为可选
ALTER TABLE users 
   ALTER COLUMN phone_number DROP NOT NULL;

-- 添加注释记录模式变更
COMMENT ON TABLE users IS '更新为EMAIL_ONLY模式，原有phone_number值已保留';

-- Step 7: 清理 - 删除临时创建的函数
DROP FUNCTION IF EXISTS generate_unique_email(VARCHAR);

-- Step 8: Clean up - Remove table comment (if you don't want to keep it)
-- COMMENT ON TABLE users IS '';  -- Uncomment if you want to completely remove the comment 

-- 提交事务
COMMIT;

-- 注意：运行此脚本后，请使用db-field-crypto脚本重新加密敏感字段 