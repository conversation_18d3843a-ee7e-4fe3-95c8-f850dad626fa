import { useDark, usePreferredDark } from "@vueuse/core";
import { watch } from "vue";

export function useTheme() {
	const preferredDark = usePreferredDark();

	const isDark = useDark({
		selector: "html",
		attribute: "class",
		valueDark: "dark",
		valueLight: "",
	});

	// 检查是否跟随系统
	const isFollowingSystem = () => {
		return !localStorage.getItem("theme");
	};

	// 切换主题并处理存储
	const toggleTheme = () => {
		const newValue = !isDark.value;

		// 如果新的主题值与系统偏好一致，则移除存储
		if (newValue === preferredDark.value) {
			localStorage.removeItem("theme");
		} else {
			// 否则存储用户的选择
			localStorage.setItem("theme", newValue ? "dark" : "light");
		}

		isDark.value = newValue;
	};

	// 初始化主题
	const initializeTheme = () => {
		const savedTheme = localStorage.getItem("theme");
		if (!savedTheme) {
			// 如果没有存储的主题，跟随系统
			isDark.value = preferredDark.value;
		} else {
			// 否则使用存储的主题
			isDark.value = savedTheme === "dark";
		}
	};

	// 监听系统主题变化
	watch(preferredDark, (newValue) => {
		if (isFollowingSystem()) {
			isDark.value = newValue;
		}
	});

	// 初始化
	initializeTheme();

	return {
		isDark,
		toggleTheme,
		isFollowingSystem,
	};
}
