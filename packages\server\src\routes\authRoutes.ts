import { ENV } from "@/config/configManager.js";
import { Router, type Request } from "express";
import * as authService from "@/services/authService.js";
import { wrapRoute, wrapUserRoute } from "@/utils/routeWrapper.js";
import { validateLoginRequest } from "@/middlewares/validator.js";
import logger from "@/utils/logger.js";
import type {
	LoginRequest,
	ResetPasswordRequest,
	VerifyResetPasswordRequest,
} from "@packages/shared";
import * as adminAuthService from "@/services/admin/authService.js";
import { authenticateToken } from "@/middlewares/jwtAuth.js";
import { loginLimiter } from "@/middlewares/rateLimiter.js";
import { AppError } from "@/core/appError.js";

const router = Router();

const getClientIP = (req: Request): string => {
	// 生产环境应该使用可信的代理设置的 IP
	if (ENV.NODE_ENV === "production") {
		// 如果使用了 nginx 等反向代理，应该使用 x-real-ip
		const realIP = req.headers["x-real-ip"];
		if (typeof realIP === "string") {
			return realIP.trim();
		}
		logger.warn("Production environment missing x-real-ip header");
	}

	// 开发环境或没有代理时，使用 socket 的远程地址
	const socketIP = req.socket.remoteAddress;
	if (socketIP) {
		// 处理 IPv6 格式的 IP
		return socketIP.replace(/^::ffff:/, ""); // 移除 IPv6 前缀
	}

	logger.warn(
		{
			headers: req.headers,
			socketIP: req.socket.remoteAddress,
			env: ENV.NODE_ENV,
		},
		"Failed to get client IP",
	);
	return "0.0.0.0";
};

/**
 * User register route: POST /api/auth/register
 */
router.post(
	"/register",
	wrapRoute<RegisterBody>(async (req, res) => {
		const { email } = req.body;

		// 确保至少有一个标识符不为空
		if (!email) {
			throw AppError.create("BAD_REQUEST", "Email is required");
		}

		const result = await authService.registerUser(email);
		res.status(200).json(result);
	}),
);

/**
 * Verify and complete registration route: POST /api/auth/verify-registration
 */
router.post(
	"/verify-registration",
	wrapRoute<VerifyRegistrationBody>(async (req, res) => {
		const { email, sms_code, password } = req.body;

		if (!email) {
			throw AppError.create("BAD_REQUEST", "Email is required");
		}

		const result = await authService.verifyRegistration(
			email,
			password,
			sms_code,
		);
		res.status(201).json(result);
	}),
);

/**
 * Send reset code route: POST /api/auth/send-reset-code
 */
router.post(
	"/send-reset-code",
	wrapRoute<ResetPasswordRequest>(async (req, res) => {
		const { email } = req.body;
		const result = await authService.sendResetCode(email);
		res.status(200).json(result);
	}),
);

/**
 * Verify reset code route: POST /api/auth/verify-reset-password
 */
router.post(
	"/verify-reset-password",
	wrapRoute<VerifyResetPasswordRequest>(async (req, res) => {
		const { email, sms_code, new_password } = req.body;
		const result = await authService.verifyResetPassword(
			email,
			sms_code,
			new_password,
		);
		res.status(200).json(result);
	}),
);

/**
 * User login route: POST /api/auth/login
 */
router.post(
	"/login",
	loginLimiter,
	validateLoginRequest,
	wrapRoute<LoginRequest>(async (req, res) => {
		const { email, password } = req.body;
		const result = await authService.loginUser(email, password);

		// 设置 authorization header，express 总是使用小写
		res.set("authorization", result.headers.authorization);

		// 设置 refresh token cookie
		if (result.cookies) {
			for (const cookie of result.cookies) {
				res.cookie(cookie.name, cookie.value, cookie.options);
			}
		}

		res.status(200).json(result.body);
	}),
);

/**
 * Logout route: POST /api/auth/logout
 */
router.post(
	"/logout",
	wrapRoute(async (req, res) => {
		const refresh_token = req.cookies.refresh_token;
		const result = await authService.logout(refresh_token);

		// 清除 refresh token cookie
		res.clearCookie("refresh_token", {
			httpOnly: true,
			secure: ENV.NODE_ENV === "production",
			sameSite: "strict",
		});

		res.status(200).json(result);
	}),
);

/**
 * Refresh token route: POST /api/auth/refresh-token
 */
router.post(
	"/refresh-token",
	wrapRoute(async (req, res) => {
		const refresh_token = req.cookies.refresh_token;
		const result = await authService.refreshToken(refresh_token);

		// 设置 authorization header，express 总是使用小写
		res.set("authorization", result.headers.authorization);

		// 设置 refresh token cookie
		if (result.cookies) {
			for (const cookie of result.cookies) {
				res.cookie(cookie.name, cookie.value, cookie.options);
			}
		}

		res.status(200).json(result.body);
	}),
);

/**
 * Change password route: POST /api/auth/change-password
 */
router.post(
	"/change-password",
	authenticateToken,
	wrapUserRoute<ChangePasswordBody>(async (req, res) => {
		const { old_password, new_password } = req.body;
		await authService.changePassword(
			req.jwt.user_id,
			old_password,
			new_password,
		);
		res.status(200).json({ message: "Password changed successfully" });
	}),
);

// Admin-only login endpoint: POST /api/auth/login-admin
router.post(
	"/admin-login",
	loginLimiter,
	wrapRoute<AdminLoginBody>(async (req, res) => {
		const { username, password } = req.body;
		const result = await adminAuthService.login(username, password);

		// 设置 authorization header，express 总是使用小写
		res.set("authorization", result.headers.authorization);

		// 设置 refresh token cookie
		if (result.cookies) {
			for (const cookie of result.cookies) {
				res.cookie(cookie.name, cookie.value, cookie.options);
			}
		}

		res.status(200).json(result.body);
	}),
);

/**
 * Admin refresh token route: POST /api/auth/admin-refresh-token
 */
router.post(
	"/admin-refresh-token",
	wrapRoute(async (req, res) => {
		const refresh_token = req.cookies.admin_refresh_token;
		const result = await adminAuthService.refreshAdminToken(refresh_token);

		// 设置 authorization header，express 总是使用小写
		res.set("authorization", result.headers.authorization);

		// 设置 refresh token cookie
		if (result.cookies) {
			for (const cookie of result.cookies) {
				res.cookie(cookie.name, cookie.value, cookie.options);
			}
		}

		res.status(200).json(result.body);
	}),
);

/**
 * Admin logout route: POST /api/auth/admin-logout
 */
router.post(
	"/admin-logout",
	wrapRoute(async (req, res) => {
		const refresh_token = req.cookies.admin_refresh_token;
		const result = await adminAuthService.logout(refresh_token);

		// 清除 refresh token cookie
		res.clearCookie("admin_refresh_token", {
			httpOnly: true,
			secure: ENV.NODE_ENV === "production",
			sameSite: "strict",
		});

		res.status(200).json(result);
	}),
);

export default router;
// Define request body interfaces
interface RegisterBody {
	email?: string;
}

interface VerifyRegistrationBody {
	email?: string;
	sms_code: string;
	password: string;
}

interface AdminLoginBody {
	username: string;
	password: string;
}

interface ChangePasswordBody {
	old_password: string;
	new_password: string;
}
