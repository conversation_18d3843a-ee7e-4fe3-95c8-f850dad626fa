import { ref, watch } from "vue";

export function useTableSettings() {
	// 默认设置
	const pageSize = ref(10);
	const isDescending = ref(true); // 默认最新
	// 分页状态
	const currentPage = ref(1);
	const totalPages = ref(1);
	const jumpPage = ref(1);

	// 初始化设置
	const initSettings = () => {
		const savedPageSize = localStorage.getItem("globalPageSize");
		const savedIsDescending = localStorage.getItem("globalIsDescending");

		if (savedPageSize) pageSize.value = Number.parseInt(savedPageSize);
		if (savedIsDescending) isDescending.value = savedIsDescending === "true";
	};

	// 监听设置变化
	watch([pageSize, isDescending], ([newPageSize, newIsDescending]) => {
		localStorage.setItem("globalPageSize", newPageSize.toString());
		localStorage.setItem("globalIsDescending", newIsDescending.toString());
	});

	// Watchers
	watch(currentPage, (newPage) => {
		jumpPage.value = newPage;
	});

	return {
		pageSize,
		currentPage,
		totalPages,
		jumpPage,
		isDescending,
		initSettings,
	};
}
