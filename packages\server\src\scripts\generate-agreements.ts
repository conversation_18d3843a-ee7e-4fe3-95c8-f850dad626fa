import { PrismaClient } from "@prisma/client";
import {
	generateIsdaMasterAgreement,
	generateIsdaSupplement,
} from "@/utils/pdf-generator.js";
import EmailService from "@/utils/email.js";
import { decrypt } from "@/utils/encryption.js";
import fs from "node:fs/promises";
import path from "node:path";
import { getDatabaseUrl } from "@/config/defaultParams.js";
import type { QualificationData } from "@packages/shared";
import { AuditStatus, AuditType } from "@packages/shared";

const prisma = new PrismaClient({
	datasources: {
		db: {
			url: getDatabaseUrl(),
		},
	},
});

async function getQualificationData(
	userId: number,
): Promise<{ signature: string; email: string }> {
	// 获取用户最近一条审核通过的资质审核记录
	const audit = await prisma.audits.findFirst({
		where: {
			user_id: userId,
			type: AuditType.QUALIFICATION,
			status: AuditStatus.APPROVED,
		},
		orderBy: {
			created_at: "desc",
		},
	});

	if (!audit) {
		throw new Error(`No approved qualification audit found for user ${userId}`);
	}

	// 从审核数据中获取签名
	const data = audit.data as unknown as QualificationData;
	if (!data.signature) {
		throw new Error(
			`No signature found in qualification audit for user ${userId}`,
		);
	}

	// 获取用户邮箱
	const user = await prisma.users.findUnique({
		where: { user_id: userId },
		select: { email: true },
	});

	if (!user) {
		throw new Error(`User with ID ${userId} not found`);
	}

	return {
		signature: data.signature,
		email: decrypt(user.email),
	};
}

async function generateAgreements(
	userId: number,
): Promise<{ masterPdf: Blob; supplementPdf: Blob; email: string }> {
	const { signature, email } = await getQualificationData(userId);

	// 生成协议
	const qualificationData: QualificationData = {
		name: "", // 这些字段在生成协议时不会被使用
		id_number: "",
		phone_number: "",
		bank_name: "",
		bank_code: "",
		bank_account: "",
		documents: [],
		declarationAccepted: true,
		signature,
	};

	if (!qualificationData.signature) {
		console.error("错误: 没有找到签名");
		process.exit(1);
	}

	const masterPdf = await generateIsdaMasterAgreement(
		qualificationData.signature,
	);
	const supplementPdf = await generateIsdaSupplement(
		qualificationData.signature,
	);

	return {
		masterPdf,
		supplementPdf,
		email,
	};
}

async function saveAgreements(
	masterPdf: Blob,
	supplementPdf: Blob,
	userId: number,
	outputDir: string,
) {
	// 确保输出目录存在
	await fs.mkdir(outputDir, { recursive: true });

	// 保存到文件
	const masterPath = path.join(outputDir, `ISDA主协议-${userId}.pdf`);
	const supplementPath = path.join(outputDir, `ISDA补充协议-${userId}.pdf`);

	await fs.writeFile(masterPath, Buffer.from(await masterPdf.arrayBuffer()));
	await fs.writeFile(
		supplementPath,
		Buffer.from(await supplementPdf.arrayBuffer()),
	);

	return {
		masterPath,
		supplementPath,
	};
}

async function sendAgreementsByEmail(
	email: string,
	masterPdf: Blob,
	supplementPdf: Blob,
) {
	const masterBuffer = Buffer.from(await masterPdf.arrayBuffer());
	const supplementBuffer = Buffer.from(await supplementPdf.arrayBuffer());

	await EmailService.sendEmail(
		email,
		"AUDIT_NOTIFICATION",
		{},
		{
			attachments: [
				{
					filename: "ISDA主协议.pdf",
					content: masterBuffer,
					contentType: "application/pdf",
				},
				{
					filename: "ISDA补充协议.pdf",
					content: supplementBuffer,
					contentType: "application/pdf",
				},
			],
		},
	);
}

async function main() {
	// 注意：在运行此脚本前，如果根目录 (.env) 和当前目录 (packages/server/.env) 都存在
	// 且包含同名环境变量，可能会发生冲突。为了确保脚本使用正确的配置，
	// 建议暂时注释掉根目录 .env 中与 packages/server/.env 冲突的环境变量（特别是 Docker 构建相关的）。

	const args = process.argv.slice(2);
	if (args.length < 1) {
		console.log(`
协议生成工具

用法:
  pnpm generate-agreements <用户ID> [--output <输出目录>] [--send-email]

参数:
  <用户ID>       要生成协议的用户ID
  --output       可选，指定输出目录，如果不指定则不保存文件
  --send-email   可选，如果指定则发送邮件

注意:
  必须至少指定 --output 或 --send-email 中的一个
	请先连接到用户数据库，并在 /packages/server/public/images/stamp/ 目录下放置印章图片

示例:
  pnpm generate-agreements 123 --output ./agreements
  pnpm generate-agreements 123 --output ./agreements --send-email
  pnpm generate-agreements 123 --send-email
        `);
		process.exit(0);
	}

	const userId = Number.parseInt(args[0], 10);
	const outputIndex = args.indexOf("--output");
	const outputDir = outputIndex !== -1 ? args[outputIndex + 1] : null;
	const shouldSendEmail = args.includes("--send-email");

	if (!outputDir && !shouldSendEmail) {
		console.error("错误: 必须至少指定 --output 或 --send-email 中的一个");
		process.exit(1);
	}

	try {
		const { masterPdf, supplementPdf, email } =
			await generateAgreements(userId);

		if (outputDir) {
			const { masterPath, supplementPath } = await saveAgreements(
				masterPdf,
				supplementPdf,
				userId,
				outputDir,
			);
			console.info(`协议已生成并保存到: ${outputDir}`);
			console.info(`主协议: ${masterPath}`);
			console.info(`补充协议: ${supplementPath}`);
		}

		if (shouldSendEmail) {
			await sendAgreementsByEmail(email, masterPdf, supplementPdf);
			console.info(`协议已发送到邮箱: ${email}`);
		}

		console.info("协议生成完成");
		process.exit(0);
	} catch (error) {
		console.error(error, "生成或发送协议时出错:");
		process.exit(1);
	} finally {
		await prisma.$disconnect();
	}
}

main();
