import { ENV } from "@/config/configManager.js";
import cors from "cors";
import express from "express";
import helmet from "helmet";
import cookieParser from "cookie-parser";
import prisma from "@/lib/prisma.js";

// 路由导入
import router from "./routes/index.js";

// 中间件导入
import { generateCSRFToken } from "./middlewares/csrf.js";
import limiter from "./middlewares/rateLimiter.js";
import { apiErrorHandler } from "./middlewares/apiErrorHandler.js";

// 工具导入
import WebSocketManager from "./core/websocket.js";
import logger from "./utils/logger.js";
import { shutdownLogger } from "./utils/logger.js";
import { closeAllQueues } from "./queue/taskRegistry.js";

// 导入分布式锁管理器用于优雅关闭
import { lockManager } from "./queue/ink/distributedLockManager.js";

// 服务导入
import * as InitService from "./services/initService.js";
import {
	initializeAllWorkers,
	initializeSpecificWorkers,
} from "./queue/taskRegistry.js";

// 开发环境中启用的Worker列表 - 默认为空数组，表示不启用任何Worker
// ⚠️ 谨慎: 在这里添加Worker名称会在开发环境中启用相应的队列处理
// 可能的值包括: "StockDataSyncWorker", "VwapOrderWorker", "LimitOrderWorker" 等
// 示例: ["StockDataSyncWorker", "SystemHealthCheckWorker"]
const DEV_ENABLED_WORKERS: string[] = [
	// 在此处添加需要在开发环境中启用的Worker名称
	// "StockDataSyncWorker",  // 取消注释以启用此Worker
	// "SystemHealthCheckWorker",
];

// 初始化必要服务
try {
	await InitService.initialize();
} catch (error) {
	logger.error(error, "Initialization failed");
	process.exit(1);
}

const app = express();

app.set("trust proxy", "loopback");

const allowedOrigins = [
	"http://localhost:3001",
	"http://127.0.0.1:3001",
	"http://localhost:3002",
	"http://127.0.0.1:3002",
].filter((origin): origin is string => !!origin);

// 中间件配置
app.use(cookieParser());
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ limit: "50mb", extended: true }));
app.use(helmet());
app.use(generateCSRFToken);
app.use(limiter);

app.use(
	cors({
		origin: allowedOrigins,
		credentials: true,
		methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
		allowedHeaders: ["Content-Type", "Authorization", "X-XSRF-TOKEN"],
	}),
);

// 路由配置
app.use("/api", router);

// 错误处理
app.use(apiErrorHandler);

// 服务器启动配置
const PORT = ENV.PORT || 3000;
const server = app.listen(PORT, () => {
	logger.info(`Server is running on port ${PORT}`);
});

// WebSocket 配置
WebSocketManager.initialize(server);

// 初始化队列任务
try {
	// 生产环境中初始化所有Worker，开发环境中按配置初始化特定Worker
	if (ENV.NODE_ENV === "production") {
		logger.info(
			"Production environment detected, initializing all background workers",
		);
		await initializeAllWorkers();
	} else {
		if (DEV_ENABLED_WORKERS.length > 0) {
			logger.info(
				`Development environment with specific workers enabled: ${DEV_ENABLED_WORKERS.join(", ")}`,
			);
			await initializeSpecificWorkers(DEV_ENABLED_WORKERS);
		} else {
			logger.warn(
				"Development environment detected, all background workers are disabled by default",
			);
			logger.info(
				"To enable specific workers, add their names to DEV_ENABLED_WORKERS in app.ts",
			);
		}
	}
} catch (error) {
	logger.error(error, "Failed to initialize queue workers");
}

// 优雅关闭配置
const gracefulShutdown = async () => {
	logger.info("Shutdown signal received");

	// 停止接收新的请求并等待现有连接完成
	logger.info("Closing HTTP server...");
	await new Promise<void>((resolve) => {
		if (ENV.NODE_ENV === "development") {
			setTimeout(() => {
				logger.warn(
					"Development mode: Skipping HTTP connection cleanup due to known connection issues",
				);
				resolve();
			}, 500); // 开发环境下500ms后强制继续
		}

		server.close(() => {
			logger.info("HTTP server closed");
			resolve();
		});
	});

	try {
		// 关闭WebSocket连接
		WebSocketManager.destroy();
		logger.info("WebSocket connections closed");

		// 清理分布式锁 - 在关闭队列之前进行，确保锁能正确释放
		lockManager.cleanup();
		logger.info("Distributed locks cleaned up");

		// 关闭队列连接
		if (ENV.NODE_ENV === "production") {
			await closeAllQueues();
			logger.info("Queue connections closed");
		}

		// 关闭 Prisma 连接
		await prisma.$disconnect();
		logger.info("Database connections closed");

		// 最后关闭日志系统
		await shutdownLogger();

		process.exit(0);
	} catch (error) {
		logger.error(error, "Error during shutdown");
		process.exit(1);
	}
};

process.on("SIGTERM", gracefulShutdown);
process.on("SIGINT", gracefulShutdown);

export default app;
