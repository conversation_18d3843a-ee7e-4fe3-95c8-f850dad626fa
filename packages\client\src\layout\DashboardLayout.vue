<template>
  <div class="dashboard-layout">
    <el-container class="layout-container">
      <!-- 移动端遮罩层 -->
      <div v-if="isMobile && !isSidebarCollapsed" class="mobile-overlay" @click="toggleSidebar"></div>

      <!-- 修改侧边栏 -->
      <el-aside :class="['sidebar', {
        'mobile': isMobile,
        'collapsed': isSidebarCollapsed
      }]">
        <div class="sidebar-header" @click="toggleSidebar">
          <ThemableLogo v-if="siteConfigStore.logoUrl()" :src="siteConfigStore.logoUrl() || ''" :class="logoClasses" />
        </div>

        <el-menu :collapse="isSidebarCollapsed && !isMobile" :default-active="$route.path" router class="navigation"
          :background-color="'var(--color-surface)'" :text-color="'var(--color-on-surface)'" @select="handleMenuSelect">
          <el-menu-item v-for="link in translatedNavLinks" :key="link.to" :index="`${link.to}`">
            <el-icon>
              <SvgIcon :name="link.icon" :active="$route.path === `${link.to}`" :activeName="link.iconActive"
                class="nav-icon" />
            </el-icon>
            <template #title>
              <span class="nav-text">{{ link.text }}</span>
            </template>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <div class="main-content">
        <!-- 顶栏 -->
        <header class="header">
          <div class="header-content">
            <div class="header-left">
              <el-button link class="collapse-button" @click="toggleSidebar">
                <SvgIcon name="sidebar-left-svgrepo" :active="!isSidebarCollapsed"
                  activeName="sidebar-left-expanded-svgrepo" class="collapse-icon" />
              </el-button>
            </div>
            <div class="header-right">
              <UserInfoModal v-model:show="userInfoVisible" :is-mobile="isMobile" />

              <el-tooltip content="主题" effect="dark" :disabled="isMobile">
                <el-button link @click="toggleTheme" class="header-action">
                  <SvgIcon :name="isDark ? 'moon-stars-rs-flaticon' : 'sun-2-svgrepo'" class="action-icon" />
                </el-button>
              </el-tooltip>

              <el-tooltip content="通知" effect="dark" :disabled="isMobile">
                <el-button link @click="() => toggleVisibility('NotificationModal')" class="header-action">
                  <SvgIcon name="bell-rs-flaticon" :active="isVisible('NotificationModal')"
                    activeName="bell-ss-flaticon" class="action-icon" />
                  <el-badge v-if="unreadCount > 0" :value="unreadCount" :max="99" class="notification-badge" />
                </el-button>
              </el-tooltip>

              <el-tooltip content="设置" effect="dark" :disabled="isMobile">
                <el-dropdown trigger="click" :hide-on-click="false"
                  @visible-change="(visible: boolean) => (settingsDropdownVisible = visible)"
                  @command="handleSettingsCommand">
                  <el-button link class="header-action">
                    <SvgIcon name="settings-rs-flaticon" :active="settingsDropdownVisible"
                      activeName="settings-ss-flaticon" class="action-icon" />
                  </el-button>

                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                      <el-dropdown-item divided command="password">修改密码</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-tooltip>
            </div>
          </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="content" ref="contentRef">
          <router-view v-slot="{ Component }">
            <keep-alive :exclude="['AuditView']">
              <component :is="Component" />
            </keep-alive>
          </router-view>
        </main>

        <!-- 模态框 -->
        <component v-for="(Modal, key) in visibleModals" :key="key" :is="Modal" :unreadCount="unreadCount"
          @update:unreadCount="updateUnreadCount" @close="() => toggleVisibility(key, false)" />
      </div>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import type { Component } from "vue";
import { useRouter } from "vue-router";
import SvgIcon from "@/components/SvgIcon.vue";
import { notificationApi } from "@/api";
import { navLinks } from "./dashbordConfig";
import { useModals } from "@/composables/useModals";
import { useAuthStore } from "@/stores/auth";
import { useTheme } from "@/composables/useTheme";
import { eventBus } from "@/utils/eventBus";
import { useSiteConfigStore } from "@/stores/siteConfig";
import ThemableLogo from "@/components/ThemableLogo.vue";

const siteConfigStore = useSiteConfigStore();

const router = useRouter();
const contentRef = ref<HTMLElement | null>(null);

// 导入模态框组件
import NotificationModal from "./modals/NotificationModal.vue";
import ChangePasswordModal from "./modals/ChangePasswordModal.vue";
import UserInfoModal from "./modals/UserInfoModal.vue";

// 模态框配置：确保模态框的键名与 toggleVisibility 函数中使用的键名一致
const modalComponents = {
  NotificationModal,
  ChangePasswordModal,
} as const satisfies Record<string, Component>;

// Modals setup
const { isVisible, visibleModals, toggleVisibility } =
  useModals(modalComponents);

// Sidebar state
const isSidebarCollapsed = ref(
  localStorage.getItem("sidebarCollapsed") === "true",
);
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value;
  localStorage.setItem("sidebarCollapsed", isSidebarCollapsed.value.toString());
};

// User ID
const authStore = useAuthStore();
const is_qualified = computed(() => authStore.isQualified);

// Navigation
const translatedNavLinks = computed(() =>
  navLinks
    .filter(
      (link) =>
        // 如果是审核菜单项且用户已有资质，则过滤掉
        !(link.key === "audit" && is_qualified.value)
    )
    .map((link) => ({
      ...link,
      text: link.text,
    })),
);

// Notifications
const unreadCount = ref(0);

const updateUnreadCount = async (newCount?: number) => {
  try {
    // 如果传入了新的计数，直接使用它
    if (typeof newCount === "number") {
      unreadCount.value = newCount;
    } else {
      // 否则从API获取最新计数
      unreadCount.value = (await notificationApi.getUnreadCount()) || 0;
    }
  } catch (error) {
    console.error("Failed to load unread count:", error);
  }
};

onMounted(async () => {
  await updateUnreadCount();
  // 监听通知更新事件
  eventBus.on("notification-updated", updateUnreadCount);
});

onUnmounted(() => {
  // 移除事件监听
  eventBus.off("notification-updated", updateUnreadCount);
});

// Menu dropdown
const settingsDropdownVisible = ref(false);

// 处理设置菜单命令
const handleSettingsCommand = (command: string) => {
  switch (command) {
    case "logout":
      authStore.logout();
      break;
    case "password":
      toggleVisibility("ChangePasswordModal", true);
      break;
  }
};

// 添加移动端检测
const MOBILE_THRESHOLD = 768; // 移动端断点
const isMobile = ref(window.innerWidth < MOBILE_THRESHOLD);

// updateLayout 只处理窗口大小变化时的逻辑
const updateLayout = () => {
  isMobile.value = window.innerWidth < MOBILE_THRESHOLD;
};

onMounted(() => {
  window.addEventListener("resize", updateLayout);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateLayout);
});

// 添加菜单选择处理函数
const handleMenuSelect = (index: string) => {
  // 在移动端下,选择菜单项后自动折叠侧边栏
  if (isMobile.value) {
    isSidebarCollapsed.value = true;
  }

  // 如果启用了跟随最后访问，则记录当前页面
  if (localStorage.getItem("followLastVisit") === "true") {
    localStorage.setItem("lastVisitedPage", index);
  }
};

// 监听路由变化,重置滚动位置
router.afterEach(() => {
  if (contentRef.value) {
    contentRef.value.scrollTop = 0;
  }
});

// 主题相关
const { isDark, toggleTheme } = useTheme();

const userInfoVisible = ref(false);

// Logo classes
const logoClasses = computed(() => ({
  "sidebar-logo": true,
  collapsed: isSidebarCollapsed.value && !isMobile.value,
  mobile: isMobile.value,
}));
</script>

<style scoped>
.dashboard-layout {
  height: 100vh;
}

.layout-container {
  height: 100%;
}

.sidebar {
  position: relative;
  z-index: 1000;
  width: 132px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: var(--color-surface);
  color: var(--color-on-surface);
  border-right: 1px solid var(--el-border-color);
  overflow: hidden;

  &.collapsed:not(.mobile) {
    width: 64px;
  }

  &.mobile {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 1001;
    width: 80%;
    transform: translateX(0);

    &.collapsed {
      transform: translateX(-100%);
    }
  }
}

.sidebar-header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--el-border-color);
  cursor: pointer;
}

.sidebar-logo {
  width: 90%;
  height: 90%;
  object-fit: contain;
  transition: all 0.3s ease;
}

:root {
  --theme-logo-color: var(--color-on-surface, #333);
}

html.dark {
  --theme-logo-color: var(--color-on-surface, #fff);
}

.navigation {
  border-right: none;
  width: 100%;
  padding: 10px 0;
}

:deep(.el-menu-item) {
  &.is-active {
    color: var(--el-color-primary-dark-2);

    &:hover {
      background-color: var(--el-color-primary-light-5);
    }
  }

  &:hover {
    background-color: var(--hover-bg-color);
  }
}

.nav-icon {
  width: 18px;
  height: 18px;
  min-width: 18px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.content {
  flex: 1;
  padding: 12px 24px;
  overflow-y: auto;
}

/* ------------------------------顶栏------------------------------ */
.header {
  height: 64px;
}

.header-content {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  box-sizing: border-box;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;

  button {
    margin: 0;
  }
}

.header-action {
  height: 32px;
  width: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.action-icon {
  width: 20px;
  height: 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-button {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: var(--el-color-primary-dark-2);
  }
}

.collapse-icon {
  width: 20px;
  height: 20px;
}

/* 添加一些过渡动画样式 */
:deep(.el-dropdown-menu) {
  padding: 4px 0;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  font-size: 14px;
  line-height: 1.5;

  &:hover {
    background-color: var(--hover-bg-color);
    color: var(--el-color-primary-dark-2);
  }

  &.is-disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
}

/* 可以自定义下拉菜单的主题样式 */
:deep(.el-dropdown-menu) {
  background-color: var(--color-surface);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 优化按钮激活状态 */
.header-action {

  &:hover {
    .action-icon {
      color: var(--el-color-primary-dark-2);
    }
  }
}

/* 添加认证角标样式 */
:deep(.qualified-badge) {
  position: absolute;
  right: -8px;
  bottom: -6px;

  .el-badge__content {
    border: 2px solid var(--color-surface);
    height: 15px;
    padding: 0 3px;
    font-size: 12px;
    line-height: 14px;
  }
}

/* 遮罩层样式 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 移动端下的菜单样式优化 */
.sidebar.mobile .navigation {
  width: 100%;
}

.sidebar.mobile :deep(.el-menu-item) {
  padding-left: 20px !important;
}

.sidebar.mobile :deep(.el-menu--collapse) {
  width: 100%;
}

/* 移动端主内容区域适配 */
@media (max-width: 768px) {
  .main-content {
    width: 100%;
  }

  .content {
    padding: 4px 12px;
  }

  /* 优化移动端间距 */
  .header-right {
    gap: 14px;
  }
}

.nav-text {
  user-select: none;
}
</style>
