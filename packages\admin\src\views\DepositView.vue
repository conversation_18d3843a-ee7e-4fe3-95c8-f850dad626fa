<template>
  <div class="deposit-view view">
    <h2>平台出入金</h2>
    <el-card class="deposit-card">
      <template #header>
        <div class="card-header">
          <span>出入金审核</span>
        </div>
      </template>

      <el-form ref="depositForm" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户ID" prop="user_id">
          <el-input v-model.number="form.user_id" type="number" style="width: 240px" />
        </el-form-item>

        <el-form-item label="金额" prop="amount">
          <el-input-number v-model="form.amount" :min="-999999999" :precision="2" :step="1000" style="width: 240px"
            placeholder="正数为入金，负数为扣款" />
        </el-form-item>

        <div class="form-actions">
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 添加入金记录表格 -->
    <el-card class="records-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <span>出入金记录</span>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="margin-left: 16px" clearable>
              <el-option v-for="(text, status) in statusOptions" :key="status" :label="text" :value="status" />
            </el-select>
          </div>
          <el-button type="primary" link @click="loadRecords">
            <el-icon>
              <Refresh />
            </el-icon>
          </el-button>
        </div>
      </template>

      <el-table :data="filteredRecords" style="width: 100%" v-loading="loading" @sort-change="handleSortChange">
        <el-table-column prop="audit_id" label="ID" min-width="80" sortable="custom" />
        <el-table-column prop="user_id" label="用户ID" min-width="100" sortable="custom" />
        <el-table-column prop="amount" label="金额" min-width="120">
          <template #default="{ row }">
            <span :style="{ color: row.amount < 0 ? 'var(--el-color-danger)' : 'inherit' }">
              {{ formatMoney(row.amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="100" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" min-width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- Add pagination -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          :total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>

    <!-- 修改用户确认对话框 -->
    <user-details-dialog v-model:visible="confirmDialog.visible"
      :user-id="confirmDialog.visible ? form.user_id : undefined">
      <template #footer>
        <div class="dialog-footer">
          <div class="confirm-info">
            <el-alert type="warning" :title="`确认${form.amount >= 0 ? '为该用户入金' : '从该用户扣除'} ${Math.abs(form.amount)} 元？`"
              show-icon />
          </div>
          <div class="confirm-actions">
            <el-button @click="confirmDialog.visible = false">取消</el-button>
            <el-button type="primary" :loading="confirmDialog.loading" @click="handleDeposit">
              确认入金
            </el-button>
          </div>
        </div>
      </template>
    </user-details-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { financeApi } from "@/api";
import { ElMessage } from "element-plus";
import type { FormInstance } from "element-plus";
import UserDetailsDialog from "@/components/UserDetailsDialog.vue";
import { formatDate, formatMoney } from "@/utils/format";
import { Refresh } from "@element-plus/icons-vue";
import type { PlatformDepositRecord } from "@packages/shared";
import { AuditStatus, TransactionType } from "@packages/shared";

const depositForm = ref<FormInstance>();
const form = ref({
  user_id: undefined as number | undefined,
  amount: 0,
});

const confirmDialog = ref({
  visible: false,
  loading: false,
});

const rules = {
  user_id: [
    { required: true, message: "请输入用户ID" },
    { type: "number", message: "用户ID必须为数字" },
  ],
  amount: [
    { required: true, message: "请输入金额" },
    { type: "number", message: "金额必须为数字" },
  ],
};

const records = ref<PlatformDepositRecord[]>([]);
const statusFilter = ref<AuditStatus | null>(null);
const loading = ref(false);

const statusOptions: Record<AuditStatus, string> = {
  [AuditStatus.PENDING]: "待审核",
  [AuditStatus.APPROVED]: "已通过",
  [AuditStatus.REJECTED]: "已拒绝",
};

// Add pagination state
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const sortBy = ref("created_at");
const sortOrder = ref<"ASC" | "DESC">("DESC");

// Update loadRecords function
const loadRecords = async () => {
  loading.value = true;
  try {
    const response = await financeApi.getPlatformDeposits({
      page: currentPage.value,
      pageSize: pageSize.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
      filters: {
        status: statusFilter.value || undefined,
        operation: TransactionType.PLATFORM_DEPOSIT,
      },
    });
    records.value = response?.items || [];
    total.value = response?.total || 0;
  } catch (error) {
    console.error("Failed to load records:", error);
    ElMessage.error("加载记录失败");
  } finally {
    loading.value = false;
  }
};

const getStatusType = (status: AuditStatus) => {
  const types: Record<AuditStatus, string> = {
    pending: "warning",
    approved: "success",
    rejected: "danger",
  };
  return types[status];
};

const getStatusText = (status: AuditStatus) => {
  const texts: Record<AuditStatus, string> = {
    pending: "待审核",
    approved: "已通过",
    rejected: "已拒绝",
  };
  return texts[status];
};

// 添加状态筛选变化处理
watch(statusFilter, () => {
  currentPage.value = 1; // 重置到第一页
  loadRecords();
});

// 移除本地筛选的 computed 属性
const filteredRecords = computed(() => records.value);

onMounted(() => {
  loadRecords();
});

// 表单提交，显示确认对话框
const handleSubmit = async () => {
  if (!depositForm.value) return;

  await depositForm.value.validate((valid) => {
    if (valid && form.value.user_id) {
      confirmDialog.value.visible = true;
    }
  });
};

// 确认入金操作
const handleDeposit = async () => {
  if (!form.value.user_id) return;

  confirmDialog.value.loading = true;
  try {
    await financeApi.platformDeposit(
      form.value.user_id,
      form.value.amount,
      undefined,
    );
    ElMessage.success("入金操作成功");
    confirmDialog.value.visible = false;
    depositForm.value?.resetFields();
    form.value = { user_id: undefined, amount: 0 };
  } catch (error) {
    console.error("入金操作失败:", error);
    ElMessage.error("入金操作失败");
  } finally {
    confirmDialog.value.loading = false;
  }
};

// Add pagination handlers
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  loadRecords();
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  loadRecords();
};

const handleSortChange = ({
  prop,
  order,
}: { prop?: string; order?: string }) => {
  sortBy.value = prop || "created_at";
  sortOrder.value = order === "ascending" ? "ASC" : "DESC";
  loadRecords();
};
</script>

<style scoped>
.deposit-card {
  background: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
  width: 100%;
  max-width: 400px;
  margin: 40px auto;
}

.records-card {
  background: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

h2 {
  margin: 0;
}

.form-actions {
  text-align: center;
  margin-top: 20px;
}


:deep(.el-dialog__footer) {
  padding-top: 0;
}

.confirm-info {
  margin-bottom: 30px;
}

.confirm-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-card__header) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-input__wrapper) {
  background-color: var(--el-bg-color);
}

:deep(.el-input__inner) {
  color: var(--el-text-color-primary);
}

.header-left {
  display: flex;
  align-items: center;
}

:deep(.el-select) {
  width: 120px;
}

@media (max-width: 768px) {
  .deposit-card {
    margin: 20px auto;
  }
}
</style>
