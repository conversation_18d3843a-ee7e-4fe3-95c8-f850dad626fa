import axios from "axios";
import { ENV } from "@/config/configManager.js";
import { dateToCompactString } from "@/utils/dateUtils.js";

/**
 * Fetch the trade calendar range.
 * @param startDate Start date
 * @param endDate End date
 * @returns Trade calendar range: [["SSE", "20240101", 1, "20231231"], ... date is in descending order]
 */
export async function fetchTradeCalendarRange(
	startDate: Date,
	endDate: Date,
): Promise<Array<Array<string | number>>> {
	const start = dateToCompactString(startDate);
	const end = dateToCompactString(endDate);

	try {
		const response = await axios.post("http://api.tushare.pro", {
			api_name: "trade_cal",
			token: ENV.TUSHARE_TOKEN,
			params: {
				start_date: start,
				end_date: end,
			},
			fields: "exchange,cal_date,is_open,pretrade_date",
		});

		if (!response?.data?.data?.items) {
			console.error("Invalid trade calendar response");
			return [];
		}

		return response.data.data.items;
	} catch (error) {
		console.error(
			`Error fetching trade calendar from ${start} to ${end}:`,
			error,
		);
		throw error;
	}
}

// 模拟当前时间为 2025-01-21 15:00:00
const mockDate = new Date(2025, 0, 21, 15, 0, 0);

async function getLastThreeTradingDays(): Promise<Date[]> {
	const today = mockDate;
	const startDate = new Date(today);
	startDate.setDate(startDate.getDate() - 15); // 往前查15天以确保能获取足够的交易日

	const calendar = await fetchTradeCalendarRange(startDate, today);
	console.log("Calendar data: ", calendar);

	if (!calendar.length) {
		console.warn("No trading calendar data found");
		return [];
	}

	const tradingDays = calendar
		.filter((day) => day[2] === 1) // 筛选交易日
		.sort((a, b) => Number(b[1]) - Number(a[1])) // 按日期降序排序
		.slice(0, 3);

	console.log("Filtered trading days: ", tradingDays);

	const result = tradingDays.map(
		(day) =>
			new Date(
				Date.UTC(
					Number(String(day[1]).slice(0, 4)),
					Number(String(day[1]).slice(4, 6)) - 1,
					Number(String(day[1]).slice(6, 8)),
				),
			),
	);

	return result;
}

// 运行测试
async function runTest() {
	const result = await getLastThreeTradingDays();
	console.log("Last three trading days:");
	for (const date of result) {
		console.log(date.toISOString().split("T")[0]);
	}

	// new Date 是本地时间，toISOString 是 UTC 时间
	console.log("Make date: ", new Date(2025, 0, 21).toISOString());
	// LOG: Make date: 2025-01-20T16:00:00.000Z

	console.log("Make UTC date: ", new Date(Date.UTC(2025, 0, 21)).toISOString());
	// LOG: Make UTC date: 2025-01-21T00:00:00.000Z
}

// 执行测试
runTest();
