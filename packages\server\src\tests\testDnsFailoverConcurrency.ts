/**
 * DNS故障转移系统并发测试
 *
 * 执行命令：pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\tests\testDnsFailoverConcurrency.ts
 *
 * 此测试文件用于验证DNS故障转移系统在并发场景下的稳定性和错误处理能力
 */

// 仅在测试中使用dns模块，用于创建测试场景
import logger from "@/utils/logger.js";
import axiosWithDNSFailover, {
	testDNSConnection,
} from "@/api/dnsFailoverAxios.js";

// 导入DNS服务器管理器类，用于直接测试
import { DNSServerManager } from "@/api/dnsFailoverAxios.js";

/**
 * 测试并发DNS查询场景
 *
 * 此测试专门用于重现和调试"c-ares failed to set servers: There are pending queries"错误
 * 通过同时发起多个DNS查询，并在查询过程中尝试切换DNS服务器，来触发并发冲突
 */
/**
 * 测试并发DNS查询场景
 *
 * 此函数通过同时发起多个DNS查询，模拟高并发场景，
 * 用于重现和调试"c-ares failed to set servers: There are pending queries"错误
 */
async function testConcurrentDNSQueries(): Promise<void> {
	logger.info("=== 开始测试并发DNS查询场景 ===");

	// 测试前先检查DNS服务器状态
	await testDNSConnection(["www.baidu.com"]);

	// 测试域名列表 - 使用多个不同的域名增加测试覆盖面
	const domains = ["www.baidu.com", "www.aliyun.com", "www.tencent.com"];

	logger.info(`将同时查询 ${domains.length} 个域名以触发并发场景`);

	// 创建多个并发请求
	const promises = domains.map(async (domain, index) => {
		// 随机延迟，使请求在不同时间点开始，增加并发冲突概率
		await new Promise<void>((resolve) =>
			setTimeout(resolve, Math.random() * 100),
		);

		try {
			logger.info(`[${index}] 开始解析域名: ${domain}`);
			// 使用axios发起请求，这将触发DNS解析
			const response = await axiosWithDNSFailover.head(`https://${domain}`, {
				timeout: 5000,
				validateStatus: () => true, // 接受任何状态码
			});
			logger.info(`[${index}] 成功请求 ${domain}, 状态码: ${response.status}`);
			return { domain, success: true, status: response.status };
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			logger.error(`[${index}] 请求 ${domain} 失败: ${errorMessage}`);
			return { domain, success: false, error: errorMessage };
		}
	});

	// 等待所有请求完成
	const results = await Promise.all(promises);

	// 统计结果
	const successCount = results.filter((r) => r.success).length;
	const failureCount = results.length - successCount;

	logger.info("=== 并发DNS查询测试完成 ===");
	logger.info(
		`总请求数: ${results.length}, 成功: ${successCount}, 失败: ${failureCount}`,
	);

	// 再次检查DNS服务器状态，验证测试后DNS服务是否仍然正常
	await testDNSConnection(["www.baidu.com"]);
}

/**
 * 直接测试DNS服务器切换
 *
 * 此测试专门用于测试DNS服务器切换时的并发问题
 * 通过在DNS查询进行中尝试切换服务器，来重现"c-ares failed to set servers"错误
 */
async function testDNSServerSwitching(): Promise<void> {
	logger.info("=== 开始测试DNS服务器切换 ===");

	// 获取DNS管理器实例
	const dnsManager = DNSServerManager.getInstance();

	// 获取可用的DNS服务器列表
	const servers = dnsManager.getConfiguredServers();
	logger.info(`可用的DNS服务器: ${servers.join(", ")}`);

	// 测试域名 - 使用真实域名替代不存在域名
	const testDomain = "www.baidu.com";
	const concurrentDomains = [
		"www.aliyun.com",
		"www.tencent.com",
		"www.microsoft.com",
		"www.apple.com",
	];

	// 测试场景：在解析过程中切换DNS服务器
	logger.info("测试场景：在解析过程中切换DNS服务器");

	try {
		// 开始第一个解析请求
		logger.info(`开始解析 ${testDomain}`);
		const resolvePromise = dnsManager.resolve(testDomain);

		// 记录并发错误
		let concurrencyErrors = 0;

		// 同时发起多个并发请求
		const promises = concurrentDomains.map(async (domain, index) => {
			// 随机延迟，增加并发变数
			await new Promise((resolve) => setTimeout(resolve, Math.random() * 50));

			try {
				logger.info(`[${index}] 尝试并发解析 ${domain}`);
				const ip = await dnsManager.resolve(domain);
				logger.info(`[${index}] 成功解析 ${domain} 到 ${ip}`);
				return { success: true, domain, ip };
			} catch (error) {
				// 检测是否发生并发错误
				const errorMsg = error instanceof Error ? error.message : String(error);
				const isConcurrencyError =
					errorMsg.includes("pending queries") ||
					errorMsg.includes("changing DNS server");

				if (isConcurrencyError) {
					concurrencyErrors++;
					logger.warn(`[${index}] 检测到并发错误: ${errorMsg}`);
				} else {
					logger.error(`[${index}] 解析 ${domain} 失败: ${errorMsg}`);
				}

				return { success: false, domain, error: errorMsg };
			}
		});

		// 等待所有并发请求完成
		const results = await Promise.all(promises);

		// 等待第一个解析完成
		const ip = await resolvePromise;
		logger.info(`成功解析 ${testDomain} 到 ${ip}`);

		// 统计结果
		const successCount = results.filter((r) => r.success).length;

		logger.info("=== DNS服务器切换测试完成 ===");
		logger.info(
			`总请求数: ${results.length + 1}, 成功: ${successCount + 1}, 失败: ${results.length - successCount}`,
		);
		logger.info(`检测到的并发错误数: ${concurrencyErrors}`);

		// 验证是否没有出现并发错误
		if (concurrencyErrors === 0) {
			logger.info("✅ 并发测试通过：没有检测到并发错误");
		} else {
			logger.warn(`⚠️ 并发测试存在问题：检测到 ${concurrencyErrors} 个并发错误`);
		}
	} catch (error) {
		logger.error(
			`解析 ${testDomain} 失败: ${error instanceof Error ? error.message : String(error)}`,
		);
	}

	logger.info("=== DNS服务器切换测试完成 ===");
}

/**
 * 测试高负载场景
 *
 * 此测试模拟高负载场景，发起大量并发DNS查询
 * 用于验证系统在高负载下的稳定性和错误处理能力
 */
async function testHighLoad() {
	logger.info("=== 开始测试高负载场景 ===");

	// 生成测试域名 - 只使用实际存在的域名
	const baseDomains = [
		"www.baidu.com",
		"www.aliyun.com",
		"www.tencent.com",
		"www.microsoft.com",
		"www.apple.com",
	];

	// 跟踪并发错误
	let concurrencyErrors = 0;

	// 创建多次请求，每个域名请求多次
	const domains: { domain: string; iteration: number }[] = [];
	for (let i = 0; i < 4; i++) {
		for (const domain of baseDomains) {
			domains.push({ domain, iteration: i });
		}
	}

	logger.info(`将同时发起 ${domains.length} 个请求`);

	// 分批发送请求，每批15个以创建更高的并发压力
	const batchSize = 15;
	const results: Array<{
		domain: string;
		success: boolean;
		error?: string;
		concurrencyError?: boolean;
	}> = [];

	for (let i = 0; i < domains.length; i += batchSize) {
		const batch = domains.slice(i, i + batchSize);
		logger.info(`发送第 ${i / batchSize + 1} 批请求 (${batch.length} 个)`);

		const batchPromises = batch.map(async ({ domain, iteration }) => {
			try {
				// 随机延迟增加并发冲突可能性
				await new Promise((resolve) => setTimeout(resolve, Math.random() * 30));

				// 只测试DNS解析，不进行HTTP请求
				const ip = await DNSServerManager.getInstance().resolve(domain);
				logger.debug(`成功解析 ${domain} (${iteration}) 到 ${ip}`);

				return { domain, success: true };
			} catch (error) {
				const errorMsg = error instanceof Error ? error.message : String(error);
				const isConcurrencyError =
					errorMsg.includes("pending queries") ||
					errorMsg.includes("changing DNS server") ||
					errorMsg.includes("Another thread");

				if (isConcurrencyError) {
					concurrencyErrors++;
					logger.warn(`检测到并发错误(${domain}): ${errorMsg}`);
					return {
						domain,
						success: false,
						error: errorMsg,
						concurrencyError: true,
					};
				}

				return {
					domain,
					success: false,
					error: errorMsg,
				};
			}
		});

		const batchResults = await Promise.all(batchPromises);
		results.push(...batchResults);

		// 短暂暂停，避免触发服务器限流
		await new Promise((resolve) => setTimeout(resolve, 200));
	}

	// 统计结果
	const successCount = results.filter((r) => r.success).length;
	const failureCount = results.length - successCount;
	const concurrencyCount = results.filter((r) => r.concurrencyError).length;

	logger.info("=== 高负载测试完成 ===");
	logger.info(
		`总请求数: ${results.length}, 成功: ${successCount}, 失败: ${failureCount}, 并发错误: ${concurrencyCount}`,
	);

	// 检查失败的请求
	if (failureCount > 0) {
		logger.info("失败请求详情:");
		const errorGroups: Record<string, number> = {};

		for (const result of results) {
			if (!result.success) {
				const errorMsg = result.error || "Unknown error";
				// 只取错误信息前50个字符，避免日志过长
				const shortError =
					errorMsg.substring(0, 50) + (errorMsg.length > 50 ? "..." : "");
				errorGroups[shortError] = (errorGroups[shortError] || 0) + 1;
			}
		}

		for (const [error, count] of Object.entries(errorGroups)) {
			logger.info(`  - ${error}: ${count} 次`);
		}
	}

	// 验证是否没有出现并发错误
	if (concurrencyCount === 0) {
		logger.info("✅ 高负载测试通过：没有检测到并发错误");
	} else {
		logger.warn(`⚠️ 高负载测试存在问题：检测到 ${concurrencyCount} 个并发错误`);
	}
}

/**
 * 测试DNS故障转移场景
 *
 * 此测试专门用于验证当第一个DNS服务器失败时，系统能否正确地切换到备用服务器
 * 并在高并发情况下正确处理多个请求同时切换服务器的情况
 */
async function testDnsFailoverUnderConcurrency(): Promise<void> {
	logger.info("=== 开始测试DNS故障转移并发场景 ===");

	// 获取DNS管理器实例
	const dnsManager = DNSServerManager.getInstance();

	// 手动将第一个DNS服务器标记为不可用，强制触发故障转移
	const servers = dnsManager.getConfiguredServers();
	if (servers.length > 0) {
		// 获取内部方法来标记服务器失败
		// 注意：这需要修改DNSServerManager类，暴露markServerFailed方法或提供测试接口
		logger.info(`将首选DNS服务器 ${servers[0]} 标记为不可用以触发故障转移`);
		// 假设已修改API使以下调用可行：
		dnsManager.markServerFailedForTesting(servers[0], "测试强制失败");
	}

	// 测试域名列表
	const domains = [
		"www.baidu.com",
		"www.aliyun.com",
		"www.tencent.com",
		"www.microsoft.com",
		"www.apple.com",
	];

	logger.info(`将同时查询 ${domains.length} 个域名以测试故障转移并发`);

	// 创建多个并发请求
	const promises = domains.map(async (domain, index) => {
		// 随机延迟
		await new Promise<void>((resolve) =>
			setTimeout(resolve, Math.random() * 30),
		);

		try {
			logger.info(`[${index}] 开始解析域名: ${domain}`);
			const ip = await dnsManager.resolve(domain);
			logger.info(`[${index}] 成功解析 ${domain} 到 ${ip}`);
			return { domain, success: true, ip };
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			logger.error(`[${index}] 解析 ${domain} 失败: ${errorMessage}`);
			return { domain, success: false, error: errorMessage };
		}
	});

	// 等待所有请求完成
	const results = await Promise.all(promises);

	// 统计结果
	const successCount = results.filter((r) => r.success).length;
	const failureCount = results.length - successCount;

	logger.info("=== DNS故障转移并发测试完成 ===");
	logger.info(
		`总请求数: ${results.length}, 成功: ${successCount}, 失败: ${failureCount}`,
	);

	// 检查当前使用的DNS服务器 - 应该是备用服务器而非首选
	const currentServer = dnsManager.getCurrentServer();
	if (currentServer && currentServer !== servers[0]) {
		logger.info(`✅ 故障转移成功：当前使用的DNS服务器是 ${currentServer}`);
	} else {
		logger.warn(`⚠️ 故障转移可能有问题：当前DNS服务器是 ${currentServer}`);
	}
}

/**
 * 主测试函数
 */
async function runTests() {
	try {
		logger.info("======= 开始DNS故障转移系统并发测试 =======");

		// 测试1: 并发DNS查询
		await testConcurrentDNSQueries();

		// 测试2: DNS服务器切换
		await testDNSServerSwitching();

		try {
			// 测试3: 高负载场景 - 单独捕获这个测试的错误
			await testHighLoad();
		} catch (highLoadError) {
			logger.error(
				`高负载测试发生错误但不影响整体测试: ${highLoadError instanceof Error ? highLoadError.message : String(highLoadError)}`,
			);
			if (highLoadError instanceof Error && highLoadError.stack) {
				logger.error(`错误堆栈: ${highLoadError.stack}`);
			}
		}

		// 测试4: DNS故障转移并发场景
		await testDnsFailoverUnderConcurrency();

		logger.info("======= DNS故障转移系统并发测试完成 =======");
	} catch (error) {
		logger.error(
			`测试过程中发生未捕获的错误: ${error instanceof Error ? error.message : String(error)}`,
		);
		if (error instanceof Error && error.stack) {
			logger.error(`错误堆栈: ${error.stack}`);
		}
		// 确保错误被记录但测试能够完成
		process.exitCode = 1;
	}
}

// 执行测试
runTests().catch((error) => {
	console.error("测试执行失败:", error);
	process.exit(1);
});
