import { request } from "./request";
import type { OrderModification } from "@packages/shared";

export const orderManagementApi = {
	// 获取订单修改记录
	getOrderModifications: (
		page: number,
		pageSize: number,
		filters?: { trade_no?: string },
	) =>
		request.get<{
			items: OrderModification[];
			total: number;
		}>("/admin/order/modifications", {
			params: {
				page,
				pageSize,
				filters,
			},
		}),
} as const;
