<script setup lang="ts">
import { onMounted, onUnmounted } from "vue";
import { systemStatusManager } from "@/core/systemStatusManager";
import { useTheme } from "@/composables/useTheme";
import { useAuthStore } from "@/stores/auth";
import { useStockStore } from "@/stores/stock";
import { useSiteConfigStore } from "@/stores/siteConfig";
import { useSharedConfigStore } from "@/stores/sharedConfig";
import websocketManager from "@/core/websocketManager";

// 替换原有的 useDark 调用
useTheme();

const auth = useAuthStore();
const stockStore = useStockStore();
const siteConfigStore = useSiteConfigStore();
const sharedConfigStore = useSharedConfigStore();

onMounted(async () => {
	try {
		await systemStatusManager.initialize();

		// IMPORTANT: auth.initialize() 会触发被拦截请求的 resolve
		// 这些请求会作为当前 Promise 链中的微任务执行
		// 因此，在此之后同步创建的任何 Promise 都会阻塞这些请求

		// 初始化非阻塞刷新认证状态信息，处理错误自动刷新以外的手动刷新
		auth.refreshToken();
		// WebSocket 连接在 auth store 中自动管理

		// 让被拦截的请求先执行完成
		await Promise.resolve();

		// 然后再初始化股票数据 (延时避免请求并发)
		stockStore.initialize();

		// 加载站点配置
		siteConfigStore.loadSiteConfig();

		// 加载共享配置
		sharedConfigStore.loadSharedConfig();
	} catch (error) {
		console.error("[App] Failed to initialize:", error);
	}
});

onUnmounted(() => {
	// 注意：直接关闭浏览器标签页时此方法可能不会执行
	// 但浏览器会自动清理 WebSocket 连接，这里主要处理 SPA 内路由切换的情况
	websocketManager.disconnect();
});
</script>

<template>
	<router-view />
</template>

<style scoped></style>
