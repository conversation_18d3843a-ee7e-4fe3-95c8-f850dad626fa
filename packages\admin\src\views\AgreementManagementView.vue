<template>
  <div class="agreement-management-view view">
    <div class="header-container">
      <h2>协议管理</h2>
    </div>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>印章管理</span>
        </div>
      </template>
      <div class="stamp-section">
        <el-upload class="stamp-uploader" action="#" :auto-upload="false" :limit="1" accept="image/png"
          :on-change="handleStampUpload" :show-file-list="false">
          <el-button type="primary" :icon="Upload">导入印章 (PNG)</el-button>
        </el-upload>
        <div v-if="stampFileName" class="stamp-info">
          当前印章: {{ stampFileName }}
          <el-button type="danger" link @click="removeStampFile">移除</el-button>
        </div>
        <p v-else class="stamp-info">暂未导入印章</p>
        <p class="el-upload__tip">请上传PNG格式的印章图片，用于生成协议文件。</p>
      </div>
    </el-card>

    <el-card v-if="isTradingPlatform" class="box-card">
      <template #header>
        <div class="card-header">
          <span>自定义协议模板</span>
        </div>
      </template>
      <div class="template-section">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="ISDA主协议" name="isda-master">
            <el-form label-position="top">
              <el-form-item label="上传模板PDF" class="upload-info-stacked">
                <el-upload class="template-uploader" action="#" :auto-upload="false" :limit="1" accept="application/pdf"
                  :on-change="handleTemplateUpload" :file-list="[]" :show-file-list="false">
                  <el-button :icon="UploadFilled" :loading="uploadingTemplate">上传PDF文件</el-button>
                </el-upload>
                <div v-if="templateConfig['isda-master']?.filter?.name" class="template-info">
                  当前文件: {{ templateConfig['isda-master'].filter.name }}
                  <el-button type="danger" link @click="clearTemplate('isda-master')">移除模板</el-button>
                </div>
                <p v-else class="template-info">未配置自定义模板，将使用系统默认模板</p>
              </el-form-item>

              <template v-if="templateConfig['isda-master']?.filter?.uid">
                <el-divider>配置盖章位置</el-divider>
                <el-form-item label="盖章坐标 (在PDF上添加印章的位置)">
                  <div class="positions-container">
                    <div class="position-header" v-if="templateConfig['isda-master'].stamp.length > 0">
                      位置参数：分别为页码、X坐标和 Y坐标
                    </div>
                    <div v-for="(pos, index) in templateConfig['isda-master'].stamp" :key="`stamp-${index}`"
                      class="position-item">
                      <div class="position-label">{{ index + 1 }}:</div>
                      <div class="position-coords">
                        <el-input-number v-model="pos[0]" :min="1" :max="1000" size="small"
                          placeholder="页码"></el-input-number>
                        <el-input-number v-model="pos[1]" :min="0" :max="1000" size="small"
                          placeholder="X"></el-input-number>
                        <el-input-number v-model="pos[2]" :min="0" :max="1000" size="small"
                          placeholder="Y"></el-input-number>
                        <el-button type="danger" size="small"
                          @click="removePosition('isda-master', 'stamp', index)">删除</el-button>
                      </div>
                    </div>
                    <div class="action-buttons">
                      <el-button type="primary" size="small"
                        @click="addPosition('isda-master', 'stamp')">添加盖章位置</el-button>
                    </div>
                  </div>
                </el-form-item>

                <el-divider>配置签名位置</el-divider>
                <el-form-item label="签名坐标 (在PDF上添加电子签名的位置)">
                  <div class="positions-container">
                    <div class="position-header" v-if="templateConfig['isda-master'].signature.length > 0">
                      位置参数：分别为页码、X坐标和 Y坐标
                    </div>
                    <div v-for="(pos, index) in templateConfig['isda-master'].signature" :key="`signature-${index}`"
                      class="position-item">
                      <div class="position-label">{{ index + 1 }}:</div>
                      <div class="position-coords">
                        <el-input-number v-model="pos[0]" :min="1" :max="1000" size="small"
                          placeholder="页码"></el-input-number>
                        <el-input-number v-model="pos[1]" :min="0" :max="1000" size="small"
                          placeholder="X"></el-input-number>
                        <el-input-number v-model="pos[2]" :min="0" :max="1000" size="small"
                          placeholder="Y"></el-input-number>
                        <el-button type="danger" size="small"
                          @click="removePosition('isda-master', 'signature', index)">删除</el-button>
                      </div>
                    </div>
                    <div class="action-buttons">
                      <el-button type="primary" size="small"
                        @click="addPosition('isda-master', 'signature')">添加签名位置</el-button>
                    </div>
                  </div>
                </el-form-item>
              </template>
            </el-form>
          </el-tab-pane>

          <el-tab-pane label="ISDA补充协议" name="isda-supplement">
            <el-form label-position="top">
              <el-form-item label="上传模板PDF" class="upload-info-stacked">
                <el-upload class="template-uploader" action="#" :auto-upload="false" :limit="1" accept="application/pdf"
                  :on-change="handleTemplateUpload" :file-list="[]" :show-file-list="false">
                  <el-button :icon="UploadFilled" :loading="uploadingTemplate">上传PDF文件</el-button>
                </el-upload>
                <div v-if="templateConfig['isda-supplement']?.filter?.name" class="template-info">
                  当前文件: {{ templateConfig['isda-supplement'].filter.name }}
                  <el-button type="danger" link @click="clearTemplate('isda-supplement')">移除模板</el-button>
                </div>
                <p v-else class="template-info">未配置自定义模板，将使用系统默认模板</p>
              </el-form-item>

              <template v-if="templateConfig['isda-supplement']?.filter?.uid">
                <el-divider>配置盖章位置</el-divider>
                <el-form-item label="盖章坐标 (在PDF上添加印章的位置)">
                  <div class="positions-container">
                    <div class="position-header" v-if="templateConfig['isda-supplement'].stamp.length > 0">
                      位置参数：分别为页码、X坐标和 Y坐标
                    </div>
                    <div v-for="(pos, index) in templateConfig['isda-supplement'].stamp" :key="`stamp-${index}`"
                      class="position-item">
                      <div class="position-label">{{ index + 1 }}:</div>
                      <div class="position-coords">
                        <el-input-number v-model="pos[0]" :min="1" :max="1000" size="small"
                          placeholder="页码"></el-input-number>
                        <el-input-number v-model="pos[1]" :min="0" :max="1000" size="small"
                          placeholder="X"></el-input-number>
                        <el-input-number v-model="pos[2]" :min="0" :max="1000" size="small"
                          placeholder="Y"></el-input-number>
                        <el-button type="danger" size="small"
                          @click="removePosition('isda-supplement', 'stamp', index)">删除</el-button>
                      </div>
                    </div>
                    <div class="action-buttons">
                      <el-button type="primary" size="small"
                        @click="addPosition('isda-supplement', 'stamp')">添加盖章位置</el-button>
                    </div>
                  </div>
                </el-form-item>

                <el-divider>配置签名位置</el-divider>
                <el-form-item label="签名坐标 (在PDF上添加电子签名的位置)">
                  <div class="positions-container">
                    <div class="position-header" v-if="templateConfig['isda-supplement'].signature.length > 0">
                      位置参数：分别为页码、X坐标和 Y坐标
                    </div>
                    <div v-for="(pos, index) in templateConfig['isda-supplement'].signature" :key="`signature-${index}`"
                      class="position-item">
                      <div class="position-label">{{ index + 1 }}:</div>
                      <div class="position-coords">
                        <el-input-number v-model="pos[0]" :min="1" :max="1000" size="small"
                          placeholder="页码"></el-input-number>
                        <el-input-number v-model="pos[1]" :min="0" :max="1000" size="small"
                          placeholder="X"></el-input-number>
                        <el-input-number v-model="pos[2]" :min="0" :max="1000" size="small"
                          placeholder="Y"></el-input-number>
                        <el-button type="danger" size="small"
                          @click="removePosition('isda-supplement', 'signature', index)">删除</el-button>
                      </div>
                    </div>
                    <div class="action-buttons">
                      <el-button type="primary" size="small"
                        @click="addPosition('isda-supplement', 'signature')">添加签名位置</el-button>
                    </div>
                  </div>
                </el-form-item>
              </template>
            </el-form>
          </el-tab-pane>
        </el-tabs>

        <div class="template-actions">
          <el-button type="success" @click="previewCurrentTemplate">
            预览效果
          </el-button>
          <el-button type="warning" @click="() => openPositionEditor()"
            :disabled="!templateConfig[activeTab]?.filter?.uid">
            可视化编辑位置
          </el-button>
          <el-button type="primary" @click="saveTemplateConfig" :loading="savingTemplate">保存模板配置</el-button>
        </div>
      </div>
    </el-card>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>生成ISDA协议</span>
          <el-button type="primary" @click="batchGenerateAndDownloadAgreements" :loading="generatingAgreements"
            :disabled="!hasStampFile || selectedAudits.length === 0">
            <el-icon>
              <DocumentAdd />
            </el-icon>
            生成选中协议
          </el-button>
        </div>
      </template>

      <el-alert v-if="!hasStampFile" title="请先导入印章文件才能生成协议。" type="warning" show-icon :closable="false"
        style="margin-bottom: 20px;" />

      <div class="search-container">
        <el-input v-model="searchUserId" placeholder="输入用户ID搜索" class="search-input" clearable
          @keydown.enter="handleSearch">
          <template #suffix v-if="false">
            <el-button :icon="Search" circle @click="handleSearch"></el-button>
          </template>
        </el-input>
      </div>

      <el-table v-loading="loadingAudits" :data="approvedAudits" style="width: 100%"
        @selection-change="handleSelectionChange" row-key="audit_id">
        <el-table-column type="selection" width="55" :selectable="() => hasStampFile" />
        <el-table-column prop="audit_id" label="审核ID" min-width="80" />
        <el-table-column prop="user_id" label="用户ID" min-width="100" />
        <el-table-column label="名称" min-width="120">
          <template #default="{ row }">{{ row.data?.name }}</template>
        </el-table-column>
        <el-table-column prop="created_at" label="提交时间" min-width="160">
          <template #default="{ row }">{{ formatDate(row.created_at) }}</template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next" :total="totalAudits" @size-change="fetchApprovedAudits"
          @current-change="fetchApprovedAudits" />
      </div>
    </el-card>

    <!-- PDF预览组件 -->
    <PdfPreview v-model="previewVisible" :pdf-url="previewPdfUrl" :title="previewTitle" filename="位置预览.pdf" />

    <!-- PDF位置编辑器 -->
    <PdfPositionEditor v-model="positionEditorVisible" :title="positionEditorTitle" :pdf-data="positionEditorPdfData"
      :stamp-positions="positionEditorStampPositions" :signature-positions="positionEditorSignaturePositions"
      @save="handlePositionEditorSave" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Upload, DocumentAdd, UploadFilled, Search } from "@element-plus/icons-vue";
import { qualifyApi, templateApi } from "@/api";
import { AuditStatus, AppType } from "@packages/shared";
import type { QualificationAuditData } from "@packages/shared";
import { useAuthStore } from "@/stores/auth";
import { getStampFile, setStampFile, clearStampFile } from "@/utils/file-utils";
import { generateIsdaMasterAgreement, generateIsdaSupplement } from "@/utils/pdf-utils";
import { formatDate } from "@/utils/format";
import JSZip from "jszip";
import PdfPreview from "@/components/PdfPreview.vue";
import PdfPositionEditor from "@/components/PdfPositionEditor.vue";

const authStore = useAuthStore();
const isTradingPlatform = computed(() => authStore.app_type === AppType.TRADING_PLATFORM);

// Stamp management
const stampFileObject = ref(getStampFile());
const stampFileName = computed(() => stampFileObject.value.file?.name);
const hasStampFile = computed(() => !!stampFileObject.value.file);

// 模板管理
const activeTab = ref("isda-master");
const templateConfig = ref<Record<string, {
  filter: { uid: string; name: string };
  stamp: Array<[number, number, number]>;   // [页码, X坐标, Y坐标]
  signature: Array<[number, number, number]>; // [页码, X坐标, Y坐标]
}>>({
  "isda-master": {
    filter: { uid: "", name: "" },
    stamp: [],
    signature: []
  },
  "isda-supplement": {
    filter: { uid: "", name: "" },
    stamp: [],
    signature: []
  }
});
const savingTemplate = ref(false);
const uploadingTemplate = ref(false);

// 导入印章
const handleStampUpload = (file: { raw: File, name: string }) => {
  if (!file.raw) {
    ElMessage.error("导入失败，请重试");
    return;
  }
  const newFile = file.raw; // Use a clear variable for the new file
  const isImage = newFile.type === "image/png";
  if (!isImage) {
    ElMessage.error("只能导入PNG图片");
    return;
  }

  // 验证文件大小 (1MB限制)
  const maxStampSize = 1 * 1024 * 1024; // 1MB
  if (newFile.size > maxStampSize) {
    ElMessage.error("印章文件过大，请保持在1MB以内");
    return;
  }

  setStampFile(newFile);
  // Ensure reactive update by explicitly setting the file property on the object.
  // Merge with whatever getStampFile() returns to keep other potential state,
  // ensuring 'file' property is definitively the new file.
  stampFileObject.value = { ...getStampFile(), file: newFile };
  ElMessage.success("印章导入成功");
};

const removeStampFile = () => {
  clearStampFile();
  stampFileObject.value = getStampFile(); // Refresh local state
  ElMessage.info("印章已移除");
};

// 获取模板配置
const fetchTemplateConfig = async () => {
  try {
    const response = await templateApi.getTemplateConfig();
    if (response) {
      // 确保两个模板都有初始结构
      templateConfig.value = {
        "isda-master": {
          filter: { uid: "", name: "" },
          stamp: [],
          signature: []
        },
        "isda-supplement": {
          filter: { uid: "", name: "" },
          stamp: [],
          signature: []
        },
        ...response
      };
    }
  } catch (error) {
    console.error("加载模板配置失败:", error);
    ElMessage.warning("加载模板配置失败，将使用默认配置");
  }
};

// 添加位置坐标
const addPosition = (templateKey: string, positionType: 'stamp' | 'signature') => {
  if (!templateConfig.value[templateKey]) {
    templateConfig.value[templateKey] = {
      filter: { uid: "", name: "" },
      stamp: [],
      signature: []
    };
  }
  templateConfig.value[templateKey][positionType].push([1, 0, 0]);
};

// 移除位置坐标
const removePosition = (templateKey: string, positionType: 'stamp' | 'signature', index: number) => {
  templateConfig.value[templateKey][positionType].splice(index, 1);
};

// 上传模板文件
const handleTemplateUpload = async (file: { raw: File, name: string }) => {
  if (!file.raw) {
    ElMessage.error("上传失败，请重试");
    return;
  }

  // 判断是哪个模板类型的上传
  const templateKey = activeTab.value;

  const isPdf = file.raw.type === "application/pdf";
  if (!isPdf) {
    ElMessage.error("只能上传PDF文件");
    return;
  }

  // 验证文件大小 (10MB限制)
  const maxTemplateSize = 10 * 1024 * 1024; // 10MB
  if (file.raw.size > maxTemplateSize) {
    ElMessage.error("模板文件过大，请保持在10MB以内");
    return;
  }

  // 添加延迟上传逻辑
  uploadingTemplate.value = true;

  // 延迟2秒后执行实际上传
  setTimeout(async () => {
    try {
      const response = await templateApi.uploadCustomTemplate(file.raw, file.raw.name);
      if (response) {
        if (!templateConfig.value[templateKey]) {
          templateConfig.value[templateKey] = {
            filter: { uid: "", name: "" },
            stamp: [],
            signature: []
          };
        }
        templateConfig.value[templateKey].filter = {
          uid: response.uid,
          name: response.name
        };
        ElMessage.success("模板上传成功，请记得保存配置");
      }
    } catch (error) {
      console.error("模板上传失败:", error);
      ElMessage.error("模板上传失败");
    } finally {
      uploadingTemplate.value = false;
    }
  }, 2000);
};

// 保存模板配置
const saveTemplateConfig = async () => {
  savingTemplate.value = true;
  try {
    await templateApi.saveTemplateConfig(templateConfig.value);
    ElMessage.success("模板配置保存成功");
  } catch (error) {
    console.error("保存模板配置失败:", error);
    ElMessage.error("保存模板配置失败");
  } finally {
    savingTemplate.value = false;
  }
};

// Audits data
const approvedAudits = ref<QualificationAuditData[]>([]);
const loadingAudits = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const totalAudits = ref(0);
const searchUserId = ref('');

const fetchApprovedAudits = async () => {
  loadingAudits.value = true;
  try {
    const filters: {
      status: AuditStatus;
      user_id?: number;
    } = {
      status: AuditStatus.APPROVED,
    };

    // 如果有输入用户ID，添加到筛选条件
    if (searchUserId.value.trim()) {
      const userId = Number.parseInt(searchUserId.value.trim(), 10);
      if (!Number.isNaN(userId)) {
        filters.user_id = userId;
      }
    }

    const response = await qualifyApi.getQualificationsAudits({
      page: currentPage.value,
      pageSize: pageSize.value,
      sortBy: "created_at",
      sortOrder: "DESC",
      filters
    });
    approvedAudits.value = response?.items || [];
    totalAudits.value = response?.total || 0;
  } catch (error) {
    console.error("加载已通过资质审核数据失败:", error);
    ElMessage.error("加载已通过资质审核数据失败");
  } finally {
    loadingAudits.value = false;
  }
};

// 搜索处理函数
const handleSearch = () => {
  currentPage.value = 1; // 重置到第一页
  fetchApprovedAudits();
};

onMounted(() => {
  fetchApprovedAudits();
  fetchTemplateConfig();
});

// Agreement generation
const selectedAudits = ref<QualificationAuditData[]>([]);
const generatingAgreements = ref(false);

const handleSelectionChange = (selection: QualificationAuditData[]) => {
  selectedAudits.value = selection;
};

const batchGenerateAndDownloadAgreements = async () => {
  if (!hasStampFile.value) {
    ElMessage.warning("请先上传印章文件");
    return;
  }
  if (selectedAudits.value.length === 0) {
    ElMessage.warning("请选择至少一个用户以生成协议");
    return;
  }

  generatingAgreements.value = true;
  try {
    // Fetch templates
    const [masterTemplate, supplementTemplate] = await Promise.all([
      templateApi.getIsdaMasterTemplate(),
      templateApi.getIsdaSupplementTemplate(),
    ]);

    // Check for null and assert type for subsequent use
    if (!masterTemplate || !supplementTemplate) {
      ElMessage.error("无法获取协议模板，请检查模板配置");
      generatingAgreements.value = false;
      return;
    }

    const stampFile = stampFileObject.value.file;
    if (!stampFile) { // Should be caught by hasStampFile but double check
      ElMessage.error("印章文件丢失，请重新上传");
      generatingAgreements.value = false;
      return;
    }

    const stampArrayBuffer = await stampFile.arrayBuffer();
    const zip = new JSZip();
    let generatedCount = 0;

    for (const audit of selectedAudits.value) {
      try {
        const signatureResponse = await qualifyApi.getUserSignature(audit.user_id);
        if (!signatureResponse) {
          console.warn(`用户 ${audit.user_id} (${audit.data?.name}) 没有签名数据，跳过生成协议。`);
          ElMessage.warning({
            message: `用户 ${audit.user_id} (${audit.data?.name}) 没有签名数据，已跳过。`,
            duration: 5000,
            showClose: true,
          });
          continue;
        }

        // Ensure audit.data is not null and has necessary fields
        if (!audit.data || !audit.data.name || !audit.data.id_number) {
          console.warn(`用户 ${audit.user_id} 的资质数据不完整，跳过生成协议。`);
          ElMessage.warning({
            message: `用户 ${audit.user_id} 的资质数据不完整，已跳过。`,
            duration: 5000,
            showClose: true,
          });
          continue;
        }

        const signatureData = signatureResponse || "";

        // 获取自定义模板的盖章和签名位置
        const masterCustomPositions = templateConfig.value['isda-master'].filter.uid ? {
          stamp: templateConfig.value['isda-master'].stamp.map(pos => [pos[0] - 1, pos[1], pos[2]] as [number, number, number]),
          signature: templateConfig.value['isda-master'].signature.map(pos => [pos[0] - 1, pos[1], pos[2]] as [number, number, number])
        } : undefined;

        const supplementCustomPositions = templateConfig.value['isda-supplement'].filter.uid ? {
          stamp: templateConfig.value['isda-supplement'].stamp.map(pos => [pos[0] - 1, pos[1], pos[2]] as [number, number, number]),
          signature: templateConfig.value['isda-supplement'].signature.map(pos => [pos[0] - 1, pos[1], pos[2]] as [number, number, number])
        } : undefined;

        const masterPdf = await generateIsdaMasterAgreement(
          masterTemplate,
          signatureData,
          stampArrayBuffer,
          masterCustomPositions
        );

        const supplementPdf = await generateIsdaSupplement(
          supplementTemplate,
          signatureData,
          stampArrayBuffer,
          supplementCustomPositions
        );

        const userNameForFile = audit.data?.name ? `${audit.user_id}_${audit.data.name}` : `${audit.user_id}`;
        zip.file(`用户${userNameForFile}_ISDA主协议.pdf`, masterPdf);
        zip.file(`用户${userNameForFile}_ISDA补充协议.pdf`, supplementPdf);
        generatedCount++;
      } catch (error) {
        console.error(`为用户 ${audit.user_id} (${audit.data?.name}) 生成协议时出错:`, error);
        ElMessage.error(`为用户 ${audit.user_id} (${audit.data?.name}) 生成协议失败`);
      }
    }

    if (generatedCount > 0) {
      const content = await zip.generateAsync({ type: "blob" });
      const url = URL.createObjectURL(content);
      const link = document.createElement("a");
      link.href = url;
      link.download = `ISDA协议包_${formatDate(new Date())}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      ElMessage.success(`成功生成并下载了 ${generatedCount} 个用户的ISDA协议。`);
    } else if (selectedAudits.value.length > 0) {
      ElMessage.warning("未能成功生成任何协议，请检查用户数据和模板配置。");
    }

  } catch (error) {
    console.error("批量生成协议失败:", error);
    ElMessage.error("批量生成协议操作失败，请检查控制台获取更多信息。");
  } finally {
    generatingAgreements.value = false;
  }
};

// PDF预览相关
const previewVisible = ref(false);
const previewPdfUrl = ref("");
const previewTitle = ref("");

// PDF位置编辑器相关
const positionEditorVisible = ref(false);
const positionEditorTitle = ref("");
const positionEditorPdfData = ref<ArrayBuffer | null>(null);
const positionEditorStampPositions = ref<Array<[number, number, number]>>([]);
const positionEditorSignaturePositions = ref<Array<[number, number, number]>>([]);

// 预览当前模板功能
const previewCurrentTemplate = async () => {
  const templateKey = activeTab.value;
  try {
    // 检查基本条件
    const isCustomTemplate = !!templateConfig.value[templateKey]?.filter?.uid;
    const hasCustomStamp = templateConfig.value[templateKey]?.stamp?.length > 0;
    const hasCustomSignature = templateConfig.value[templateKey]?.signature?.length > 0;


    if (!hasStampFile.value) {
      ElMessage.info("请先导入印章文件");
      return;
    }

    // 准备生成预览
    ElMessage.info("正在生成预览...");

    // 获取模板数据
    let templateData: ArrayBuffer | null;
    if (templateKey === 'isda-master') {
      templateData = await templateApi.getIsdaMasterTemplate();
    } else {
      templateData = await templateApi.getIsdaSupplementTemplate();
    }

    if (!templateData) {
      ElMessage.error("无法获取模板数据");
      return;
    }

    // 准备自定义位置信息（仅在自定义模板时使用）
    const customPositions = isCustomTemplate ? {
      stamp: hasCustomStamp ? templateConfig.value[templateKey].stamp.map(pos => [pos[0] - 1, pos[1], pos[2]] as [number, number, number]) : [],
      signature: hasCustomSignature ? templateConfig.value[templateKey].signature.map(pos => [pos[0] - 1, pos[1], pos[2]] as [number, number, number]) : []
    } : undefined;

    // 创建示例签名（如果有签名位置或使用默认模板）
    const shouldCreateSignature = isCustomTemplate ? hasCustomSignature : true;
    const exampleSignature = shouldCreateSignature ? createExampleSignature() : "";

    // 准备印章数据（如果有印章位置或使用默认模板，且用户上传了印章文件）
    const shouldUseStamp = isCustomTemplate ? hasCustomStamp : true;
    const stampData = (shouldUseStamp && stampFileObject.value.file) ? await stampFileObject.value.file.arrayBuffer() : new ArrayBuffer(0);

    // 生成预览PDF
    let previewPdf: Blob;
    if (templateKey === 'isda-master') {
      previewPdf = await generateIsdaMasterAgreement(
        templateData,
        exampleSignature,
        stampData,
        customPositions
      );
    } else {
      previewPdf = await generateIsdaSupplement(
        templateData,
        exampleSignature,
        stampData,
        customPositions
      );
    }

    // 创建Blob URL用于预览
    const url = URL.createObjectURL(previewPdf);
    previewPdfUrl.value = url;
    previewTitle.value = `${templateKey === 'isda-master' ? 'ISDA主协议' : 'ISDA补充协议'}预览效果`;
    previewVisible.value = true;

  } catch (error) {
    console.error("生成预览失败:", error);
    ElMessage.error("生成预览失败，请查看控制台获取更多信息");
  }
};

// 预览位置功能（已废弃，使用previewCurrentTemplate代替）
// const previewPositions = async (templateKey: string, positionType: 'stamp' | 'signature') => {
//   // 此方法已被previewCurrentTemplate替代
// };

// 创建一个测试用的签名图像
const createExampleSignature = (): string => {
  // 创建一个匹配实际签名弹窗的2:1宽高比的签名
  const canvas = document.createElement('canvas');
  // 使用实际签名像素比，避免缩放问题
  canvas.width = 200;
  canvas.height = 100;
  const ctx = canvas.getContext('2d');

  if (ctx) {
    // 清除背景
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 设置签名样式
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // 绘制"Sign"签名
    ctx.beginPath();

    // S - 确保是明确的S形状，不像e
    // 使用简单直接的三段曲线绘制明确的S
    ctx.moveTo(30, 30); // 顶部起点
    ctx.bezierCurveTo(20, 30, 15, 40, 25, 50); // 上半部分，向左弯曲
    ctx.bezierCurveTo(35, 60, 45, 60, 45, 70); // 中间到底部
    ctx.bezierCurveTo(45, 80, 35, 85, 25, 80); // 底部弯曲

    // i
    ctx.moveTo(65, 30);
    ctx.arc(65, 30, 2, 0, Math.PI * 2); // 点
    ctx.moveTo(65, 40);
    ctx.lineTo(65, 75);

    // g - 再次调整大小和尾巴长度
    // 圆形头部 - 再增大一点，并微调位置以保持平衡
    ctx.moveTo(108, 43); // g的右上角起笔 (微调X和Y)
    ctx.bezierCurveTo(90, 43, 82, 60, 95, 68); // g的圆形主体上半部分 (进一步增大)
    ctx.bezierCurveTo(110, 72, 115, 60, 108, 48); // g的圆形主体下半部分，连接到头部 (进一步增大并调整连接点)
    // 下降的尾巴和圈 - 拉长竖线，并调整小圈
    ctx.lineTo(108, 85); // 向下延伸，拉长竖线 (Y从75增加到85)
    ctx.bezierCurveTo(102, 95, 85, 92, 85, 80); // 左下的小圈 (调整位置和大小以适应更长的尾巴)

    // n
    ctx.moveTo(125, 75);
    ctx.lineTo(125, 45);
    ctx.bezierCurveTo(130, 40, 140, 40, 145, 45);
    ctx.lineTo(145, 70); // 稍微缩短竖线的长度，为圆润转角留出空间

    // 收尾横线 - 创建圆润的转角过渡
    // 先创建一个圆润的转角
    ctx.quadraticCurveTo(147, 75, 150, 73); // 小弧度转角，从竖直方向平滑过渡到横向
    // 然后连接到收尾的上扬曲线
    ctx.bezierCurveTo(160, 65, 170, 55, 180, 45); // 上扬的收尾

    ctx.stroke();
  }

  return canvas.toDataURL('image/png');
};

// 清空模板
const clearTemplate = (templateKey: string) => {
  if (!templateConfig.value[templateKey]?.filter?.name) {
    ElMessage.info("当前已使用默认模板");
    return;
  }

  // 清空模板的filter信息
  templateConfig.value[templateKey].filter = { uid: "", name: "" };
  // 清空位置信息，因为位置可能与默认模板不匹配
  templateConfig.value[templateKey].stamp = [];
  templateConfig.value[templateKey].signature = [];

  ElMessage.success("已清空自定义模板，请记得保存配置");
};

// 打开位置编辑器
const openPositionEditor = async (templateKey?: string) => {
  const currentTemplateKey = templateKey || activeTab.value;
  const config = templateConfig.value[currentTemplateKey];

  if (!config?.filter?.uid) {
    ElMessage.warning("请先上传模板文件");
    return;
  }

  try {
    // 获取模板数据
    let templateData: ArrayBuffer | null;
    if (currentTemplateKey === 'isda-master') {
      templateData = await templateApi.getIsdaMasterTemplate();
      positionEditorTitle.value = "ISDA主协议位置编辑";
    } else {
      templateData = await templateApi.getIsdaSupplementTemplate();
      positionEditorTitle.value = "ISDA补充协议位置编辑";
    }

    if (!templateData) {
      ElMessage.error("无法获取模板数据");
      return;
    }

    // 设置编辑器数据
    positionEditorPdfData.value = templateData;
    positionEditorStampPositions.value = [...config.stamp];
    positionEditorSignaturePositions.value = [...config.signature];

    // 打开编辑器
    positionEditorVisible.value = true;

  } catch (error) {
    console.error("打开位置编辑器失败:", error);
    ElMessage.error("打开位置编辑器失败");
  }
};

// 处理位置编辑器保存
const handlePositionEditorSave = (data: {
  stamp: Array<[number, number, number]>;
  signature: Array<[number, number, number]>;
}) => {
  const currentTemplateKey = activeTab.value;
  if (!templateConfig.value[currentTemplateKey]) return;

  // 更新配置
  templateConfig.value[currentTemplateKey].stamp = data.stamp;
  templateConfig.value[currentTemplateKey].signature = data.signature;

  ElMessage.success("位置已更新，请记得保存模板配置");
};

</script>

<style scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

h2 {
  margin: 0;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stamp-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}

.stamp-uploader .el-button {
  margin-bottom: 10px;
}

.stamp-info {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.el-upload__tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.template-section {
  padding: 0 0 20px 0;
}

.template-info {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.upload-info-stacked :deep(.el-form-item__content) {
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  padding-top: 4px;
}

.positions-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.position-header {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.position-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.position-label {
  width: 40px;
}

.position-coords {
  display: flex;
  flex: 1;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .position-coords {
    width: 100%;
  }

  /* 优化移动端模板信息和清空按钮的显示 */
  .template-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .template-info .el-button {
    margin-left: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 4px;
}

.template-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  flex-wrap: wrap;
}

/* 调整输入框高度 */
:deep(.el-input-number) {
  height: 28px;
  line-height: 28px;
}

:deep(.el-input-number .el-input__wrapper) {
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-container {
  margin-bottom: 16px;
}

.search-input {
  width: 300px;
}
</style>