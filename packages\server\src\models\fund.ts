import prisma from "@/lib/prisma.js";
import type { Prisma } from "@prisma/client";
import { withTransaction } from "@/core/dbTxnManager.js";
import * as User from "./user.js";
import { AppError } from "@/core/appError.js";
import type { TransactionData, Currency } from "@packages/shared";
import { TransactionType } from "@packages/shared";

async function create(
	{
		user_id,
		signed_amount,
		type,
		currency,
		trade_no,
	}: Omit<TransactionData, "txn_id" | "created_at">,
	client?: Prisma.TransactionClient,
): Promise<TransactionData> {
	const createFn = async (tx: Prisma.TransactionClient) => {
		const result = await tx.transactions.create({
			data: {
				user_id,
				signed_amount,
				type,
				currency: currency || "CNY",
				trade_no,
			},
		});
		return {
			...result,
			signed_amount: Number(result.signed_amount),
			type: result.type as TransactionType,
			currency: result.currency as Currency,
			trade_no: result.trade_no || undefined,
			created_at: result.created_at?.toISOString() || "",
		};
	};
	return client ? createFn(client) : withTransaction(createFn);
}

export async function record(
	{
		user_id,
		signed_amount,
		type,
		currency,
		trade_no,
	}: Omit<TransactionData, "txn_id" | "created_at">,
	client?: Prisma.TransactionClient,
): Promise<TransactionData> {
	const recordFn = async (tx: Prisma.TransactionClient) => {
		const user = await User.findById(user_id);

		if (!user) throw AppError.create("USER_NOT_FOUND", "User not found");

		const balanceField =
			`balance_${currency.toLowerCase()}` as keyof typeof user;
		const currentBalance = Number(user[balanceField]) || 0;
		const newBalance = currentBalance + signed_amount;

		if (user[balanceField] === undefined || newBalance < 0) {
			throw AppError.create(
				"INSUFFICIENT_BALANCE",
				"Insufficient or invalid balance",
			);
		}

		await User.updateBalance(user_id, newBalance, currency, tx);

		return await create(
			{
				user_id,
				signed_amount,
				type,
				currency,
				trade_no,
			},
			tx,
		);
	};

	return client ? recordFn(client) : withTransaction(recordFn);
}

function adjustDateRange(filters?: {
	startDate?: string;
	endDate?: string;
}): { startDate?: Date; endDate?: Date } {
	if (!filters) return {};

	return {
		startDate: filters.startDate ? new Date(filters.startDate) : undefined,
		endDate: filters.endDate
			? new Date(new Date(filters.endDate).getTime() + 86400000)
			: undefined,
	};
}

export async function countTransactions(
	user_id?: number,
	filters?: {
		types?: TransactionType[];
		startDate?: string;
		endDate?: string;
	},
): Promise<number> {
	const { startDate, endDate } = adjustDateRange(filters);

	return prisma.transactions.count({
		where: {
			user_id,
			type: filters?.types?.length ? { in: filters.types } : undefined,
			created_at: {
				gte: startDate,
				lt: endDate,
			},
		},
	});
}

export async function getTransactions(
	offset: number,
	limit: number,
	isDescending: boolean,
	user_id?: number,
	filters?: {
		types?: TransactionType[];
		startDate?: string;
		endDate?: string;
	},
): Promise<TransactionData[]> {
	const { startDate, endDate } = adjustDateRange(filters);

	const results = await prisma.transactions.findMany({
		where: {
			user_id,
			type: filters?.types?.length ? { in: filters.types } : undefined,
			created_at: {
				gte: startDate,
				lt: endDate,
			},
		},
		orderBy: [
			{ created_at: isDescending ? "desc" : "asc" },
			{ signed_amount: isDescending ? "desc" : "asc" },
		],
		skip: offset,
		take: limit,
	});

	return results.map((result) => ({
		...result,
		signed_amount: Number(result.signed_amount),
		type: result.type as TransactionType,
		currency: result.currency as Currency,
		trade_no: result.trade_no || undefined,
		created_at: result.created_at?.toISOString() || "",
	}));
}

export async function getSettleVolumeByDateRange(
	start: Date,
	end: Date,
): Promise<number> {
	const result = await prisma.$queryRaw<
		[{ volume: number }]
	>`SELECT COALESCE(SUM(ABS(signed_amount)), 0) as volume 
			 FROM transactions 
			 WHERE created_at >= ${start} 
			 AND created_at < ${end} 
			 AND type = ${TransactionType.SELL}::transaction_type`;

	return Number(result[0].volume);
}

export async function getTotalByType(type: TransactionType): Promise<number> {
	const result = await prisma.$queryRaw<
		[{ volume: number }]
	>`SELECT COALESCE(SUM(ABS(signed_amount)), 0) as volume 
     FROM transactions 
     WHERE type = ${type}::transaction_type`;

	return Number(result[0].volume);
}

export async function getAllByApi() {
	return prisma.transactions.findMany({});
}
