import { WebSocketServer } from "ws";
import type { Server } from "node:http";
import type { WebSocket } from "ws";
import { WebSocketMessageType } from "@packages/shared";
import logger from "@/utils/logger.js";

interface CustomWebSocket extends WebSocket {
	user_id?: number;
	isAlive?: boolean;
	lastPing?: number;
}

class WebSocketManager {
	private instance: WebSocketServer | null = null;
	private constructor() {} // Prevent new instances
	private static manager: WebSocketManager;
	private heartbeatInterval: NodeJS.Timeout | null = null;
	private readonly HEARTBEAT_INTERVAL = 30000; // 30秒
	private readonly HEARTBEAT_TIMEOUT = 10000; // 10秒超时

	static getInstance(): WebSocketManager {
		if (!WebSocketManager.manager) {
			WebSocketManager.manager = new WebSocketManager();
		}
		return WebSocketManager.manager;
	}

	initialize(server: Server): WebSocketServer {
		if (!this.instance) {
			this.instance = new WebSocketServer({ server });
			this.setupWebSocket();
			this.startHeartbeat();
		}
		return this.instance;
	}

	getServer(): WebSocketServer | null {
		return this.instance;
	}

	private setupWebSocket() {
		this.instance?.on("connection", (ws: CustomWebSocket, req) => {
			// 从 URL 参数中获取用户 ID
			const userId = req.url
				? new URL(req.url, `ws://${req.headers.host}`).searchParams.get(
						"userId",
					)
				: null;

			if (userId) {
				ws.user_id = Number(userId);
			}

			// 初始化连接状态
			ws.isAlive = true;
			ws.lastPing = Date.now();

			logger.info(
				{ user_id: ws.user_id },
				"New WebSocket connection established",
			);

			// 处理消息
			ws.on("message", (data) => {
				try {
					const message = JSON.parse(data.toString());
					if (message.type === WebSocketMessageType.PONG) {
						ws.isAlive = true;
						ws.lastPing = Date.now();
					}
				} catch (error) {
					logger.warn({ error }, "Failed to parse WebSocket message");
				}
			});

			// 处理连接断开
			ws.on("close", () => {
				logger.info({ user_id: ws.user_id }, "WebSocket connection closed");
			});

			// 处理连接错误
			ws.on("error", (error) => {
				logger.warn(
					{ error, user_id: ws.user_id },
					"WebSocket connection error",
				);
			});

			// 立即发送一次ping确认连接
			this.sendPing(ws);
		});
	}

	private startHeartbeat() {
		if (this.heartbeatInterval) {
			clearInterval(this.heartbeatInterval);
		}

		this.heartbeatInterval = setInterval(() => {
			if (!this.instance) return;

			let activeConnections = 0;
			let cleanedConnections = 0;

			for (const client of this.instance.clients) {
				const customClient = client as CustomWebSocket;

				// 检查连接是否超时
				const now = Date.now();
				const timeSinceLastPing = now - (customClient.lastPing || 0);

				if (
					customClient.readyState === customClient.OPEN &&
					customClient.isAlive &&
					timeSinceLastPing < this.HEARTBEAT_TIMEOUT
				) {
					// 发送ping
					this.sendPing(customClient);
					customClient.isAlive = false; // 等待pong响应
					activeConnections++;
				} else {
					// 连接已死或超时，终止连接
					logger.info(
						{ user_id: customClient.user_id },
						"Terminating inactive WebSocket connection",
					);
					customClient.terminate();
					cleanedConnections++;
				}
			}

			if (activeConnections > 0 || cleanedConnections > 0) {
				logger.debug(
					{ activeConnections, cleanedConnections },
					"WebSocket heartbeat check completed",
				);
			}
		}, this.HEARTBEAT_INTERVAL);
	}

	private sendPing(ws: CustomWebSocket) {
		try {
			if (ws.readyState === ws.OPEN) {
				ws.send(JSON.stringify({ type: WebSocketMessageType.PING }));
			}
		} catch (error) {
			logger.warn({ error }, "Failed to send ping");
		}
	}

	destroy() {
		if (this.heartbeatInterval) {
			clearInterval(this.heartbeatInterval);
			this.heartbeatInterval = null;
		}

		if (this.instance) {
			this.instance.close();
			this.instance = null;
		}
	}
}

export default WebSocketManager.getInstance();
