// 执行命令：pnpm tsx --tsconfig D:/ink-dev/packages/server/tsconfig.json D:/ink-dev/packages/server/src/tests/tplusNTest.ts

import {
	dateToISOString,
	dateToCompactString,
	getChinaDate,
} from "@/utils/dateUtils.js";
import { getLastNTradingDays } from "@/financeUtils/marketTimeManager.js";

/**
 * T+N 结算检查测试函数
 *
 * 该测试函数用于验证在不同时区和时间条件下T+N的检查逻辑是否正确
 * 它模拟了不同下单时间和不同的T+N要求，检查在每种情况下是否能正确判断订单是否可以结算
 */
async function testTPlusNSettlement(): Promise<void> {
	console.log("==================== T+N Settlement Test ====================");

	// 定义不同的测试场景
	const testScenarios = [
		{ description: "测试当前时间作为订单时间", orderDate: new Date() },
		{
			description: "测试昨天作为订单时间",
			orderDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
		},
		{
			description: "测试前天作为订单时间",
			orderDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
		},
		{
			description: "测试三天前作为订单时间",
			orderDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
		},
		{
			description: "测试一周前作为订单时间",
			orderDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
		},
		// 特殊时段：下午15:00后的订单
		{
			description: "测试当天交易时段后的订单",
			orderDate: (() => {
				const date = new Date();
				date.setHours(15, 30, 0, 0);
				return date;
			})(),
		},
		// 特殊情况：周末下单
		{
			description: "测试周末下单",
			orderDate: (() => {
				const date = new Date();
				// 找到最近的周六
				while (date.getDay() !== 6) {
					date.setDate(date.getDate() - 1);
				}
				return date;
			})(),
		},
	];

	// 定义不同的T+N要求
	const tPlusNRequirements = [0, 1, 5];

	// 在每个测试场景和T+N要求组合下进行测试
	for (const scenario of testScenarios) {
		console.log(`\n${scenario.description}`);
		console.log(`订单时间: ${scenario.orderDate.toISOString()}`);

		// 测试不同的格式转换和时区
		const orderDateISOString = dateToISOString(scenario.orderDate);
		const orderDateCompactString = dateToCompactString(scenario.orderDate);

		console.log(`ISO格式: ${orderDateISOString}`);
		console.log(`Compact格式: ${orderDateCompactString}`);
		console.log(`订单时间的本地展示: ${scenario.orderDate.toString()}`);

		// 获取中国时间的当前日期
		const chinaDate = getChinaDate();
		console.log(`中国当前时间: ${chinaDate.toISOString()}`);

		for (const tPlusN of tPlusNRequirements) {
			console.log(`\n测试T+${tPlusN}规则:`);

			try {
				// 1. 获取最近N个交易日 (加上今天)
				const recentTradingDays = await getLastNTradingDays(tPlusN + 1);
				console.log(`获取到最近${tPlusN}个交易日:`);
				recentTradingDays.forEach((day, index) => {
					console.log(
						`  交易日 ${index + 1}: ${dateToISOString(day)} [${day.toISOString()}]`,
					);
				});

				// 2. 测试实际的T+N比较逻辑
				const orderDate = scenario.orderDate;
				const orderDateStr = dateToISOString(orderDate);

				// 找到最早的交易日（交易日是倒序排列的，所以是最后一个）
				const earliestTradingDay =
					tPlusN > 0 && recentTradingDays.length > 0
						? recentTradingDays[recentTradingDays.length]
						: new Date();

				const earliestTradingDayStr = dateToISOString(earliestTradingDay);

				console.log(
					`最早允许交易的日期: ${earliestTradingDayStr} [${earliestTradingDay.toISOString()}]`,
				);
				console.log(`订单日期: ${orderDateStr} [${orderDate.toISOString()}]`);

				// 3. 执行比较
				// 打印内部比较值，确保比较的是我们期望的值
				console.log(
					`字符串比较值: "${orderDateStr}" >= "${earliestTradingDayStr}" -> ${orderDateStr >= earliestTradingDayStr}`,
				);
				console.log(
					`时间戳比较值: ${orderDate.getTime()} >= ${earliestTradingDay.getTime()} -> ${orderDate.getTime() >= earliestTradingDay.getTime()}`,
				);

				// 当前系统使用字符串比较的逻辑 - 如果订单日期大于最早的交易日，则不满足T+N
				// 等于最早的交易日时，满足T+N
				const isRestrictedByString = orderDateStr > earliestTradingDayStr;
				// 使用时间戳比较的逻辑
				const isRestrictedByTimestamp =
					orderDate.getTime() > earliestTradingDay.getTime();

				console.log(
					`按照当前系统的字符串比较逻辑，${isRestrictedByString ? "不满足" : "满足"}T+${tPlusN}要求`,
				);
				console.log(
					`按照时间戳比较逻辑，${isRestrictedByTimestamp ? "不满足" : "满足"}T+${tPlusN}要求`,
				);

				// 检查两种比较逻辑是否得到相同结果
				console.log(
					`两种比较逻辑${isRestrictedByString === isRestrictedByTimestamp ? "一致" : "不一致"}`,
				);

				// 模拟实际系统中的T+N检查代码
				if (isRestrictedByString) {
					console.log(`❌ 未满足T+${tPlusN}限制，订单起始日：${orderDateStr}`);
				} else {
					console.log(`✅ 满足T+${tPlusN}限制，可以进行交易`);
				}
			} catch (error) {
				console.error(`测试T+${tPlusN}时出错:`, error);
			}
		}
	}
}

/**
 * 测试"当前时间"的不同获取方法对比
 * 检查本地时间、UTC时间和中国时间之间的差异
 */
function testTimezoneDifferences(): void {
	console.log("\n==================== Timezone Test ====================");

	// 获取当前本地时间
	const localNow = new Date();
	console.log(`本地当前时间: ${localNow.toString()}`);
	console.log(`本地时间的ISO字符串: ${localNow.toISOString()}`);
	console.log(`本地时间的UTC字符串: ${localNow.toUTCString()}`);

	// 获取中国时间
	const chinaNow = getChinaDate();
	console.log(`\n中国当前时间: ${chinaNow.toString()}`);
	console.log(`中国时间的ISO字符串: ${chinaNow.toISOString()}`);
	console.log(`经过dateToISOString转换: ${dateToISOString(chinaNow)}`);

	// 对比不同获取ISO日期的方式
	console.log("\n日期转换对比:");
	console.log(
		`localNow.toISOString().slice(0, 10): ${localNow.toISOString().slice(0, 10)}`,
	);
	console.log(`dateToISOString(localNow): ${dateToISOString(localNow)}`);
	console.log(
		`chinaNow.toISOString().slice(0, 10): ${chinaNow.toISOString().slice(0, 10)}`,
	);
	console.log(`dateToISOString(chinaNow): ${dateToISOString(chinaNow)}`);

	// 检查时区差异
	const timezoneOffset = localNow.getTimezoneOffset();
	console.log(`\n本地时区偏移(分钟): ${timezoneOffset}`);
	console.log(`本地时区偏移(小时): ${-timezoneOffset / 60}`);

	// 验证构造相同日期的不同方式
	console.log("\n验证不同日期构造方式:");

	// 方式1: 使用 new Date() 构造
	const date1 = new Date(2024, 4, 15); // 2024年5月15日（月份是0-indexed）

	// 方式2: 使用 ISO 字符串构造
	const date2 = new Date("2024-05-15T00:00:00.000Z");

	// 方式3: 使用时间戳构造
	const date3 = new Date(date1.getTime());

	console.log(
		`方式1 - new Date(2024, 4, 15): ${date1.toString()} [${date1.toISOString()}]`,
	);
	console.log(
		`方式2 - new Date("2024-05-15T00:00:00.000Z"): ${date2.toString()} [${date2.toISOString()}]`,
	);
	console.log(
		`方式3 - new Date(timestamp): ${date3.toString()} [${date3.toISOString()}]`,
	);

	console.log(`dateToISOString(date1): ${dateToISOString(date1)}`);
	console.log(`dateToISOString(date2): ${dateToISOString(date2)}`);

	// 检查日期比较
	console.log("\n日期比较测试:");
	console.log(
		`date1.getTime() === date2.getTime(): ${date1.getTime() === date2.getTime()}`,
	);
	console.log(
		`dateToISOString(date1) === dateToISOString(date2): ${dateToISOString(date1) === dateToISOString(date2)}`,
	);
}

/**
 * 测试真实交易日期与日历日期的关系
 * 比较T+N在交易日vs日历日下的行为差异
 */
async function testTradingDaysVsCalendarDays(): Promise<void> {
	console.log(
		"\n==================== Trading Days vs Calendar Days ====================",
	);

	// 获取今天的日期
	const today = new Date();
	const todayStr = dateToISOString(today);
	console.log(`今天日期: ${todayStr}`);

	// 获取最近几个交易日
	const tradingDays1 = await getLastNTradingDays(1);
	const tradingDays5 = await getLastNTradingDays(5);

	console.log("\n最近1个交易日:");
	tradingDays1.forEach((day, i) => {
		console.log(`  交易日 ${i + 1}: ${dateToISOString(day)}`);
	});

	console.log("\n最近5个交易日:");
	tradingDays5.forEach((day, i) => {
		console.log(`  交易日 ${i + 1}: ${dateToISOString(day)}`);
	});

	// 计算日历日
	const calendarDay1 = new Date(today);
	calendarDay1.setDate(calendarDay1.getDate() - 1);

	const calendarDay5 = new Date(today);
	calendarDay5.setDate(calendarDay5.getDate() - 5);

	console.log(`\n1个日历日前: ${dateToISOString(calendarDay1)}`);
	console.log(`5个日历日前: ${dateToISOString(calendarDay5)}`);

	// 比较交易日和日历日
	console.log("\nT+1对比:");
	console.log(
		`  交易日检查: ${todayStr} >= ${dateToISOString(tradingDays1[tradingDays1.length - 1])} -> ${todayStr >= dateToISOString(tradingDays1[tradingDays1.length - 1])}`,
	);
	console.log(
		`  日历日检查: ${todayStr} >= ${dateToISOString(calendarDay1)} -> ${todayStr >= dateToISOString(calendarDay1)}`,
	);

	console.log("\nT+5对比:");
	console.log(
		`  交易日检查: ${todayStr} >= ${dateToISOString(tradingDays5[tradingDays5.length - 1])} -> ${todayStr >= dateToISOString(tradingDays5[tradingDays5.length - 1])}`,
	);
	console.log(
		`  日历日检查: ${todayStr} >= ${dateToISOString(calendarDay5)} -> ${todayStr >= dateToISOString(calendarDay5)}`,
	);
}

// 执行所有测试
async function runTPlusNTests() {
	try {
		console.log("开始执行T+N结算测试...");

		// 测试时区差异
		testTimezoneDifferences();

		// 测试交易日vs日历日
		await testTradingDaysVsCalendarDays();

		// 测试T+N结算逻辑
		await testTPlusNSettlement();

		console.log("\n测试完成！");
	} catch (error) {
		console.error("测试过程中发生错误:", error);
	}
}

// 执行测试
runTPlusNTests();
