import { request } from "./request";
import type { BankAccount } from "@packages/shared";

export const bankAccountApi = {
	// 获取银行账户信息
	getBankAccount: () => request.get<BankAccount>("/admin/bank-account"),

	// 获取平台银行账户信息
	getPlatformBankAccount: () =>
		request.get<BankAccount>("/admin/bank-account/platform"),

	// 获取指定通道的银行账户信息
	getChannelBankAccount: (channelId: string) =>
		request.get<BankAccount>(`/admin/bank-account/channel/${channelId}`),

	// 更新银行账户信息
	updateBankAccount: (data: Partial<BankAccount>) =>
		request.post<{ message: string }>("/admin/bank-account", data),

	// 获取银行账户历史记录
	getBankAccountHistory: (
		page = 1,
		pageSize = 10,
		options?: {
			sortBy?: string;
			sortOrder?: "ASC" | "DESC";
		},
	) =>
		request.get<{
			total: number;
			items: {
				config_id: number;
				config: BankAccount;
				created_at: string;
				admin_id: number;
			}[];
		}>("/admin/bank-account/history", {
			params: {
				page,
				pageSize,
				sortBy: options?.sortBy,
				sortOrder: options?.sortOrder,
			},
		}),
} as const;
