import cron from "node-cron";
import * as SystemHealthService from "@/services/systemHealthService.js";
import logger from "@/utils/logger.js";

export class SystemHealthCheckCron {
	private static instance: SystemHealthCheckCron;

	private constructor() {}

	public static getInstance(): SystemHealthCheckCron {
		if (!SystemHealthCheckCron.instance) {
			SystemHealthCheckCron.instance = new SystemHealthCheckCron();
		}
		return SystemHealthCheckCron.instance;
	}

	public start(): void {
		try {
			// Run health checks daily at 7:30 AM (before market open)
			cron.schedule(
				"30 7 * * *",
				async () => {
					try {
						logger.info("Starting daily system health checks");

						// Run all health checks
						const results = await SystemHealthService.runAllHealthChecks();

						// Log summary of results
						const summary = Object.entries(results).map(([type, result]) => ({
							type,
							status: result.status,
							execution_time: result.execution_time,
						}));

						logger.info({ summary }, "Daily system health checks completed");
					} catch (error) {
						logger.error(error, "Error running daily system health checks");
					}
				},
				{
					timezone: "Asia/Shanghai",
				},
			);

			logger.info("System health check cron job started");
		} catch (error) {
			logger.error(error, "Failed to start system health check cron job");
			throw error;
		}
	}
}

// Export singleton instance
export const systemHealthCheckCron = SystemHealthCheckCron.getInstance();
