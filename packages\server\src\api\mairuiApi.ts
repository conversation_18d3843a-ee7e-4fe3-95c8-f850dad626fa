import { ENV } from "@/config/configManager.js";
import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";
import type { MarketData } from "@packages/shared";
import axiosWithDNSFailover from "./dnsFailoverAxios.js";

// 麦睿接口配置
export const MAIRUI_API_CONFIG = {
	BASE_URL: "http://api.mairui.club/hsrl/ssjy_more",
	SINGLE_STOCK_BASE_URL: "http://api.mairui.club/hsrl/ssjy",
	// 钻石版专用服务器
	VIP_PACKAGE_URL: "http://v.mairui.club/hsrl/ssjy",
	LICENSE: ENV.MAIRUI_LICENSE || "",
	MAX_STOCKS_PER_REQUEST: 20,
};

// 麦睿接口返回数据类型
export interface MairuiStockData {
	fm: number; // 五分钟涨跌幅（%）
	h: number; // 最高价（元）
	hs: number; // 换手（%）
	lb: number; // 量比（%）
	l: number; // 最低价（元）
	lt: number; // 流通市值（元）
	o: number; // 开盘价（元）
	pe: number; // 市盈率
	pc: number; // 涨跌幅（%）
	p: number; // 当前价格（元）
	sz: number; // 总市值（元）
	cje: number; // 成交额（元）
	ud: number; // 涨跌额（元）
	v: number; // 成交量（手）
	yc: number; // 昨日收盘价（元）
	zf: number; // 振幅（%）
	zs: number; // 涨速（%）
	sjl: number; // 市净率
	zdf60: number; // 60日涨跌幅（%）
	zdfnc: number; // 年初至今涨跌幅（%）
	t: string; // 更新时间YYYY-MM-DD HH:MM
}

/**
 * 从麦睿接口获取多支股票数据
 * @param ts_codes 股票代码数组
 * @param isForPrice 是否用于获取价格数据，默认为 false
 * @returns 股票代码到市场数据的映射
 */
export async function fetchMultipleStocksData(
	ts_codes: string[],
	isForPrice = false,
): Promise<Map<string, MarketData | number>> {
	// 确保不超过每次请求的最大股票数量
	const batchSize = MAIRUI_API_CONFIG.MAX_STOCKS_PER_REQUEST;
	const resultMap = new Map<string, MarketData | number>();

	// 将股票代码转换为麦睿接口需要的格式（去掉.SZ/.SH后缀）
	const convertedCodes = ts_codes.map((code) => code.split(".")[0]);

	// 分批处理请求
	for (let i = 0; i < convertedCodes.length; i += batchSize) {
		const batchCodes = convertedCodes.slice(i, i + batchSize);
		const stockCodesParam = batchCodes.join(",");

		try {
			// 尝试主接口 (BASE_URL for multiple stocks)
			const url = `${MAIRUI_API_CONFIG.BASE_URL}/${MAIRUI_API_CONFIG.LICENSE}?stock_codes=${stockCodesParam}`;
			const response = await axiosWithDNSFailover.get(url);
			const data = response.data as MairuiStockData[];

			// 将麦睿数据转换为我们的MarketData格式或价格
			data.forEach((item, index) => {
				const originalCode = ts_codes[i + index];
				if (isForPrice) {
					resultMap.set(originalCode, item.p);
				} else {
					resultMap.set(originalCode, {
						high: item.h,
						low: item.l,
						pre_close: item.yc,
						swing: item.zf,
						amount: item.cje,
					});
				}
			});
		} catch (error) {
			// 主接口失败，记录错误，不再尝试其他Mairui接口获取批量数据
			logger.error(
				error,
				`Failed to fetch batch data from MAIRUI_API_CONFIG.BASE_URL for codes ${batchCodes}. No Mairui backup for multiple stocks.`,
			);
			// 继续处理下一批，不中断整个过程
		}
	}

	if (resultMap.size === 0 && ts_codes.length > 0) {
		// 只有当请求了股票且无任何结果时才抛错
		throw new Error("Failed to fetch data from all available APIs");
	}

	return resultMap;
}

/**
 * 从麦睿接口获取单支股票数据
 * @param ts_code 股票代码
 * @returns 麦睿股票数据
 */
export async function fetchSingleStockData(
	ts_code: string,
): Promise<MairuiStockData> {
	const stockCode = ts_code.split(".")[0];

	// 1. 首先尝试钻石版专用接口 (VIP_PACKAGE_URL)
	try {
		const packageUrl = `${MAIRUI_API_CONFIG.VIP_PACKAGE_URL}/${stockCode}/${MAIRUI_API_CONFIG.LICENSE}`;
		const packageResponse = await axiosWithDNSFailover.get(packageUrl);

		if (
			packageResponse.data &&
			Array.isArray(packageResponse.data) &&
			packageResponse.data.length > 0
		) {
			return packageResponse.data[0] as MairuiStockData;
		}
		throw new Error("Invalid response from package API");
	} catch (e) {
		logger.warn(
			{
				error: e instanceof Error ? e.message : String(e),
				name: e instanceof Error ? e.name : undefined,
			},
			`Package API failed for ${ts_code}, falling back to standard API`,
		);

		// 2. 钻石版接口失败，尝试标准接口 (SINGLE_STOCK_BASE_URL)
		try {
			const standardUrl = `${MAIRUI_API_CONFIG.SINGLE_STOCK_BASE_URL}/${stockCode}/${MAIRUI_API_CONFIG.LICENSE}`;
			const standardResponse = await axiosWithDNSFailover.get(standardUrl);

			if (
				standardResponse.data &&
				Array.isArray(standardResponse.data) &&
				standardResponse.data.length > 0
			) {
				return standardResponse.data[0] as MairuiStockData;
			}
			throw new Error(
				"Invalid response from standard API (SINGLE_STOCK_BASE_URL)",
			);
		} catch (standardError) {
			logger.warn(
				standardError,
				`All Mairui APIs (VIP_PACKAGE_URL and SINGLE_STOCK_BASE_URL) failed to fetch data for ${ts_code}`,
			);
			throw AppError.create(
				"FETCH_STOCK_DATA_FAILED",
				"Failed to fetch stock data from all available Mairui sources",
				{ ts_code },
			);
		}
	}
}

/**
 * 获取当前价格
 * @param ts_code 股票代码
 * @returns 当前价格
 */
export async function getCurrentPrice(ts_code: string): Promise<number> {
	const data = await fetchSingleStockData(ts_code);
	return data.p;
}

/**
 * 获取成交量和成交额
 * @param ts_code 股票代码
 * @returns 成交量和成交额
 */
export async function getTurnoverAndVolume(
	ts_code: string,
): Promise<{ volume: number; turnover: number }> {
	const data = await fetchSingleStockData(ts_code);
	return {
		volume: data.v,
		turnover: data.cje,
	};
}

/**
 * 获取当前行情数据
 * @param ts_codes 股票代码数组
 * @returns 股票代码到市场数据的映射
 */
export async function getCurrentDayData(
	ts_codes: string[],
): Promise<Map<string, MarketData>> {
	const result = await fetchMultipleStocksData(ts_codes, false);
	// 类型转换，确保返回的是 MarketData
	return new Map(
		Array.from(result.entries()).map(([key, value]) => [
			key,
			value as MarketData,
		]),
	);
}

/**
 * 获取当前价格映射
 * @param ts_codes 股票代码数组
 * @returns 股票代码到价格的映射
 */
export async function getCurrentPrices(
	ts_codes: string[],
): Promise<Map<string, number>> {
	// 使用 fetchMultipleStocksData 批量获取价格数据
	const result = await fetchMultipleStocksData(ts_codes, true);

	// 类型转换，确保返回的是 number
	const priceMap = new Map(
		Array.from(result.entries()).map(([key, value]) => [key, value as number]),
	);

	// 如果批量获取失败或数据不完整，尝试使用单股票接口补充
	if (priceMap.size < ts_codes.length) {
		const missingCodes = ts_codes.filter((code) => !priceMap.has(code));
		logger.warn(
			`Missing prices for ${missingCodes.length} stocks, trying single stock API`,
		);

		// 对缺失的股票使用单股票接口获取价格数据
		const promises = missingCodes.map(async (code) => {
			try {
				const stockData = await fetchSingleStockData(code);
				priceMap.set(code, stockData.p);
			} catch (error) {
				logger.error(
					error,
					`Failed to fetch price for ${code} using single stock API`,
				);
				// 单个股票失败不影响整体结果
			}
		});

		await Promise.all(promises);
	}

	// 如果没有获取到任何数据，抛出错误
	if (priceMap.size === 0) {
		throw AppError.create(
			"FETCH_STOCK_DATA_FAILED",
			"Failed to fetch any stock prices",
			{ ts_codes },
		);
	}

	return priceMap;
}

/**
 * 获取今日前收盘价
 * @param ts_code 股票代码
 * @returns 前收盘价
 */
export async function getTodayPreClose(ts_code: string): Promise<number> {
	const data = await fetchSingleStockData(ts_code);
	return data.yc; // 麦睿接口中的昨收价字段
}

/**
 * 获取价格详情：最高价、最低价和昨日收盘价
 * @param ts_code 股票代码
 * @returns 包含最高价、最低价和昨日收盘价的对象
 */
export async function getPriceDetails(
	ts_code: string,
): Promise<{ high: number; low: number; pre_close: number }> {
	const data = await fetchSingleStockData(ts_code);
	return {
		high: data.h,
		low: data.l,
		pre_close: data.yc,
	};
}
