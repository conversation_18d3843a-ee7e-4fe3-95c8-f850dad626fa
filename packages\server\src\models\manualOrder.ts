import prisma from "@/lib/prisma.js";
import type { manual_order_status } from "@prisma/client";
import { Prisma } from "@prisma/client";
import { AppError } from "@/core/appError.js";
import type {
	ManualOrderData,
	ManualOrderStatus,
	StructureType,
} from "@packages/shared";

interface PrismaManualOrderData {
	manual_order_id: number;
	user_id: number;
	ts_code: string;
	entry_price: Prisma.Decimal;
	exercise_price: Prisma.Decimal;
	scale: number;
	term: number;
	structure: string;
	quote: Prisma.Decimal;
	status: manual_order_status;
	quote_provider: string | null;
	created_at: Date;
	entry_date: Date | null;
	expiry_date: Date | null;
	exit_date: Date | null;
	settle_price: Prisma.Decimal | null;
	remarks: string | null;
}

function transformManualOrderData(
	order: PrismaManualOrderData,
): ManualOrderData {
	return {
		...order,
		structure: order.structure as StructureType,
		entry_price: Number(order.entry_price),
		exercise_price: Number(order.exercise_price),
		quote: Number(order.quote),
		settle_price: order.settle_price ? Number(order.settle_price) : undefined,
		status: order.status as ManualOrderStatus,
		quote_provider: order.quote_provider ?? "",
		entry_date: order.entry_date?.toISOString() ?? undefined,
		expiry_date: order.expiry_date?.toISOString() ?? undefined,
		exit_date: order.exit_date?.toISOString() ?? undefined,
		remarks: order.remarks ?? undefined,
	};
}

/**
 * 处理日期字段格式转换：将日期字符串转换为完整的ISO格式
 */
function processDateFields<T extends Record<string, unknown>>(data: T): T {
	const processedData = { ...data };
	const dateFields = ["entry_date", "expiry_date", "exit_date"] as const;

	for (const field of dateFields) {
		const value = processedData[field];
		if (value && typeof value === "string" && !value.includes("T")) {
			(processedData as Record<string, unknown>)[field] = new Date(
				value,
			).toISOString();
		}
	}

	return processedData;
}

export async function create(
	data: Omit<ManualOrderData, "manual_order_id" | "created_at">,
): Promise<ManualOrderData> {
	try {
		// 处理日期格式转换
		const processedData = processDateFields(data);

		const manualOrder = await prisma.manual_orders.create({
			data: processedData,
		});

		return transformManualOrderData(manualOrder);
	} catch (error) {
		throw AppError.create(
			"SERVER_ERROR",
			`Error creating manual order: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

export async function update(
	id: number,
	data: Partial<ManualOrderData>,
): Promise<ManualOrderData> {
	try {
		// 白名单：明确定义可更新的字段
		const updatableFields = [
			"ts_code",
			"entry_price",
			"exercise_price",
			"scale",
			"term",
			"structure",
			"quote",
			"status",
			"quote_provider",
			"entry_date",
			"expiry_date",
			"exit_date",
			"settle_price",
			"remarks",
		] as const;

		// 只提取可更新的字段
		const updateableData: Partial<ManualOrderData> = {};
		for (const field of updatableFields) {
			if (field in data && data[field] !== undefined) {
				(updateableData as Record<string, unknown>)[field] = data[field];
			}
		}

		// 处理日期格式转换
		const processedData = processDateFields(updateableData);

		const manualOrder = await prisma.manual_orders.update({
			where: { manual_order_id: id },
			data: processedData,
		});

		return transformManualOrderData(manualOrder);
	} catch (error) {
		if (
			error instanceof Prisma.PrismaClientKnownRequestError &&
			error.code === "P2025"
		) {
			throw AppError.create(
				"NOT_FOUND",
				`Manual order with ID ${id} not found`,
			);
		}
		throw AppError.create(
			"SERVER_ERROR",
			`Error updating manual order: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

export async function findById(id: number): Promise<ManualOrderData | null> {
	const manualOrder = await prisma.manual_orders.findUnique({
		where: { manual_order_id: id },
	});

	if (!manualOrder) {
		return null;
	}

	return transformManualOrderData(manualOrder);
}

export async function deleteById(id: number): Promise<void> {
	try {
		await prisma.manual_orders.delete({
			where: { manual_order_id: id },
		});
	} catch (error) {
		if (
			error instanceof Prisma.PrismaClientKnownRequestError &&
			error.code === "P2025"
		) {
			throw AppError.create(
				"NOT_FOUND",
				`Manual order with ID ${id} not found`,
			);
		}
		throw AppError.create(
			"SERVER_ERROR",
			`Error deleting manual order: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

export async function getAllForUser(
	user_id: number,
	offset: number,
	limit: number,
	isDescending: boolean,
	filters?: {
		ts_codes?: string[];
		status?: string[];
		startDate?: string;
		endDate?: string;
	},
): Promise<{ items: ManualOrderData[]; total: number }> {
	// 构建查询条件
	const where: Prisma.manual_ordersWhereInput = { user_id };

	// 添加标的代码过滤
	if (filters?.ts_codes?.length) {
		where.ts_code = { in: filters.ts_codes };
	}

	// 添加状态过滤
	if (filters?.status?.length) {
		where.status = { in: filters.status as manual_order_status[] };
	}

	// 添加日期范围过滤
	if (filters?.startDate || filters?.endDate) {
		where.created_at = {};
		if (filters.startDate) where.created_at.gte = new Date(filters.startDate);
		if (filters.endDate) {
			// 为结束日期添加一天，以包含当天
			const endDate = new Date(filters.endDate);
			endDate.setDate(endDate.getDate() + 1);
			where.created_at.lt = endDate;
		}
	}

	const [total, manualOrders] = await Promise.all([
		prisma.manual_orders.count({ where }),
		prisma.manual_orders.findMany({
			where,
			orderBy: { created_at: isDescending ? "desc" : "asc" },
			skip: offset,
			take: limit,
		}),
	]);

	return {
		total,
		items: manualOrders.map(transformManualOrderData),
	};
}

export async function getAllTsCodes(user_id: number): Promise<string[]> {
	const manualOrders = await prisma.manual_orders.findMany({
		where: { user_id },
		distinct: ["ts_code"],
		select: { ts_code: true },
		orderBy: { ts_code: "asc" },
	});

	return manualOrders.map((order) => order.ts_code);
}
