import { PrismaClient } from "@prisma/client";
import { encrypt, decrypt } from "@/utils/encryption.js";
import { getDatabaseUrl } from "@/config/defaultParams.js";

/**
 * 简单的数据库字段加解密工具：
 *
 * 用法：
 * npm run field-crypto <操作> <表名> <字段名>
 *
 */

// 创建Prisma客户端
const prisma = new PrismaClient({
	datasources: {
		db: {
			url: getDatabaseUrl(),
		},
	},
	log: ["error"],
});

/**
 * 处理一个表的一个字段，进行加密或解密
 */
async function processField(
	table: string,
	field: string,
	action: "encrypt" | "decrypt",
): Promise<void> {
	try {
		// 使用原生SQL查询
		console.info(
			`开始${action === "encrypt" ? "加密" : "解密"}表 ${table} 的 ${field} 字段...`,
		);

		// 先获取总记录数
		const countResult = await prisma.$queryRawUnsafe(
			`SELECT COUNT(*) as count FROM ${table} WHERE ${field} IS NOT NULL`,
		);
		const count = Number((countResult as { count: string }[])[0].count);
		console.info(`找到 ${count} 条记录需要处理`);

		if (count === 0) {
			console.info("没有记录需要处理");
			return;
		}

		// 处理数据
		const records = await prisma.$queryRawUnsafe(
			`SELECT user_id, ${field} FROM ${table} WHERE ${field} IS NOT NULL`,
		);
		let processed = 0;
		let skipped = 0;

		for (const record of records as {
			user_id: string;
			[key: string]: string;
		}[]) {
			try {
				const id = record.user_id;
				const value = record[field];

				if (!value) continue;

				let newValue: string;

				// 处理值
				if (action === "encrypt") {
					try {
						// 先尝试解密，看是否已经是明文
						decrypt(value);
						console.debug(`ID=${id} 的 ${field} 已经是加密的，跳过`);
						skipped++;
						continue;
					} catch {
						// 解密失败，说明是明文，可以直接加密
						newValue = encrypt(value);
					}
				} else {
					try {
						// 尝试解密
						newValue = decrypt(value);
					} catch {
						// 解密失败，可能已经是明文
						console.debug(`ID=${id} 的 ${field} 已经是明文，跳过`);
						skipped++;
						continue;
					}
				}

				// 更新记录
				await prisma.$executeRawUnsafe(
					`UPDATE ${table} SET ${field} = $1 WHERE user_id = $2`,
					newValue,
					id,
				);
				processed++;

				if (processed % 100 === 0) {
					console.info(`已处理 ${processed}/${count} 条记录...`);
				}
			} catch (e) {
				console.error(
					`处理记录时出错: ${e instanceof Error ? e.message : String(e)}`,
				);
			}
		}

		console.info(
			`处理完成: ${processed} 条记录已${action === "encrypt" ? "加密" : "解密"}, ${skipped} 条记录已跳过`,
		);
	} catch (error) {
		console.error(`处理表 ${table} 的 ${field} 字段时出错:`, error);
		throw error;
	}
}

/**
 * 主函数
 */
async function main() {
	const args = process.argv.slice(2);

	if (args.length < 3) {
		console.log(`
简单的数据库字段加解密工具

用法:
  pnpm field-crypto <操作> <表名> <字段名>

操作:
  decrypt    解密指定字段
  encrypt    加密指定字段

示例:
  pnpm field-crypto decrypt users email
  pnpm field-crypto encrypt users phone_number
    `);
		process.exit(0);
	}

	const action = args[0];
	const table = args[1];
	const field = args[2];

	if (action !== "encrypt" && action !== "decrypt") {
		console.error(`错误: 操作必须是 'encrypt' 或 'decrypt'`);
		process.exit(1);
	}

	try {
		// 创建备份
		console.info(`创建 ${table} 表备份...`);
		await prisma.$executeRawUnsafe(
			`CREATE TABLE IF NOT EXISTS ${table}_backup_${Date.now()} AS SELECT * FROM ${table}`,
		);
		console.info("备份已创建");

		// 处理字段
		await processField(table, field, action as "encrypt" | "decrypt");

		console.info("操作成功完成");
		process.exit(0);
	} catch (error) {
		console.error("操作失败:", error);
		process.exit(1);
	} finally {
		await prisma.$disconnect();
	}
}

main();
