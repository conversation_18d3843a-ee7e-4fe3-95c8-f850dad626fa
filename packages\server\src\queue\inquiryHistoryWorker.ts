import type { Job } from "bullmq";
import {
	createWorker,
	inquiryHistoryTasksQueue,
	addRepeatedJob,
} from "./index.js";
import logger from "@/utils/logger.js";
import * as InquiryModel from "@/models/inquiry.js";

// 定义询价历史清理相关的作业类型
export const INQUIRY_HISTORY_JOBS = {
	CLEAR_INQUIRIES: "inquiry-clear-history",
};

// 处理询价历史清理相关的作业
async function processInquiryHistoryJob(job: Job) {
	const { name } = job;

	logger.info(`Processing inquiry history job: ${name}`);

	try {
		switch (name) {
			case INQUIRY_HISTORY_JOBS.CLEAR_INQUIRIES:
				await clearInquiries();
				break;
			default:
				logger.warn(`Unknown inquiry history job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process inquiry history job: ${name}`);
		throw error;
	}
}

// 清理询价历史
async function clearInquiries(): Promise<void> {
	try {
		const startTime = Date.now();
		await InquiryModel.deleteAll();
		const duration = Date.now() - startTime;

		logger.info(`Daily inquiry cleanup completed in ${duration}ms`);
	} catch (error) {
		logger.error(error, "Error during inquiry cleanup");
	}
}

// 创建Worker实例
export const inquiryHistoryWorker = createWorker(
	inquiryHistoryTasksQueue,
	processInquiryHistoryJob,
);

// 初始化定时作业调度
export async function initializeInquiryHistoryJobs() {
	try {
		// 每天凌晨00:00执行清理询价历史
		await addRepeatedJob(
			inquiryHistoryTasksQueue,
			INQUIRY_HISTORY_JOBS.CLEAR_INQUIRIES,
			{},
			"0 0 * * *",
		);

		logger.info("Inquiry history jobs scheduled successfully");
	} catch (error) {
		logger.error(error, "Failed to schedule inquiry history jobs");
		throw error;
	}
}

// 用于应用程序启动时初始化所有询价历史任务
export async function initializeOnStartup() {
	await initializeInquiryHistoryJobs();
	logger.info("Inquiry history worker initialized successfully");
}
