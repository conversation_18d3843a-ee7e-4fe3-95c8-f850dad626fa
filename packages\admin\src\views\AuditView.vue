<template>
	<div class="audit-view view">
		<template v-if="hasBothPermissions">
			<div class="custom-tabs">
				<div class="tab-header">
					<div class="tab-item" :class="{ active: activeTab === 'qualify' }" @click="navigateTo('/audits/qualify')">
						资质审核
						<el-badge v-if="qualifyNewCount > 0" :value="qualifyNewCount" class="item" type="danger" />
					</div>
					<div class="tab-item" :class="{ active: activeTab === 'finance' }" @click="navigateTo('/audits/finance')">
						财务审核
						<el-badge v-if="financeNewCount > 0" :value="financeNewCount" class="item" type="danger" />
					</div>
				</div>
				<div class="tab-content">
					<QualifyAuditPanel v-if="activeTab === 'qualify'" ref="qualifyPanelRef" />
					<FinanceAuditPanel v-else-if="activeTab === 'finance'" ref="financePanelRef" />
				</div>
			</div>
		</template>
		<template v-else>
			<div class="header-container">
				<h2>{{ getSinglePermissionTitle }}</h2>
			</div>
			<QualifyAuditPanel v-if="hasQualifyPermission" ref="qualifyPanelRef" />
			<FinanceAuditPanel v-if="hasFinancePermission" ref="financePanelRef" />
		</template>
	</div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import QualifyAuditPanel from "./panels/QualifyAuditPanel.vue";
import FinanceAuditPanel from "./panels/FinanceAuditPanel.vue";
import { qualifyApi } from "@/api/qualify";
import { financeApi } from "@/api/finance";
import { AuditStatus, AdminPermission } from "@packages/shared";
import { useAuthStore } from "@/stores/auth";
import { setupBrowserNotification, showAuditNotification } from "@/utils/notification";

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const activeTab = ref("qualify");

// 权限检查
const hasQualifyPermission = computed(() =>
	authStore.permissions.includes(AdminPermission.QUALIFY),
);
const hasFinancePermission = computed(() =>
	authStore.permissions.includes(AdminPermission.FINANCE),
);
const hasBothPermissions = computed(
	() => hasQualifyPermission.value && hasFinancePermission.value,
);

// 获取单一权限时的标题
const getSinglePermissionTitle = computed(() => {
	if (hasQualifyPermission.value) return "资质审核";
	if (hasFinancePermission.value) return "财务审核";
	return "";
});

// 导航到指定路由
const navigateTo = (path: string) => {
	console.log("Navigating to:", path);
	router.push(path);
};

// 根据路由路径设置当前标签页
const updateTabFromRoute = () => {
	console.log("Current route path:", route.path);
	// 优先从meta中获取子视图名称
	if (route.meta.subView) {
		activeTab.value = route.meta.subView as string;
		console.log("Setting tab to", activeTab.value, "based on route meta");
	} else if (route.path.includes("/audits/finance")) {
		activeTab.value = "finance";
		console.log("Setting tab to finance based on route path");
	} else {
		// 默认为资质审核
		activeTab.value = "qualify";
		console.log("Setting tab to qualify based on route path");
	}
};

// 监听路由变化
watch(() => route.path, updateTabFromRoute, { immediate: true });
// 同时监听路由的meta数据变化
watch(() => route.meta.subView, updateTabFromRoute, { immediate: true });

// 切换标签页时重置对应的计数
watch(activeTab, (newTab) => {
	if (newTab === "qualify") {
		qualifyNewCount.value = 0;
	} else if (newTab === "finance") {
		financeNewCount.value = 0;
	}
});

// 新内容计数 - 用来显示标签上的小红点
const qualifyNewCount = ref(0);
const financeNewCount = ref(0);

// 上次统计的计数 - 用来计算新增
const lastCounts = ref({
	qualify: 0,
	finance: 0,
});

// 检查新内容的定时器
const checkTimer = ref<ReturnType<typeof setInterval> | null>(null);

// 组件挂载时初始化
onMounted(async () => {
	// 首先检查权限并设置初始标签页
	if (hasBothPermissions.value) {
		// 如果两种权限都有，从路由确定标签页
		updateTabFromRoute();
	} else {
		// 如果只有单一权限，直接设置对应标签页
		if (hasFinancePermission.value) {
			activeTab.value = "finance";
			// 确保URL与当前标签页一致
			if (!route.path.includes("/finance")) {
				navigateTo("/audits/finance");
			}
		} else if (hasQualifyPermission.value) {
			activeTab.value = "qualify";
			// 确保URL与当前标签页一致
			if (!route.path.includes("/qualify")) {
				navigateTo("/audits/qualify");
			}
		}
	}

	// 初始化计数和设置定时检查 (30秒检查一次)
	checkNewAudits();
	checkTimer.value = setInterval(checkNewAudits, 30000);

	// 检查并请求通知权限
	await setupBrowserNotification();
});

// 检查新内容 - 不论当前标签页是什么，都会同时检查两种类型的审核
const checkNewAudits = async () => {
	try {
		// 只在有对应权限时检查
		if (hasQualifyPermission.value) {
			await checkQualifyAudits();
		}

		if (hasFinancePermission.value) {
			await checkFinanceAudits();
		}
	} catch (error) {
		console.error("检查新内容失败:", error);
	}
};

// 检查资质审核
const checkQualifyAudits = async () => {
	try {
		const qualifyResponse = await qualifyApi.getQualificationsAudits(
			{
				page: 1,
				pageSize: 1,
				filters: {
					status: AuditStatus.PENDING,
				},
			},
		);

		const newTotal = qualifyResponse?.total || 0;

		// 仅当有初始值且有新增时才计数
		if (
			lastCounts.value.qualify !== undefined &&
			lastCounts.value.qualify !== null &&
			newTotal > lastCounts.value.qualify
		) {
			// 计算新增数
			const newCount = newTotal - lastCounts.value.qualify;
			qualifyNewCount.value += newCount;

			// 当前标签页不是资质审核时才显示通知
			if (activeTab.value !== "qualify") {
				showAuditNotification("qualify", newCount);
			}
		}

		lastCounts.value.qualify = newTotal;

		// 如果当前是资质审核标签，刷新QualifyPanel
		if (activeTab.value === "qualify" && qualifyPanelRef.value) {
			qualifyPanelRef.value.refreshData();
		}
	} catch (error) {
		console.error("检查资质审核失败:", error);
	}
};

// 检查财务审核
const checkFinanceAudits = async () => {
	try {
		const financeResponse = await financeApi.getFinanceAudits(
			AuditStatus.PENDING,
			{
				page: 1,
				pageSize: 1,
			},
		);

		const newTotal = financeResponse?.total || 0;

		// 仅当有初始值且有新增时才计数
		if (
			lastCounts.value.finance !== undefined &&
			lastCounts.value.finance !== null &&
			newTotal > lastCounts.value.finance
		) {
			// 计算新增数
			const newCount = newTotal - lastCounts.value.finance;
			financeNewCount.value += newCount;

			// 当前标签页不是财务审核时才显示通知
			if (activeTab.value !== "finance") {
				showAuditNotification("finance", newCount);
			}
		}

		lastCounts.value.finance = newTotal;

		// 如果当前是财务审核标签，刷新FinancePanel
		if (activeTab.value === "finance" && financePanelRef.value) {
			financePanelRef.value.refreshData();
		}
	} catch (error) {
		console.error("检查财务审核失败:", error);
	}
};

// 组件引用，用于调用子组件的刷新方法
const qualifyPanelRef = ref<InstanceType<typeof QualifyAuditPanel> | null>(null);
const financePanelRef = ref<InstanceType<typeof FinanceAuditPanel> | null>(
	null,
);

// 清理
onUnmounted(() => {
	if (checkTimer.value) {
		clearInterval(checkTimer.value);
	}
});
</script>

<style scoped>
.header-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

h2 {
	margin: 0;
	color: var(--el-text-color-primary);
}

.tab-header {
	display: flex;
	margin-bottom: 20px;
	gap: 20px;
}

.tab-item {
	font-size: 14px;
	color: var(--el-text-color-regular);
	height: 36px;
	line-height: 36px;
	cursor: pointer;
	position: relative;
	transition: all 0.3s;
}

.tab-item:hover {
	color: var(--el-color-primary);
}

.tab-item.active {
	font-size: 1.5em;
	font-weight: bold;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 3px;
	background-color: var(--el-color-primary);
}

/* 适配移动端 */
@media (max-width: 767px) {
	.tab-item.active {
		font-size: 18px;
	}

	.tab-header {
		margin-bottom: 10px;
	}
}
</style>