import { getErrorMessage, ErrorType, type ErrorCode } from "@packages/shared";

const CUSTOM_MESSAGE_ERRORS = [
	ErrorType.PAYMENT_PASSWORD_LOCKED.code,
	ErrorType.INVALID_PAYMENT_PASSWORD.code,
] as const;

export function getDisplayErrorMessage(error: unknown): string {
	console.log(error);
	if (error && typeof error === "object" && "response" in error) {
		const apiError = error as {
			response?: { data?: { code?: ErrorCode; message?: string } };
		};
		const errorData = apiError.response?.data;

		// 对于特定错误码，直接显示服务端返回的消息
		if (
			errorData?.code &&
			(CUSTOM_MESSAGE_ERRORS as readonly number[]).includes(errorData.code)
		) {
			return errorData.message || "";
		}
	}

	// 其他情况使用通用的错误消息处理
	return getErrorMessage(error);
}
