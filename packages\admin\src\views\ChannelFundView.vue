<template>
  <div class="channel-fund view">
    <div class="page-header">
      <h2>通道资金审核</h2>
      <div class="header-controls">
        <el-button type="primary" :icon="Refresh" @click="refreshCurrentTab" :loading="loading">刷新</el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane name="0" label="通道列表">
        <el-table :data="channels" style="width: 100%">
          <el-table-column prop="channel_id" label="通道ID" min-width="80" />
          <el-table-column prop="name" label="通道名称" />
          <el-table-column label="CNY余额" min-width="100">
            <template #default="scope">
              {{ formatCurrency(scope.row.balance_cny, 'CNY') }}
            </template>
          </el-table-column>
          <el-table-column label="HKD余额" min-width="100">
            <template #default="scope">
              {{ formatCurrency(scope.row.balance_hkd, 'HKD') }}
            </template>
          </el-table-column>
          <el-table-column label="USD余额" min-width="100">
            <template #default="scope">
              {{ formatCurrency(scope.row.balance_usd, 'USD') }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="created_at" min-width="100">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="160">
            <template #default="scope">
              <el-button size="small" type="info" plain @click="viewChannelTransactions(scope.row.channel_id)">
                资金记录
              </el-button>
              <el-button size="small" type="info" plain @click="previewBankAccount(scope.row.channel_id)">
                银行账户
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane name="1" label="待审核">
        <el-table :data="pendingTransactions.filter(t => t.type === 'deposit' || t.type === 'withdraw')"
          style="width: 100%">
          <el-table-column prop="transaction_id" label="ID" min-width="60" />
          <el-table-column prop="channel_id" label="通道ID" min-width="80" />
          <el-table-column prop="type" label="类型" min-width="60">
            <template #default="scope">
              {{ formatTransactionType(scope.row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" min-width="80">
            <template #default="scope">
              {{ formatCurrency(scope.row.amount, scope.row.currency) }}
            </template>
          </el-table-column>
          <el-table-column prop="currency" label="币种" min-width="60" />
          <el-table-column prop="created_at" label="申请时间" min-width="100">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="120">
            <template #default="scope">
              <el-button type="success" size="small" @click="approveTransaction(scope.row)">
                批准
              </el-button>
              <el-button type="danger" size="small" @click="rejectTransaction(scope.row)">
                拒绝
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="银行账户" min-width="104">
            <template #default="scope">
              <el-button type="info" size="small" plain
                @click="previewBankAccount(scope.row.channel_id, scope.row.type)"
                v-if="scope.row.type === 'deposit' || scope.row.type === 'withdraw'">
                查看账户
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="remarks" label="备注" min-width="120">
            <template #default="scope">
              <span v-if="scope.row.remarks">{{ scope.row.remarks }}</span>
              <span v-else class="no-data">—</span>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="pendingTransactions.filter(t => t.type === 'deposit' || t.type === 'withdraw').length === 0"
          class="empty-placeholder">
          <el-empty description="没有待审核的出入金申请"></el-empty>
        </div>
      </el-tab-pane>

      <el-tab-pane name="2" label="资金记录">
        <el-form :inline="true" class="filter-form">
          <el-form-item label="通道">
            <el-select v-model="filters.channelId" placeholder="选择通道" class="filter-select" @change="loadTransactions">
              <el-option label="全部" value="" />
              <el-option v-for="channel in channels" :key="channel.channel_id" :label="channel.name"
                :value="channel.channel_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filters.status" placeholder="选择状态" class="filter-select" @change="loadTransactions">
              <el-option label="全部" value="" />
              <el-option label="待确认" value="pending" />
              <el-option label="已确认" value="confirmed" />
              <el-option label="已拒绝" value="rejected" />
              <el-option label="自动处理" value="auto_confirmed" />
            </el-select>
          </el-form-item>
        </el-form>

        <el-table :data="transactions" style="width: 100%" v-loading="transactionsLoading">
          <el-table-column prop="transaction_id" label="ID" min-width="60" />
          <el-table-column prop="channel_id" label="通道ID" min-width="80" />
          <el-table-column prop="type" label="操作类型" min-width="80">
            <template #default="scope">
              {{ formatTransactionType(scope.row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" min-width="100">
            <template #default="scope">
              <span :class="{ 'amount-positive': scope.row.amount > 0, 'amount-negative': scope.row.amount < 0 }">
                {{ scope.row.amount > 0 ? '+' : '' }}{{ formatCurrency(scope.row.amount, scope.row.currency) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="currency" label="币种" min-width="60" />
          <el-table-column prop="status" label="状态" min-width="80">
            <template #default="scope">
              <el-tooltip v-if="scope.row.status === 'auto_confirmed'" effect="dark" content="系统自动处理，无需人工审核"
                placement="top">
                <el-tag type="info">{{ formatStatus(scope.row.status) }}</el-tag>
              </el-tooltip>
              <el-tag v-else :type="getStatusTagType(scope.row.status)">
                {{ formatStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="申请时间" min-width="100">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="remarks" label="备注" min-width="160">
            <template #default="scope">
              <span v-if="scope.row.remarks">{{ scope.row.remarks }}</span>
              <span v-else class="no-data">—</span>
            </template>
          </el-table-column>
          <el-table-column label="审核信息" min-width="150"
            v-if="filters.status === 'confirmed' || filters.status === 'rejected'" show-overflow-tooltip>
            <template #default="scope">
              <div v-if="scope.row.review_comment || scope.row.reviewed_at" class="review-info">
                <div v-if="scope.row.reviewed_at" class="review-time">
                  <el-icon>
                    <Clock />
                  </el-icon> {{ formatDate(scope.row.reviewed_at) }}
                </div>
                <div class="review-content">
                  <span v-if="scope.row.admin_id" class="review-admin">
                    <el-icon>
                      <User />
                    </el-icon> {{ scope.row.admin_id }}:
                  </span>
                  <span v-if="scope.row.review_comment" class="review-comment">{{ scope.row.review_comment }}</span>
                  <span v-else class="no-data">无审核备注</span>
                </div>
              </div>
              <span v-else class="no-data">—</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination">
          <el-pagination layout="prev, pager, next" :total="totalTransactions" @current-change="handlePageChange" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 审核对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogType === 'approve' ? '批准出入金申请' : '拒绝出入金申请'" width="50%" top="10vh">
      <div class="transaction-info">
        <div class="transaction-info-item">
          <div class="info-label">ID:</div>
          <div class="info-value">{{ selectedTransaction?.transaction_id }}</div>
        </div>
        <div class="transaction-info-item">
          <div class="info-label">通道:</div>
          <div class="info-value">{{ selectedTransaction?.channel_id }}</div>
        </div>
        <div class="transaction-info-item">
          <div class="info-label">金额:</div>
          <div class="info-value"
            :class="{ 'amount-positive': selectedTransaction?.amount > 0, 'amount-negative': selectedTransaction?.amount < 0 }">
            {{ formatCurrency(selectedTransaction?.amount, selectedTransaction?.currency) }}
          </div>
        </div>
        <div class="transaction-info-item">
          <div class="info-label">操作类型:</div>
          <div class="info-value">{{ formatTransactionType(selectedTransaction?.type) }}</div>
        </div>
      </div>

      <!-- 银行账户信息区域，仅在入金/出金操作时显示 -->
      <div v-if="showBankAccountInfo" class="bank-account-info" v-loading="bankAccountLoading">
        <h4 class="bank-account-title">{{ bankAccountTitle }}</h4>
        <div class="bank-account-source">
          <el-tag size="small" type="info" effect="plain">{{ bankAccountSource }}</el-tag>
        </div>
        <el-descriptions :column="1" border size="small">
          <el-descriptions-item label="账户名">{{ bankAccount.name || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="银行名称">{{ bankAccount.bankName || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="银行代码">{{ bankAccount.bankCode || '未设置' }}</el-descriptions-item>
          <el-descriptions-item v-if="bankAccount.branchCode" label="支行代码">{{ bankAccount.branchCode
          }}</el-descriptions-item>
          <el-descriptions-item label="账号">{{ bankAccount.accountNumber || '未设置' }}</el-descriptions-item>
          <el-descriptions-item v-if="bankAccount.accountNumberHKD" label="HKD账号">{{ bankAccount.accountNumberHKD
          }}</el-descriptions-item>
          <el-descriptions-item v-if="bankAccount.accountNumberUSD" label="USD账号">{{ bankAccount.accountNumberUSD
          }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedTransaction?.type === 'withdraw'" class="withdrawal-warning">
          <el-alert type="warning" :closable="false" show-icon>
            <template #title>
              出金操作需要特别核实以下信息:
            </template>
            <ol>
              <li>确认收款账户信息与系统记录是否一致</li>
              <li>确认出金金额是否与申请一致</li>
              <li>确认通道账户余额是否充足</li>
            </ol>
          </el-alert>
        </div>
      </div>

      <el-form :model="auditForm">
        <el-form-item label="审核备注">
          <el-input v-model="auditForm.review_comment" type="textarea" placeholder="输入审核备注" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button :type="dialogType === 'approve' ? 'success' : 'danger'" @click="confirmAudit">
            {{ dialogType === 'approve' ? '批准' : '拒绝' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 银行账户信息预览对话框 -->
    <el-dialog v-model="bankAccountPreviewVisible" title="银行账户信息" width="500px">
      <div v-loading="bankAccountLoading">
        <div v-if="previewTransactionType" class="bank-account-context">
          <el-alert :title="previewContextTitle" type="info" :closable="false"></el-alert>
        </div>

        <div class="bank-account-preview">
          <el-descriptions :column="1" border class="bank-account-details">
            <el-descriptions-item label="账户名">{{ bankAccount.name || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="银行名称">{{ bankAccount.bankName || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="银行代码">{{ bankAccount.bankCode || '未设置' }}</el-descriptions-item>
            <el-descriptions-item v-if="bankAccount.branchCode" label="支行代码">{{ bankAccount.branchCode
            }}</el-descriptions-item>
            <el-descriptions-item label="CNY账号">{{ bankAccount.accountNumber || '未设置' }}</el-descriptions-item>
            <el-descriptions-item v-if="bankAccount.accountNumberHKD" label="HKD账号">{{ bankAccount.accountNumberHKD
            }}</el-descriptions-item>
            <el-descriptions-item v-if="bankAccount.accountNumberUSD" label="USD账号">{{ bankAccount.accountNumberUSD
            }}</el-descriptions-item>
          </el-descriptions>

          <div v-if="previewTransactionType === 'withdraw'" class="withdrawal-warning">
            <el-alert type="warning" :closable="false" show-icon>
              <template #title>
                出金操作需要特别核实以下信息:
              </template>
              <ol>
                <li>确认收款账户信息与系统记录是否一致</li>
                <li>确认出金金额是否与申请一致</li>
                <li>确认通道账户余额是否充足</li>
              </ol>
            </el-alert>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, watch, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { channelAdminApi } from "@/api/channelAdmin";
import { bankAccountApi } from "@/api/bankAccount";
import { Clock, User, Refresh } from "@element-plus/icons-vue";
import { getErrorMessage, ChannelTransactionType } from "@packages/shared";

// 数据
const channels = ref([]);
const pendingTransactions = ref([]);
const transactions = ref([]);
const totalTransactions = ref(0);
const currentPage = ref(1);
const activeTab = ref("0");
const loading = ref(false);
const transactionsLoading = ref(false);
const filters = reactive({
  channelId: "",
  status: "confirmed",
});

// 审核对话框
const dialogVisible = ref(false);
const dialogType = ref("approve"); // 'approve' or 'reject'
const selectedTransaction = ref(null);
const auditForm = reactive({
  review_comment: "",
});

// 银行账户数据
const bankAccount = ref({});
const bankAccountLoading = ref(false);

// 计算属性 - 是否显示银行账户信息
const showBankAccountInfo = computed(() => {
  if (!selectedTransaction.value) return false;

  // 入金和出金操作需要显示银行账户信息
  return ["deposit", "withdraw"].includes(selectedTransaction.value.type);
});

// 计算属性 - 银行账户标题
const bankAccountTitle = computed(() => {
  if (!selectedTransaction.value) return "";

  // 根据交易类型判断显示哪个账户信息
  if (selectedTransaction.value.type === "deposit") {
    return "入金账户 (请确认资金已到账)";
  }

  if (selectedTransaction.value.type === "withdraw") {
    return "出金账户 (请确认收款账户信息)";
  }
  return "账户信息";
});

// 银行账户来源描述
const bankAccountSource = computed(() => {
  if (!selectedTransaction.value) return "";

  // 根据交易类型确定显示不同的来源信息
  if (selectedTransaction.value.type === "deposit") {
    return "平台/通道账户信息 (入金目标账户)";
  }

  if (selectedTransaction.value.type === "withdraw") {
    return "平台/通道账户信息 (出金来源账户)";
  }

  return "银行账户信息";
});

// 银行账户预览相关
const bankAccountPreviewVisible = ref(false);
const previewTransactionType = ref("");
const previewContextTitle = computed(() => {
  if (previewTransactionType.value === "deposit") {
    return "入金操作：请确认资金已到达此银行账户";
  }

  if (previewTransactionType.value === "withdraw") {
    return "出金操作：款项将从此账户转出";
  }

  return "通道银行账户信息";
});

// 方法
const loadChannels = async () => {
  try {
    const response = await channelAdminApi.channel.getAll();
    channels.value = response || [];
  } catch (error) {
    ElMessage.error(`加载通道信息失败: ${error.message}`);
  }
};

const loadPendingTransactions = async () => {
  try {
    const response = await channelAdminApi.transaction.getPending();
    pendingTransactions.value = response.items || [];
  } catch (error) {
    ElMessage.error(`加载待审核资金申请失败: ${error.message}`);
  }
};

const loadTransactions = async () => {
  transactionsLoading.value = true;
  try {
    const response = await channelAdminApi.transaction.getAll({
      channelId: filters.channelId,
      status: filters.status,
      page: currentPage.value,
      size: 10,
    });
    transactions.value = response.items || [];
    totalTransactions.value = response.total || 0;
  } catch (error) {
    ElMessage.error(`加载资金操作记录失败: ${error.message}`);
    transactions.value = [];
    totalTransactions.value = 0;
  } finally {
    transactionsLoading.value = false;
  }
};

const viewChannelTransactions = (channelId) => {
  filters.channelId = channelId;
  // 切换到资金操作记录标签
  activeTab.value = "2";
  loadTransactions();
};

const handlePageChange = (page) => {
  currentPage.value = page;
  loadTransactions();
};

const approveTransaction = (transaction) => {
  selectedTransaction.value = transaction;
  dialogType.value = "approve";
  dialogVisible.value = true;

  // 如果是出入金操作，加载银行账户信息
  if (["deposit", "withdraw"].includes(transaction.type)) {
    loadBankAccount(transaction.channel_id);
  }
};

const rejectTransaction = (transaction) => {
  selectedTransaction.value = transaction;
  dialogType.value = "reject";
  dialogVisible.value = true;

  // 如果是出入金操作，加载银行账户信息
  if (["deposit", "withdraw"].includes(transaction.type)) {
    loadBankAccount(transaction.channel_id);
  }
};

// 加载银行账户信息
const loadBankAccount = async (channelId) => {
  if (!channelId) return;

  bankAccountLoading.value = true;
  try {
    // 获取银行账户信息，使用通道特定API
    const response = await bankAccountApi.getChannelBankAccount(channelId);
    bankAccount.value = response || {};
  } catch (error) {
    ElMessage.error(`加载银行账户信息失败: ${getErrorMessage(error)}`);
    bankAccount.value = {};
  } finally {
    bankAccountLoading.value = false;
  }
};

const confirmAudit = async () => {
  if (!selectedTransaction.value) return;

  try {
    if (dialogType.value === "approve") {
      await channelAdminApi.transaction.approve(
        selectedTransaction.value.transaction_id,
        auditForm.review_comment,
      );
      ElMessage.success("出入金申请已批准");
    } else {
      await channelAdminApi.transaction.reject(
        selectedTransaction.value.transaction_id,
        auditForm.review_comment,
      );
      ElMessage.success("出入金申请已拒绝");
    }

    // 刷新数据
    loadPendingTransactions();
    loadTransactions();

    // 关闭对话框
    dialogVisible.value = false;
    auditForm.review_comment = "";
  } catch (error) {
    ElMessage.error(`操作失败: ${getErrorMessage(error)}`);
  }
};

// 格式化函数
const formatCurrency = (amount, currency) => {
  if (amount === undefined) return "";
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: currency || "CNY",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatDate = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const formatTransactionType = (type) => {
  const typeMap = {
    [ChannelTransactionType.DEPOSIT]: "入金",
    [ChannelTransactionType.WITHDRAW]: "出金",
    [ChannelTransactionType.USER_ORDER]: "用户下单",
    [ChannelTransactionType.USER_EXECUTE]: "用户结算",
    [ChannelTransactionType.EXCHANGE]: "货币兑换",
  };
  return typeMap[type] || type;
};

const formatStatus = (status) => {
  const statusMap = {
    pending: "待确认",
    confirmed: "已确认",
    rejected: "已拒绝",
    auto_confirmed: "自动处理",
  };
  return statusMap[status] || status;
};

const getStatusTagType = (status) => {
  const typeMap = {
    pending: "warning",
    confirmed: "success",
    rejected: "danger",
  };
  return typeMap[status] || "";
};

// 根据当前标签页刷新数据
const refreshCurrentTab = () => {
  loading.value = true;
  try {
    if (activeTab.value === "0") {
      loadChannels();
    } else if (activeTab.value === "1") {
      loadPendingTransactions();
    } else if (activeTab.value === "2") {
      transactionsLoading.value = true;
      loadTransactions();
    }
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 500); // 给用户一个加载的视觉反馈
  }
};

// 预览银行账户信息
const previewBankAccount = async (channelId, transactionType) => {
  previewTransactionType.value = transactionType || "";
  bankAccountPreviewVisible.value = true;
  await loadBankAccount(channelId);
};

// 初始化
onMounted(() => {
  loadChannels();
  loadPendingTransactions();
  loadTransactions();
});
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h2 {
  margin: 0;
}

.header-controls {
  display: flex;
  gap: 10px;
}

.filter-form {
  margin-bottom: 20px;
}

.filter-select {
  min-width: 120px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.transaction-info {
  margin-bottom: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--el-border-color-lighter);
}

.transaction-info-item {
  display: flex;
  margin-bottom: 10px;
  border-bottom: 1px dashed var(--el-border-color-lighter);
  padding-bottom: 8px;
}

.transaction-info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 80px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
}

.info-value {
  flex: 1;
  font-weight: 500;
}

.amount-positive {
  color: var(--el-color-success);
  font-weight: bold;
}

.amount-negative {
  color: var(--el-color-danger);
  font-weight: bold;
}

.review-info {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.review-time {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.review-content {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.review-admin {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-secondary);
  font-size: 13px;
  white-space: nowrap;
}

.review-comment {
  color: var(--el-text-color-primary);
}

.no-data {
  color: var(--el-text-color-disabled);
  font-style: italic;
}

/* 添加操作列按钮样式 */
.el-table .el-button {
  margin: 2px 4px;
  min-width: 60px;
}

/* 银行账户信息样式 */
.bank-account-info {
  margin-top: 20px;
  margin-bottom: 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--el-border-color);
}

.bank-account-title {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: var(--el-text-color-primary);
  border-left: 3px solid var(--el-color-primary);
  padding-left: 10px;
}

.bank-account-source {
  margin-bottom: 16px;
}

.withdrawal-warning {
  margin-top: 16px;
}

.withdrawal-warning ol {
  margin-top: 8px;
  margin-bottom: 0;
  padding-left: 20px;
}

.withdrawal-warning li {
  margin-bottom: 4px;
}

.withdrawal-warning li:last-child {
  margin-bottom: 0;
}

.bank-account-preview {
  margin-top: 16px;
}

.bank-account-context {
  margin-bottom: 16px;
}

.bank-account-details {
  margin-bottom: 20px;
}
</style>