// 执行命令：pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\tests\quoteAdjustTest.ts

import assert from "node:assert";

const DISCOUNT_MULTIPLIER = 0.85;

// 模拟内部的adjustQuoteBasedOnExternal函数实现
function adjustQuoteBasedOnExternal(
	calculatedQuote: number,
	allExternalQuotes: Record<string, number | null>,
): number {
	// 如果没有外部报价或内部报价无效，直接返回内部报价
	if (!calculatedQuote || Object.keys(allExternalQuotes).length === 0) {
		return calculatedQuote * DISCOUNT_MULTIPLIER;
	}

	// 先对内部报价应用折扣
	const discountedQuote = calculatedQuote * DISCOUNT_MULTIPLIER;
	console.log(
		`内部报价: ${calculatedQuote}，应用折扣(${DISCOUNT_MULTIPLIER})后: ${discountedQuote}`,
	);

	// 获取所有有效的外部报价（包括未启用的提供商）用于价格下限计算
	const validAllExternalQuotes = Object.values(allExternalQuotes).filter(
		(quote): quote is number =>
			quote !== null && !Number.isNaN(quote) && quote > 0,
	);

	// 如果没有有效的外部报价，直接返回折扣后的内部报价
	if (validAllExternalQuotes.length === 0) {
		return Number(discountedQuote.toFixed(2));
	}

	// 获取外部最优报价（最低）
	const bestExternalQuote = Math.min(...validAllExternalQuotes);

	// 计算最优外部报价的95%
	const externalQuoteThreshold = bestExternalQuote * 0.95;

	// 如果折扣后的内部报价低于最优外部报价的95%，则调整为最优外部报价的95%
	if (discountedQuote < externalQuoteThreshold) {
		console.log(
			`折扣后报价 ${discountedQuote} 低于外部最优报价 ${bestExternalQuote} 的95%(${externalQuoteThreshold})，调整为后者`,
		);
		return Number(externalQuoteThreshold.toFixed(2));
	}

	// 否则返回折扣后的内部报价
	return Number(discountedQuote.toFixed(2));
}

// Test suite for adjustQuoteBasedOnExternal function
function runTests() {
	console.log("=== Testing adjustQuoteBasedOnExternal function ===");

	// Test case 1: No external quotes provided
	console.log("\nTest case 1: No external quotes");
	let result = adjustQuoteBasedOnExternal(5.0, {});
	assert.strictEqual(
		result,
		4.25, // 5.0 * 0.85 = 4.25
		"Should return discounted quote when no external quotes provided",
	);
	console.log(
		"✓ Passed: Returns discounted quote when no external quotes provided",
	);

	// Test case 2: External quotes provided but all null
	console.log("\nTest case 2: All null external quotes");
	result = adjustQuoteBasedOnExternal(5.0, {
		PROVIDER1: null,
		PROVIDER2: null,
	});
	assert.strictEqual(
		result,
		4.25, // 5.0 * 0.85 = 4.25
		"Should return discounted quote when all external quotes are null",
	);
	console.log(
		"✓ Passed: Returns discounted quote when all external quotes are null",
	);

	// Test case 3: 内部报价折扣后大于外部报价的95%（不需要调整）
	console.log("\nTest case 3: 内部报价折扣后大于外部报价的95%");
	// 内部报价: 6.0，折扣后: 6.0 * 0.85 = 5.1
	// 外部最优报价: 5.0，其95%: 5.0 * 0.95 = 4.75
	// 5.1 > 4.75，不需要调整
	result = adjustQuoteBasedOnExternal(6.0, { PROVIDER1: 5.0, PROVIDER2: 5.5 });
	assert.strictEqual(
		result,
		5.1, // 6.0 * 0.85 = 5.1
		"Should return discounted quote when it's above external quote threshold",
	);
	console.log(
		"✓ Passed: Returns discounted quote when it's above external quote threshold",
	);

	// Test case 4: 内部报价折扣后小于外部报价的95%（需要调整）
	console.log("\nTest case 4: 内部报价折扣后小于外部报价的95%");
	// 内部报价: 4.0，折扣后: 4.0 * 0.85 = 3.4
	// 外部最优报价: 6.0，其95%: 6.0 * 0.95 = 5.7
	// 3.4 < 5.7，需要调整为5.7
	result = adjustQuoteBasedOnExternal(4.0, { PROVIDER1: 6.5, PROVIDER2: 6.0 });
	assert.strictEqual(
		result,
		5.7,
		"Should adjust quote to external threshold when discounted quote is below it",
	);
	console.log("✓ Passed: Adjusts quote to external threshold when necessary");

	// Test case 5: 边界情况 - 内部报价折扣后刚好等于外部报价95%
	console.log("\nTest case 5: 边界情况 - 折扣后刚好等于外部报价95%");
	// 内部报价: 6.0，折扣后: 6.0 * 0.85 = 5.1
	// 外部最优报价: 5.37，其95%: 5.37 * 0.95 ≈ 5.1
	result = adjustQuoteBasedOnExternal(6.0, { PROVIDER1: 5.37, PROVIDER2: 6.0 });
	assert.strictEqual(
		result,
		5.1,
		"Should return discounted quote when it equals external threshold",
	);
	console.log(
		"✓ Passed: Returns discounted quote when it equals external threshold",
	);

	console.log("\n=== All tests passed successfully ===");
}

// 执行测试
runTests();
