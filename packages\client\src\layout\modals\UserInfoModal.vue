<template>
  <el-popover :visible="show" @update:visible="updateShow" :trigger="isMobile ? 'click' : 'hover'" :show-after="100"
    :hide-after="200" placement="bottom-end" width="220" @hide="close">
    <template #reference>
      <div class="user-avatar">
        <el-avatar :size="28">
          <el-icon :size="20">
            <UserFilled />
          </el-icon>
        </el-avatar>
        <el-badge v-if="isQualified" value="✓" class="qualified-badge" type="primary" />
      </div>
    </template>

    <div class="user-info">
      <div class="info-header">
        <span class="username">{{ authStore.username }}</span>
        <el-tag v-if="isQualified" type="success" size="small">已认证</el-tag>
        <el-tag v-else type="info" size="small">未认证</el-tag>
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { UserFilled } from "@element-plus/icons-vue";
import { useAuthStore } from "@/stores/auth";

defineProps<{
	show: boolean;
	isMobile: boolean;
}>();

const emit = defineEmits<{
	"update:show": [value: boolean];
}>();

const authStore = useAuthStore();

const isQualified = computed(() => authStore.isQualified);

const close = () => emit("update:show", false);

const updateShow = (value: boolean) => {
	emit("update:show", value);
};
</script>

<style scoped>
.user-avatar {
  cursor: pointer;
  transition: transform 0.2s ease-out;
  position: relative;

  &:hover {
    transform: scale(1.05);
  }
}

.qualified-badge :deep(.el-badge__content) {
  border: 2px solid var(--color-surface);
  height: 15px;
  padding: 0 3px;
  font-size: 12px;
  line-height: 14px;
}

.user-info {
  padding: 12px;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.username {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .label {
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  .value {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}
</style>