/**
 * 任务注册表 - 用于统一管理所有BullMQ任务Worker
 * 提供统一的初始化和关闭机制
 */

import logger from "@/utils/logger.js";
import { initializeOnStartup as initStockDataSyncJobs } from "./stockDataSyncWorker.js";
import { initializeOnStartup as initVwapOrders } from "./vwapOrderWorker.js";
import { initializeOnStartup as initLimitOrders } from "./limitOrderWorker.js";
import { initializeOnStartup as initExpiryJobs } from "./expiryWorker.js";
import { initializeOnStartup as initKnockoutJobs } from "./knockoutWorker.js";
import { initializeOnStartup as initMarketStatusJobs } from "./marketStatusWorker.js";
import { initializeOnStartup as initInquiryHistoryJobs } from "./inquiryHistoryWorker.js";
import { initializeOnStartup as initAdjFactorJobs } from "./adjFactorWorker.js";
import { initializeOnStartup as initExpiryDatesJobs } from "./expiryDatesWorker.js";
import { initializeOnStartup as initErrorNotificationJobs } from "./errorNotificationWorker.js";
import { initializeOnStartup as initChannelLockJobs } from "./channelLockWorker.js";
import { initializeOnStartup as initSystemHealthCheckJobs } from "./systemHealthCheckWorker.js";
import { initializeOnStartup as initInkDataSyncJobs } from "./inkDataSyncWorker.js";

// 导入所有BullMQ队列
import {
	stockDataQueue,
	inkDataSyncQueue,
	vwapOrdersQueue,
	limitOrdersQueue,
	systemTasksQueue,
	adjFactorTasksQueue,
	marketStatusTasksQueue,
	inquiryHistoryTasksQueue,
	expiryTasksQueue,
	knockoutTasksQueue,
	expiryDatesTasksQueue,
	errorNotificationTasksQueue,
	channelLockTasksQueue,
	systemHealthCheckTasksQueue,
} from "./index.js";

// 定义所有任务工作器
interface TaskWorker {
	name: string;
	initFunction: () => Promise<void>;
}

export const workers: TaskWorker[] = [
	{ name: "StockDataSyncWorker", initFunction: initStockDataSyncJobs },
	{ name: "VwapOrderWorker", initFunction: initVwapOrders },
	{ name: "LimitOrderWorker", initFunction: initLimitOrders },
	{ name: "ExpiryWorker", initFunction: initExpiryJobs },
	{ name: "KnockoutWorker", initFunction: initKnockoutJobs },
	{ name: "MarketStatusWorker", initFunction: initMarketStatusJobs },
	{ name: "InquiryHistoryWorker", initFunction: initInquiryHistoryJobs },
	{ name: "AdjFactorWorker", initFunction: initAdjFactorJobs },
	{ name: "ExpiryDatesWorker", initFunction: initExpiryDatesJobs },
	{ name: "ErrorNotificationWorker", initFunction: initErrorNotificationJobs },
	{ name: "ChannelLockWorker", initFunction: initChannelLockJobs },
	{ name: "SystemHealthCheckWorker", initFunction: initSystemHealthCheckJobs },
	{ name: "InkDataSyncWorker", initFunction: initInkDataSyncJobs },
];

/**
 * 初始化所有工作器
 */
export async function initializeAllWorkers(): Promise<void> {
	logger.info("Initializing all BullMQ workers...");

	const results = await Promise.allSettled(
		workers.map(async (worker) => {
			try {
				logger.info(`Initializing ${worker.name}...`);
				await worker.initFunction();
				logger.info(`Successfully initialized ${worker.name}`);
				return { name: worker.name, success: true };
			} catch (error) {
				logger.error(
					{ error, workerName: worker.name },
					`Failed to initialize ${worker.name}`,
				);
				throw error;
			}
		}),
	);

	const failed = results.filter((result) => result.status === "rejected");

	if (failed.length > 0) {
		logger.warn(
			`${failed.length} workers failed to initialize. See above logs for details.`,
		);
	} else {
		logger.info("All BullMQ workers initialized successfully");
	}
}

/**
 * 初始化指定的工作器
 * @param workerNames 要初始化的工作器名称数组
 */
export async function initializeSpecificWorkers(
	workerNames: string[],
): Promise<void> {
	if (workerNames.length === 0) {
		logger.info("No workers specified to initialize");
		return;
	}

	logger.info(
		`Initializing specified BullMQ workers: ${workerNames.join(", ")}`,
	);

	// 显示警告消息
	logger.warn(
		"⚠️ DEVELOPMENT WARNING: Initializing workers in development environment may affect business data!",
	);
	logger.warn("⚠️ Only enable workers that you specifically need for testing");

	const workersToInitialize = workers.filter((worker) =>
		workerNames.includes(worker.name),
	);

	if (workersToInitialize.length < workerNames.length) {
		const missingWorkers = workerNames.filter(
			(name) => !workers.some((w) => w.name === name),
		);
		logger.warn(
			`Some specified workers were not found: ${missingWorkers.join(", ")}`,
		);
	}

	const results = await Promise.allSettled(
		workersToInitialize.map(async (worker) => {
			try {
				logger.info(`Initializing ${worker.name}...`);
				await worker.initFunction();
				logger.info(`Successfully initialized ${worker.name}`);
				return { name: worker.name, success: true };
			} catch (error) {
				logger.error(
					{ error, workerName: worker.name },
					`Failed to initialize ${worker.name}`,
				);
				throw error;
			}
		}),
	);

	const failed = results.filter((result) => result.status === "rejected");

	if (failed.length > 0) {
		logger.warn(
			`${failed.length} workers failed to initialize. See above logs for details.`,
		);
	} else if (workersToInitialize.length > 0) {
		logger.info(
			`All specified BullMQ workers (${workersToInitialize.length}) initialized successfully`,
		);
	}
}

/**
 * 优雅关闭所有队列
 */
export async function closeAllQueues(): Promise<void> {
	logger.info("Closing all BullMQ queues...");

	// 收集所有队列
	const allQueues = [
		stockDataQueue,
		inkDataSyncQueue,
		vwapOrdersQueue,
		limitOrdersQueue,
		systemTasksQueue,
		adjFactorTasksQueue,
		marketStatusTasksQueue,
		inquiryHistoryTasksQueue,
		expiryTasksQueue,
		knockoutTasksQueue,
		expiryDatesTasksQueue,
		errorNotificationTasksQueue,
		channelLockTasksQueue,
		systemHealthCheckTasksQueue,
	];

	logger.info(`Found ${allQueues.length} queues to close`);

	const results = await Promise.allSettled(
		allQueues.map(async (queue) => {
			try {
				const queueName = queue.name;
				logger.debug(`Closing queue: ${queueName}`);
				await queue.close();
				logger.debug(`Successfully closed queue: ${queueName}`);
				return { name: queueName, success: true };
			} catch (error) {
				logger.error(
					{ error, queueName: queue.name },
					`Failed to close queue: ${queue.name}`,
				);
				throw error;
			}
		}),
	);

	const failed = results.filter((result) => result.status === "rejected");

	if (failed.length > 0) {
		logger.warn(
			`${failed.length} queues failed to close properly. See above logs for details.`,
		);
	} else {
		logger.info("All BullMQ queues closed successfully");
	}
}
