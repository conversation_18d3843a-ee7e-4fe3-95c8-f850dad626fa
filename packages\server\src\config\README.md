# 配置管理系统

这个配置管理系统用于管理应用配置。

## 工作原理

1.  系统主要从 `.env` 文件中加载环境变量，特别是敏感信息，例如：
    -   数据库密码 (`DB_PASSWORD`)
    -   JWT 密钥 (`JWT_SECRET`)
    -   第三方服务 API 密钥等。
2.  非敏感的基础配置或默认值可能存储在 `baseConfig.json` 或代码内的常量中（如 `defaultParams.ts`）。
3.  `configManager.js` 负责整合来自 `.env` 和其他来源的配置，并提供统一的访问接口 `ENV`。

## 如何使用

### 1. 设置环境变量

在项目根目录或 `packages/server` 目录下创建 `.env` 文件，并填入必要的环境变量：

```dotenv
# .env 文件示例
DB_PASSWORD=your_secure_password
JWT_SECRET=your_very_secret_key
REDIS_PASSWORD=your_redis_password
# 其他必要的环境变量...
PORT=3000
NODE_ENV=development # 或 production
# 根据部署需要设置 TRADING_PLATFORM_ID 或 CHANNEL_ID
# TRADING_PLATFORM_ID=INK
# CHANNEL_ID=FO_CHANNEL
```