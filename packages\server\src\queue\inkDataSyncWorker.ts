import type { Job } from "bullmq";
import { createWorker, inkDataSyncQueue, addRepeatedJob } from "./index.js";
import { getChildLogger } from "@/utils/logger.js";
import * as InkApi from "@/api/inkApi.js";
import { isMarketDay } from "@/financeUtils/marketTimeManager.js";
import { getChinaDateCompactString } from "@/utils/dateUtils.js";
import * as ConfigService from "@/services/admin/index.js";
import type { BusinessConfig } from "@packages/shared";

// 导入新的优化模块
import {
	INK_DATA_SYNC_JOBS,
	JOB_IDS,
	REDIS_KEYS,
	TradingTimeUtils,
} from "./ink/constants.js";
import { lockManager } from "./ink/distributedLockManager.js";
import { inkSyncConfig } from "./ink/config.js";
import { retryHandler } from "./ink/retryHandler.js";
import { redisManager } from "./ink/redisManager.js";
import { metricsCollector } from "./ink/metricsCollector.js";

/**
 * INK Data Synchronization Worker (重构版本)
 *
 * 该模块负责处理所有与INK系统数据同步相关的后台作业。
 * 重构后的版本具有以下特性：
 * - 模块化架构：各功能模块独立，易于维护和测试
 * - 增强的错误处理：断路器模式和智能重试机制
 * - 优化的Redis操作：批量操作和连接池管理
 * - 全面的监控：性能指标收集和健康状态评估
 * - 配置驱动：支持运行时配置更新和环境变量覆盖
 */

const workerLogger = getChildLogger("InkDataSyncWorker");

/**
 * 数据更新服务类
 *
 * 职责：
 * - 封装所有数据更新逻辑，使用新的优化模块
 * - 处理INK波动数据、价格报价、业务配置的更新
 * - 提供错误处理和重试机制
 * - 支持批量处理和并发控制
 */
class InkDataUpdateService {
	/**
	 * 更新INK波动数据
	 *
	 * 流程：
	 * 1. 检查是否为交易日
	 * 2. 获取当日数据
	 * 3. 使用重试机制调用API
	 * 4. 存储到Redis缓存
	 * 5. 记录执行历史和指标
	 */
	async updateInkData(): Promise<void> {
		metricsCollector.recordJobStart("updateInkData");

		try {
			// 检查是否为交易日
			const isTradeDay = await isMarketDay();
			if (!isTradeDay) {
				workerLogger.info("Not a market day, skipping INK data update");
				metricsCollector.recordJobComplete("updateInkData", true);
				await redisManager.recordExecution("update-ink-data", {
					reason: "not-market-day",
				});
				return;
			}

			workerLogger.info("Fetching new INK API data");
			const date = getChinaDateCompactString();

			// 使用重试机制获取数据
			const swingData = await retryHandler.executeWithRetry(
				() => InkApi.fetchSwingData(date),
				{ maxAttempts: 3 },
				"ink-api-swing-data",
			);

			if (!swingData || swingData.size === 0) {
				workerLogger.warn(
					`No data available for today (${date}), keeping existing cache`,
				);
				metricsCollector.recordJobComplete("updateInkData", true);
				await redisManager.recordExecution("update-ink-data", {
					reason: "no-data",
					date,
				});
				return;
			}

			// 转换Map为Record
			const swingRecord: Record<string, number> = {};
			swingData.forEach((value, key) => {
				swingRecord[key] = value;
			});

			// 使用Redis管理器存储数据
			await redisManager.setSwingData(swingRecord);

			workerLogger.info(
				`Successfully updated swing data with ${swingData.size} entries`,
			);
			metricsCollector.recordJobComplete("updateInkData", true);
			await redisManager.recordExecution("update-ink-data", {
				entriesCount: swingData.size,
				date,
				success: true,
			});
		} catch (error) {
			workerLogger.error(error, "Failed to update INK swing data");
			metricsCollector.recordJobComplete(
				"updateInkData",
				false,
				(error as Error).message,
			);
			await redisManager.recordExecution("update-ink-data", {
				error: (error as Error).message,
				success: false,
			});
			throw error;
		}
	}

	/**
	 * 更新价格报价数据
	 *
	 * 特点：
	 * - 支持多个价格提供商并发处理
	 * - 智能缓存检查，避免重复更新
	 * - 支持强制更新模式
	 * - 限制并发数量，保护外部API
	 *
	 * @param forceUpdate 是否强制更新，忽略缓存
	 */
	async updatePriceQuotes(forceUpdate = false): Promise<void> {
		metricsCollector.recordJobStart("updatePriceQuotes");

		try {
			const isTradeDay = await isMarketDay();
			if (!isTradeDay && !forceUpdate) {
				workerLogger.info("Not a market day, skipping price quotes update");
				metricsCollector.recordJobComplete("updatePriceQuotes", true);
				return;
			}

			workerLogger.info("Fetching external price quotes data");
			const date = getChinaDateCompactString();
			const providers = Object.values(InkApi.PriceProvider);

			// 并发处理所有提供商，但限制并发数
			const concurrencyLimit = inkSyncConfig.concurrencyLimit;
			const results = await this.processBatchWithConcurrency(
				providers,
				async (provider) =>
					this.updateProviderQuotes(provider, date, forceUpdate),
				concurrencyLimit,
			);

			const successCount = results.filter((r) => r.success).length;
			workerLogger.info(
				`Price quotes update completed: ${successCount}/${providers.length} providers successful`,
			);

			metricsCollector.recordJobComplete("updatePriceQuotes", successCount > 0);
		} catch (error) {
			workerLogger.error(error, "Failed to update price quotes data");
			metricsCollector.recordJobComplete(
				"updatePriceQuotes",
				false,
				(error as Error).message,
			);
			throw error;
		}
	}

	/**
	 * 更新单个提供商的报价数据
	 *
	 * 逻辑：
	 * 1. 检查缓存是否存在（非强制更新时）
	 * 2. 调用外部API获取数据
	 * 3. 转换数据格式并存储
	 * 4. 记录缓存命中/未命中统计
	 *
	 * @param provider 价格提供商
	 * @param date 日期字符串
	 * @param forceUpdate 是否强制更新
	 * @returns 更新结果
	 */
	private async updateProviderQuotes(
		provider: InkApi.PriceProviderValue,
		date: string,
		forceUpdate: boolean,
	): Promise<{ success: boolean; provider: string }> {
		try {
			// 检查缓存是否存在
			const existingQuotes = await redisManager.getPriceQuotes(provider, date);

			if (!forceUpdate && existingQuotes) {
				workerLogger.info(
					`Cache already exists for ${provider}, skipping update`,
				);
				metricsCollector.recordCacheHit();
				return { success: true, provider };
			}

			metricsCollector.recordCacheMiss();

			// 获取新数据
			const quoteData = await retryHandler.executeWithRetry(
				() => InkApi.fetchPriceQuotes(provider, date),
				{ maxAttempts: 3 },
				`price-quotes-${provider}`,
			);

			if (quoteData && quoteData.size > 0) {
				// 转换为数组格式
				const quotesRecord: Record<string, Array<number | null>> = {};
				quoteData.forEach((quote, stockCode) => {
					quotesRecord[stockCode] = this.optionQuoteToArray(quote);
				});

				// 存储到Redis
				await redisManager.setPriceQuotes(provider, date, quotesRecord);

				workerLogger.info(
					`${provider}: Successfully updated with ${quoteData.size} quotes for ${date}`,
				);
				return { success: true, provider };
			}

			if (forceUpdate) {
				// 强制更新但无数据时，清除缓存
				workerLogger.warn(
					`Force update requested but no data available for ${provider}, clearing cache`,
				);
				// 这里可以添加清除缓存的逻辑
				return { success: false, provider };
			}

			return { success: false, provider };
		} catch (error) {
			workerLogger.error(
				error,
				`Failed to fetch price quotes from ${provider}`,
			);
			return { success: false, provider };
		}
	}

	/**
	 * 更新业务配置
	 *
	 * 功能：
	 * - 从INK API获取最新的业务配置参数
	 * - 并发检查多个配置项
	 * - 只更新有变化的配置
	 * - 验证配置值的有效性
	 */
	async updateBusinessConfig(): Promise<void> {
		metricsCollector.recordJobStart("updateBusinessConfig");

		try {
			const isTradeDay = await isMarketDay();
			if (!isTradeDay) {
				workerLogger.debug("Not a market day, skipping business config update");
				metricsCollector.recordJobComplete("updateBusinessConfig", true);
				return;
			}

			workerLogger.info("Checking for business config updates from INK API");

			const config = await ConfigService.getConfig();
			const updates: Partial<BusinessConfig> = {};
			let hasUpdates = false;

			// 需要检查的配置项列表
			const configKeys = [
				"OPTION_MULTIPLIER",
				"STOCK_SCALE_LIMIT",
				"TOTAL_SCALE_LIMIT",
				"CHANNEL_CREDIT_LIMIT",
				"DISCOUNT_MULTIPLIER",
			] as const;

			// 并发检查所有配置项
			const configResults = await Promise.allSettled(
				configKeys.map(async (key) => {
					const apiData = await retryHandler.executeWithRetry(
						() => InkApi.fetchBusinessConfig(key),
						{ maxAttempts: 2 },
						`business-config-${key}`,
					);

					const newValue = apiData.get(key);
					const currentValue = config[key];

					if (
						newValue !== undefined &&
						typeof newValue === "number" &&
						!Number.isNaN(newValue) &&
						newValue > 0 &&
						currentValue !== newValue
					) {
						return { key, newValue, currentValue };
					}
					return null;
				}),
			);

			// 处理结果
			for (const result of configResults) {
				if (result.status === "fulfilled" && result.value) {
					const { key, newValue, currentValue } = result.value;
					updates[key] = newValue;
					hasUpdates = true;
					workerLogger.info(
						`Found new ${key}: ${newValue} (previous: ${currentValue})`,
					);
				} else if (result.status === "rejected") {
					workerLogger.error(result.reason, "Failed to fetch config item");
				}
			}

			if (hasUpdates) {
				workerLogger.info("Updating business config with new values:", updates);
				await ConfigService.updateConfig(updates);
				workerLogger.info("Business config updated successfully");
			} else {
				workerLogger.debug("No changes in business config values");
			}

			metricsCollector.recordJobComplete("updateBusinessConfig", true);
		} catch (error) {
			workerLogger.error(error, "Failed to update business config values");
			metricsCollector.recordJobComplete(
				"updateBusinessConfig",
				false,
				(error as Error).message,
			);
			throw error;
		}
	}

	/**
	 * 将报价对象转换为数组
	 *
	 * 数组结构：
	 * [c100_2w, c100_1m, c100_2m, c100_3m,  // 100%行权价
	 *  c103_2w, c103_1m, c103_2m, c103_3m,  // 103%行权价
	 *  c105_2w, c105_1m, c105_2m, c105_3m,  // 105%行权价
	 *  c110_2w, c110_1m, c110_2m, c110_3m]  // 110%行权价
	 *
	 * @param quote 期权报价对象
	 * @returns 报价数组
	 */
	private optionQuoteToArray(
		quote: InkApi.OptionPriceQuote,
	): Array<number | null> {
		return [
			quote.c100_2w,
			quote.c100_1m,
			quote.c100_2m,
			quote.c100_3m,
			quote.c103_2w,
			quote.c103_1m,
			quote.c103_2m,
			quote.c103_3m,
			quote.c105_2w,
			quote.c105_1m,
			quote.c105_2m,
			quote.c105_3m,
			quote.c110_2w,
			quote.c110_1m,
			quote.c110_2m,
			quote.c110_3m,
		];
	}

	/**
	 * 批量处理，支持并发限制
	 *
	 * 特点：
	 * - 分批处理，避免同时发起过多请求
	 * - 使用Promise.allSettled确保部分失败不影响整体
	 * - 支持自定义并发限制
	 *
	 * @param items 待处理的项目列表
	 * @param processor 处理函数
	 * @param concurrencyLimit 并发限制
	 * @returns 处理结果数组
	 */
	private async processBatchWithConcurrency<T, R>(
		items: T[],
		processor: (item: T) => Promise<R>,
		concurrencyLimit: number,
	): Promise<R[]> {
		const results: R[] = [];

		// 分批处理
		for (let i = 0; i < items.length; i += concurrencyLimit) {
			const batch = items.slice(i, i + concurrencyLimit);
			const batchResults = await Promise.allSettled(
				batch.map((item) => processor(item)),
			);

			// 收集成功的结果
			for (const result of batchResults) {
				if (result.status === "fulfilled") {
					results.push(result.value);
				}
			}
		}

		return results;
	}
}

/**
 * 作业调度管理器
 *
 * 职责：
 * - 根据交易时段动态管理高频更新任务
 * - 处理盘前时段的外部报价更新
 * - 在关键时间点自动启动/停止相关任务
 * - 使用分布式锁确保多实例环境下的一致性
 */
class JobScheduleManager {
	/**
	 * 判断当前是否处于交易时段（考虑午休时间），影响高频更新的状态（不包含结束时间）
	 *
	 * 交易时段定义：
	 * - 上午：9:25-11:30
	 * - 下午：13:00-15:00
	 *
	 * @returns 是否在交易时段内
	 */
	private isInTradingHours(): boolean {
		const now = new Date();
		const currentTime = now.getHours() * 100 + now.getMinutes();

		// 上午交易时段：9:25-11:30
		const morningTrading =
			currentTime >= inkSyncConfig.morningTradingStart &&
			currentTime < inkSyncConfig.morningTradingEnd;

		// 下午交易时段：13:00-15:00
		const afternoonTrading =
			currentTime >= inkSyncConfig.afternoonTradingStart &&
			currentTime < inkSyncConfig.afternoonTradingEnd;

		return morningTrading || afternoonTrading;
	}

	/**
	 * 判断当前是否处于盘前时段
	 *
	 * 盘前时段：8:30-9:30
	 * 用于外部价格报价的更新
	 *
	 * @returns 是否在盘前时段内
	 */
	private isInPreMarketHours(): boolean {
		const now = new Date();
		const currentTime = now.getHours() * 100 + now.getMinutes();
		return (
			currentTime >= inkSyncConfig.premarketStartTime &&
			currentTime < inkSyncConfig.premarketEndTime
		);
	}

	/**
	 * 判断是否应该触发管理任务的关键时间点
	 *
	 * 关键时间点：
	 * - 9:25 上午开盘
	 * - 11:30 午休开始
	 * - 13:00 下午开盘
	 * - 15:00 收盘
	 *
	 * @returns 是否为关键时间点（前后1分钟内）
	 */
	private isTradingManagementTriggerTime(): boolean {
		const now = new Date();
		const currentTime = now.getHours() * 100 + now.getMinutes();

		// 关键时间点：开盘时、午休前、午休后、收盘前
		const triggerTimes = [
			inkSyncConfig.morningTradingStart, // 9:25 上午开盘
			inkSyncConfig.morningTradingEnd, // 11:30 午休开始
			inkSyncConfig.afternoonTradingStart, // 13:00 下午开盘
			inkSyncConfig.afternoonTradingEnd, // 15:00 收盘
		];

		// 检查是否在关键时间点的前后1分钟内
		return triggerTimes.some((time) => Math.abs(currentTime - time) <= 1);
	}

	/**
	 * 判断是否应该触发盘前管理任务的关键时间点
	 *
	 * 关键时间点：
	 * - 8:30 盘前开始
	 * - 9:30 盘前结束
	 *
	 * @returns 是否为盘前关键时间点（前后1分钟内）
	 */
	private isPreMarketManagementTriggerTime(): boolean {
		const now = new Date();
		const currentTime = now.getHours() * 100 + now.getMinutes();

		// 关键时间点：开盘前、盘前结束
		const triggerTimes = [
			inkSyncConfig.premarketStartTime, // 8:30 盘前开始
			inkSyncConfig.premarketEndTime, // 9:30 盘前结束
		];

		// 检查是否在关键时间点的前后1分钟内
		return triggerTimes.some((time) => Math.abs(currentTime - time) <= 1);
	}

	/**
	 * 管理交易时段的高频更新（带分布式锁）
	 *
	 * 功能：
	 * - 在交易时段启动高频更新任务（5秒间隔）
	 * - 在非交易时段停止高频更新任务
	 * - 使用分布式锁防止多实例冲突
	 * - 在交易结束时延迟清理，确保最后一次更新完成
	 */
	async manageMarketTimeUpdates(): Promise<void> {
		// 只在关键时间点或已有高频任务运行时执行
		if (!this.isTradingManagementTriggerTime() && !this.isInTradingHours()) {
			return;
		}

		const lockAcquired = await lockManager.acquireLockWithRenewal(
			REDIS_KEYS.INK_MANAGE_MARKET_LOCK,
			300,
			5000,
		);

		if (!lockAcquired) {
			workerLogger.debug(
				"Market time management already in progress by another instance",
			);
			return;
		}

		try {
			await this.performMarketTimeUpdates();
		} finally {
			await lockManager.releaseLock(REDIS_KEYS.INK_MANAGE_MARKET_LOCK);
		}
	}

	/**
	 * 执行交易时段（内部报价）高频更新管理
	 *
	 * 逻辑：
	 * 1. 判断当前是否应该运行高频更新
	 * 2. 清理现有的高频任务
	 * 3. 如果在交易时段，创建新的高频任务
	 * 4. 记录执行历史
	 */
	private async performMarketTimeUpdates(): Promise<void> {
		try {
			const shouldBeRunning = this.isInTradingHours();
			const repeatableJobs = await inkDataSyncQueue.getJobSchedulers();

			// 如果是停止高频更新（交易结束），延迟几秒确保最后一次更新能完成
			if (!shouldBeRunning) {
				const now = new Date();
				const currentTime = now.getHours() * 100 + now.getMinutes();

				// 在交易结束时点（11:30 或 15:00）延迟清理
				if (
					currentTime === inkSyncConfig.morningTradingEnd ||
					currentTime === inkSyncConfig.afternoonTradingEnd
				) {
					workerLogger.info(
						"Trading session ended, delaying cleanup to allow final update",
					);
					await new Promise((resolve) =>
						setTimeout(resolve, inkSyncConfig.cleanupDelaySeconds * 1000),
					);
				}
			}

			// 清理现有的高频任务
			let tasksRemoved = 0;
			for (const job of repeatableJobs) {
				if (
					job.name === INK_DATA_SYNC_JOBS.HIGH_FREQ_INK_DATA_UPDATE ||
					job.id?.includes(JOB_IDS.HIGH_FREQ)
				) {
					await inkDataSyncQueue.removeJobScheduler(job.key);
					tasksRemoved++;
				}
			}

			if (tasksRemoved > 0) {
				workerLogger.info(
					`Removed ${tasksRemoved} high-frequency update tasks`,
				);
			}

			// 在交易时段添加高频更新任务
			if (shouldBeRunning) {
				await inkDataSyncQueue.add(
					INK_DATA_SYNC_JOBS.HIGH_FREQ_INK_DATA_UPDATE,
					{},
					{
						repeat: {
							every: inkSyncConfig.highFreqUpdateInterval,
							immediately: true,
						},
						jobId: JOB_IDS.HIGH_FREQ,
						removeOnComplete: true,
					},
				);
				workerLogger.info(
					`Added high-frequency update task (every ${inkSyncConfig.highFreqUpdateInterval}ms)`,
				);

				// 记录执行历史
				await redisManager.recordExecution("manage-market-time-updates", {
					action: "started",
					shouldBeRunning,
					tasksRemoved,
				});
			} else {
				workerLogger.info(
					"Outside trading hours, high-frequency updates stopped",
				);

				// 记录执行历史
				await redisManager.recordExecution("manage-market-time-updates", {
					action: "stopped",
					shouldBeRunning,
					tasksRemoved,
				});
			}
		} catch (error) {
			workerLogger.error(error, "Failed to manage market time updates");
			throw error;
		}
	}

	/**
	 * 管理盘前时段的外部报价更新（带分布式锁）
	 *
	 * 功能：
	 * - 在盘前时段启动外部报价更新任务（5分钟间隔）
	 * - 在非盘前时段停止相关任务
	 * - 使用分布式锁防止多实例冲突
	 */
	async managePreMarketPriceQuoteUpdates(): Promise<void> {
		// 只在关键时间点或盘前时段执行
		if (
			!this.isPreMarketManagementTriggerTime() &&
			!this.isInPreMarketHours()
		) {
			return;
		}

		const lockAcquired = await lockManager.acquireLockWithRenewal(
			REDIS_KEYS.INK_MANAGE_PREMARKET_LOCK,
			300,
			5000,
		);

		if (!lockAcquired) {
			workerLogger.debug(
				"Premarket management already in progress by another instance",
			);
			return;
		}

		try {
			await this.performPreMarketUpdates();
		} finally {
			await lockManager.releaseLock(REDIS_KEYS.INK_MANAGE_PREMARKET_LOCK);
		}
	}

	/**
	 * 执行盘前报价更新管理
	 *
	 * 逻辑：
	 * 1. 判断当前是否应该运行盘前更新
	 * 2. 清理现有的盘前任务
	 * 3. 如果在盘前时段，创建新的盘前任务
	 * 4. 记录执行历史
	 */
	private async performPreMarketUpdates(): Promise<void> {
		try {
			const shouldBeRunning = this.isInPreMarketHours();
			const repeatableJobs = await inkDataSyncQueue.getJobSchedulers();

			// 如果是停止盘前更新（盘前结束），延迟几秒确保最后一次更新能完成
			if (!shouldBeRunning) {
				const now = new Date();
				const currentTime = now.getHours() * 100 + now.getMinutes();

				// 在盘前结束时点（9:30）延迟清理
				if (currentTime === inkSyncConfig.premarketEndTime) {
					workerLogger.info(
						"Premarket session ended, delaying cleanup to allow final update",
					);
					await new Promise((resolve) =>
						setTimeout(resolve, inkSyncConfig.cleanupDelaySeconds * 1000),
					);
				}
			}

			// 清理现有的盘前任务
			let tasksRemoved = 0;
			for (const job of repeatableJobs) {
				if (
					job.name === INK_DATA_SYNC_JOBS.PREMARKET_PRICE_QUOTES_UPDATE ||
					job.id?.includes(JOB_IDS.PREMARKET)
				) {
					await inkDataSyncQueue.removeJobScheduler(job.key);
					tasksRemoved++;
				}
			}

			if (tasksRemoved > 0) {
				workerLogger.info(`Removed ${tasksRemoved} premarket quote tasks`);
			}

			// 在盘前时段添加报价更新任务
			if (shouldBeRunning) {
				await inkDataSyncQueue.add(
					INK_DATA_SYNC_JOBS.PREMARKET_PRICE_QUOTES_UPDATE,
					{},
					{
						repeat: {
							every: inkSyncConfig.premarketUpdateInterval,
							immediately: true,
						},
						jobId: JOB_IDS.PREMARKET,
						removeOnComplete: true,
					},
				);
				workerLogger.info("Started premarket price quote updates");

				// 记录执行历史
				await redisManager.recordExecution("manage-premarket-quotes", {
					action: "started",
					shouldBeRunning,
					tasksRemoved,
				});
			} else {
				workerLogger.info("Outside premarket hours, premarket updates stopped");

				// 记录执行历史
				await redisManager.recordExecution("manage-premarket-quotes", {
					action: "stopped",
					shouldBeRunning,
					tasksRemoved,
				});
			}
		} catch (error) {
			workerLogger.error(error, "Failed to manage premarket quote updates");
			throw error;
		}
	}
}

// 创建服务实例
const dataUpdateService = new InkDataUpdateService();
const scheduleManager = new JobScheduleManager();

/**
 * 带分布式锁的数据更新函数
 *
 * 使用分布式锁确保同一时间只有一个实例在执行INK数据更新
 */
async function updateInkDataWithLock(): Promise<void> {
	const lockKey = REDIS_KEYS.INK_DATA_UPDATE_LOCK;

	// 先诊断锁状态
	const lockDiagnosis = await lockManager.diagnoseLock(lockKey);
	workerLogger.debug("Lock diagnosis before acquisition:", lockDiagnosis);

	const lockAcquired = await lockManager.acquireLockWithRenewal(lockKey);

	if (!lockAcquired) {
		workerLogger.info(
			"INK data update already in progress by another instance",
		);

		// 获取锁失败时再次诊断
		const postFailureDiagnosis = await lockManager.diagnoseLock(lockKey);
		workerLogger.debug(
			"Lock diagnosis after acquisition failure:",
			postFailureDiagnosis,
		);
		return;
	}

	try {
		await dataUpdateService.updateInkData();
	} finally {
		// 释放前再次诊断
		const preReleaseDiagnosis = await lockManager.diagnoseLock(lockKey);
		workerLogger.debug("Lock diagnosis before release:", preReleaseDiagnosis);

		await lockManager.releaseLock(lockKey);

		// 释放后确认
		const postReleaseDiagnosis = await lockManager.diagnoseLock(lockKey);
		workerLogger.debug("Lock diagnosis after release:", postReleaseDiagnosis);
	}
}

/**
 * 带分布式锁的价格报价更新函数
 *
 * @param forceUpdate 是否强制更新
 */
async function updatePriceQuotesWithLock(forceUpdate = false): Promise<void> {
	const lockKey = REDIS_KEYS.INK_QUOTES_UPDATE_LOCK;

	// 先诊断锁状态
	const lockDiagnosis = await lockManager.diagnoseLock(lockKey);
	workerLogger.debug(
		lockDiagnosis,
		"Lock diagnosis before acquisition (quotes):",
	);

	const lockAcquired = await lockManager.acquireLockWithRenewal(lockKey);

	if (!lockAcquired) {
		workerLogger.info(
			"Price quotes update already in progress by another instance",
		);

		// 获取锁失败时再次诊断
		const postFailureDiagnosis = await lockManager.diagnoseLock(lockKey);
		workerLogger.debug(
			postFailureDiagnosis,
			"Lock diagnosis after acquisition failure (quotes):",
		);
		return;
	}

	try {
		await dataUpdateService.updatePriceQuotes(forceUpdate);
	} finally {
		// 释放前再次诊断
		const preReleaseDiagnosis = await lockManager.diagnoseLock(lockKey);
		workerLogger.debug(
			preReleaseDiagnosis,
			"Lock diagnosis before release (quotes):",
		);

		await lockManager.releaseLock(lockKey);

		// 释放后确认
		const postReleaseDiagnosis = await lockManager.diagnoseLock(lockKey);
		workerLogger.debug(
			postReleaseDiagnosis,
			"Lock diagnosis after release (quotes):",
		);
	}
}

/**
 * 带分布式锁的业务配置更新函数
 */
async function updateBusinessConfigWithLock(): Promise<void> {
	const lockAcquired = await lockManager.acquireLockWithRenewal(
		REDIS_KEYS.INK_CONFIG_UPDATE_LOCK,
	);

	if (!lockAcquired) {
		workerLogger.info(
			"Business config update already in progress by another instance",
		);
		return;
	}

	try {
		await dataUpdateService.updateBusinessConfig();
	} finally {
		await lockManager.releaseLock(REDIS_KEYS.INK_CONFIG_UPDATE_LOCK);
	}
}

/**
 * 处理INK数据同步相关的作业
 *
 * 作业路由器：根据作业名称分发到对应的处理函数
 *
 * @param job BullMQ作业对象
 */
async function processInkDataSyncJob(job: Job) {
	const { name, data } = job;
	metricsCollector.recordJobStart(name);

	workerLogger.info(`Processing INK data sync job: ${name}`);

	try {
		switch (true) {
			// 基础数据更新作业
			case name === INK_DATA_SYNC_JOBS.UPDATE_INK_DATA:
				await updateInkDataWithLock();
				break;
			case name === INK_DATA_SYNC_JOBS.UPDATE_PRICE_QUOTES:
				await updatePriceQuotesWithLock(data?.forceUpdate);
				break;
			case name === INK_DATA_SYNC_JOBS.UPDATE_BUSINESS_CONFIG:
				await updateBusinessConfigWithLock();
				break;

			// 调度管理作业
			case name === INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES ||
				name.startsWith(`${INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES}-`):
				await scheduleManager.manageMarketTimeUpdates();
				break;
			case name === INK_DATA_SYNC_JOBS.MANAGE_PREMARKET_PRICE_QUOTES:
				await scheduleManager.managePreMarketPriceQuoteUpdates();
				break;

			// 高频执行作业
			case name === INK_DATA_SYNC_JOBS.HIGH_FREQ_INK_DATA_UPDATE:
				if (await isMarketDay()) {
					await dataUpdateService.updateInkData();
				}
				break;
			case name === INK_DATA_SYNC_JOBS.PREMARKET_PRICE_QUOTES_UPDATE:
				await dataUpdateService.updatePriceQuotes();
				break;

			// 系统初始化作业
			case name === INK_DATA_SYNC_JOBS.INITIALIZE_INK_DATA_SCHEDULES:
				await processInitializeInkDataSchedules();
				break;
			default:
				workerLogger.warn(`Unknown INK data sync job type: ${name}`);
		}

		metricsCollector.recordJobComplete(name, true);
	} catch (error) {
		workerLogger.error(error, `Failed to process INK data sync job: ${name}`);
		metricsCollector.recordJobComplete(name, false, (error as Error).message);
		throw error;
	}
}

/**
 * 处理初始化调度任务（带分布式锁和执行记录）
 *
 * 功能：
 * - 检查最近是否已执行过初始化
 * - 使用分布式锁防止重复初始化
 * - 记录初始化执行历史
 */
async function processInitializeInkDataSchedules(): Promise<void> {
	// 检查是否有最近的执行记录
	// 临时注释掉这个检查，强制执行初始化以获取外部报价
	// if (await redisManager.hasRecentExecution()) {
	// 	workerLogger.info(
	// 		"Skipping INK data schedule initialization due to recent execution",
	// 	);
	// 	return;
	// }
	workerLogger.info(
		"Force executing INK data schedule initialization for debugging",
	);

	const lockAcquired = await lockManager.acquireLockWithRenewal(
		REDIS_KEYS.INK_SYNC_LOCK,
	);

	if (!lockAcquired) {
		workerLogger.info("INK data schedule initialization already in progress");
		return;
	}

	try {
		await initializeInkDataSyncJobs();
		// 记录执行历史
		await redisManager.recordExecution("initialize-schedules", {
			message: "INK data sync jobs initialized successfully",
		});
		workerLogger.info("INK data sync jobs initialized successfully");
	} finally {
		await lockManager.releaseLock(REDIS_KEYS.INK_SYNC_LOCK);
	}
}

/**
 * 初始化定时作业调度
 *
 * 功能：
 * - 设置所有定时作业的调度规则
 * - 创建初始更新任务
 * - 配置关键时间点的管理任务
 * - 设置定期维护任务
 */
export async function initializeInkDataSyncJobs() {
	try {
		workerLogger.info(
			"Initializing INK data sync jobs with optimized configuration",
		);

		// 记录配置摘要
		const configSummary = inkSyncConfig.getConfigSummary();
		workerLogger.info("Current configuration:", configSummary);

		const initialJobOpts = { removeOnComplete: true, removeOnFail: 5 };
		const defaultCronJobOptions = { removeOnComplete: true, removeOnFail: 50 };

		// 获取预设的cron表达式
		const cronExpressions = TradingTimeUtils.getCronExpressions();

		// 初始更新任务 - 立即执行一次
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.UPDATE_INK_DATA,
			{},
			initialJobOpts,
		);
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.UPDATE_PRICE_QUOTES,
			{ forceUpdate: true },
			initialJobOpts,
		);
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.UPDATE_BUSINESS_CONFIG,
			{},
			initialJobOpts,
		);
		// 时间段内启动应用时立即检查
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES,
			{},
			initialJobOpts,
		);
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.MANAGE_PREMARKET_PRICE_QUOTES,
			{},
			initialJobOpts,
		);

		// 优化的管理任务调度 - 使用常量中的时间
		// 开盘前
		await addRepeatedJob(
			inkDataSyncQueue,
			INK_DATA_SYNC_JOBS.MANAGE_PREMARKET_PRICE_QUOTES,
			{},
			cronExpressions.premarketStart,
			defaultCronJobOptions,
		);

		// 盘前结束
		await addRepeatedJob(
			inkDataSyncQueue,
			INK_DATA_SYNC_JOBS.MANAGE_PREMARKET_PRICE_QUOTES,
			{},
			cronExpressions.premarketEnd,
			defaultCronJobOptions,
		);

		// 开盘时
		await addRepeatedJob(
			inkDataSyncQueue,
			`${INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES}-morning-start`,
			{},
			cronExpressions.morningStart,
			defaultCronJobOptions,
		);

		// 午休前
		await addRepeatedJob(
			inkDataSyncQueue,
			`${INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES}-morning-end`,
			{},
			cronExpressions.morningEnd,
			defaultCronJobOptions,
		);

		// 午休后
		await addRepeatedJob(
			inkDataSyncQueue,
			`${INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES}-afternoon-start`,
			{},
			cronExpressions.afternoonStart,
			defaultCronJobOptions,
		);

		// 收盘时
		await addRepeatedJob(
			inkDataSyncQueue,
			`${INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES}-afternoon-end`,
			{},
			cronExpressions.afternoonEnd,
			defaultCronJobOptions,
		);

		// 定时更新任务
		await addRepeatedJob(
			inkDataSyncQueue,
			INK_DATA_SYNC_JOBS.UPDATE_INK_DATA,
			{},
			"*/30 0-8,15-23 * * *", // 非交易时段每30分钟
			defaultCronJobOptions,
		);

		await addRepeatedJob(
			inkDataSyncQueue,
			INK_DATA_SYNC_JOBS.UPDATE_BUSINESS_CONFIG,
			{},
			"0 * * * *", // 每小时
			defaultCronJobOptions,
		);

		workerLogger.info("INK data sync jobs scheduled successfully");
	} catch (error) {
		workerLogger.error(error, "Failed to schedule INK data sync jobs");
		throw error;
	}
}

/**
 * 创建Worker实例
 *
 * 使用BullMQ创建工作进程，处理队列中的作业
 */
export const inkDataSyncWorker = createWorker(
	inkDataSyncQueue,
	processInkDataSyncJob,
);

/**
 * 用于应用程序启动时初始化所有INK数据同步任务
 *
 * 在应用启动时调用，确保所有调度任务正确设置
 */
export async function initializeOnStartup() {
	try {
		// 首先验证分布式锁配置
		const lockValidation = await validateLockConfiguration();
		workerLogger.info("Lock configuration validation:", lockValidation);

		if (!lockValidation.isValid) {
			workerLogger.error("分布式锁配置验证失败，可能影响任务调度的正常运行");
		}

		if (lockValidation.warnings.length > 0) {
			workerLogger.warn("分布式锁配置存在潜在问题:", {
				warnings: lockValidation.warnings,
				recommendations: lockValidation.recommendations,
			});
		}

		const jobId = "init-ink-data-schedules";
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.INITIALIZE_INK_DATA_SCHEDULES,
			{},
			{ jobId, removeOnComplete: true, removeOnFail: 5 },
		);
		workerLogger.info(
			`Added INK data schedule initialization job to queue with ID: ${jobId}`,
		);
	} catch (error) {
		workerLogger.error(
			error,
			"Failed to initialize INK data worker on startup",
		);
	}
}

/**
 * 验证分布式锁配置
 *
 * 检查应用标识符配置，确保锁功能正常工作
 */
export async function validateLockConfiguration(): Promise<{
	isValid: boolean;
	appIdentifier: string;
	warnings: string[];
	recommendations: string[];
}> {
	const warnings: string[] = [];
	const recommendations: string[] = [];

	// 获取当前应用标识符
	const lockDiagnosis = await lockManager.diagnoseLock("test-validation-lock");
	const appIdentifier = lockDiagnosis.currentAppId;

	// 检查应用标识符的有效性
	if (appIdentifier.startsWith("unknown:")) {
		warnings.push("使用随机生成的应用标识符，可能导致锁管理问题");
		recommendations.push(
			"配置 tradingPlatformId 或 channelId 以获得稳定的应用标识符",
		);
	}

	if (appIdentifier.includes("platform:") && appIdentifier === "platform:") {
		warnings.push("平台ID为空，可能导致锁标识符冲突");
		recommendations.push("确保 tradingPlatformId 配置了有效的非空值");
	}

	// 检查锁的基本功能
	let isValid = true;
	try {
		const testLock = `test-validation-lock-${Date.now()}`;
		const acquired = await lockManager.acquireLockWithRenewal(testLock, 5, 0);
		if (acquired) {
			await lockManager.releaseLock(testLock);
		} else {
			isValid = false;
			warnings.push("锁获取测试失败");
		}
	} catch (error) {
		isValid = false;
		warnings.push(`锁功能测试异常: ${(error as Error).message}`);
	}

	return {
		isValid,
		appIdentifier,
		warnings,
		recommendations,
	};
}

// ============================================================================
// 兼容性导出 - 保持原有的API接口
// ============================================================================

/**
 * 获取缓存的波动数据
 *
 * @returns 波动数据记录或null
 */
export async function getCachedSwingData(): Promise<Record<
	string,
	number
> | null> {
	return redisManager.getSwingData();
}

/**
 * 获取缓存的期权价格
 *
 * @param stockCode 股票代码
 * @param provider 价格提供商
 * @param strikePercent 行权价百分比
 * @param period 期限
 * @returns 期权价格或null
 */
export async function getCachedOptionPrice(
	stockCode: string,
	provider: InkApi.PriceProviderValue,
	strikePercent: 100 | 103 | 105 | 110,
	period: "2w" | "1m" | "2m" | "3m",
): Promise<number | null> {
	const date = getChinaDateCompactString();
	return redisManager.getOptionPrice(
		stockCode,
		provider,
		date,
		strikePercent,
		period,
	);
}

// 导出新的监控和管理功能
export {
	metricsCollector,
	redisManager,
	inkSyncConfig,
	retryHandler,
	lockManager,
};
