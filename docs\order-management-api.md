# 订单管理 API 文档

本文档描述了总控端使用的订单管理 API，包括人工录单、修改订单和结束订单等操作。这些 API 通过开放接口（Open API）暴露，需要使用有效的 API Token 进行认证。

## API 认证

所有 API 请求都需要包含 `api_token` 参数。这个 token 可以在管理平台上生成。

## API 端点

所有 API 端点的基础 URL 为: `/api/open`

### 1. 人工录单

创建一个新的订单记录。

**请求**:
- **方法**: POST
- **URL**: `/api/open/order/manual-create`
- **内容类型**: `application/json`

**请求参数**:

```json
{
  "api_token": "your_api_token",
  "user_id": 1234,
  "ts_code": "600000.SH",
  "entry_price": 10.50,
  "scale": 100,
  "term": 1,
  "quote": 5.25,
  "structure": "100C",
  "comment": "人工录单原因说明",
  "quote_provider": "INK",
  "quote_diff": 0.5
}
```

**参数说明**:
- `api_token`: API 认证 token (必填)
- `user_id`: 用户 ID (必填)
- `ts_code`: 标的代码 (必填)
- `entry_price`: 开仓价 (必填)
- `scale`: 规模，单位为万 (必填)
- `term`: 期限，1表示1个月，2表示2个月，3表示3个月，14表示2周 (必填)
- `quote`: 期权费率，百分比 (必填)
- `structure`: 结构，如 "100C", "103C", "105C", "110C", "100P", "97P", "95P", "90P" (必填)
- `comment`: 录单原因说明 (必填)
- `quote_provider`: 报价提供商，如 "INK", "HAIYING", "YINHE_DERUI" 等 (必填)
- `quote_diff`: 加价费率，百分比 (必填)

**响应**:
- **状态码**: 200 (成功)

```json
{
  "trade_no": "ORD20230615001",
  "user_id": 1234,
  "ts_code": "600000.SH",
  "entry_price": 10.50,
  "scale": 100,
  "term": 1,
  "quote": 5.25,
  "structure": "100C",
  "status": "holding",
  "created_at": "2023-06-15T08:30:00Z",
  "quote_provider": "INK"
}
```

### 2. 修改订单

修改现有订单的信息。

**请求**:
- **方法**: POST
- **URL**: `/api/open/order/manual-update`
- **内容类型**: `application/json`

**请求参数**:

```json
{
  "api_token": "your_api_token",
  "trade_no": "ORD20230615001",
  "newValue": {
    "entry_price": 10.75,
    "scale": 200,
    "term": 2,
    "quote": 5.5,
    "structure": "105C"
  },
  "comment": "修改订单原因说明"
}
```

**参数说明**:
- `api_token`: API 认证 token (必填)
- `trade_no`: 订单号 (必填)
- `newValue`: 新的字段值 (必填)
  - 可包含 `entry_price`, `scale`, `term`, `quote`, `structure` 等字段
  - 不能修改 `user_id` 和 `quote_provider` 字段
- `comment`: 修改原因说明 (必填)

**响应**:
- **状态码**: 200 (成功)

```json
{
  "trade_no": "ORD20230615001",
  "user_id": 1234,
  "ts_code": "600000.SH",
  "entry_price": 10.75,
  "scale": 200,
  "term": 2,
  "quote": 5.5,
  "structure": "105C",
  "status": "holding",
  "created_at": "2023-06-15T08:30:00Z",
  "updated_at": "2023-06-16T10:15:00Z",
  "quote_provider": "INK"
}
```

### 3. 结束订单

手动结束一个订单。

**请求**:
- **方法**: POST
- **URL**: `/api/open/order/manual-close`
- **内容类型**: `application/json`

**请求参数**:

```json
{
  "api_token": "your_api_token",
  "trade_no": "ORD20230615001",
  "settle_price": 11.25,
  "comment": "结束订单原因说明"
}
```

**参数说明**:
- `api_token`: API 认证 token (必填)
- `trade_no`: 订单号 (必填)
- `settle_price`: 结算价 (必填)
- `comment`: 结束原因说明 (必填)

**响应**:
- **状态码**: 200 (成功)

```json
{
  "trade_no": "ORD20230615001",
  "user_id": 1234,
  "ts_code": "600000.SH",
  "entry_price": 10.75,
  "scale": 200,
  "term": 2,
  "quote": 5.5,
  "structure": "105C",
  "status": "sold",
  "settle_price": 11.25,
  "created_at": "2023-06-15T08:30:00Z",
  "updated_at": "2023-06-20T14:45:00Z",
  "quote_provider": "INK"
}
```

### 4. 获取订单详情

获取指定订单的详细信息。

**请求**:
- **方法**: GET
- **URL**: `/api/open/order/details`
- **查询参数**: 
  - `api_token`: API 认证 token (必填)
  - `trade_no`: 订单号 (必填)

**示例请求**:
```
GET /api/open/order/details?api_token=your_api_token&trade_no=ORD20230615001
```

**响应**:
- **状态码**: 200 (成功)

```json
{
  "trade_no": "ORD20230615001",
  "user_id": 1234,
  "ts_code": "600000.SH",
  "entry_price": 10.75,
  "scale": 200,
  "term": 2,
  "quote": 5.5,
  "structure": "105C",
  "status": "holding",
  "created_at": "2023-06-15T08:30:00Z",
  "updated_at": "2023-06-16T10:15:00Z",
  "quote_provider": "INK"
}
```

## 错误处理

API 可能返回以下错误：

### 认证错误
- **状态码**: 401
- **响应**:
```json
{
  "error": "INVALID_TOKEN",
  "message": "API token required"
}
```
或
```json
{
  "error": "INVALID_TOKEN",
  "message": "Invalid API token"
}
```

### 请求错误
- **状态码**: 400
- **响应**:
```json
{
  "error": "BAD_REQUEST",
  "message": "Missing required fields"
}
```

### 订单不存在
- **状态码**: 404
- **响应**:
```json
{
  "error": "NOT_FOUND",
  "message": "Order not found"
}
```

### 订单已关闭
- **状态码**: 400
- **响应**:
```json
{
  "error": "ORDER_ALREADY_CLOSED",
  "message": "Order is already closed"
}
```

### 非法标的代码
- **状态码**: 400
- **响应**:
```json
{
  "error": "INVALID_TS_CODE",
  "message": "Invalid ts_code: 600000"
}
```

## 总控端 UI 开发指南

总控端的订单管理界面应该包含以下功能：

1. **人工录单**:
   - 表单包含用户ID、标的代码、开仓价、规模、期限、期权费率、结构、报价提供商、加价和备注说明等字段
   - 提供标的代码自动补全功能
   - 提供实时价格获取功能
   - 表单提交前进行字段验证
   - 操作确认对话框，显示关键字段的确认信息

2. **修改订单**:
   - 通过订单号搜索订单
   - 显示原始订单信息
   - 允许修改开仓价、规模、期限、期权费率和结构等字段
   - 显示修改前后的字段值对比
   - 要求填写修改原因
   - 修改确认对话框，明确显示变更前后的对比

3. **结束订单**:
   - 通过订单号搜索订单
   - 显示订单的当前状态和基本信息
   - 输入结算价
   - 提供实时价格获取功能
   - 要求填写操作原因
   - 明确的结束确认对话框，提醒操作不可逆

4. **订单修改记录**:
   - 显示所有订单的修改历史记录
   - 包括修改时间、操作类型、订单号、用户ID、操作员ID和修改原因等信息
   - 提供查看修改详情的功能
   - 支持按订单号筛选修改记录

### 界面布局建议

总控端可以采用以下界面布局：

1. **顶部操作区**:
   - 包含"人工录单"、"修改订单"、"结束订单"等操作按钮
   - 搜索框，支持按订单号查询修改记录

2. **操作表单区**:
   - 使用对话框形式展示各种操作表单
   - 表单内容与原管理端保持一致，但需添加API Token字段（可从配置获取）

3. **数据展示区**:
   - 展示订单修改记录表格
   - 分页控件
   - 修改详情对话框

总控端可以复用管理端原有的前端代码，但需要将 API 请求路径从 `/admin/order/*` 更改为 `/api/open/order/*`，并添加 API Token 认证参数。

## 实际实现要点

1. **管理Token权限**:
   - 在总控端配置中存储API Token
   - 定期更新Token以确保安全
   - 可设置不同环境（开发、测试、生产）的不同Token

2. **接口错误处理**:
   - 正确解析服务器返回的错误码和消息
   - 显示友好的错误提示
   - 记录API调用日志，便于问题排查

3. **实时价格获取**:
   - 保持定时刷新的逻辑
   - 处理网络问题可能导致的价格获取失败
   - 清晰展示价格的获取时间

4. **数据验证**:
   - 前端和后端双重验证
   - 对用户输入的数据进行格式和有效性验证
   - 敏感操作（如结束订单）前进行二次确认

## 编码实现建议

1. 创建一个统一的 API 封装模块，处理 API Token 的添加和错误处理。

```typescript
// orderControlApi.ts
import axios from 'axios';
import { ElMessage } from 'element-plus';

// API Token应从安全的配置源获取
const API_TOKEN = 'your_api_token'; 

// 创建axios实例
const apiClient = axios.create({
  baseURL: '/api/open',
  timeout: 10000,
});

// 统一错误处理
const handleApiError = (error: any) => {
  if (error.response) {
    // 服务器返回错误
    const { data, status } = error.response;
    const errorMsg = data.message || '未知错误';
    
    ElMessage.error(`操作失败(${status}): ${errorMsg}`);
    
    // 如果是认证错误，可能需要重新获取token
    if (status === 401) {
      console.error('API Token认证失败，请检查Token配置');
    }
  } else if (error.request) {
    // 请求发出但没有响应
    ElMessage.error('服务器无响应，请稍后重试');
  } else {
    // 请求配置出错
    ElMessage.error(`请求错误: ${error.message}`);
  }
  
  return Promise.reject(error);
};

// 封装API
export const orderControlApi = {
  // 人工录单
  createOrderManually: (data: any) => 
    apiClient.post('/order/manual-create', {
      api_token: API_TOKEN,
      ...data
    }).catch(handleApiError),
    
  // 修改订单
  updateOrderManually: (data: any) => 
    apiClient.post('/order/manual-update', {
      api_token: API_TOKEN,
      ...data
    }).catch(handleApiError),
    
  // 结束订单
  closeOrderManually: (data: any) => 
    apiClient.post('/order/manual-close', {
      api_token: API_TOKEN,
      ...data
    }).catch(handleApiError),
    
  // 获取订单详情
  getOrderDetails: (trade_no: string) => 
    apiClient.get(`/order/details?api_token=${API_TOKEN}&trade_no=${trade_no}`)
      .catch(handleApiError)
};
```

2. 实现股票标的自动补全功能:

```typescript
const handleTsCodeInput = async () => {
  stockSuggestion.value = "";
  realTimePrice.value = null;
  priceUpdateTime.value = null;
  stopPriceRefresh();
  
  if (/^\d{6}$/.test(createForm.value.ts_code)) {
    const fullCode = stockStore.findCompleteCode(createForm.value.ts_code);
    if (fullCode) {
      stockSuggestion.value = stockStore.formatSubject(fullCode);
      startPriceRefresh(fullCode);
    }
  }
};
```

3. 实现实时价格获取功能:

```typescript
const startPriceRefresh = (tsCode: string) => {
  stopPriceRefresh(); // 先停止之前的刷新

  const updatePrice = async () => {
    try {
      const price = await fetchCurrentPrice(tsCode);
      if (price) {
        realTimePrice.value = price;
        priceUpdateTime.value = new Date();
      }
    } catch (error) {
      console.error("Failed to refresh stock price:", error);
    }
  };

  // 立即更新一次
  updatePrice();
  // 每5秒更新一次
  priceRefreshTimer = window.setInterval(updatePrice, 5000);
};

const stopPriceRefresh = () => {
  if (priceRefreshTimer !== null) {
    clearInterval(priceRefreshTimer);
    priceRefreshTimer = null;
  }
};
```

4. 字段变更跟踪:

```typescript
const trackChanges = (field: string, newValue: string | number | boolean | null) => {
  if (!modifyForm.value.originalOrder) return;

  // 不允许修改报价提供商
  if (field === "quote_provider") return;

  const originalValue =
    modifyForm.value.originalOrder[field as keyof OrderData];
  if (newValue !== originalValue) {
    modifyForm.value.changedFields[field] = {
      oldValue: originalValue as string | number | boolean | null,
      newValue: newValue,
    };
  } else {
    delete modifyForm.value.changedFields[field];
  }
};
```

## 注意事项

1. 所有操作都需要完整的参数验证，确保数据的完整性和正确性
2. 必须处理所有可能的错误情况，并向用户提供清晰的错误信息
3. 人工录单和修改订单会影响用户资金和交易数据，需要谨慎操作
4. 所有操作都会记录到系统的修改历史中，包括操作人、操作时间和操作原因
5. API Token 应该妥善保管，不应该暴露给未授权的人员
6. 可考虑实现请求日志记录，便于后续审计和问题排查
7. 总控端建议增加操作权限控制，避免无权限人员进行关键操作 