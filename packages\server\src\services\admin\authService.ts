import { ENV } from "@/config/configManager.js";
import jwt from "jsonwebtoken";
import { AppError } from "@/core/appError.js";
import * as AdminModel from "@/models/admins.js";
import { hashPassword, verifyPassword } from "@/utils/encryption.js";
import { appRedis } from "@/lib/redis.js";
import { REDIS_KEYS } from "@/lib/redis.js";
import logger from "@/utils/logger.js";
import type {
	AdminAuthServiceResponseWithCookie,
	AdminRefreshJWTPayload,
} from "@packages/shared";
import { APP_TYPE } from "@/config/configManager.js";

const JWT_SECRET = ENV.JWT_SECRET;
const ACCESS_TOKEN_EXPIRES = "1h";
const REFRESH_TOKEN_EXPIRES = "1d";
const REFRESH_TOKEN_EXPIRES_MS = 1 * 24 * 60 * 60 * 1000;

export async function login(
	username: string,
	password: string,
): Promise<AdminAuthServiceResponseWithCookie> {
	const admin = await AdminModel.findByUsername(username);
	if (!admin) {
		throw AppError.create("INVALID_ADMIN_CREDENTIALS", "Admin not found");
	}

	const isValid = await verifyPassword(password, admin.password_hash);
	if (!isValid) {
		throw AppError.create(
			"INVALID_ADMIN_CREDENTIALS",
			"Invalid admin credentials",
		);
	}

	// 检查管理员账号是否被禁用
	const isActive = await AdminModel.isAdminActive(admin.admin_id);
	if (!isActive) {
		throw AppError.create(
			"ADMIN_ACCOUNT_DISABLED",
			"Admin account is disabled",
		);
	}

	const accessToken = jwt.sign(
		{
			admin_id: admin.admin_id,
			type: "admin",
			permissions: admin.permissions,
			app_type: APP_TYPE,
		},
		JWT_SECRET,
		{ expiresIn: ACCESS_TOKEN_EXPIRES },
	);

	const refreshToken = jwt.sign(
		{
			admin_id: admin.admin_id,
			type: "admin_refresh",
			app_type: APP_TYPE,
		},
		JWT_SECRET,
		{ expiresIn: REFRESH_TOKEN_EXPIRES },
	);

	return {
		headers: {
			authorization: `Bearer ${accessToken}`,
		},
		body: {
			message: "登录成功",
		},
		cookies: [
			{
				name: "admin_refresh_token",
				value: refreshToken,
				options: {
					httpOnly: true,
					secure: ENV.NODE_ENV === "production",
					sameSite: "strict" as const,
					maxAge: REFRESH_TOKEN_EXPIRES_MS,
				},
			},
		],
	};
}

export async function refreshAdminToken(
	refresh_token: string,
): Promise<AdminAuthServiceResponseWithCookie> {
	try {
		logger.info("[refreshAdminToken] Starting token refresh");
		logger.info("[refreshAdminToken] Checking token blacklist status");

		const isBlacklisted = await isTokenBlacklisted(refresh_token);
		if (isBlacklisted) {
			logger.warn("[refreshAdminToken] Token is blacklisted");
			throw AppError.create("TOKEN_EXPIRED", "Token has been revoked");
		}

		logger.info("[refreshAdminToken] Verifying JWT token");
		const decoded = jwt.verify(
			refresh_token,
			JWT_SECRET,
		) as AdminRefreshJWTPayload;

		if (decoded.type !== "admin_refresh") {
			logger.warn(`[refreshAdminToken] Invalid token type: ${decoded.type}`);
			throw AppError.create("INVALID_TOKEN", "Invalid token type");
		}

		logger.info(
			`[refreshAdminToken] Getting admin info for ID: ${decoded.admin_id}`,
		);
		const admin = await AdminModel.getAdminById(decoded.admin_id);
		if (!admin) {
			logger.warn(
				`[refreshAdminToken] Admin not found for ID: ${decoded.admin_id}`,
			);
			throw AppError.create("ADMIN_NOT_FOUND", "Admin not found");
		}

		logger.info("[refreshAdminToken] Generating new tokens");
		const accessToken = jwt.sign(
			{
				admin_id: admin.admin_id,
				type: "admin",
				permissions: admin.permissions,
				app_type: APP_TYPE,
			},
			JWT_SECRET,
			{ expiresIn: ACCESS_TOKEN_EXPIRES },
		);

		const refreshToken = jwt.sign(
			{
				admin_id: admin.admin_id,
				type: "admin_refresh",
				app_type: APP_TYPE,
			},
			JWT_SECRET,
			{ expiresIn: REFRESH_TOKEN_EXPIRES },
		);

		logger.info("[refreshAdminToken] Token refresh completed successfully");
		return {
			headers: {
				authorization: `Bearer ${accessToken}`,
			},
			body: {
				message: "Token refreshed successfully",
			},
			cookies: [
				{
					name: "admin_refresh_token",
					value: refreshToken,
					options: {
						httpOnly: true,
						secure: ENV.NODE_ENV === "production",
						sameSite: "strict" as const,
						maxAge: REFRESH_TOKEN_EXPIRES_MS,
					},
				},
			],
		};
	} catch (error) {
		logger.warn(error, "[refreshAdminToken] Error during token refresh");
		throw AppError.create("INVALID_TOKEN", "Invalid or expired token");
	}
}

export async function updatePassword(
	admin_id: number,
	oldPassword: string,
	newPassword: string,
) {
	const admin = await AdminModel.getAdminById(admin_id);
	if (!admin) {
		throw AppError.create("ADMIN_NOT_FOUND", "Admin not found");
	}

	const isValid = await verifyPassword(oldPassword, admin.password_hash);
	if (!isValid) {
		throw AppError.create(
			"INVALID_ADMIN_CREDENTIALS",
			"Invalid admin credentials",
		);
	}

	const hashedPassword = await hashPassword(newPassword);
	await AdminModel.updatePassword(admin_id, hashedPassword);
}

export async function logout(refresh_token?: string) {
	try {
		if (refresh_token) {
			try {
				const decoded = jwt.verify(
					refresh_token,
					JWT_SECRET,
				) as AdminRefreshJWTPayload;

				if (decoded.type === "admin_refresh") {
					// Add refresh token to blacklist
					await addToBlacklist(refresh_token, decoded.exp);
				}
			} catch (error) {
				logger.warn(
					`Invalid refresh token during logout: ${error instanceof Error ? error.message : String(error)}`,
				);
			}
		}

		return {
			message: "Logged out successfully",
		};
	} catch (error) {
		if (error instanceof AppError) throw error;
		throw AppError.create("FAILED_LOGOUT", "Failed to logout admin");
	}
}

async function addToBlacklist(token: string, exp: number): Promise<void> {
	try {
		// 1. 先添加新 token（需要等待完成）
		await appRedis.zadd(REDIS_KEYS.TOKEN_BLACKLIST, exp, token);

		// 2. 异步清理过期 token（不等待结果）
		const now = Math.floor(Date.now() / 1000);
		appRedis
			.zremrangebyscore(REDIS_KEYS.TOKEN_BLACKLIST, "-inf", now)
			.catch((err) => {
				logger.error(err, "Error cleaning up admin token blacklist");
			});
	} catch (error) {
		logger.error(error, "Error adding admin token to blacklist");
		throw AppError.create("FAILED_LOGOUT", "Failed to logout admin");
	}
}

async function isTokenBlacklisted(token: string): Promise<boolean> {
	try {
		const now = Math.floor(Date.now() / 1000);
		const score = await appRedis.zscore(REDIS_KEYS.TOKEN_BLACKLIST, token);
		return score !== null && Number(score) > now;
	} catch (error) {
		logger.error(error, "Error checking admin token blacklist");
		throw AppError.create(
			"FAILED_VERIFY_TOKEN_STATUS",
			"Failed to verify token status",
		);
	}
}
