import prisma from "@/lib/prisma.js";
import type { Prisma, inquiry_status } from "@prisma/client";
import { AppError } from "@/core/appError.js";
import type {
	InquiryData,
	InquiryStatus,
	StructureType,
} from "@packages/shared";

interface PrismaInquiry {
	inquiry_id: number;
	user_id: number;
	ts_code: string;
	scale: number;
	term: number;
	structure: string;
	quote: Prisma.Decimal;
	status: inquiry_status;
	created_at: Date | null;
	external_quotes?: Prisma.JsonValue;
	quote_diffs?: Prisma.JsonValue;
}

function transformInquiryData(inquiry: PrismaInquiry): InquiryData {
	return {
		...inquiry,
		structure: inquiry.structure as StructureType,
		quote: Number(inquiry.quote),
		status: inquiry.status as InquiryStatus,
		created_at: inquiry.created_at?.toISOString() ?? "",
		external_quotes: inquiry.external_quotes as InquiryData["external_quotes"],
		quote_diffs: inquiry.quote_diffs as InquiryData["quote_diffs"],
	};
}

export async function create(
	data: Omit<InquiryData, "inquiry_id" | "created_at">,
): Promise<InquiryData> {
	try {
		const inquiry = await prisma.inquiries.create({
			data,
		});

		return transformInquiryData(inquiry);
	} catch (error) {
		throw AppError.create(
			"SERVER_ERROR",
			`Error creating inquiry: ${error instanceof Error ? error.message : "Unknown error"}`,
		);
	}
}

export async function getByInquiryId(inquiry_id: number): Promise<InquiryData> {
	const inquiry = await prisma.inquiries.findUnique({
		where: { inquiry_id },
	});

	if (!inquiry) {
		throw AppError.create(
			"NOT_FOUND",
			`Inquiry with ID ${inquiry_id} not found`,
		);
	}

	return transformInquiryData(inquiry);
}

export async function countByUserId(
	user_id: number,
	filters?: {
		ts_codes?: string[];
	},
): Promise<number> {
	return prisma.inquiries.count({
		where: {
			user_id,
			...(filters?.ts_codes?.length
				? { ts_code: { in: filters.ts_codes } }
				: {}),
		},
	});
}

export async function getByUserId(
	user_id: number,
	offset: number,
	limit: number,
	isDescending: boolean,
	filters?: {
		ts_codes?: string[];
	},
): Promise<InquiryData[]> {
	const inquiries = await prisma.inquiries.findMany({
		where: {
			user_id,
			...(filters?.ts_codes?.length
				? { ts_code: { in: filters.ts_codes } }
				: {}),
		},
		orderBy: { created_at: isDescending ? "desc" : "asc" },
		skip: offset,
		take: limit,
	});

	return inquiries.map(transformInquiryData);
}

export async function deleteAll(): Promise<void> {
	await prisma.$transaction([
		// 先删除旧数据
		prisma.inquiries.deleteMany(),
		// 然后重置序列
		prisma.$queryRaw`ALTER SEQUENCE inquiries_inquiry_id_seq RESTART WITH 1;`,
	]);
}

export async function getAllTsCodes(user_id: number): Promise<string[]> {
	const inquiries = await prisma.inquiries.findMany({
		where: { user_id },
		distinct: ["ts_code"],
		select: { ts_code: true },
		orderBy: { ts_code: "asc" },
	});

	return inquiries.map((inquiry) => inquiry.ts_code);
}

export async function updateStatus(
	inquiry_id: number,
	status: InquiryStatus,
): Promise<InquiryData> {
	const inquiry = await prisma.inquiries.update({
		where: { inquiry_id },
		data: { status },
	});

	if (!inquiry) {
		throw AppError.create(
			"NOT_FOUND",
			`Inquiry with ID ${inquiry_id} not found`,
		);
	}

	return transformInquiryData(inquiry);
}

export interface InquiryQueryOptions {
	page?: number;
	pageSize?: number;
	sortBy?: string;
	sortOrder?: "ASC" | "DESC";
	filters?: {
		user_id?: number;
		ts_codes?: string[];
		startDate?: string;
		endDate?: string;
		// Add more filters as needed
	};
}

export async function getAll(
	options: InquiryQueryOptions = {},
): Promise<{ total: number; items: InquiryData[] }> {
	const {
		page = 1,
		pageSize = 10,
		sortBy = "created_at",
		sortOrder = "DESC",
		filters = {},
	} = options;

	const [total, items] = await Promise.all([
		prisma.inquiries.count({
			where: {
				user_id: filters.user_id,
				ts_code: filters.ts_codes?.length
					? { in: filters.ts_codes }
					: undefined,
			},
		}),
		prisma.inquiries.findMany({
			where: {
				user_id: filters.user_id,
				ts_code: filters.ts_codes?.length
					? { in: filters.ts_codes }
					: undefined,
			},
			orderBy: [{ [sortBy]: sortOrder.toLowerCase() }, { created_at: "desc" }],
			skip: (page - 1) * pageSize,
			take: pageSize,
		}),
	]);

	return {
		total,
		items: items.map(transformInquiryData),
	};
}

export async function getAllByApi(): Promise<InquiryData[]> {
	const inquiries = await prisma.inquiries.findMany({
		orderBy: { inquiry_id: "asc" },
	});
	return inquiries.map(transformInquiryData);
}

export async function getTotalCount(): Promise<number> {
	return await prisma.inquiries.count();
}
