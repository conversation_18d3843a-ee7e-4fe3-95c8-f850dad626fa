import type { OrderStatus } from "./order";

export enum NotificationType {
	SYSTEM = "system",
	FEATURE = "feature",
	ACCOUNT = "account",
	ORDER = "order",
}

export type TargetType = "system" | "personal";

export type NotificationMetadataType =
	| "qualification" // 资质审核相关
	| "fund_transfer" // 资金转账相关
	| "pending_failed" // 挂单执行失败
	| "knockout" // 强制敲出
	| "expiry"; // 到期日提醒

export interface NotificationMetadata {
	type: NotificationMetadataType;
	// 出入金、转账相关
	formattedAmount?: string;
	operation?: string;
	receiver_name?: string;
	receiver_phone?: string;
	sender_name?: string;
	sender_phone?: string;
	// 资质审核相关
	name?: string;
	email?: string;
	phone_number?: string;
	bank_name?: string;
	// 挂单执行失败
	trade_no?: string;
	ts_code?: string;
	status?: OrderStatus;
	vwap_price?: number;
	limit_price?: number;
	exercise_price?: number;
}

export interface NotificationData {
	notif_id?: number;
	user_id?: number;
	title: string;
	content: string;
	type: NotificationType;
	target_type: TargetType;
	is_read: boolean;
	metadata?: NotificationMetadata;
	created_at?: Date;
}

export interface NotificationGroup {
	date: string;
	notifications: NotificationData[];
}

export interface NotificationOptions {
	title: string;
	content: string;
	type: NotificationType;
	metadata?: NotificationMetadata;
}

export const welcomeNotificationOptions: NotificationOptions = {
	title: "Welcome to Our Platform",
	content:
		"Thank you for joining us! We are excited to have you here. Feel free to explore our features and reach out if you need any assistance.",
	type: NotificationType.SYSTEM,
};
