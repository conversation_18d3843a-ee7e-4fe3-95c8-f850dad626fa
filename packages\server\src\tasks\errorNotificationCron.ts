import cron from "node-cron";
import { ENV, APP_CONFIG } from "@/config/configManager.js";
import logger, { getLastErrorTime } from "@/utils/logger.js";
import emailService from "@/utils/email.js";
import { notificationThrottle } from "@/utils/notificationThrottle.js";

/**
 * 错误日志监控和邮件通知服务
 * 每30分钟检查是否有新的错误时间，如果有错误，发送通知给开发人员
 */

export class ErrorNotificationCron {
	private static instance: ErrorNotificationCron;
	// 上次检查时间
	private lastCheckTime = Date.now();
	// 错误通知的时间间隔: 30分钟

	// typescript will auto privatize constructor

	public static getInstance(): ErrorNotificationCron {
		if (!ErrorNotificationCron.instance) {
			ErrorNotificationCron.instance = new ErrorNotificationCron();
		}
		return ErrorNotificationCron.instance;
	}

	public start(): void {
		try {
			// 每30分钟执行一次
			cron.schedule(
				"*/30 * * * *",
				async () => {
					await this.checkForErrors();
				},
				{
					timezone: "Asia/Shanghai",
				},
			);

			logger.info("Error notification cron job started");
		} catch (error) {
			logger.error(error, "Failed to start error notification cron job");
			throw error;
		}
	}

	// 发送错误日志通知到开发人员邮箱
	private async sendErrorNotification(): Promise<void> {
		// 检查是否配置了开发人员邮箱
		const developerEmail = ENV.DEVELOPER_EMAIL;
		if (!developerEmail) {
			logger.warn("No developer email configured for error notifications");
			return;
		}

		try {
			const message = `系统在过去30分钟内检测到错误，请检查错误日志。
最近一次错误发生时间: ${new Date(await getLastErrorTime()).toLocaleString(
				"zh-CN",
				{
					timeZone: "Asia/Shanghai",
				},
			)}`;

			// 为每个开发人员邮箱发送通知
			// 使用节流机制，确保同一类型的错误不会频繁发送
			const shouldSend = await notificationThrottle.shouldSend(
				developerEmail,
				"ERROR_NOTIFICATION",
				"system_errors",
				// 使用当前日期作为key的一部分，确保每天至少发送一次
				new Date().toISOString().split("T")[0],
			);

			if (shouldSend) {
				await emailService.sendEmail(
					developerEmail,
					"ERROR_NOTIFICATION",
					{
						errorTime: new Date(await getLastErrorTime()).toLocaleString(
							"zh-CN",
							{
								timeZone: "Asia/Shanghai",
							},
						),
						appId: APP_CONFIG.tradingPlatformId || APP_CONFIG.channelId || "",
					},
					{
						attachments: [
							{
								filename: "error_notification.txt",
								content: Buffer.from(message),
								contentType: "text/plain",
							},
						],
					},
				);

				logger.info(`Error notification sent to ${developerEmail}`);
			} else {
				logger.debug(`Error notification throttled for ${developerEmail}`);
			}
		} catch (error) {
			logger.error(error, "Failed to send error notification email");
		}
	}

	// 检查是否有最近的错误
	private async checkForErrors(): Promise<void> {
		try {
			const now = Date.now();

			// 检查上次检查后是否有新的错误
			if ((await getLastErrorTime()) > this.lastCheckTime) {
				logger.info(
					"Detected new errors since last check, sending notification",
				);
				await this.sendErrorNotification();
			} else {
				logger.debug("No new errors detected since last check");
			}

			// 更新上次检查时间
			this.lastCheckTime = now;
		} catch (error) {
			logger.error(error, "Failed to check for errors");
		}
	}

	// 手动检查错误，供外部调用
	public async manualCheck(): Promise<void> {
		await this.checkForErrors();
	}
}

// 导出单例实例
export const errorNotificationCron = ErrorNotificationCron.getInstance();
