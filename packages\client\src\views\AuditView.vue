<template>
  <div class="audit-view view">
    <!-- 只在加载完成后显示内容 -->
    <div v-if="!isLoading && !isQualified && !pendingQualification" class="qualification card">
      <div class="card-header">
        <div class="card-title">资格认证</div>
      </div>

      <div class="qualification-form">
        <!-- Basic Information -->
        <div class="form-section">
          <h3>基本信息</h3>
          <div class="form-content form-grid">
            <div class="form-group">
              <label for="name">姓名/公司名</label>
              <input id="name" v-model="qualificationData.name" type="text" @input="handleNameInput"
                @change="handleFormChange" required :disabled="isSubmitted" />
            </div>
            <div class="form-group">
              <label for="idNumber">身份证号/登记证号</label>
              <input id="idNumber" v-model="qualificationData.id_number" type="text" placeholder="请输入身份证号或登记证号" required
                @change="handleFormChange" :disabled="isSubmitted" />
            </div>
            <div class="form-group">
              <label for="phoneNumber">手机号</label>
              <input id="phoneNumber" v-model="qualificationData.phone_number" type="tel" placeholder="请输入手机号" required
                :disabled="isSubmitted" @change="handleFormChange" />
            </div>
            <div class="form-group">
              <label for="bankName">银行名称</label>
              <input id="bankName" v-model="qualificationData.bank_name" type="text" placeholder="请输入开户银行名称" required
                :disabled="isSubmitted" />
            </div>
            <div class="form-group">
              <label for="bankCode">银行编号</label>
              <input id="bankCode" v-model="qualificationData.bank_code" type="text" maxlength="3" pattern="[0-9]{3}"
                placeholder="请输入3位数银行编号" required :disabled="isSubmitted" @input="handleBankCodeInput" />
            </div>
            <div class="form-group">
              <label for="bankAccount">银行账号</label>
              <input id="bankAccount" v-model="qualificationData.bank_account" type="text" placeholder="请输入银行账号"
                required :disabled="isSubmitted" />
            </div>
          </div>
        </div>

        <!-- Document Upload -->
        <div class="form-section">
          <h3>证件上传</h3>
          <div class="form-content">
            <div class="upload-area">
              <el-upload v-model:file-list="uploadFiles.documents" :disabled="isSubmitted" accept=".pdf,.jpg,.jpeg,.png"
                :auto-upload="false" multiple @change="handleFileChange">
                <div class="upload-trigger">
                  <el-icon>
                    <Upload />
                  </el-icon>
                  <div class="upload-text">
                    <span>上传证件</span>
                    <span class="upload-hint">
                      请上传PDF、JPG、JPEG或PNG格式的文件：
                      个人用户：身份证/港澳通行证/护照
                      企业用户：营业执照/香港商业登记证书
                    </span>
                  </div>
                </div>
              </el-upload>
            </div>
          </div>
        </div>

        <!-- Declaration -->
        <div class="form-section">
          <h3>协议确认</h3>
          <div class="form-content">
            <div class="declaration-files">
              <div class="declaration-label">协议文件：</div>
              <div class="declaration-links">
                <a href="#" @click.prevent="openAgreement(AGREEMENT_PATHS.personalForm)">
                  个人开户表格
                </a>
                <a href="#" @click.prevent="openAgreement(AGREEMENT_PATHS.corporateForm)">
                  公司开户表格
                </a>
                <a href="#" @click.prevent="openAgreement(AGREEMENT_PATHS.boardResolution)">
                  董事会授权决议
                </a>
                <a href="#" @click.prevent="openAgreement(AGREEMENT_PATHS.investorStatement)">
                  专业投资者声明
                </a>
                <a href="#" @click.prevent="openAgreement(AGREEMENT_PATHS.w8ben)">
                  W8BEN表格
                </a>
                <a href="#" @click.prevent="openAgreement(AGREEMENT_PATHS.taxResidency)">
                  税务自我证明表格
                </a>
                <a href="#" @click.prevent="openAgreement(AGREEMENT_PATHS.selfCertification)">
                  自我证明表格
                </a>
                <a href="#" @click.prevent="openAgreement(AGREEMENT_PATHS.masterAgreement)">
                  ISDA主协议及附约
                </a>
                <a href="#" @click.prevent="openAgreement(AGREEMENT_PATHS.supplementalAgreement)">
                  2002主协议补充协议
                </a>
              </div>
            </div>
            <div class="declaration-text">
              本人/本机构已充分阅读并完全理解上述场外衍生品协议的内容，了解场外衍生品的投资风险并自愿承担。本人/本机构自证为场外衍生品投资合格者，所用于投资的款项皆为合法的自有资金，并自愿签署上述全部协议。
            </div>
            <div class="declaration-confirm">
              <el-checkbox v-model="qualificationData.declarationAccepted" :disabled="isSubmitted"
                @change="handleFormChange">
                我已阅读并同意上述声明
              </el-checkbox>
            </div>
          </div>
        </div>

        <!-- Electronic Signature -->
        <div class="form-section" v-if="!isSubmitted">
          <h3>电子签名</h3>
          <div class="form-content">
            <div class="signature-preview" @click="openSignatureDialog">
              <div v-if="qualificationData.signature" class="preview-image">
                <img :src="qualificationData.signature" alt="signature" />
              </div>
              <div v-else class="empty-signature">
                <i class="el-icon-edit"></i>
                <span>点击签名</span>
              </div>
            </div>

            <!-- 签名弹窗 -->
            <el-dialog v-model="showSignatureDialog" title="电子签名" width="650px" :destroy-on-close="true">
              <div class="signature-pad">
                <Vue3Signature ref="signaturePad" :sigOption="signatureOptions" :w="'600px'" :h="'300px'"
                  class="signature-canvas" />
              </div>
              <template #footer>
                <div class="dialog-footer">
                  <el-button @click="undoSignature">撤销</el-button>
                  <el-button @click="clearSignature">重新签名</el-button>
                  <el-button type="primary" @click="confirmSignature">确认签名</el-button>
                </div>
              </template>
            </el-dialog>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="form-actions" v-if="!isSubmitted">
          <button class="primary" :disabled="isSubmitting" @click="handleQualificationSubmit">
            提交
          </button>
        </div>
      </div>

      <!-- Status Display -->
      <div class="status-section" v-if="isSubmitted">
        <div class="status-badge" :class="qualificationStatus">
          {{ formatAuditStatus(qualificationStatus) }}
        </div>
        <div class="status-message" v-if="qualificationComment">
          {{ qualificationComment }}
        </div>
      </div>
    </div>

    <!-- 待审核记录板块 - 仅显示资质审核 -->
    <div v-else-if="pendingQualification" class="pending-audits card">
      <div class="card-header">
        <div class="card-title">待确认</div>
      </div>

      <div class="audit-list">
        <!-- 资质审核记录 -->
        <div class="audit-item qualification-audit">
          <div class="audit-type">资格认证</div>
          <div class="audit-status pending">待审核</div>
          <div class="audit-date">
            {{ formatDate(pendingQualification.created_at) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { onBeforeRouteLeave } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { Upload } from "@element-plus/icons-vue";
import { auditApi, fileApi } from "@/api";
import { getErrorMessage } from "@packages/shared";
import { AuditStatus } from "@packages/shared";
import type {
  QualificationData,
  QualificationAuditData,
} from "@packages/shared";
import type { UploadUserFile } from "element-plus";
import { useAuthStore } from "@/stores/auth";

const auth = useAuthStore();

// Qualification State
const qualificationData = ref<QualificationData>({
  name: "",
  id_number: "",
  phone_number: "",
  bank_name: "",
  bank_code: "",
  bank_account: "",
  documents: [],
  declarationAccepted: false,
  signature: null,
});

// 更新类型定义
interface SignaturePadRef {
  clear: () => void;
  undo: () => void;
  save: (type?: string) => string;
  isEmpty: () => boolean;
  fromDataURL: (dataUrl: string) => void;
}

const signaturePad = ref<SignaturePadRef>();
const signatureOptions = {
  penColor: "rgb(0, 0, 0)",
  backgroundColor: "rgba(0, 0, 0, 0)",
  minWidth: 2, // 最小笔画宽度
  maxWidth: 4, // 最大笔画宽度
  velocityFilterWeight: 0.7, // 根据书写速度调整笔画宽度的权重
};

const isSubmitting = ref(false);
const isSubmitted = ref(false);
const isQualified = ref(false);
const qualificationStatus = ref<AuditStatus>(AuditStatus.PENDING);
const qualificationComment = ref("");

// 待审核状态
const pendingQualification = ref<QualificationAuditData | null>(null);

// 添加新的响应式变量用于存储上传文件列表
const uploadFiles = ref({
  documents: [] as UploadUserFile[],
});

// 添加表单是否被修改的状态
const isFormDirty = ref(false);

// 监听表单数据变化
const handleFormChange = () => {
  if (!isSubmitted.value) {
    isFormDirty.value = true;
  }
};

// 添加路由离开守卫
onBeforeRouteLeave(async (_to, _from, next) => {
  if (isFormDirty.value && !isSubmitted.value) {
    try {
      await ElMessageBox.confirm("您有未保存的更改，确定要离开吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });
      next();
    } catch {
      next(false);
    }
  } else {
    next();
  }
});

const handleQualificationSubmit = async () => {
  try {
    isSubmitting.value = true;
    if (!validateQualificationData()) {
      return;
    }

    // 上传所有文件并获取 uid
    const uploadTasks = [];
    for (const type of ["documents"] as const) {
      const files = uploadFiles.value[type];
      if (files?.length) {
        for (const file of files) {
          if (file.raw) {
            const formData = new FormData();
            formData.append("file", file.raw);
            uploadTasks.push(
              fileApi.uploadFile(formData).then((response) => ({
                type,
                uid: response?.uid,
                name: file.name,
              })),
            );
          }
        }
      }
    }

    // 等待所有文件上传完成
    const uploadResults = await Promise.all(uploadTasks);

    // 更新 qualificationData 中的 proofFiles
    qualificationData.value.documents = uploadResults
      .filter((r) => r.type === "documents")
      .map(({ uid, name }) => ({ uid: uid || "", name }));

    // 提交数据
    await auditApi.createQualificationAudit({ data: qualificationData.value });

    isFormDirty.value = false; // 重置表单状态
    isSubmitted.value = true;
    qualificationStatus.value = AuditStatus.PENDING;
    ElMessage.success("提交成功");
    await initializeStatus(); // 刷新状态
  } catch (error) {
    console.error("Failed to create qualification audit:", error);
    ElMessage.error(`提交失败：${getErrorMessage(error)}`);
  } finally {
    isSubmitting.value = false;
  }
};

const validateQualificationData = () => {
  const {
    name,
    id_number,
    phone_number,
    bank_name,
    bank_code,
    bank_account,
    declarationAccepted,
  } = qualificationData.value;

  if (
    !name ||
    !id_number ||
    !phone_number ||
    !bank_name ||
    !bank_code ||
    !bank_account
  ) {
    ElMessage.warning("请填写所有必填项");
    return false;
  }

  // 验证邮箱或手机号格式
  if (!validatePhoneNumber(phone_number)) {
    ElMessage.warning("请输入有效的手机号");
    return false;
  }

  if (!uploadFiles.value.documents.length) {
    ElMessage.warning("请上传所需证件");
    return false;
  }

  if (!declarationAccepted) {
    ElMessage.warning("请确认协议内容");
    return false;
  }

  if (!qualificationData.value.signature) {
    ElMessage.warning("请先完成签名");
    return false;
  }

  return true;
};

// 添加手机号验证函数
const validatePhoneNumber = (phoneNumber: string): boolean => {
  const pattern = /^1[3-9]\d{9}$/;
  return pattern.test(phoneNumber);
};

// 签名相关
const showSignatureDialog = ref(false);
const tempSignature = ref<string | null>(null);

const openSignatureDialog = () => {
  showSignatureDialog.value = true;
  if (tempSignature.value) {
    nextTick(() => {
      if (tempSignature.value) {
        signaturePad.value?.fromDataURL(tempSignature.value);
      }
    });
  }
};

const confirmSignature = () => {
  if (signaturePad.value?.isEmpty()) {
    ElMessage.warning("请先完成签名");
    return;
  }
  const signatureData = signaturePad.value?.save("image/png") || null;
  qualificationData.value.signature = signatureData;
  tempSignature.value = signatureData;
  showSignatureDialog.value = false;
  handleFormChange();
};

const clearSignature = () => {
  signaturePad.value?.clear();
};

const undoSignature = () => {
  signaturePad.value?.undo();
};

// 添加 loading 状态
const isLoading = ref(true);

// 修改初始化函数
const initializeStatus = async () => {
  try {
    isQualified.value = auth.isQualified || false;
    const pendingAudits = await auditApi.getPendingAudits();
    pendingQualification.value = pendingAudits?.qualification || null;
  } catch (error) {
    ElMessage.error("获取审核记录失败");
    console.error("Failed to load audit status:", error);
  } finally {
    isLoading.value = false;
  }
};

const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString();
};

const formatAuditStatus = (status: string) => {
  const statuses: Record<string, string> = {
    pending: "待审核",
    approved: "已通过",
    rejected: "已拒绝",
  };
  return statuses[status] || status;
};

// Initialize
onMounted(async () => {
  await initializeStatus();
});

// Add a method to force name to uppercase
const handleNameInput = (event: Event) => {
  const input = event.target as HTMLInputElement;
  // 允许中文和英文字母、空格和常用符号
  input.value = input.value.replace(/[^\u4e00-\u9fa5a-zA-Z\s\-\.&()]+/g, "");
  // 转换英文为大写，保留中文（大陆客户）
  qualificationData.value.name = input.value.replace(/[a-zA-Z]+/g, (match) =>
    match.toUpperCase(),
  );
  handleFormChange();
};

// 协议路径常量
const AGREEMENT_PATHS = {
  personalForm: "/agreements/001-个人开户表格.pdf",
  corporateForm: "/agreements/001-1-公司开户表格.pdf",
  boardResolution: "/agreements/001-2-董事会授权决议.pdf",
  investorStatement: "/agreements/002-专业投资者声明.pdf",
  w8ben: "/agreements/003-w8ben表格.pdf",
  taxResidency: "/agreements/004-税务自我证明表格.pdf",
  selfCertification: "/agreements/004-2-自我证明表格.pdf",
  masterAgreement: auditApi.getIsdaMasterAgreementUrl(),
  supplementalAgreement: auditApi.getIsdaSupplementAgreementUrl(),
} as const;

// 简单的打开方法
const openAgreement = (path: string) => {
  globalThis.window.open(path, "_blank");
};

// 添加银行编号输入处理函数
const handleBankCodeInput = (event: Event) => {
  const input = event.target as HTMLInputElement;
  // 只允许输入数字
  input.value = input.value.replace(/[^0-9]/g, "");
  // 限制长度为3位
  if (input.value.length > 3) {
    input.value = input.value.slice(0, 3);
  }
  qualificationData.value.bank_code = input.value;
  handleFormChange();
};

// 修改文件上传变更检测
const handleFileChange = () => {
  handleFormChange();
};
</script>

<style scoped>
.qualification-form {
  padding: 20px 32px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section h3 {
  margin-bottom: 16px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.form-content {
  padding: 0 16px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
  gap: 20px;
  padding: 20px;
}

.form-group {
  margin: 0;
  display: grid;
  grid-template-columns: 120px 1fr;
  align-items: center;
  gap: 12px;
}

.form-group label {
  color: var(--el-text-color-regular);
  font-size: 14px;
  text-align: right;
  white-space: nowrap;
}

.form-group input {
  width: 100%;
  box-sizing: border-box;
  padding: 8px 12px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  background-color: var(--el-input-bg-color);
  color: var(--el-text-color-primary);
  transition: all 0.3s;

  &:focus {
    border-color: var(--el-color-primary);
    outline: none;
    box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
  }

  &:disabled {
    background-color: var(--el-disabled-bg-color);
    cursor: not-allowed;
    opacity: 0.7;
  }

  &::placeholder {
    color: var(--el-text-color-placeholder);
  }
}

.criteria-item {
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid var(--el-border-color);
}

.criteria-proof {
  margin-top: 16px;
  padding: 0 24px;
}

.upload-area {
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
  transition: all 0.3s;
  padding: 16px;

  &:hover {
    border-color: var(--el-color-primary);
  }
}

.upload-trigger {
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.upload-text {
  text-align: center;
}

.upload-hint {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.signature-preview {
  width: 200px;
  height: 100px;
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &:hover {
    border-color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
  }
}

.empty-signature {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--el-text-color-secondary);
}

.preview-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.signature-pad {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  margin-top: 16px;
}

.status-section {
  text-align: center;
  padding: 20px;
}

.status-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: 500;

  &.pending {
    background-color: var(--el-color-warning-light);
    color: var(--el-color-warning);
  }

  &.approved {
    background-color: var(--el-color-success-light);
    color: var(--el-color-success);
  }

  &.rejected {
    background-color: var(--el-color-error-light);
    color: var(--el-color-error);
  }
}

.status-message {
  margin-top: 10px;
  color: var(--el-text-color-secondary);
}

.audit-list {
  padding: 20px;
}

.audit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--el-border-color);
  gap: 20px;

  &:last-child {
    border-bottom: none;
  }
}

.audit-type {
  font-weight: 500;
}

.audit-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;

  &.pending {
    background-color: var(--el-color-warning-light);
    color: var(--el-color-warning);
  }
}

.audit-date {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.qualification-audit {
  background-color: var(--el-color-warning-lighter);
}

.signature-canvas {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  background-color: white;
}

.upload-hint {
  white-space: pre-line;
}

.declaration-files {
  margin-bottom: 16px;
}

.declaration-label {
  font-weight: 500;
  margin-bottom: 8px;
}

.declaration-links {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.declaration-text {
  margin: 16px 0;
  padding: 16px;
  background-color: var(--el-color-info-light);
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
}

.declaration-confirm {
  margin-top: 16px;
}

.loading-container {
  padding: 20px;
}
</style>
