<template>
	<div class="qualify-panel">
		<div class="header-container">
			<div class="header-controls">
				<div class="refresh-controls">
					<span class="last-refresh">上次刷新: {{ lastRefreshTime }}</span>
					<el-select v-model="refreshInterval" class="refresh-select" size="small">
						<el-option label="30秒" :value="30" />
						<el-option label="1分钟" :value="60" />
						<el-option label="5分钟" :value="300" />
					</el-select>
					<el-button size="small" :loading="loading" @click="refreshData" :icon="Refresh">
					</el-button>
				</div>
				<div class="controls">
					<el-button :loading="exporting" @click="exportData">
						<el-icon>
							<Download />
						</el-icon>
						导出数据
					</el-button>
				</div>
			</div>
		</div>
		<!-- 标签页切换 -->
		<el-tabs v-model="activeTab" class="dark-tabs">
			<el-tab-pane v-for="status in ['pending', 'approved', 'rejected']" :key="status" :label="getStatusLabel(status)"
				:name="status">
				<div class="audit-list">
					<el-table v-loading="loading" :data="currentAudits" style="width: 100%" :empty-text="loading ? ' ' : '暂无数据'"
						@sort-change="handleSortChange">
						<el-table-column prop="audit_id" label="审核ID" min-width="80" sortable="custom" />
						<el-table-column prop="user_id" label="用户ID" min-width="100" sortable="custom">
							<template #default="{ row }">
								{{ row.user_id }}
							</template>
						</el-table-column>
						<el-table-column prop="created_at" label="创建时间" min-width="180" sortable="custom">
							<template #default="{ row }">
								{{ formatDate(row.created_at) }}
							</template>
						</el-table-column>
						<el-table-column label="详情" min-width="200">
							<template #default="{ row }">
								<div class="audit-details">
									<el-button type="primary" size="small" @click.stop="showQualificationDetails(row)">
										查看详情
									</el-button>
								</div>
							</template>
						</el-table-column>

						<el-table-column v-if="status === 'pending'" label="操作" min-width="100">
							<template #default="{ row }">
								<el-button-group>
									<el-button type="success" size="small" @click.stop="handleAudit(row, AuditStatus.APPROVED)">
										通过
									</el-button>
									<el-button type="danger" size="small" @click.stop="handleAudit(row, AuditStatus.REJECTED)">
										拒绝
									</el-button>
								</el-button-group>
							</template>
						</el-table-column>
					</el-table>

					<div class="pagination-container">
						<el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
							:page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next" :total="total"
							@size-change="handleSizeChange" @current-change="handleCurrentChange" />
					</div>

					<el-empty v-if="!loading && (!currentAudits || currentAudits.length === 0)" description="暂无审核请求" />
				</div>
			</el-tab-pane>
		</el-tabs>

		<!-- 审核确认对话框 -->
		<el-dialog v-model="dialogVisible" :title="dialogTitle" width="30%"
			:class="['dark-dialog', `dialog-${pendingAction?.toLowerCase()}`]" @close="dialogVisible = false"
			@closed="resetDialogState">
			<el-form>
				<el-form-item label="备注">
					<el-input v-model="auditComment" type="textarea" :rows="3" placeholder="请输入审核备注" />
				</el-form-item>
				<el-form-item v-if="pendingAction === AuditStatus.APPROVED">
					<el-checkbox v-model="downloadDocuments">下载证明文件</el-checkbox>
				</el-form-item>
			</el-form>
			<template #footer>
				<span>
					<el-button @click="cancelAudit">取消</el-button>
					<el-button :type="pendingAction === AuditStatus.APPROVED ? 'success' : 'danger'" @click="confirmAudit"
						:loading="processing">
						{{ pendingAction === AuditStatus.APPROVED ? '通过' : '拒绝' }}
					</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 资质详情对话框 -->
		<qualification-details-dialog v-model="qualificationDialogVisible" :data="currentQualification" />

		<!-- 批量下载文件区域 -->
		<el-dialog v-model="batchDownloadVisible" title="批量下载" width="30%" destroy-on-close>
			<div>
				<p>选择要批量下载的内容：</p>
				<el-checkbox v-model="batchDownloadDocuments">证明文件</el-checkbox>
			</div>
			<template #footer>
				<span>
					<el-button @click="batchDownloadVisible = false">取消</el-button>
					<el-button type="primary" @click="doBatchDownload" :loading="batchDownloading"
						:disabled="!batchDownloadDocuments">
						开始下载
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import {
	ref,
	computed,
	onMounted,
	watch,
	onUnmounted,
	onBeforeUnmount,
} from "vue";
import { qualifyApi } from "@/api";
import { ElMessage, ElMessageBox } from "element-plus";
import { AuditStatus, AuditType } from "@packages/shared";
import type {
	QualificationData,
	QualificationAuditData,
} from "@packages/shared";
import { Refresh, Download } from "@element-plus/icons-vue";
import QualificationDetailsDialog from "@/components/QualificationDetailsDialog.vue";
import { formatDate, formatTime } from "@/utils/format";
import { exportToCsv } from "@/utils/export";
import { showAuditNotification } from "@/utils/notification";
import { downloadFilesAsZip } from "@/utils/file-utils";

// 状态管理
const activeTab = ref("pending");
const loading = ref(false);
const processing = ref(false);
const dialogVisible = ref(false);
const auditComment = ref("");
const currentAudit = ref<QualificationAuditData | null>(null);
const pendingAction = ref<AuditStatus.APPROVED | AuditStatus.REJECTED | null>(
	null,
);

// 资质详情对话框状态
const qualificationDialogVisible = ref(false);
const currentQualification = ref<QualificationData | null>(null);

// 刷新相关的状态
const refreshInterval = ref(30); // 刷新间隔（秒）
const lastRefreshTime = ref(formatTime(new Date()));
const refreshTimer = ref<ReturnType<typeof setInterval> | null>(null);

// 分页相关的状态
const currentPage = ref(1);
const pageSize = ref(10);
const sortBy = ref("created_at");
const sortOrder = ref<"ASC" | "DESC">("DESC");
const total = ref(0);

// 导出状态
const exporting = ref(false);

// 上次计数状态
const lastPendingCount = ref(0);

// 文件下载相关状态
const downloadDocuments = ref(localStorage.getItem('download_documents') === 'true');

// 批量下载状态
const batchDownloadVisible = ref(false);
const batchDownloadDocuments = ref(false);
const batchDownloading = ref(false);

// 监听文件下载设置变化，保存到localStorage
watch(downloadDocuments, (newVal) => {
	localStorage.setItem('download_documents', newVal.toString());
});

// 显示资质详情
const showQualificationDetails = (audit: QualificationAuditData) => {
	if (audit.type === AuditType.QUALIFICATION) {
		console.log("audit:", audit);
		currentQualification.value = audit.data;
		qualificationDialogVisible.value = true;
	}
};

// 添加批量下载入口
const showBatchDownloadDialog = () => {
	batchDownloadVisible.value = true;
	batchDownloadDocuments.value = true;
};

// 执行批量下载
const doBatchDownload = async () => {
	if (!batchDownloadDocuments.value) {
		ElMessage.warning("请选择至少一种下载内容");
		return;
	}

	batchDownloading.value = true;

	try {
		// 获取当前标签页的数据
		const currentTabAudits = audits.value[activeTab.value as AuditStatus] || [];

		if (currentTabAudits.length === 0) {
			ElMessage.warning("当前没有可下载的审核记录");
			batchDownloading.value = false;
			return;
		}

		let successCount = 0;

		// 批量下载证明文件
		if (batchDownloadDocuments.value) {
			// 收集所有文档
			const allDocuments: Array<{ uid: string; name: string }> = [];

			for (const audit of currentTabAudits) {
				if (audit.data?.documents?.length) {
					// 构建用户ID和姓名的部分
					const userIdAndName = audit.data?.name ? `${audit.user_id}_${audit.data.name}` : `${audit.user_id}`;
					const docsWithPrefix = audit.data.documents.map(doc => ({
						uid: doc.uid,
						name: `用户${userIdAndName}_${doc.name}`
					}));
					allDocuments.push(...docsWithPrefix);
				}
			}

			if (allDocuments.length > 0) {
				const zipFileName = `资质证明文件_${formatDate(new Date())}.zip`;
				const success = await downloadFilesAsZip(allDocuments, zipFileName);
				if (success) {
					successCount++;
				}
			} else {
				ElMessage.warning("没有找到证明文件");
			}
		}

		if (successCount > 0) {
			ElMessage.success("批量下载完成");
			batchDownloadVisible.value = false;
		} else {
			ElMessage.warning("没有成功下载任何内容");
		}
	} catch (error) {
		console.error("批量下载失败:", error);
		ElMessage.error("批量下载失败");
	} finally {
		batchDownloading.value = false;
	}
};

// 审核数据
const audits = ref<Record<AuditStatus, QualificationAuditData[]>>({
	[AuditStatus.PENDING]: [],
	[AuditStatus.APPROVED]: [],
	[AuditStatus.REJECTED]: [],
});

// 计算属性：获取当前标签页的审核数据
const currentAudits = computed(() => {
	const status = activeTab.value as AuditStatus;
	return audits.value[status] || [];
});

// 加载审核数据
const loadAudits = async (status: AuditStatus) => {
	loading.value = true;
	try {
		const response = await qualifyApi.getQualificationsAudits({
			page: currentPage.value,
			pageSize: pageSize.value,
			sortBy: sortBy.value,
			sortOrder: sortOrder.value,
			filters: {
				status,
			},
		});
		audits.value[status] = response?.items || [];
		total.value = response?.total || 0;
		updateLastRefreshTime();

		// 检查是否有新的待审核请求
		if (status === AuditStatus.PENDING) {
			checkForNewAudits(response?.total || 0);
		}
	} catch (error) {
		console.error(`加载${getStatusLabel(status)}审核数据失败:`, error);
		ElMessage.error(`加载${getStatusLabel(status)}审核数据失败`);
	} finally {
		loading.value = false;
	}
};

// 添加检查新审核的函数
const checkForNewAudits = (currentCount: number) => {
	// 当前标签是待审核时才发送通知
	if (
		activeTab.value === "pending" &&
		lastPendingCount.value &&
		currentCount > lastPendingCount.value
	) {
		const newCount = currentCount - lastPendingCount.value;
		showAuditNotification("qualify", newCount);
	}
	lastPendingCount.value = currentCount;
};

// 监听标签页变化
watch(activeTab, async (newStatus) => {
	// 如果该状态的数据还未加载，则加载数据
	if (!audits.value[newStatus as AuditStatus]?.length) {
		await loadAudits(newStatus as AuditStatus);
	}
});

// 对话框标题
const dialogTitle = computed(() => {
	return pendingAction.value === AuditStatus.APPROVED ? "通过审核" : "拒绝审核";
});

// 处理审核操作
const handleAudit = (
	audit: QualificationAuditData,
	action: AuditStatus.APPROVED | AuditStatus.REJECTED,
) => {
	currentAudit.value = audit;
	pendingAction.value = action;
	dialogVisible.value = true;
};

// 确认审核
const confirmAudit = async () => {
	if (!currentAudit.value || !pendingAction.value) return;

	processing.value = true;
	try {
		await qualifyApi.processQualificationAudit(
			currentAudit.value.audit_id,
			pendingAction.value,
			auditComment.value,
		);
		ElMessage.success("审核处理成功");

		// 审核通过并且需要下载文件
		if (pendingAction.value === AuditStatus.APPROVED) {
			// 下载证明文件
			if (downloadDocuments.value && currentAudit.value.data?.documents?.length) {
				const userIdAndName = currentAudit.value.data?.name ? `${currentAudit.value.user_id}_${currentAudit.value.data.name}` : `${currentAudit.value.user_id}`;
				await downloadFilesAsZip(
					currentAudit.value.data.documents,
					`用户${userIdAndName}_资质证明文件_${formatDate(new Date())}.zip`
				);
			}
		}

		dialogVisible.value = false;
		auditComment.value = "";
		await loadAudits(AuditStatus.PENDING); // 重新加载数据
	} catch (error) {
		console.error("Failed to process audit:", error);
		ElMessage.error("审核处理失败");
	} finally {
		processing.value = false;
	}
};

// 取消审核
const cancelAudit = () => {
	dialogVisible.value = false;
};

// 重置对话框状态
const resetDialogState = () => {
	currentAudit.value = null;
	pendingAction.value = null;
	auditComment.value = "";
};

// 保存页面状态
const saveState = () => {
	const state = {
		activeTab: activeTab.value,
		currentPage: currentPage.value,
		pageSize: pageSize.value,
		sortBy: sortBy.value,
		sortOrder: sortOrder.value,
	};
	sessionStorage.setItem("qualifyPanel_state", JSON.stringify(state));
};

// 恢复页面状态
const restoreState = () => {
	const savedState = sessionStorage.getItem("qualifyPanel_state");
	if (savedState) {
		try {
			const state = JSON.parse(savedState);
			activeTab.value = state.activeTab || "pending";
			currentPage.value = state.currentPage || 1;
			pageSize.value = state.pageSize || 10;
			sortBy.value = state.sortBy || "created_at";
			sortOrder.value = state.sortOrder || "DESC";
		} catch (e) {
			console.error("Failed to parse saved state:", e);
		}
	}
};

// 修改onMounted函数
onMounted(async () => {

	// 恢复状态
	restoreState();

	// 加载初始数据
	loadAudits(activeTab.value as AuditStatus);

	// 设置定时刷新
	refreshTimer.value = setInterval(refreshData, refreshInterval.value * 1000);
});

// 更新最后刷新时间
const updateLastRefreshTime = () => {
	lastRefreshTime.value = formatTime(new Date());
};

// 刷新数据
const refreshData = async () => {
	if (loading.value) return;
	await loadAudits(activeTab.value as AuditStatus);
	updateLastRefreshTime();
};

// 监听刷新间隔变化
watch(refreshInterval, (newInterval) => {
	if (refreshTimer.value) {
		clearInterval(refreshTimer.value);
		refreshTimer.value = null;
	}

	if (newInterval > 0) {
		refreshTimer.value = setInterval(refreshData, newInterval * 1000);
	}
});

// 组件卸载时清理定时器
onUnmounted(() => {
	if (refreshTimer.value) {
		clearInterval(refreshTimer.value);
	}
});

// 组件卸载前保存状态
onBeforeUnmount(() => {
	saveState();
});

const getStatusLabel = (status: string) => {
	const statusMap: Record<string, string> = {
		pending: "待审核",
		approved: "已通过",
		rejected: "已拒绝",
	};
	return statusMap[status] || status;
};

// 添加分页处理函数
const handleCurrentChange = (newPage: number) => {
	currentPage.value = newPage;
	loadAudits(activeTab.value as AuditStatus);
};

const handleSizeChange = (newSize: number) => {
	pageSize.value = newSize;
	currentPage.value = 1; // 重置到第一页
	loadAudits(activeTab.value as AuditStatus);
};

// 添加排序处理函数
const handleSortChange = ({
	prop,
	order,
}: { prop?: string; order?: string }) => {
	sortBy.value = prop || "created_at";
	sortOrder.value = order === "ascending" ? "ASC" : "DESC";
	loadAudits(activeTab.value as AuditStatus);
};

// 更新导出数据函数，添加批量下载功能按钮
const exportData = async () => {
	const actionSelect = await ElMessageBox.confirm(
		'请选择要执行的操作',
		'数据导出',
		{
			confirmButtonText: '导出CSV',
			cancelButtonText: '批量下载文件',
			distinguishCancelAndClose: true,
		}
	)
		.then(() => 'csv')
		.catch((action: string | 'cancel' | 'close') => {
			if (action === 'cancel') {
				return 'download';
			}
			return '';
		});

	if (!actionSelect) return;

	if (actionSelect === 'download') {
		showBatchDownloadDialog();
		return;
	}

	// 导出CSV部分
	exporting.value = true;
	try {
		const response = await qualifyApi.getQualificationsAudits({
			page: 1,
			pageSize: 999999,
			sortBy: sortBy.value,
			sortOrder: sortOrder.value,
		});

		const headers = ["审核ID", "用户ID", "创建时间", "状态", "备注"];
		const csvData =
			response?.items.map((item) => [
				item.audit_id,
				item.user_id,
				formatDate(item.created_at),
				getStatusLabel(item.status),
				item.comment || "",
			]) || [];

		exportToCsv(headers, csvData, `qualifications-${activeTab.value}`);

		ElMessage.success("导出成功");
	} catch (error) {
		console.error("Export failed:", error);
		ElMessage.error("导出失败");
	} finally {
		exporting.value = false;
	}
};

// 公开方法以便父组件可以调用
defineExpose({
	refreshData,
});
</script>

<style scoped>
.qualify-view {
	padding: 20px;
}

.header-container {
	margin-bottom: 20px;
}

.header-controls {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 24px;
}

.refresh-controls {
	display: flex;
	align-items: center;
	gap: 12px;
}

.last-refresh {
	color: var(--el-text-color-secondary);
	font-size: 14px;
}

.refresh-select {
	width: 100px;
}

h2 {
	margin: 0;
	color: var(--el-text-color-primary);
}

@media (max-width: 768px) {
	.header-main {
		align-items: flex-start;
		gap: 12px;
	}

	.refresh-controls {
		padding: 8px 0;
	}
}

:deep(.el-tabs__item) {
	color: var(--el-text-color-secondary);
}

:deep(.el-tabs__item.is-active) {
	color: var(--el-color-primary);
}

:deep(.el-tabs__nav-wrap::after) {
	background-color: var(--el-border-color-light);
}

:deep(.el-dialog) {
	background: var(--el-bg-color-overlay);
	border: 1px solid var(--el-border-color);
}

:deep(.dialog-approved .el-dialog) {
	border-top: 4px solid var(--el-color-success);
}

:deep(.dialog-rejected .el-dialog) {
	border-top: 4px solid var(--el-color-danger);
}

:deep(.dialog-approved .el-dialog__title) {
	color: var(--el-color-success);
}

:deep(.dialog-rejected .el-dialog__title) {
	color: var(--el-color-danger);
}

:deep(.el-dialog__body) {
	color: var(--el-text-color-primary);
}

/* 从 AuditList 移植的样式 */
.audit-list {
	min-height: 200px;
}

.audit-details {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 4px;
	border-radius: 4px;
	cursor: pointer;
	transition: background-color 0.2s;
}

.audit-details:hover {
	background-color: var(--el-fill-color-light);
}
</style>
