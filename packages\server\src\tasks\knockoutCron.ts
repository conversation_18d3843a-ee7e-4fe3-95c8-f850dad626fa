import cron from "node-cron";
import logger from "@/utils/logger.js";
import { getKnockoutStocks } from "@/financeUtils/knockoutUtils.js";
import { isMarketDay } from "@/financeUtils/marketTimeManager.js";
import * as Position from "@/models/position.js";
import * as TradeService from "@/services/trade/tradeService.js";
import * as NotifService from "@/services/notifService.js";
import type { PositionData } from "@packages/shared";
import type { KnockoutResult } from "@/types/api.js";
import { NotificationType, OrderType, TradeDirection } from "@packages/shared";
import prisma from "@/lib/prisma.js";
import { AppError } from "@/core/appError.js";
import * as User from "@/models/user.js";
import EmailService from "@/utils/email.js";

export class KnockoutCron {
	private static instance: KnockoutCron;

	// typescript will auto privatize constructor

	public static getInstance(): KnockoutCron {
		if (!KnockoutCron.instance) {
			KnockoutCron.instance = new KnockoutCron();
		}
		return KnockoutCron.instance;
	}

	public start(): void {
		try {
			// 每天 03:00PM 执行
			cron.schedule(
				"0 15 * * *",
				async () => {
					await this.processKnockoutPositions();
				},
				{
					timezone: "Asia/Shanghai",
				},
			);

			logger.info("Knockout position processing cron job started");
		} catch (error) {
			logger.error(
				error,
				"Failed to start knockout position processing cron job",
			);
			throw error;
		}
	}

	private async processKnockoutPositions(): Promise<void> {
		try {
			// 检查是否为交易日
			if (!(await isMarketDay())) {
				logger.info("Not a market day, skipping knockout position processing");
				return;
			}

			const startTime = Date.now();
			const knockoutStocks = await getKnockoutStocks();

			if (!knockoutStocks.length) {
				logger.info("No knockout stocks found");
				return;
			}

			// 获取所有需要平仓的持仓
			const positions = await this.getKnockoutPositionsData(knockoutStocks);

			// 执行市价卖出
			for (const [position, knockoutStartDate] of positions) {
				const createdDate = new Date(position.created_at).toLocaleDateString(
					"zh-CN",
					{ timeZone: "Asia/Shanghai" },
				);
				const knockoutDate = `${knockoutStartDate.slice(0, 4)}/${knockoutStartDate.slice(4, 6)}/${knockoutStartDate.slice(6, 8)}`;

				// 北交所当日买当日敲出
				if (createdDate <= knockoutDate) {
					try {
						// 使用 isExpiry 参数，强制敲出
						await TradeService.placeOrder(
							{
								type: OrderType.MARKET,
								direction: TradeDirection.SELL,
								user_id: position.user_id,
								ts_code: position.ts_code,
								trade_no: position.trade_no,
								scale: position.scale,
								quote_provider: position.quote_provider,
								quote_diff: position.quote_diff,
							},
							{ isExpiry: true },
						);

						await NotifService.sendNotification(
							position.user_id,
							{
								title: "强制敲出",
								content: `您的持仓 ${position.ts_code} 因连续涨跌停，已被强制敲出。`,
								type: NotificationType.ORDER,
								metadata: {
									type: "knockout",
									ts_code: position.ts_code,
									trade_no: position.trade_no,
								},
							},
							prisma,
						);

						const user = await User.findById(position.user_id);
						if (!user) {
							throw AppError.create("USER_NOT_FOUND", "User not found");
						}
						EmailService.sendEmail(user.email, "FORCE_NOTIFICATION", {});

						logger.info(
							`Successfully placed knockout sell order for position ${position.trade_no}`,
						);
					} catch (error) {
						logger.error(
							error,
							`Failed to process knockout position ${position.trade_no}`,
						);
					}
				}
			}

			const duration = Date.now() - startTime;
			logger.info(
				`Knockout position processing completed in ${duration}ms, processed ${positions.length} positions`,
			);
		} catch (error) {
			logger.error(error, "Error during knockout position processing");
		}
	}

	private async getKnockoutPositionsData(
		knockoutStocks: KnockoutResult[],
	): Promise<[PositionData, string][]> {
		try {
			const positions: [PositionData, string][] = [];
			for (const stock of knockoutStocks) {
				const stockPositions = await Position.findByStock(stock.ts_code);
				for (const position of stockPositions) {
					positions.push([position, stock.knockoutStartDate]);
				}
			}
			return positions;
		} catch (error) {
			logger.error(error, "Failed to fetch knockout positions");
			throw error;
		}
	}
}

// 导出单例实例
export const knockoutCron = KnockoutCron.getInstance();
