#!/usr/bin/env tsx

/**
 * 运行命令：
 * pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\queue\ink\lockVerification.ts
 */

import { getChildLogger } from "@/utils/logger.js";
import { coordinationRedis } from "@/lib/redis.js";
import { lockManager } from "./distributedLockManager.js";
import { metricsCollector } from "./metricsCollector.js";
import { REDIS_KEYS } from "./constants.js";
import { fileURLToPath } from "node:url";

const logger = getChildLogger("LockVerification");

// 定义锁信息类型
interface LockInfo {
	status: string;
	holder?: string;
	ttl?: number;
	holdDuration?: number;
}

// 定义业务锁详情类型
interface BusinessLockDetails {
	lockKey: string;
	isLocked: boolean;
	holder?: string;
	ttl?: number;
	holdDuration?: number;
}

// 定义泄漏锁类型
interface LeakedLock {
	key: string;
	holder: string;
	holdDuration: number;
	ttl: number;
}

/**
 * 分布式锁验证工具
 * 提供多种方法来验证锁机制是否在多实例环境中正常工作
 */
export class LockVerificationTool {
	/**
	 * 实时监控锁状态
	 * 每5秒输出一次所有活跃锁的状态
	 */
	async startLockMonitoring(durationMinutes = 10): Promise<void> {
		logger.info(`开始监控锁状态，持续 ${durationMinutes} 分钟`);

		const endTime = Date.now() + durationMinutes * 60 * 1000;
		const interval = 5000; // 5秒间隔

		while (Date.now() < endTime) {
			try {
				await this.logCurrentLockStatus();
				await new Promise((resolve) => setTimeout(resolve, interval));
			} catch (error) {
				logger.error(error, "监控锁状态时发生错误");
				await new Promise((resolve) => setTimeout(resolve, interval));
			}
		}

		logger.info("锁状态监控结束");
	}

	/**
	 * 记录当前锁状态
	 */
	private async logCurrentLockStatus(): Promise<void> {
		const timestamp = new Date().toISOString();
		console.log(`\n=== 锁状态报告 [${timestamp}] ===`);

		// 1. 检查所有INK相关的锁
		const lockKeys = [
			REDIS_KEYS.INK_DATA_UPDATE_LOCK,
			REDIS_KEYS.INK_QUOTES_UPDATE_LOCK,
			REDIS_KEYS.INK_CONFIG_UPDATE_LOCK,
			REDIS_KEYS.INK_SYNC_LOCK,
		];

		for (const lockKey of lockKeys) {
			const lockInfo = await this.getLockInfo(lockKey);
			console.log(`🔒 ${lockKey}: ${lockInfo.status}`);
			if (lockInfo.holder) {
				console.log(`   持有者: ${lockInfo.holder}`);
				console.log(`   剩余时间: ${lockInfo.ttl}秒`);
				console.log(`   持有时长: ${lockInfo.holdDuration}秒`);
			}
		}

		// 2. 检查当前实例的锁情况
		const activeLocks = lockManager.getActiveLockCount();
		console.log(`📊 当前实例活跃锁数量: ${activeLocks}`);

		// 3. 获取锁相关指标
		const metrics = await metricsCollector.getCurrentMetrics();
		console.log(
			`📈 锁获取率: ${metrics.lockMetrics.lockAcquisitionRate.toFixed(2)}/秒`,
		);

		console.log("=".repeat(50));
	}

	/**
	 * 获取特定锁的详细信息
	 */
	private async getLockInfo(lockKey: string): Promise<LockInfo> {
		try {
			const lockValue = await coordinationRedis.get(lockKey);
			const ttl = await coordinationRedis.ttl(lockKey);

			if (!lockValue) {
				return { status: "未锁定" };
			}

			const [holderId, timestamp] = lockValue.split(":");
			const holdDuration =
				Math.floor(Date.now() / 1000) - Number.parseInt(timestamp);

			return {
				status: "已锁定",
				holder: holderId,
				ttl: ttl > 0 ? ttl : undefined,
				holdDuration,
			};
		} catch (error) {
			return { status: "检查失败" };
		}
	}

	/**
	 * 并发锁竞争测试
	 * 模拟多个实例同时尝试获取同一个锁
	 */
	async testLockCompetition(testDurationSeconds = 60): Promise<void> {
		logger.info(`开始锁竞争测试，持续 ${testDurationSeconds} 秒`);

		const testLockKey = "test-competition-lock";
		const results = {
			attempts: 0,
			successes: 0,
			failures: 0,
			conflicts: 0,
		};

		const endTime = Date.now() + testDurationSeconds * 1000;

		while (Date.now() < endTime) {
			results.attempts++;

			try {
				const acquired = await lockManager.acquireLockWithRenewal(
					testLockKey,
					10,
				); // 10秒锁

				if (acquired) {
					results.successes++;
					console.log(
						`✅ [${new Date().toISOString()}] 锁获取成功 (${results.successes}/${results.attempts})`,
					);

					// 持有锁2秒后释放
					await new Promise((resolve) => setTimeout(resolve, 2000));
					await lockManager.releaseLock(testLockKey);

					console.log(`🔓 [${new Date().toISOString()}] 锁已释放`);
				} else {
					results.failures++;

					// 检查是否是因为其他实例持有锁
					const lockInfo = await this.getLockInfo(testLockKey);
					if (lockInfo.holder) {
						results.conflicts++;
						console.log(
							`⚠️ [${new Date().toISOString()}] 锁被其他实例持有: ${lockInfo.holder}`,
						);
					} else {
						console.log(
							`❌ [${new Date().toISOString()}] 锁获取失败 (原因未知)`,
						);
					}
				}
			} catch (error) {
				console.error(`💥 [${new Date().toISOString()}] 锁操作异常:`, error);
			}

			// 随机等待1-3秒
			const waitTime = 1000 + Math.random() * 2000;
			await new Promise((resolve) => setTimeout(resolve, waitTime));
		}

		console.log("\n=== 锁竞争测试结果 ===");
		console.log(`总尝试次数: ${results.attempts}`);
		console.log(
			`成功获取: ${results.successes} (${((results.successes / results.attempts) * 100).toFixed(1)}%)`,
		);
		console.log(
			`获取失败: ${results.failures} (${((results.failures / results.attempts) * 100).toFixed(1)}%)`,
		);
		console.log(
			`冲突次数: ${results.conflicts} (${((results.conflicts / results.attempts) * 100).toFixed(1)}%)`,
		);

		if (results.conflicts > 0) {
			console.log("✅ 检测到锁冲突，说明分布式锁机制正常工作！");
		} else {
			console.log("⚠️ 未检测到锁冲突，可能只有一个实例在运行");
		}
	}

	/**
	 * 业务流程锁验证
	 * 检查实际业务流程中的锁使用情况
	 */
	async verifyBusinessLocks(): Promise<{
		summary: string;
		details: Record<string, BusinessLockDetails>;
	}> {
		logger.info("开始验证业务流程锁");

		const results: Record<string, BusinessLockDetails> = {};
		const businessLocks = {
			INK数据更新: REDIS_KEYS.INK_DATA_UPDATE_LOCK,
			价格报价更新: REDIS_KEYS.INK_QUOTES_UPDATE_LOCK,
			业务配置更新: REDIS_KEYS.INK_CONFIG_UPDATE_LOCK,
			同步初始化: REDIS_KEYS.INK_SYNC_LOCK,
		};

		for (const [business, lockKey] of Object.entries(businessLocks)) {
			const lockInfo = await this.getLockInfo(lockKey);
			results[business] = {
				lockKey,
				isLocked: lockInfo.status === "已锁定",
				holder: lockInfo.holder,
				ttl: lockInfo.ttl,
				holdDuration: lockInfo.holdDuration,
			};
		}

		// 分析结果
		const lockedCount = Object.values(results).filter((r) => r.isLocked).length;
		const summary =
			lockedCount > 0
				? `发现 ${lockedCount} 个业务流程正在执行，锁机制正常工作`
				: "当前没有业务流程在执行，或锁已释放";

		return { summary, details: results };
	}

	/**
	 * 锁泄漏检测
	 * 检查是否有长时间未释放的锁
	 */
	async detectLockLeaks(): Promise<{
		leakedLocks: LeakedLock[];
		summary: string;
	}> {
		logger.info("开始检测锁泄漏");

		const allLockKeys = Object.values(REDIS_KEYS).filter((key) =>
			key.includes("lock"),
		);
		const leakedLocks: LeakedLock[] = [];
		const LEAK_THRESHOLD = 300; // 5分钟阈值

		for (const lockKey of allLockKeys) {
			const lockInfo = await this.getLockInfo(lockKey);

			if (
				lockInfo.status === "已锁定" &&
				lockInfo.holdDuration &&
				lockInfo.holdDuration > LEAK_THRESHOLD &&
				lockInfo.holder
			) {
				leakedLocks.push({
					key: lockKey,
					holder: lockInfo.holder,
					holdDuration: lockInfo.holdDuration,
					ttl: lockInfo.ttl || 0,
				});
			}
		}

		const summary =
			leakedLocks.length > 0
				? `检测到 ${leakedLocks.length} 个可能泄漏的锁`
				: "未检测到锁泄漏";

		return { leakedLocks, summary };
	}

	/**
	 * 生成锁验证报告
	 */
	async generateVerificationReport(): Promise<string> {
		const report = [];
		const timestamp = new Date().toLocaleString("zh-CN");

		report.push("=== 分布式锁验证报告 ===");
		report.push(`生成时间: ${timestamp}`);
		report.push("");

		// 1. 当前锁状态
		report.push("📊 当前锁状态:");
		const businessResult = await this.verifyBusinessLocks();
		report.push(`  ${businessResult.summary}`);

		for (const [business, info] of Object.entries(businessResult.details)) {
			const status = info.isLocked ? "🔒 已锁定" : "🔓 未锁定";
			const holder = info.holder ? ` (${info.holder})` : "";
			report.push(`    ${business}: ${status}${holder}`);
		}
		report.push("");

		// 2. 锁泄漏检测
		const leakResult = await this.detectLockLeaks();
		report.push("🔍 锁泄漏检测:");
		report.push(`  ${leakResult.summary}`);

		if (leakResult.leakedLocks.length > 0) {
			for (const leak of leakResult.leakedLocks) {
				report.push(
					`    ⚠️ ${leak.key}: 持有 ${leak.holdDuration}秒 (${leak.holder})`,
				);
			}
		}
		report.push("");

		// 3. 实例状态
		const activeLocks = lockManager.getActiveLockCount();
		const metrics = await metricsCollector.getCurrentMetrics();

		report.push("🖥️ 当前实例状态:");
		report.push(`  活跃锁数量: ${activeLocks}`);
		report.push(
			`  锁获取率: ${metrics.lockMetrics.lockAcquisitionRate.toFixed(2)}/秒`,
		);
		report.push("");

		// 4. 建议
		report.push("💡 建议:");
		if (leakResult.leakedLocks.length > 0) {
			report.push("  - 存在锁泄漏，建议检查业务逻辑和异常处理");
		}
		if (activeLocks > 3) {
			report.push("  - 当前实例持有较多锁，注意监控性能");
		}
		if (
			businessResult.details &&
			Object.values(businessResult.details).some((r) => r.isLocked)
		) {
			report.push("  - 有业务流程正在执行，锁机制工作正常");
		} else {
			report.push("  - 当前无业务流程执行，可适当增加测试负载");
		}

		return report.join("\n");
	}
}

// 命令行工具函数
export async function runLockVerification(
	command: string,
	...args: string[]
): Promise<void> {
	const tool = new LockVerificationTool();

	switch (command) {
		case "monitor": {
			const duration = Number.parseInt(args[0]) || 10;
			await tool.startLockMonitoring(duration);
			break;
		}

		case "test": {
			const testDuration = Number.parseInt(args[0]) || 60;
			await tool.testLockCompetition(testDuration);
			break;
		}

		case "report": {
			const report = await tool.generateVerificationReport();
			console.log(report);
			break;
		}

		case "leaks": {
			const leakResult = await tool.detectLockLeaks();
			console.log(leakResult.summary);
			if (leakResult.leakedLocks.length > 0) {
				console.log("\n详细信息:");
				for (const leak of leakResult.leakedLocks) {
					console.log(`  ${leak.key}: ${leak.holdDuration}s (${leak.holder})`);
				}
			}
			break;
		}

		default:
			console.log("使用方法:");
			console.log(
				"  pnpm tsx lockVerification.ts monitor [分钟]  # 监控锁状态",
			);
			console.log("  pnpm tsx lockVerification.ts test [秒]      # 锁竞争测试");
			console.log(
				"  pnpm tsx lockVerification.ts report         # 生成验证报告",
			);
			console.log("  pnpm tsx lockVerification.ts leaks          # 检测锁泄漏");
	}
}

// 如果直接运行此脚本
if (process.argv[1] === fileURLToPath(import.meta.url)) {
	const [, , command, ...args] = process.argv;

	if (!command) {
		console.log("请指定命令: monitor, test, report, leaks");
		process.exit(1);
	}

	runLockVerification(command, ...args)
		.then(() => process.exit(0))
		.catch((error) => {
			console.error("验证失败:", error);
			process.exit(1);
		});
}
