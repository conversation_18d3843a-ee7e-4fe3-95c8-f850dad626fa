const TRADING_END = 14 * 60 * 60 * 1000 + 57 * 60 * 1000; // 14:57
const VWAP_CUTOFF = 15 * 60 * 1000; // 15 minute

export function validateVwapTime(): {
	valid: boolean;
	message?: string;
} {
	const now = new Date();
	const currentMs =
		now.getHours() * 60 * 60 * 1000 + now.getMinutes() * 60 * 1000;
	const timeToClose = TRADING_END - currentMs;

	// console.log("timeToClose", {
	// 	TRADING_END,
	// 	currentMs,
	// 	timeToClose,
	// 	VWAP_CUTOFF,
	// });
	// VWAP订单额外验证15分钟限制
	if (timeToClose <= VWAP_CUTOFF && timeToClose > 0) {
		return {
			valid: false,
			message: "当前时间无法创建均价订单",
		};
	}

	return { valid: true };
}
