import type { AdminPermission, AppType } from "../index.js";

export interface LoginRequest {
	email: string;
	password: string;
}

export interface BaseAuthResponse {
	message: string;
	username: string;
	can_transfer: boolean;
}

export interface CookieOptions {
	httpOnly: boolean;
	secure: boolean;
	sameSite: "strict" | "lax" | "none";
	maxAge: number;
}

export interface AuthServiceResponseWithCookie {
	headers: { authorization: string };
	body: BaseAuthResponse;
	cookies: {
		name: string;
		value: string;
		options: CookieOptions;
	}[];
}

export interface AdminAuthServiceResponseWithCookie
	extends Omit<AuthServiceResponseWithCookie, "body"> {
	body: { message: string };
}

export type AuthServiceResponse =
	| BaseAuthResponse
	| AuthServiceResponseWithCookie;

// 基础 JWT 载荷类型
export interface BaseJWTPayload {
	type: "access" | "refresh" | "admin" | "admin_refresh";
	iat: number;
	exp: number;
	app_type: AppType;
}

// 用户 token 载荷类型
export interface UserJWTPayload extends BaseJWTPayload {
	type: "access";
	user_id: number;
	is_qualified: boolean;
}

export interface UserRefreshJWTPayload extends BaseJWTPayload {
	type: "refresh";
	user_id: number;
}

// 管理员 token 载荷类型
export interface AdminJWTPayload extends BaseJWTPayload {
	type: "admin";
	admin_id: number;
	permissions: AdminPermission[];
}

export interface AdminRefreshJWTPayload extends BaseJWTPayload {
	type: "admin_refresh";
	admin_id: number;
}

// 统一导出类型
export type JWTPayload =
	| UserJWTPayload
	| UserRefreshJWTPayload
	| AdminJWTPayload
	| AdminRefreshJWTPayload;

// 注册请求
export interface RegisterRequest {
	email: string;
}

export interface VerifyRegisterRequest {
	email: string;
	password: string;
	sms_code: string;
}

// 重置密码请求
export interface ResetPasswordRequest {
	email: string;
}

export interface VerifyResetPasswordRequest {
	email: string;
	sms_code: string;
	new_password: string;
}
