import type { Job } from "bullmq";
import {
	createWorker,
	expiryDatesTasksQueue,
	addRepeatedJob,
} from "./index.js";
import logger from "@/utils/logger.js";
import { calculateEstimatedExpiryDate } from "@/financeUtils/marketTimeManager.js";
import * as OrderModel from "@/models/trade/order.js";
import * as PositionModel from "@/models/position.js";
import { withTransaction } from "@/core/dbTxnManager.js";

// 定义到期日更新相关的作业类型
export const EXPIRY_DATES_JOBS = {
	UPDATE_UNCONFIRMED_EXPIRY_DATES: "expirydate-update-unconfirmed",
};

// 处理到期日更新相关的作业
async function processExpiryDatesJob(job: Job) {
	const { name } = job;

	logger.info(`Processing expiry dates job: ${name}`);

	try {
		switch (name) {
			case EXPIRY_DATES_JOBS.UPDATE_UNCONFIRMED_EXPIRY_DATES:
				await updateUnconfirmedExpiryDates();
				break;
			default:
				logger.warn(`Unknown expiry dates job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process expiry dates job: ${name}`);
		throw error;
	}
}

// 更新未确认的到期日
async function updateUnconfirmedExpiryDates(): Promise<void> {
	try {
		const orders = await OrderModel.findUnconfirmedExpiryDates();

		for (const order of orders) {
			const { expiryDate, isConfirmed } = await calculateEstimatedExpiryDate(
				new Date(order.created_at),
				order.term,
			);

			if (isConfirmed) {
				withTransaction(async (txn) => {
					await OrderModel.update(
						order.trade_no,
						{
							expiry_date: expiryDate.toISOString(),
							expiry_date_confirmed: true,
						},
						txn,
					);

					await PositionModel.update(
						order.trade_no,
						{
							expiry_date: expiryDate.toISOString(),
							expiry_date_confirmed: true,
						},
						txn,
					);
				});
			}
		}

		logger.info(`Updated expiry dates for ${orders.length} orders`);
	} catch (error) {
		logger.error(error, "Failed to update unconfirmed expiry dates");
	}
}

// 创建Worker实例
export const expiryDatesWorker = createWorker(
	expiryDatesTasksQueue,
	processExpiryDatesJob,
);

// 初始化定时作业调度
export async function initializeExpiryDatesJobs() {
	try {
		// 每天凌晨0点执行
		await addRepeatedJob(
			expiryDatesTasksQueue,
			EXPIRY_DATES_JOBS.UPDATE_UNCONFIRMED_EXPIRY_DATES,
			{},
			"0 0 * * *",
		);

		logger.info("Expiry dates jobs scheduled successfully");
	} catch (error) {
		logger.error(error, "Failed to schedule expiry dates jobs");
		throw error;
	}
}

// 用于应用程序启动时初始化所有到期日更新任务
export async function initializeOnStartup() {
	await initializeExpiryDatesJobs();
	logger.info("Expiry dates worker initialized successfully");
}
