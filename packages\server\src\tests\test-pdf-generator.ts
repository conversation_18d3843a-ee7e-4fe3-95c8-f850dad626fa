// 执行命令：pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\tests\test-pdf-generator.ts

import {
	generateIsdaMasterAgreement,
	generateIsdaSupplement,
} from "../utils/pdf-generator.js";
import fs from "node:fs/promises";

async function testPdfGeneration() {
	// 测试数据
	const testSignature =
		"data:image/png;base64,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";

	try {
		// 生成 PDF
		const pdfBlob1 = await generateIsdaMasterAgreement(testSignature);
		const pdfBlob2 = await generateIsdaSupplement(testSignature);

		// 将 Blob 转换为 Buffer
		const buffer1 = Buffer.from(await pdfBlob1.arrayBuffer());
		const buffer2 = Buffer.from(await pdfBlob2.arrayBuffer());

		// 保存到本地文件
		await fs.writeFile("test-output_1.pdf", buffer1);
		await fs.writeFile("test-output_2.pdf", buffer2);

		console.log("PDF 生成成功！文件已保存在工作目录。");
	} catch (error) {
		console.error("生成 PDF 时出错:", error);
	}
}

// 运行测试
testPdfGeneration();
