<template>
  <div class="modal-overlay" @click.self="close">
    <div class="modal-content">
      <div class="modal-header">
        <h3>通知</h3>
        <div class="header-actions">
          <el-button text size="small" @click="markAllAsRead" :disabled="!hasUnread">全部标记已读</el-button>
          <button class="close-button" @click="close">
            <el-icon>
              <Close />
            </el-icon>
          </button>
        </div>
      </div>

      <div class="notification-container">
        <div v-if="loading" class="loading-state">
          <el-skeleton :rows="3" animated />
        </div>

        <template v-else>
          <div v-if="notifications.length" class="notification-list" v-infinite-scroll="loadMore"
            :infinite-scroll-disabled="loading || noMore" infinite-scroll-immediate="false"
            infinite-scroll-distance="100">
            <div v-for="notification in notifications" :key="notification.notif_id" class="notification-item" :class="{
              unread: !notification.is_read,
              active: selectedNotification?.notif_id === notification.notif_id,
            }" @click="selectNotification(notification)">
              <div class="notification-brief">
                <div class="notification-icon">
                  <el-icon :size="20" :color="getNotificationIconColor(notification.type)">
                    <component :is="getNotificationIcon(notification.type, notification)" />
                  </el-icon>
                </div>
                <div class="notification-info">
                  <div class="notification-title">
                    <span class="unread-dot" v-if="!notification.is_read"></span>
                    {{ notification.title }}
                  </div>
                  <div class="notification-amount" v-if="notification.metadata?.formattedAmount">
                    {{ notification.metadata.formattedAmount }}
                  </div>
                  <div class="notification-time">
                    {{ formatTime(notification.created_at) }}
                  </div>
                </div>
              </div>
            </div>
            <div v-if="loading" class="loading-more">
              <el-skeleton :rows="1" animated />
            </div>
          </div>

          <div v-if="selectedNotification" class="notification-detail">
            <div v-if="isMobile" class="mobile-back-header">
              <el-button link @click="backToList">
                <el-icon>
                  <ArrowLeft />
                </el-icon>
                返回列表
              </el-button>
            </div>

            <div class="detail-header">
              <h4>{{ selectedNotification.title }}</h4>
              <div class="notification-meta">
                <span class="notification-type">
                  {{ getNotificationType(selectedNotification.type) }}
                </span>
                <span class="notification-status" :class="{ unread: !selectedNotification.is_read }">
                  {{ selectedNotification.is_read ? '已读' : '未读' }}
                </span>
              </div>
            </div>
            <div class="detail-content">
              <div class="notification-content" v-if="selectedNotification.content">{{ selectedNotification.content }}
              </div>

              <div class="metadata-info" v-if="hasDisplayableMetadata">
                <template v-if="selectedNotification.metadata?.type === 'fund_transfer'">
                  <div class="metadata-row" v-if="selectedNotification.metadata.formattedAmount">
                    <div class="label">金额：</div>
                    <div class="value highlight">
                      {{ selectedNotification.metadata.formattedAmount }}
                    </div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.operation">
                    <div class="label">操作类型：</div>
                    <div class="value">
                      {{ getOperationType(selectedNotification.metadata.operation) }}
                    </div>
                  </div>
                  <!-- 如果是转账发送方，显示接收方信息 -->
                  <template v-if="selectedNotification.metadata.receiver_name">
                    <div class="metadata-row">
                      <div class="label">收款人：</div>
                      <div class="value">{{ selectedNotification.metadata.receiver_name }}</div>
                    </div>
                    <div class="metadata-row">
                      <div class="label">收款手机号：</div>
                      <div class="value">{{ selectedNotification.metadata.receiver_phone }}</div>
                    </div>
                  </template>
                  <!-- 如果是转账接收方，显示发送方信息 -->
                  <template v-if="selectedNotification.metadata.sender_name">
                    <div class="metadata-row">
                      <div class="label">汇款人：</div>
                      <div class="value">{{ selectedNotification.metadata.sender_name }}</div>
                    </div>
                    <div class="metadata-row">
                      <div class="label">汇款手机号：</div>
                      <div class="value">{{ selectedNotification.metadata.sender_phone }}</div>
                    </div>
                  </template>
                </template>

                <template v-else-if="selectedNotification.metadata?.type === 'qualification'">
                  <div class="metadata-row" v-if="selectedNotification.metadata.name">
                    <div class="label">姓名/公司名：</div>
                    <div class="value">{{ selectedNotification.metadata.name }}</div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.email">
                    <div class="label">邮箱：</div>
                    <div class="value">{{ selectedNotification.metadata.email }}</div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.phone_number">
                    <div class="label">手机号：</div>
                    <div class="value">{{ selectedNotification.metadata.phone_number }}</div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.bank_name">
                    <div class="label">银行名称：</div>
                    <div class="value">{{ selectedNotification.metadata.bank_name }}</div>
                  </div>
                </template>

                <template v-else-if="selectedNotification.metadata?.type === 'pending_failed'">
                  <div class="metadata-row" v-if="selectedNotification.metadata.ts_code">
                    <div class="label">股票代码：</div>
                    <div class="value">{{ selectedNotification.metadata.ts_code }}</div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.status">
                    <div class="label">类型：</div>
                    <div class="value">{{ getOrderStatus(selectedNotification.metadata.status) }}</div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.vwap_price">
                    <div class="label">VWAP均价：</div>
                    <div class="value">{{ selectedNotification.metadata.vwap_price }}</div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.limit_price">
                    <div class="label">限价：</div>
                    <div class="value">{{ selectedNotification.metadata.limit_price }}</div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.exercise_price">
                    <div class="label">执行价格：</div>
                    <div class="value">{{ Number(selectedNotification.metadata.exercise_price).toFixed(2) }}</div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.trade_no">
                    <div class="label">订单号：</div>
                    <div class="value">{{ selectedNotification.metadata.trade_no }}</div>
                  </div>
                </template>

                <template v-else-if="selectedNotification.metadata?.type === 'knockout'">
                  <div class="metadata-row" v-if="selectedNotification.metadata.ts_code">
                    <div class="label">股票代码：</div>
                    <div class="value">{{ selectedNotification.metadata.ts_code }}</div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.trade_no">
                    <div class="label">订单号：</div>
                    <div class="value">{{ selectedNotification.metadata.trade_no }}</div>
                  </div>
                </template>

                <template v-else-if="selectedNotification.metadata?.type === 'expiry'">
                  <div class="metadata-row" v-if="selectedNotification.metadata.ts_code">
                    <div class="label">股票代码：</div>
                    <div class="value">{{ selectedNotification.metadata.ts_code }}</div>
                  </div>
                  <div class="metadata-row" v-if="selectedNotification.metadata.trade_no">
                    <div class="label">订单号：</div>
                    <div class="value">{{ selectedNotification.metadata.trade_no }}</div>
                  </div>
                </template>
              </div>
            </div>
            <div class="detail-time">
              {{ formatTime(selectedNotification.created_at) }}
            </div>
          </div>

          <div v-else-if="!notifications.length" class="empty-state">
            <el-icon :size="48" class="empty-icon">
              <Bell />
            </el-icon>
            <p>暂无通知</p>
          </div>

          <div v-else class="no-selection">选择要查看的通知</div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import {
  Bell,
  Close,
  Message,
  Setting,
  Wallet,
  Document,
  Stamp,
  User,
  ArrowLeft,
  Clock,
} from "@element-plus/icons-vue";
import { notificationApi } from "@/api";
import type {
  NotificationData,
  NotificationMetadataType,
} from "@packages/shared";
import {
  NotificationType,
  TransactionType,
  OrderStatus,
} from "@packages/shared";
import { eventBus } from "@/utils/eventBus";

const emit = defineEmits(["close", "update:unreadCount"]);

const props = defineProps<{
  unreadCount: number;
}>();

const notifications = ref<NotificationData[]>([]);
const selectedNotification = ref<NotificationData | null>(null);
const loading = ref(false);

// 添加分页相关的响应式变量
const page = ref(1);
const pageSize = ref(20);
const total = ref(0);
const noMore = computed(() => notifications.value.length >= total.value);

// 计算是否有未读通知，使用 props 中的 unreadCount
const hasUnread = computed(() => {
  return props.unreadCount > 0;
});

// 获取通知图标
const getNotificationIcon = (
  type: NotificationType,
  notification?: NotificationData,
) => {
  if (notification?.metadata?.type) {
    const metadataIcons: Partial<
      Record<NotificationMetadataType, typeof Stamp>
    > = {
      qualification: Stamp,
      fund_transfer: Wallet,
      expiry: Clock,
    };
    // 如果找到对应的 metadata 图标就用它，否则继续使用默认类型图标
    if (metadataIcons[notification.metadata.type]) {
      return metadataIcons[notification.metadata.type];
    }
  }

  // 默认类型图标
  const icons = {
    [NotificationType.SYSTEM]: Setting,
    [NotificationType.FEATURE]: Message,
    [NotificationType.ACCOUNT]: User,
    [NotificationType.ORDER]: Document,
  } as const;

  return icons[type] || Bell;
};

// 获取图标颜色
const getNotificationIconColor = (type: NotificationType) => {
  const colors = {
    [NotificationType.SYSTEM]: "var(--el-color-info)",
    [NotificationType.FEATURE]: "var(--el-color-success)",
    [NotificationType.ACCOUNT]: "var(--el-color-primary)",
    [NotificationType.ORDER]: "var(--el-color-warning)",
  } as const;

  return colors[type] || "var(--el-color-info)";
};

// 格式化时间
const formatTime = (date: Date | undefined) => {
  if (!date) return "";
  const now = new Date();
  const diff = now.getTime() - new Date(date).getTime();
  const minutes = Math.floor(diff / 60000);

  if (minutes < 1) return "刚刚";
  if (minutes < 60) return `${minutes}分钟前`;

  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}小时前`;

  const days = Math.floor(hours / 24);
  return `${days}天前`;
};

// 修改加载通知列表的方法
const loadNotifications = async (isLoadMore = false) => {
  if (!isLoadMore) {
    loading.value = true;
    notifications.value = [];
    page.value = 1;
  }

  try {
    const response = await notificationApi.getNotifications({
      limit: pageSize.value,
      offset: (page.value - 1) * pageSize.value,
    });
    if (!response) return;
    const { notifications: notifs, total: totalCount } = response;

    if (isLoadMore) {
      notifications.value.push(...notifs);
    } else {
      notifications.value = notifs;
    }

    total.value = totalCount;
  } catch (error) {
    console.error("Failed to load notifications:", error);
    ElMessage.error("通知加载失败");
  } finally {
    loading.value = false;
  }
};

// 添加加载更多方法
const loadMore = async () => {
  if (loading.value || noMore.value) return;
  page.value++;
  await loadNotifications(true);
};

// 选择并标记通知为已读
const selectNotification = async (notification: NotificationData) => {
  selectedNotification.value = notification;
  if (!notification.is_read && notification.notif_id) {
    try {
      await notificationApi.markAsRead(notification.notif_id);
      notification.is_read = true;
      // 更新未读数
      const newUnreadCount = await notificationApi.getUnreadCount();
      emit("update:unreadCount", newUnreadCount);
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  }
};

// 标记所有为已读，使用 API 的 markAllAsRead
const markAllAsRead = async () => {
  try {
    // 调用 API 标记所有为已读
    await notificationApi.markAllAsRead();

    // 更新本地状态
    for (const n of notifications.value) {
      n.is_read = true;
    }

    // 更新未读计数
    emit("update:unreadCount", 0);
    ElMessage.success("已全部标记为已读");
  } catch (error) {
    console.error("Failed to mark all as read:", error);
    ElMessage.error("标记已读失败");
  }
};

const close = () => {
  selectedNotification.value = null;
  emit("close");
};

// 添加计算属性来判断是否有可显示的元信息
const hasDisplayableMetadata = computed(() => {
  if (!selectedNotification.value?.metadata) return false;

  const { type, ...metadataWithoutType } = selectedNotification.value.metadata;
  return Object.keys(metadataWithoutType).length > 0;
});

// 获取通知类型显示文本
const getNotificationType = (type: string) => {
  const types: Record<string, string> = {
    account: "账户通知",
    system: "系统通知",
    feature: "功能通知",
    order: "订单通知",
  };
  return types[type] || type;
};

// 获取操作类型显示文本
const getOperationType = (operation: unknown) => {
  if (typeof operation !== "string") return "";
  const operations: Record<string, string> = {
    [TransactionType.DEPOSIT]: "入金",
    [TransactionType.WITHDRAW]: "出金",
    [TransactionType.PLATFORM_DEPOSIT]: "平台入金",
    [TransactionType.EXCHANGE]: "换汇",
    [TransactionType.TRANSFER]: "转账",
  };
  return operations[operation] || operation;
};

// 获取订单状态显示文本
const getOrderStatus = (status: OrderStatus) => {
  const statuses: Record<string, string> = {
    [OrderStatus.LIMIT_SELLING]: "限价待沽",
    [OrderStatus.LIMIT_BUYING]: "限价待购",
    [OrderStatus.VWAP_SELLING]: "VWAP待沽",
    [OrderStatus.VWAP_BUYING]: "VWAP待购",
  };
  return statuses[status] || status;
};

// 添加移动端检测
const isMobile = ref(window.innerWidth < 768);
window.addEventListener("resize", () => {
  isMobile.value = window.innerWidth < 768;
});

// 添加返回列表方法
const backToList = () => {
  selectedNotification.value = null;
};

// 添加事件监听
onMounted(() => {
  loadNotifications();
  // 监听通知更新事件
  eventBus.on("notification-updated", () => {
    loadNotifications();
  });
});

// 记得在组件卸载时移除事件监听
onUnmounted(() => {
  eventBus.off("notification-updated", () => {
    loadNotifications();
  });
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: var(--el-fill-color-light);
  border-radius: 8px;
  width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color);

  h3 {
    margin: 5px 0;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-container {
  display: flex;
  height: calc(80vh - 64px);
  min-height: 300px;
  max-height: 500px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .modal-content {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }

  .notification-container {
    height: calc(100vh - 64px);
    max-height: none;
  }

  .notification-list {
    width: 100%;
    border-right: none;
  }

  .notification-detail {
    display: none;
    /* 默认隐藏详情 */
  }

  /* 当选中通知时显示详情，隐藏列表 */
  .notification-container:has(.notification-detail:not(:empty)) {
    .notification-list {
      display: none;
    }

    .notification-detail {
      display: block;
    }
  }

  .modal-header {
    padding: 12px;
  }

  .header-actions {
    gap: 12px;
  }

  .notification-brief {
    gap: 8px;
  }
}

.notification-list {
  width: 300px;
  height: 100%;
  overflow-y: auto;
  border-right: 1px solid var(--el-border-color);
}

.notification-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--el-border-color);
  color: var(--el-text-color-secondary);

  &.unread {
    color: var(--el-text-color-primary);
  }

}

.notification-item:hover {
  background: var(--hover-bg-color);
}

.notification-item.active {
  background: var(--el-fill-color-light);
}

.notification-brief {
  display: flex;
  gap: 12px;
}

.notification-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  background: var(--el-fill-color-lighter);
}

.notification-info {
  flex: 1;
}

.notification-title {
  font-size: 14px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-amount {
  color: var(--el-color-primary);
  font-size: 14px;
  font-weight: 500;
  margin: 4px 0;
}

.notification-time {
  font-size: 12px;
  text-align: right;
}

.notification-detail {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.detail-header {
  margin-bottom: 24px;
}

.detail-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.notification-meta {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-top: 8px;
}

.notification-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  background: var(--success-bg-color);
  color: var(--success-text-color);
}

.notification-status.unread {
  background: var(--primary-bg-color);
  color: var(--primary-text-color);
}

.metadata-info {
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  padding: 16px;
}

.metadata-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color);
}

.metadata-row:last-child {
  border-bottom: none;
}

.metadata-row .label {
  color: var(--text-color-secondary);
  font-size: 14px;
  min-width: 80px;
}

.metadata-row .value {
  font-size: 14px;
}

.metadata-row .value.highlight {
  color: var(--el-color-primary);
  font-size: 16px;
  font-weight: 500;
}

.detail-time {
  margin-top: 16px;
  text-align: right;
  color: var(--text-color-secondary);
  font-size: 12px;
}

.empty-state,
.no-selection {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-color-secondary);
}

.loading-state {
  padding: 20px;
  width: 100%;
}

.close-button {
  padding: 5px;
  display: flex;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-color-secondary);
  padding: 40px 0;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.5;
}

.notification-content {
  margin-bottom: 16px;
  line-height: 1.5;
  font-size: 14px;
  white-space: pre-line;
}

.unread-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--el-color-primary-dark-2);
  flex-shrink: 0;
}

.loading-more {
  padding: 10px;
  text-align: center;
}

.notification-list {
  height: 100%;
  overflow-y: auto;
}

/* 移动端返回按钮样式 */
.mobile-back-header {
  display: none;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color);
}

@media (max-width: 768px) {
  .mobile-back-header {
    display: flex;
    align-items: center;
  }

  .notification-detail {
    padding-top: 0;
    /* 移除顶部内边距，因为有了返回按钮 */
  }

  .detail-header {
    margin-top: 16px;
    /* 添加一些间距 */
  }
}
</style>
