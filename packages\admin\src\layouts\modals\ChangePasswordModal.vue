<template>
  <el-dialog title="修改密码" v-model="visible" width="500px">
    <el-form ref="passwordForm" :model="form" :rules="rules" label-width="140px">
      <el-form-item label="当前密码" prop="oldPassword">
        <el-input v-model="form.oldPassword" type="password" show-password />
      </el-form-item>

      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="form.newPassword" type="password" show-password />
      </el-form-item>

      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input v-model="form.confirmPassword" type="password" show-password />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        确认
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { authApi } from '@/api'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const visible = defineModel<boolean>('visible')
const loading = ref(false)
const passwordForm = ref<FormInstance>()

const form = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const rules: FormRules = {
  oldPassword: [{ required: true, message: '请输入当前密码' }],
  newPassword: [{ required: true, message: '请输入新密码' }],
  confirmPassword: [
    { required: true, message: '请确认新密码' },
    {
      validator: (_rule, value, callback) => {
        if (value !== form.value.newPassword) {
          callback(new Error('密码不一致'))
        } else {
          callback()
        }
      },
    },
  ],
}

const handleSubmit = async () => {
  if (!passwordForm.value) return

  try {
    await passwordForm.value.validate()
    loading.value = true

    await authApi.updatePassword(form.value.oldPassword, form.value.newPassword)
    ElMessage.success('密码修改成功')
    visible.value = false
    form.value = {
      oldPassword: '',
      newPassword: '',
      confirmPassword: '',
    }
    useAuthStore().logout()
  } catch (error) {
    console.error('Failed to update password:', error)
    ElMessage.error('密码修改失败')
  } finally {
    loading.value = false
  }
}
</script>