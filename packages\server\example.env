# 数据库密码
DB_PASSWORD=postgres
REDIS_PASSWORD=redis
MYSQL_PASSWORD=mysql

# JWT密钥 (用于身份验证)
JWT_SECRET=your-secret-key-here

# 加密密钥 (用于数据加密)
ENCRYPTION_KEY=your-encryption-key-here

# 第三方API密钥
TUSHARE_TOKEN=your-tushare-token
NOWAPI_SIGN=your-nowapi-sign
MAIRUI_LICENSE=your-mairui-license

# 邮箱密码
EMAIL_PASS=your-email-password

# 合作伙伴API密钥
OPEN_API_KEY=your-open-api-key

# 应用ID
TRADING_PLATFORM_ID=
CHANNEL_ID=

# 端口
PORT=3000

# 生产环境
NODE_ENV=production

# 演示？
IS_DEMO=false

# Docker开发环境？
IS_DOCKER_DEV=false