/**
 * 通道订单服务 - 用于处理交易台查看通道订单的功能
 */
import { AppError } from "@/core/appError.js";
import {
	getChannelDbConnection,
	isChannelDbConfigured,
} from "@/lib/channelDatabaseManager.js";
import type { OrderStatus } from "@packages/shared";

// 订单数据接口
interface OrderData {
	trade_no: string;
	user_id: number;
	ts_code: string;
	entry_price: number;
	exercise_price: number;
	settle_price: number;
	scale: number;
	total_scale: number;
	term: number;
	quote: number;
	status: OrderStatus;
	created_at: string;
	closed_at: string | null;
	is_split: boolean;
	structure: string;
	expiry_date: string | null;
	expiry_date_confirmed: boolean;
	quote_provider?: string;
	quote_diff?: number;
	channel_id?: string;
}

// 查询选项接口
interface QueryOptions {
	page?: number;
	pageSize?: number;
	sortBy?: string;
	sortOrder?: string;
	filters?: {
		ts_codes?: string[];
		startDate?: string;
		endDate?: string;
		status?: OrderStatus;
	};
}

// Prisma where子句类型
interface OrderWhereClause {
	ts_code?: { in: string[] };
	status?: OrderStatus;
	created_at?: {
		gte?: Date;
		lt?: Date;
	};
}

/**
 * 获取特定通道的订单列表
 */
export async function getChannelOrders(
	channelId: string,
	options: QueryOptions = {},
): Promise<{ items: OrderData[]; total: number }> {
	if (!channelId) {
		throw AppError.create("BAD_REQUEST", "通道ID不能为空");
	}

	// 检查通道数据库配置是否存在
	if (!isChannelDbConfigured(channelId)) {
		throw AppError.create("NOT_FOUND", `未找到通道 ${channelId} 的数据库配置`);
	}

	// 默认查询配置
	const {
		page = 1,
		pageSize = 10,
		sortBy = "created_at",
		sortOrder = "DESC",
		filters = {},
	} = options;

	try {
		// 获取通道数据库连接
		const channelPrisma = await getChannelDbConnection(channelId);

		// 构建查询条件
		const where: OrderWhereClause = {};

		// 添加股票代码过滤
		if (filters.ts_codes?.length) {
			where.ts_code = { in: filters.ts_codes };
		}

		// 添加状态过滤
		if (filters.status) {
			// 只有当status不是'all'时才添加筛选条件
			const statusFilter = filters.status as string;
			if (statusFilter !== "all") {
				where.status = filters.status;
			}
		}

		// 添加日期过滤
		if (filters.startDate || filters.endDate) {
			where.created_at = {};
			if (filters.startDate) {
				where.created_at.gte = new Date(filters.startDate);
			}
			if (filters.endDate) {
				// 加一天以包含当天
				const endDate = new Date(filters.endDate);
				endDate.setDate(endDate.getDate() + 1);
				where.created_at.lt = endDate;
			}
		}

		// 计算总数
		const total = await channelPrisma.orders.count({ where });

		// 查询订单数据
		const orders = await channelPrisma.orders.findMany({
			where,
			orderBy: [{ [sortBy]: sortOrder.toLowerCase() }, { created_at: "desc" }],
			skip: (page - 1) * pageSize,
			take: pageSize,
		});

		// 转换数据格式
		return {
			items: orders.map((order) => ({
				...order,
				entry_price: Number(order.entry_price),
				exercise_price: Number(order.exercise_price),
				settle_price: Number(order.settle_price),
				quote: Number(order.quote),
				created_at: order.created_at?.toISOString() || "",
				closed_at: order.closed_at?.toISOString() || null,
				expiry_date: order.expiry_date?.toISOString() || null,
				expiry_date_confirmed: order.expiry_date_confirmed || false,
				quote_provider: order.quote_provider || "INK",
				quote_diff: order.quote_diff ? Number(order.quote_diff) : undefined,
				channel_id: channelId, // 显式包含通道ID
				status: order.status as OrderStatus, // 类型转换确保兼容
			})) as OrderData[],
			total,
		};
	} catch (error: unknown) {
		console.error(`获取通道 ${channelId} 订单失败:`, error);
		const errorMessage = error instanceof Error ? error.message : String(error);
		throw AppError.create("SERVER_ERROR", `获取通道订单失败: ${errorMessage}`);
	}
}

/**
 * 获取特定通道的订单汇总信息
 */
export async function getChannelOrdersSummary(channelId: string): Promise<{
	totalOrders: number;
	completedOrders: number;
	totalNotional: number;
	totalPremium: number;
}> {
	if (!channelId) {
		throw AppError.create("BAD_REQUEST", "通道ID不能为空");
	}

	// 检查通道数据库配置是否存在
	if (!isChannelDbConfigured(channelId)) {
		throw AppError.create("NOT_FOUND", `未找到通道 ${channelId} 的数据库配置`);
	}

	try {
		// 获取通道数据库连接
		const channelPrisma = await getChannelDbConnection(channelId);

		// 获取订单总数
		const totalOrders = await channelPrisma.orders.count();

		// 获取已结算订单数量
		const completedOrders = await channelPrisma.orders.count({
			where: { status: "sold" },
		});

		// 获取总交易量
		const orders = await channelPrisma.orders.findMany({
			select: {
				scale: true,
				quote: true,
			},
		});

		// 计算总名本: scale
		const totalNotional = orders.reduce((sum, order) => {
			return sum + Number(order.scale);
		}, 0);

		// 计算总期权费: quote * scale *  / 100 (万)
		const totalPremium = orders.reduce((sum, order) => {
			return sum + (Number(order.quote) * Number(order.scale)) / 100;
		}, 0);

		return {
			totalOrders,
			completedOrders,
			totalNotional,
			totalPremium,
		};
	} catch (error: unknown) {
		console.error(`获取通道 ${channelId} 订单汇总失败:`, error);
		const errorMessage = error instanceof Error ? error.message : String(error);
		throw AppError.create(
			"SERVER_ERROR",
			`获取通道订单汇总失败: ${errorMessage}`,
		);
	}
}
