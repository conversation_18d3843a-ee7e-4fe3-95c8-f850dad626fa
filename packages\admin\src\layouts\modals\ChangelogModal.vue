<template>
  <el-dialog v-model="visible" title="更新日志" width="600px" top="8vh" class="changelog-modal">
    <div class="changelog-content">
      <!-- 年份选择器 -->
      <div class="year-selector">
        <el-select v-model="selectedYear" placeholder="选择年份" @change="loadChangelog" class="year-select">
          <el-option v-for="year in availableYears" :key="year" :label="`${year}年`" :value="year" />
        </el-select>
      </div>

      <!-- 更新日志内容 -->
      <div class="changelog-list" v-loading="loading">
        <div v-if="changelogItems.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无更新日志" />
        </div>

        <div v-else class="changelog-items">
          <div v-for="(item, index) in changelogItems" :key="index" class="changelog-item">
            <div class="changelog-month">
              <span class="month-badge">{{ item.month }}</span>
            </div>
            <div class="changelog-changes">
              <ul class="change-list">
                <li v-for="(change, changeIndex) in item.changes" :key="changeIndex" class="change-item">
                  {{ change }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

interface ChangelogItem {
  month: string;
  changes: string[];
}

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const loading = ref(false);
const selectedYear = ref(new Date().getFullYear().toString());
const changelogItems = ref<ChangelogItem[]>([]);

// 可用年份列表（从2025年开始）
const availableYears = computed(() => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let year = currentYear; year >= 2025; year--) {
    years.push(year.toString());
  }
  return years;
});

// 解析 Markdown 格式的更新日志
const parseChangelog = (content: string): ChangelogItem[] => {
  const items: ChangelogItem[] = [];
  const lines = content.split('\n').filter(line => line.trim());

  let currentItem: ChangelogItem | null = null;

  for (const line of lines) {
    const trimmedLine = line.trim();

    // 检查是否是月份标题（格式：## 05月 或 ## 5月）
    const monthMatch = trimmedLine.match(/^##\s*(\d{1,2})月?/);
    if (monthMatch) {
      if (currentItem) {
        items.push(currentItem);
      }
      const monthNum = monthMatch[1].padStart(2, '0');
      currentItem = {
        month: `${monthNum}月`,
        changes: []
      };
    }
    // 检查是否是更新项（格式：- 内容）
    else if (trimmedLine.startsWith('- ') && currentItem) {
      const change = trimmedLine.substring(2).trim();
      if (change) {
        currentItem.changes.push(change);
      }
    }
  }

  // 添加最后一个项目
  if (currentItem && currentItem.changes.length > 0) {
    items.push(currentItem);
  }

  return items;
};

// 加载更新日志
const loadChangelog = async () => {
  loading.value = true;
  changelogItems.value = [];

  try {
    // 尝试加载指定年份的更新日志文件
    const response = await fetch(`/changelogs/${selectedYear.value}.md`);

    if (!response.ok) {
      if (response.status === 404) {
        // 文件不存在，显示空状态
        changelogItems.value = [];
        return;
      }
      throw new Error(`加载失败: ${response.statusText}`);
    }

    const content = await response.text();
    changelogItems.value = parseChangelog(content);

  } catch (error) {
    console.error('加载更新日志失败:', error);
    if (selectedYear.value === new Date().getFullYear().toString()) {
      // 如果是当前年份，使用默认内容
      changelogItems.value = [
        {
          month: '05月',
          changes: [
            '新增协议管理',
            '新增客户证明文件导出',
            '新增通道管理'
          ]
        }
      ];
    } else {
      ElMessage.warning(`无法加载 ${selectedYear.value} 年的更新日志`);
    }
  } finally {
    loading.value = false;
  }
};

// 监听年份变化
watch(selectedYear, loadChangelog);

// 监听模态框打开状态
watch(visible, (newVisible) => {
  if (newVisible) {
    loadChangelog();
  }
});

onMounted(() => {
  if (visible.value) {
    loadChangelog();
  }
});
</script>

<style scoped>
/* 移除不必要的CSS变量，直接使用Element Plus变量 */

.changelog-content {
  max-height: 60vh;
  display: flex;
  flex-direction: column;
}

.year-selector {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.year-select {
  width: 120px;
}

.changelog-list {
  flex: 1;
  overflow-y: auto;
  min-height: 200px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.changelog-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.changelog-item {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.changelog-item:last-child {
  border-bottom: none;
}

.changelog-month {
  flex-shrink: 0;
  width: 60px;
}

.month-badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  text-align: center;
  min-width: 50px;
  border: 1px solid var(--el-color-primary-light-5);
  color: white;
}

.changelog-changes {
  flex: 1;
  min-width: 0;
}

.change-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.change-item {
  position: relative;
  padding: 6px 0 6px 20px;
  color: var(--el-text-color-primary);
  line-height: 1.5;
  font-size: 14px;
}

.change-item:before {
  content: '•';
  position: absolute;
  left: 0;
  top: 6px;
  color: var(--el-color-primary);
  font-weight: bold;
  font-size: 16px;
}

.change-item:hover {
  color: var(--el-color-primary);
  transition: color 0.2s ease;
}

/* 滚动条样式 */
.changelog-list::-webkit-scrollbar {
  width: 6px;
}

.changelog-list::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
  border-radius: 3px;
}

.changelog-list::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.changelog-list::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .changelog-item {
    flex-direction: column;
    gap: 8px;
  }

  .changelog-month {
    width: auto;
  }

  .month-badge {
    display: inline-block;
    min-width: auto;
  }
}

/* 暗色模式适配 */
:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding: 16px 24px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid var(--el-border-color-lighter);
  padding: 12px 24px;
}
</style>