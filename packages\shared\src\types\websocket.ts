import type { OrderStatus, OrderType, TradeDirection } from "./order";

export enum WebSocketMessageType {
	NOTIFICATION_UPDATE = "notification_update",
	PAGE_RELOAD = "page_reload",
	ORDER_UPDATE = "order_update",
	AUTH_UPDATE = "auth_update",
	SYSTEM_AVAILABLE_UPDATE = "system_available_update",
	PING = "ping",
	PONG = "pong",
}

export type WebSocketMessageData = {
	[WebSocketMessageType.NOTIFICATION_UPDATE]: undefined;
	[WebSocketMessageType.PAGE_RELOAD]: undefined;
	[WebSocketMessageType.ORDER_UPDATE]: {
		orderType: OrderType;
		direction: TradeDirection;
	};
	[WebSocketMessageType.AUTH_UPDATE]: undefined;
	[WebSocketMessageType.SYSTEM_AVAILABLE_UPDATE]: undefined;
	[WebSocketMessageType.PING]: undefined;
	[WebSocketMessageType.PONG]: undefined;
};

export interface WebSocketMessage {
	type: WebSocketMessageType;
	data?: WebSocketMessageData[WebSocketMessageType];
}
