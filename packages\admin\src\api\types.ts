import type {
	AuditStatus,
	AuditType,
	TransactionType,
	AdminLoginRequest,
	AdminLoginResponse,
	OrderStatus,
} from "@packages/shared";

export type LoginParams = AdminLoginRequest;
export type AuthResponse = AdminLoginResponse;

export interface QueryOptions {
	page?: number;
	pageSize?: number;
	sortBy?: string;
	sortOrder?: "ASC" | "DESC";
	filters?: {
		status?: AuditStatus;
		type?: AuditType;
		operation?: TransactionType;
		user_id?: number;
	};
}

export interface UserQueryOptions {
	page?: number;
	pageSize?: number;
	sortBy?: string;
	sortOrder?: "ASC" | "DESC";
	filters?: {
		user_id?: number;
		email?: string;
		phone_number?: string;
		name?: string;
		is_qualified?: boolean;
	};
}

export interface OrderQueryOptions {
	sortBy?: string;
	sortOrder?: "ASC" | "DESC";
	status?: string;
	startDate?: string;
	endDate?: string;
}

export interface PendingOrderQueryOptions {
	types?: OrderStatus[];
	ts_code?: string;
	user_id?: number;
}

export interface LeadQueryOptions {
	page?: number;
	pageSize?: number;
	sortBy?: string;
	sortOrder?: "ASC" | "DESC";
	filters?: {
		id?: number;
		phone_number?: string;
		source?: string;
		created_at_start?: string;
		created_at_end?: string;
	};
}
