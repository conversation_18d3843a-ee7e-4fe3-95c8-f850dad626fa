import { formatDate } from "./format";

export const exportToCsv = (
	headers: string[],
	data: (string | number | Date)[][],
	filename: string,
) => {
	const csvContent = [headers, ...data]
		.map((row) =>
			row
				.map((cell) => {
					// 处理日期类型
					if (cell instanceof Date) {
						return `"${formatDate(cell)}"`;
					}
					// 处理包含逗号、换行符或引号的单元格
					if (
						typeof cell === "string" &&
						(cell.includes(",") || cell.includes("\n") || cell.includes('"'))
					) {
						return `"${cell.replace(/"/g, '""')}"`;
					}
					return cell;
				})
				.join(","),
		)
		.join("\n");

	const blob = new Blob([`\uFEFF${csvContent}`], {
		type: "text/csv;charset=utf-8",
	});
	const url = window.URL.createObjectURL(blob);
	const link = document.createElement("a");
	link.href = url;
	link.setAttribute("download", `${filename}-${formatDate(new Date())}.csv`);
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
	window.URL.revokeObjectURL(url);
};
