<template>
  <div class="manual-order-view view">
    <div class="manual-orders card">
      <div class="card-header">
        <div class="card-title">
          录单记录
          <span class="card-subtitle">外部订单管理，非真实交易订单</span>
        </div>
        <el-button type="primary" size="small" @click="showManualOrderModal = true">新增录单</el-button>
      </div>

      <div class="filter-row">
        <div class="form-group date-filter">
          <label for="manual-order-dateRange">日期范围：</label>
          <DateRangePicker id="manual-order-dateRange" v-model:startDate="startDate" v-model:endDate="endDate"
            placeholder="所有" @change="handleDateChange" />
        </div>

        <div class="form-group">
          <label for="orderSubject">选择标的：</label>
          <MySelect id="orderSubject" v-model="selectedSubject" :options="subjectOptions" placeholder="全部"
            :page-size="20" />
        </div>

        <div class="form-group">
          <label for="orderStatus">状态：</label>
          <el-select v-model="selectedStatus" placeholder="全部状态" class="status-filter" clearable>
            <el-option v-for="option in statusOptions" :key="option.value" :label="option.label"
              :value="option.value" />
          </el-select>
        </div>
      </div>

      <TableWrapper v-model:page-size="pageSize" v-model:current-page="currentPage" v-model:is-descending="isDescending"
        :total-pages="totalPages">
        <!-- 当没有数据时显示居中的 loading -->
        <LoadingState v-if="!manualOrders.length" :loading="isLoading" :has-data="manualOrders.length > 0"
          :icon="DataLine" :overlay="true" />

        <!-- 有数据时显示表格和覆盖式 loading -->
        <template v-else>
          <div class="table-container">
            <LoadingState :loading="isLoading" :has-data="manualOrders.length > 0" :icon="DataLine"
              :class="['loading-overlay', { 'visible': isLoading }]" />
            <table class="data-table">
              <thead>
                <tr>
                  <th>标的</th>
                  <th class="desktop-only">开仓日期</th>
                  <th class="desktop-only">名本</th>
                  <th class="desktop-only">期限</th>
                  <th>到期日</th>
                  <th class="desktop-only">开仓价</th>
                  <th class="desktop-only">结构</th>
                  <th class="desktop-only">执行价</th>
                  <th class="desktop-only">交易方</th>
                  <th class="desktop-only">市价</th>
                  <th class="desktop-only">期权费率</th>
                  <th>收益</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <template v-for="order in manualOrders" :key="order.manual_order_id">
                  <tr class="order-row"
                    :class="{ 'selected': selectedOrderRow?.manual_order_id === order.manual_order_id }"
                    @click="toggleOrderDetails(order)">
                    <td>{{ formatSubject(order.ts_code) }}</td>
                    <td class="desktop-only">{{ order.entry_date ? formatDate(order.entry_date) : '-' }}</td>
                    <td class="desktop-only">{{ order.scale + "万" }}</td>
                    <td class="desktop-only">{{ order.term === 14 ? '2周' : order.term + "个月" }}</td>
                    <td>{{ order.expiry_date ? formatDate(order.expiry_date) : '-' }}</td>
                    <td class="desktop-only">{{ order.entry_price }}</td>
                    <td class="desktop-only">{{ formatStructure(order.structure) }}</td>
                    <td class="desktop-only">{{ formatNumber(order.exercise_price) }}</td>
                    <td class="desktop-only">
                      <span class="provider">
                        {{ order.quote_provider }}
                      </span>
                    </td>
                    <td class="desktop-only">{{ getCurrentPrice(order.ts_code) ?
                      getCurrentPrice(order.ts_code)?.toFixed(2) : '-' }}</td>
                    <td class="desktop-only">{{ order.quote + '%' }}</td>
                    <td :class="getProfitClass(calculateOrderValue(order))">
                      {{ calculateOrderValue(order) == 0 ? '0' : formatNumber(calculateOrderValue(order)) }}
                    </td>
                    <td>
                      <span class="status-badge" :class="getStatusClass(order.status)">
                        {{ getStatusText(order.status) }}
                      </span>
                    </td>
                    <td>
                      <div class="action-buttons">
                        <el-button v-if="order.status === 'holding'" class="action-button"
                          @click.stop="handleSettleOrder(order)"
                          :disabled="submittingOrderId === order.manual_order_id">
                          结算
                        </el-button>
                        <el-button class="action-button" @click.stop="handleEditOrder(order)"
                          :disabled="submittingOrderId === order.manual_order_id">
                          编辑
                        </el-button>
                        <el-button class="action-button danger" @click.stop="handleDeleteOrder(order)"
                          :disabled="submittingOrderId === order.manual_order_id">
                          删除
                        </el-button>
                      </div>
                    </td>
                  </tr>
                  <template v-if="selectedOrderRow?.manual_order_id === order.manual_order_id">
                    <tr class="mobile-only details-row">
                      <td colspan="5">
                        <div class="order-details details-transition">
                          <div class="detail-item">
                            <span class="label">开仓日期：</span>
                            <span class="value">{{ order.entry_date ? formatDate(order.entry_date) : '-' }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">名本：</span>
                            <span class="value">{{ order.scale + "万" }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">期限：</span>
                            <span class="value">{{ order.term === 14 ? '2周' : order.term + "个月" }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">开仓价：</span>
                            <span class="value">{{ order.entry_price }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">结构：</span>
                            <span class="value">{{ formatStructure(order.structure) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">执行价：</span>
                            <span class="value">{{ formatNumber(order.exercise_price) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">交易方：</span>
                            <span class="value">{{ order.quote_provider }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">市价：</span>
                            <span class="value">{{ getCurrentPrice(order.ts_code) ?
                              getCurrentPrice(order.ts_code)?.toFixed(2) : '-' }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">期权费率：</span>
                            <span class="value">{{ order.quote + '%' }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">收益：</span>
                            <span class="value" :class="getProfitClass(calculateOrderValue(order))">
                              {{ calculateOrderValue(order) == 0 ? '0' : formatNumber(calculateOrderValue(order)) }}
                            </span>
                          </div>
                          <div class="detail-item" v-if="order.remarks">
                            <span class="label">备注：</span>
                            <span class="value">{{ order.remarks }}</span>
                          </div>
                          <div class="detail-item" v-if="order.exit_date">
                            <span class="label">结算日期：</span>
                            <span class="value">{{ formatDate(order.exit_date) }}</span>
                          </div>
                          <div class="detail-item" v-if="order.settle_price">
                            <span class="label">结算价：</span>
                            <span class="value">{{ order.settle_price }}</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr class="dummy-row"></tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </template>
      </TableWrapper>
    </div>

    <!-- 模态框 -->
    <ManualOrderModal v-model="showManualOrderModal" :order-data="currentOrder" @submit="handleSubmitOrder" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted, onActivated, onDeactivated } from "vue";
import { useRoute } from "vue-router";
import MySelect from "@/components/MySelect.vue";
import DateRangePicker from "@/components/DateRangePicker.vue";
import TableWrapper from "@/components/TableWrapper.vue";
import LoadingState from "@/components/LoadingState.vue";
import { DataLine } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useTableSettings } from "@/composables/useTableSettings";
import { formatStructure, formatDate } from "@/utils/format";
import { useStockStore } from "@/stores/stock";
import { manualOrderApi } from "@/api";
import ManualOrderModal from "./modals/ManualOrderModal.vue";
import { ManualOrderStatus, StructureType } from "@packages/shared";
import type { ManualOrderData, ManualOrderRequest } from "@packages/shared";
import { fetchCurrentPrices } from "@/utils/stock";

const route = useRoute();
const { formatSubject } = useStockStore();

// 表格设置
const { initSettings, pageSize, isDescending, currentPage, totalPages } = useTableSettings();

// 筛选
const selectedSubject = ref<string[]>([]);
const selectedStatus = ref<string[]>([]);
const startDate = ref("");
const endDate = ref("");

// 表格数据
const manualOrders = ref<ManualOrderData[]>([]);
const isLoading = ref(false);
const availableTsCodes = ref<string[]>([]);
const submittingOrderId = ref<number | null>(null);

// 价格相关状态
const currentPrices = ref<Map<string, number>>(new Map());
let pricePollingTimer: number | null = null;
const INTERVAL_PRICE_POLLING = 5000;

// 模态框
const showManualOrderModal = ref(false);
const currentOrder = ref<ManualOrderData | null>(null);

// 状态选项
const statusOptions = [
  { value: "holding", label: "持仓中" },
  { value: "sold", label: "已结算" },
];

// 计算属性
const subjectOptions = computed(() => {
  if (!availableTsCodes.value?.length) return [];

  return availableTsCodes.value.map((ts_code) => ({
    value: ts_code,
    label: formatSubject(ts_code),
  }));
});

// 计算收益值（预估或实际）
const calculateOrderValue = (order: ManualOrderData): number => {
  // 如果是已结算状态，计算实际收益
  if (order.status === 'sold' && order.settle_price) {
    return calculateActualValue(order);
  }

  // 如果是持仓中状态，计算预估收益
  if (order.status === 'holding') {
    return calculateEstimatedValue(order);
  }

  return 0;
};

// 计算实际收益（基于结算价）
const calculateActualValue = (order: ManualOrderData): number => {
  if (!order.settle_price) return 0;

  // 根据结构判断是看涨还是看跌
  const isCall = order.structure.endsWith("C");

  if (isCall) {
    // 看涨期权：当结算价高于执行价时有价值
    if (order.settle_price <= order.exercise_price) return 0;
    return (
      (order.scale * 10000 * (order.settle_price - order.exercise_price)) /
      order.entry_price
    );
  }

  // 看跌期权：当结算价低于执行价时有价值
  if (order.settle_price >= order.exercise_price) return 0;
  return (
    (order.scale * 10000 * (order.exercise_price - order.settle_price)) /
    order.entry_price
  );
};

// 计算预估收益（基于当前市价）
const calculateEstimatedValue = (order: ManualOrderData): number => {
  const currentPrice = getCurrentPrice(order.ts_code);
  if (!currentPrice) return 0;

  // 根据结构判断是看涨还是看跌
  const isCall = order.structure.endsWith("C");

  if (isCall) {
    // 看涨期权：当市价高于执行价时有价值
    if (currentPrice <= order.exercise_price) return 0;
    return (
      (order.scale * 10000 * (currentPrice - order.exercise_price)) /
      order.entry_price
    );
  }

  // 看跌期权：当市价低于执行价时有价值
  if (currentPrice >= order.exercise_price) return 0;
  return (
    (order.scale * 10000 * (order.exercise_price - currentPrice)) /
    order.entry_price
  );
};

// 获取当前价格
const getCurrentPrice = (tsCode: string): number | undefined => {
  return currentPrices.value.get(tsCode);
};

// 获取收益样式类
const getProfitClass = (value: number | string): string => {
  const numValue = typeof value === "string" ? Number.parseFloat(value) : value;
  if (Number.isNaN(numValue) || value === "-") return "";
  return numValue > 0 ? "profit-positive" : "";
};

// 批量获取当前价格
const updateCurrentPrices = async () => {
  const holdingOrders = manualOrders.value.filter(order => order.status === 'holding');
  if (holdingOrders.length === 0) return;

  try {
    const codes = holdingOrders.map((order) => order.ts_code);
    const prices = await fetchCurrentPrices(codes);

    // 更新价格映射
    prices.forEach((price, index) => {
      currentPrices.value.set(codes[index], price);
    });
  } catch (error) {
    console.error("Failed to fetch current prices:", error);
  }
};

// 设置价格轮询
const setupPricePolling = () => {
  // 清除现有定时器
  if (pricePollingTimer) {
    clearInterval(pricePollingTimer);
  }

  // 立即执行一次
  updateCurrentPrices();

  // 设置定时轮询
  pricePollingTimer = window.setInterval(
    updateCurrentPrices,
    INTERVAL_PRICE_POLLING,
  );
};

// 加载手动录单数据
const loadManualOrders = async () => {
  try {
    isLoading.value = true;

    const response = await manualOrderApi.getManualOrders(
      currentPage.value,
      pageSize.value,
      isDescending.value,
      {
        ts_codes: selectedSubject.value,
        status: selectedStatus.value,
        startDate: startDate.value || undefined,
        endDate: endDate.value || undefined,
      }
    );

    if (!response) {
      console.log("Response to get manual orders is null!");
      return;
    }

    manualOrders.value = response.items || [];
    totalPages.value = Math.ceil((response.total || 1) / pageSize.value);
    availableTsCodes.value = response.ts_codes || [];
  } catch (error) {
    console.error("Failed to load manual orders:", error);
    ElMessage.error("获取录单记录失败");
  } finally {
    isLoading.value = false;
  }
};

// 日期过滤处理
const handleDateChange = () => {
  loadManualOrders();
};

// 格式化数字
const formatNumber = (num: number) => {
  return new Intl.NumberFormat("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(num);
};

// 获取状态文本
const getStatusText = (status: ManualOrderStatus) => {
  switch (status) {
    case "holding":
      return "持仓中";
    case "sold":
      return "已结算";
    default:
      return status;
  }
};

// 获取状态样式类
const getStatusClass = (status: ManualOrderStatus) => {
  switch (status) {
    case "holding":
      return "status-holding";
    case "sold":
      return "status-sold";
    default:
      return "";
  }
};

// 处理编辑订单
const handleEditOrder = (order: ManualOrderData) => {
  currentOrder.value = { ...order };
  showManualOrderModal.value = true;
};

// 处理结算订单
const handleSettleOrder = async (order: ManualOrderData) => {
  try {
    const confirmed = await ElMessageBox.confirm(
      "确定要将此录单标记为已结算吗？",
      "结算确认",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    if (confirmed) {
      submittingOrderId.value = order.manual_order_id;
      await manualOrderApi.updateManualOrder(order.manual_order_id, {
        status: ManualOrderStatus.SOLD,
        exit_date: new Date().toISOString(),
        settle_price: order.entry_price,
      });
      ElMessage.success("结算成功");
      loadManualOrders();
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("Failed to settle order:", error);
      ElMessage.error("结算失败");
    }
  } finally {
    submittingOrderId.value = null;
  }
};

// 处理删除订单
const handleDeleteOrder = async (order: ManualOrderData) => {
  try {
    const confirmed = await ElMessageBox.confirm(
      "确定要删除此录单记录吗？此操作不可撤销。",
      "删除确认",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    if (confirmed) {
      submittingOrderId.value = order.manual_order_id;
      await manualOrderApi.deleteManualOrder(order.manual_order_id);
      ElMessage.success("删除成功");
      loadManualOrders();
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("Failed to delete order:", error);
      ElMessage.error("删除失败");
    }
  } finally {
    submittingOrderId.value = null;
  }
};

// 处理提交订单
const handleSubmitOrder = async (data: ManualOrderRequest | ManualOrderData) => {
  try {
    isLoading.value = true;

    if ('manual_order_id' in data) {
      // 更新
      const { manual_order_id, entry_date, ...rest } = data;
      await manualOrderApi.updateManualOrder(manual_order_id, rest);
      ElMessage.success("更新成功");
    } else {
      // 创建
      await manualOrderApi.createManualOrder(data);
      ElMessage.success("创建成功");
    }

    showManualOrderModal.value = false;
    currentOrder.value = null;
    await loadManualOrders();
  } catch (error) {
    console.error("Failed to submit order:", error);
    ElMessage.error("提交失败");
  } finally {
    isLoading.value = false;
  }
};

// 移动端详情展开
const selectedOrderRow = ref<ManualOrderData | null>(null);

const toggleOrderDetails = (order: ManualOrderData) => {
  if (selectedOrderRow.value?.manual_order_id === order.manual_order_id) {
    selectedOrderRow.value = null;
  } else {
    selectedOrderRow.value = order;
  }
};

// 从URL参数中提取初始值
onMounted(() => {
  initSettings();

  // 读取URL参数
  const query = route.query;

  // 如果有URL参数，则打开模态框
  if (query.ts_code) {
    const initialData: ManualOrderRequest = {
      ts_code: query.ts_code as string,
      structure: query.structure as StructureType,
      scale: Number(query.scale || 0),
      term: Number(query.term || 0),
      quote_provider: query.provider as string || "",
      quote: Number(query.quote || 0),
      entry_price: 0,
      exercise_price: 0,
    };

    // 如果存在所有必需字段，则显示模态框
    if (initialData.ts_code && initialData.structure &&
      initialData.scale && initialData.term &&
      initialData.quote_provider && initialData.quote) {
      currentOrder.value = initialData as ManualOrderData;
      showManualOrderModal.value = true;
    }
  }

  // 加载数据
  loadManualOrders();
});

// 监听数据变化，重新设置轮询
watch(manualOrders, () => {
  setupPricePolling();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (pricePollingTimer) {
    clearInterval(pricePollingTimer);
  }
});

onActivated(() => {
  // 每次激活时都更新价格
  setupPricePolling();
});

// 组件失活时清理定时器
onDeactivated(() => {
  if (pricePollingTimer) {
    clearInterval(pricePollingTimer);
  }
});

// 监听筛选变化
watch([selectedSubject, selectedStatus], () => {
  currentPage.value = 1; // 重置到第一页
  loadManualOrders(); // 重新获取数据
});

// 监听分页和排序变化
watch([currentPage, pageSize, isDescending], () => {
  loadManualOrders();
});
</script>

<style scoped>
/* 卡片标题样式 */
.card-subtitle {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-weight: normal;
  margin-left: 12px;
}

/* 统一标签样式 */
.form-group label {
  min-width: 80px;
  text-align: left;
}

/* 统一选择框样式 */
.status-filter :deep(.el-select__wrapper) {
  min-height: 40px;
  width: 230px;
  background-color: var(--el-fill-color-light);
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.action-button {
  padding: 2px 8px;
  font-size: 12px;
}

.action-button.danger {
  color: var(--el-color-error);
}

.action-button.danger:hover {
  border-color: var(--el-color-error);
  background-color: rgba(var(--el-color-error-rgb), 0.1);
}

/* 国内股市红涨绿跌 */
.profit-positive {
  color: var(--el-color-error);
}

.profit-negative {
  color: var(--el-color-success);
}

/* 状态标签样式 */
.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-holding {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

.status-sold {
  background-color: var(--el-color-success-light-8);
  color: var(--el-color-success);
}

.status-closed {
  background-color: var(--el-color-info-light-8);
  color: var(--el-color-info);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .data-table {
    min-width: unset;
    width: 100%;
  }

  .order-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .order-row:hover {
    background-color: var(--el-fill-color-light);
  }

  .order-row.selected {
    background-color: var(--el-fill-color);
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }

  .order-details {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px 12px;
    font-size: 14px;
  }

  .detail-item {
    display: flex;
    padding: 6px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .detail-item:nth-last-child(2),
  .detail-item:last-child {
    border-bottom: none;
  }

  .detail-item .label {
    color: var(--el-text-color-secondary);
    width: 70px;
    flex-shrink: 0;
  }

  .detail-item .value {
    flex-grow: 1;
  }

  /* 隐藏空行但保持其对奇偶计数的影响 */
  tr.dummy-row {
    display: none;
  }

  /* 详情展开动画 */
  .details-transition {
    animation: expand 0.25s ease-in-out forwards;
  }

  @keyframes expand {
    from {
      max-height: 134px;
      opacity: 0.5;
    }

    to {
      max-height: 400px;
      opacity: 1;
    }
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
}
</style>