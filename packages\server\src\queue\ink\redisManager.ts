import { coordinationRedis } from "@/lib/redis.js";
import { getChildLogger } from "@/utils/logger.js";
import { REDIS_KEYS, LOCK_CONFIG } from "./constants.js";
import { inkSyncConfig } from "./config.js";
import { retryHandler } from "./retryHandler.js";
import { APP_CONFIG, APP_TYPE } from "@/config/configManager.js";
import type * as InkApi from "@/api/inkApi.js";

const logger = getChildLogger("RedisManager");

/**
 * 批量操作接口
 *
 * 定义Redis批量操作的统一格式，支持设置、删除、过期等操作
 */
interface BatchOperation {
	type: "set" | "del" | "expire"; // 操作类型
	key: string; // Redis键名
	value?: string; // 值（set操作时需要）
	expiry?: number; // 过期时间（expire操作时需要）
}

/**
 * 执行记录接口
 *
 * 记录任务执行历史的数据结构
 */
interface ExecutionRecord {
	appId: string; // 应用实例标识
	taskName: string; // 任务名称
	timestamp: number; // 执行时间戳（秒）
	type: string; // 应用类型
	date: string; // 执行日期（ISO格式）
	details?: Record<string, unknown>; // 额外详情
}

/**
 * Redis操作管理器
 *
 * 职责：
 * - 提供批量操作、连接池管理、错误重试等功能
 * - 封装INK数据的Redis存储逻辑
 * - 管理执行历史记录
 * - 提供缓存统计和健康检查
 *
 * 设计特点：
 * - 批量操作优化：减少网络往返次数
 * - 重试机制：提高操作可靠性
 * - 执行记录：支持分布式环境下的任务协调
 * - 性能监控：提供缓存命中率等指标
 */
export class RedisManager {
	private batchOperations: BatchOperation[] = [];
	private batchTimer: NodeJS.Timeout | null = null;
	private readonly BATCH_DELAY = 100; // 100ms内的操作会被批量处理

	/**
	 * 批量设置缓存
	 *
	 * 优势：
	 * - 使用Redis Pipeline减少网络延迟
	 * - 支持带过期时间的批量设置
	 * - 集成重试机制提高可靠性
	 *
	 * @param operations 批量操作列表
	 */
	async batchSet(
		operations: Array<{ key: string; value: string; expiry?: number }>,
	): Promise<void> {
		if (operations.length === 0) return;

		const pipeline = coordinationRedis.pipeline();

		// 构建Pipeline命令
		for (const op of operations) {
			if (op.expiry) {
				pipeline.setex(op.key, op.expiry, op.value);
			} else {
				pipeline.set(op.key, op.value);
			}
		}

		// 执行批量操作，带重试机制
		await retryHandler.executeWithRetry(
			() => pipeline.exec(),
			{ maxAttempts: 3 },
			"redis-batch-set",
		);

		logger.debug(`Batch set ${operations.length} operations completed`);
	}

	/**
	 * 批量获取缓存
	 *
	 * 特点：
	 * - 使用MGET命令一次获取多个键
	 * - 返回键值对映射，便于使用
	 * - 集成重试机制
	 *
	 * @param keys 键名列表
	 * @returns 键值对映射
	 */
	async batchGet(keys: string[]): Promise<Record<string, string | null>> {
		if (keys.length === 0) return {};

		const values = await retryHandler.executeWithRetry(
			() => coordinationRedis.mget(...keys),
			{ maxAttempts: 3 },
			"redis-batch-get",
		);

		// 构建结果映射
		const result: Record<string, string | null> = {};
		keys.forEach((key, index) => {
			result[key] = values[index];
		});

		return result;
	}

	/**
	 * 设置INK波动数据
	 *
	 * 存储格式：JSON字符串
	 * 用途：缓存当日的股票波动数据
	 *
	 * @param data 波动数据记录
	 */
	async setSwingData(data: Record<string, number>): Promise<void> {
		await retryHandler.executeWithRetry(
			() =>
				coordinationRedis.set(REDIS_KEYS.INK_SWING_DATA, JSON.stringify(data)),
			{ maxAttempts: 3 },
			"redis-swing-data",
		);
		logger.debug("INK swing data updated in Redis");
	}

	/**
	 * 获取INK波动数据
	 *
	 * @returns 波动数据记录或null（如果不存在）
	 */
	async getSwingData(): Promise<Record<string, number> | null> {
		try {
			const cachedData = await retryHandler.executeWithRetry(
				() => coordinationRedis.get(REDIS_KEYS.INK_SWING_DATA),
				{ maxAttempts: 3 },
				"redis-swing-data",
			);

			return cachedData ? JSON.parse(cachedData) : null;
		} catch (error) {
			logger.error(error, "Failed to get swing data from Redis");
			return null;
		}
	}

	/**
	 * 批量设置价格报价
	 *
	 * 键名格式：ink:quotes:{provider}:{date}
	 * 存储格式：JSON字符串
	 * 过期时间：使用配置的缓存过期时间
	 *
	 * @param provider 价格提供商
	 * @param date 日期字符串
	 * @param quotes 报价数据
	 */
	async setPriceQuotes(
		provider: InkApi.PriceProviderValue,
		date: string,
		quotes: Record<string, Array<number | null>>,
	): Promise<void> {
		const key = `${REDIS_KEYS.INK_PRICE_QUOTES_PREFIX}:${provider}:${date}`;

		await retryHandler.executeWithRetry(
			() =>
				coordinationRedis.setex(
					key,
					inkSyncConfig.cacheExpiration,
					JSON.stringify(quotes),
				),
			{ maxAttempts: 3 },
			"redis-price-quotes",
		);

		logger.debug(`Price quotes updated for ${provider}:${date}`);
	}

	/**
	 * 获取价格报价
	 *
	 * @param provider 价格提供商
	 * @param date 日期字符串
	 * @returns 报价数据或null
	 */
	async getPriceQuotes(
		provider: InkApi.PriceProviderValue,
		date: string,
	): Promise<Record<string, Array<number | null>> | null> {
		try {
			const key = `${REDIS_KEYS.INK_PRICE_QUOTES_PREFIX}:${provider}:${date}`;
			const cachedData = await retryHandler.executeWithRetry(
				() => coordinationRedis.get(key),
				{ maxAttempts: 3 },
				"redis-price-quotes",
			);

			return cachedData ? JSON.parse(cachedData) : null;
		} catch (error) {
			logger.error(error, `Failed to get price quotes for ${provider}:${date}`);
			return null;
		}
	}

	/**
	 * 获取特定股票的期权价格
	 *
	 * 功能：
	 * - 从缓存的报价数据中提取特定期权的价格
	 * - 支持不同行权价和期限的组合
	 * - 自动计算数组索引
	 *
	 * 数组索引计算：
	 * - 行权价索引：100%=0, 103%=4, 105%=8, 110%=12
	 * - 期限索引：2w=0, 1m=1, 2m=2, 3m=3
	 * - 最终索引 = 行权价索引 + 期限索引
	 *
	 * @param stockCode 股票代码
	 * @param provider 价格提供商
	 * @param date 日期字符串
	 * @param strikePercent 行权价百分比
	 * @param period 期限
	 * @returns 期权价格或null
	 */
	async getOptionPrice(
		stockCode: string,
		provider: InkApi.PriceProviderValue,
		date: string,
		strikePercent: 100 | 103 | 105 | 110,
		period: "2w" | "1m" | "2m" | "3m",
	): Promise<number | null> {
		try {
			const quotes = await this.getPriceQuotes(provider, date);
			if (!quotes || !quotes[stockCode]) {
				return null;
			}

			// 计算数组索引
			const strikeIndex = {
				100: 0, // 100%行权价起始索引
				103: 4, // 103%行权价起始索引
				105: 8, // 105%行权价起始索引
				110: 12, // 110%行权价起始索引
			}[strikePercent];

			const periodIndex = {
				"2w": 0, // 2周期限偏移
				"1m": 1, // 1月期限偏移
				"2m": 2, // 2月期限偏移
				"3m": 3, // 3月期限偏移
			}[period];

			const index = strikeIndex + periodIndex;
			return quotes[stockCode][index];
		} catch (error) {
			logger.error(error, `Failed to get option price for ${stockCode}`);
			return null;
		}
	}

	/**
	 * 删除过期的价格报价缓存
	 *
	 * 清理策略：
	 * - 查找所有报价缓存键
	 * - 检查TTL，删除已过期或无过期时间的键
	 * - 分批处理，避免阻塞Redis
	 *
	 * 适用场景：定期维护任务
	 */
	async cleanupExpiredQuotes(): Promise<void> {
		try {
			const pattern = `${REDIS_KEYS.INK_PRICE_QUOTES_PREFIX}:*`;
			const keys = await coordinationRedis.keys(pattern);

			if (keys.length === 0) return;

			// 分批处理，避免阻塞Redis
			const batchSize = inkSyncConfig.batchSize;
			for (let i = 0; i < keys.length; i += batchSize) {
				const batch = keys.slice(i, i + batchSize);
				const pipeline = coordinationRedis.pipeline();

				// 检查每个key的TTL
				for (const key of batch) {
					pipeline.ttl(key);
				}

				const ttls = await pipeline.exec();

				// 删除已过期的key (-1表示没有设置过期时间但可能是老数据, -2表示key不存在)
				const keysToDelete = batch.filter((_, index) => {
					const ttl = ttls?.[index]?.[1] as number;
					return ttl === -1 || ttl === -2;
				});

				if (keysToDelete.length > 0) {
					await coordinationRedis.del(...keysToDelete);
					logger.info(
						`Cleaned up ${keysToDelete.length} expired quote cache entries`,
					);
				}
			}
		} catch (error) {
			logger.error(error, "Failed to cleanup expired quotes");
		}
	}

	/**
	 * 获取缓存统计信息
	 *
	 * 统计内容：
	 * - 波动数据是否存在
	 * - 报价缓存数量
	 * - Redis内存使用情况
	 *
	 * @returns 缓存统计对象
	 */
	async getCacheStats(): Promise<{
		swingDataExists: boolean;
		quoteCacheCount: number;
		totalMemoryUsage: string;
	}> {
		try {
			const [swingExists, quoteKeys, memoryInfo] = await Promise.all([
				coordinationRedis.exists(REDIS_KEYS.INK_SWING_DATA),
				coordinationRedis.keys(`${REDIS_KEYS.INK_PRICE_QUOTES_PREFIX}:*`),
				coordinationRedis.info("memory"),
			]);

			// 解析内存使用信息
			const memoryUsage =
				memoryInfo
					.split("\r\n")
					.find((line) => line.startsWith("used_memory_human:"))
					?.split(":")[1] || "unknown";

			return {
				swingDataExists: Boolean(swingExists),
				quoteCacheCount: quoteKeys.length,
				totalMemoryUsage: memoryUsage,
			};
		} catch (error) {
			logger.error(error, "Failed to get cache stats");
			return {
				swingDataExists: false,
				quoteCacheCount: 0,
				totalMemoryUsage: "unknown",
			};
		}
	}

	/**
	 * 健康检查
	 *
	 * 检查项：
	 * - Redis连接是否正常
	 * - 响应延迟是否在可接受范围内
	 *
	 * @returns 健康状态对象
	 */
	async healthCheck(): Promise<{
		healthy: boolean;
		latency: number;
		error?: string;
	}> {
		const start = Date.now();

		try {
			await coordinationRedis.ping();
			const latency = Date.now() - start;

			return {
				healthy: latency < 1000, // 1秒内响应认为健康
				latency,
			};
		} catch (error) {
			return {
				healthy: false,
				latency: Date.now() - start,
				error: (error as Error).message,
			};
		}
	}

	/**
	 * 获取应用标识符
	 *
	 * 标识符生成规则：
	 * 1. 优先使用交易平台ID
	 * 2. 其次使用渠道ID
	 * 3. 最后生成随机ID
	 *
	 * 用途：在分布式环境中标识不同的应用实例
	 *
	 * @returns 应用标识符
	 */
	private getAppIdentifier(): string {
		const platformId = APP_CONFIG.tradingPlatformId;
		const channelId = APP_CONFIG.channelId;

		if (platformId) return `platform:${platformId}`;
		if (channelId) return `channel:${channelId}`;

		return `unknown:${Math.random().toString(36).substring(2, 9)}`;
	}

	/**
	 * 记录执行历史
	 *
	 * 功能：
	 * - 记录任务执行的详细信息
	 * - 支持分布式环境下的执行协调
	 * - 自动限制历史记录数量
	 * - 设置过期时间防止数据堆积
	 *
	 * 存储结构：
	 * - 按任务名称分别存储历史记录
	 * - 使用Redis List结构，新记录在头部
	 * - 限制每个任务最多保留100条记录
	 *
	 * @param taskName 任务名称
	 * @param details 执行详情
	 */
	async recordExecution(
		taskName: string,
		details?: Record<string, unknown>,
	): Promise<void> {
		try {
			const appId = this.getAppIdentifier();
			const now = Math.floor(Date.now() / 1000);

			const record: ExecutionRecord = {
				appId,
				taskName,
				timestamp: now,
				type: APP_TYPE,
				date: new Date().toISOString(),
				details,
			};

			// 记录到列表中，保持最近的执行记录
			const recordKey = `${REDIS_KEYS.INK_EXECUTION_HISTORY}:${taskName}`;
			await coordinationRedis.lpush(recordKey, JSON.stringify(record));

			// 限制列表长度，只保留最近100条记录
			await coordinationRedis.ltrim(recordKey, 0, 99);

			// 设置过期时间
			await coordinationRedis.expire(
				recordKey,
				LOCK_CONFIG.EXECUTION_RECORD_EXPIRY,
			);

			// 更新最后执行记录
			await coordinationRedis.set(
				REDIS_KEYS.INK_SYNC_RECORD,
				JSON.stringify(record),
			);

			logger.debug(`Execution recorded: ${taskName} by ${appId}`);
		} catch (error) {
			logger.error(error, `Failed to record execution for ${taskName}`);
		}
	}

	/**
	 * 检查是否有最近的执行记录
	 *
	 * 用途：
	 * - 防止重复执行相同任务
	 * - 支持分布式环境下的任务协调
	 * - 可检查特定任务或全局执行状态
	 *
	 * @param taskName 任务名称（可选，不指定则检查全局）
	 * @returns 是否有最近的执行记录
	 */
	async hasRecentExecution(taskName?: string): Promise<boolean> {
		try {
			let recordData: string | null;

			if (taskName) {
				// 检查特定任务的最近执行
				const recordKey = `${REDIS_KEYS.INK_EXECUTION_HISTORY}:${taskName}`;
				const records = await coordinationRedis.lrange(recordKey, 0, 0);
				recordData = records[0] || null;
			} else {
				// 检查总体的最近执行
				recordData = await coordinationRedis.get(REDIS_KEYS.INK_SYNC_RECORD);
			}

			if (!recordData) return false;

			const record = JSON.parse(recordData);
			const now = Math.floor(Date.now() / 1000);

			// 根据是否指定任务名称使用不同的阈值
			const threshold = taskName
				? inkSyncConfig.recentExecutionThreshold
				: LOCK_CONFIG.RECENT_EXECUTION_THRESHOLD;

			if (now - record.timestamp < threshold) {
				logger.info(
					`Recent execution detected: ${record.taskName || "general"} at ${new Date(record.timestamp * 1000).toISOString()} by ${record.appId}`,
				);
				return true;
			}

			return false;
		} catch (error) {
			logger.error(error, "Failed to check recent execution");
			return false;
		}
	}

	/**
	 * 获取执行历史
	 *
	 * 功能：
	 * - 获取指定任务或所有任务的执行历史
	 * - 按时间倒序排列
	 * - 支持限制返回数量
	 *
	 * @param taskName 任务名称（可选）
	 * @param limit 返回数量限制
	 * @returns 执行记录列表
	 */
	async getExecutionHistory(
		taskName?: string,
		limit = 10,
	): Promise<ExecutionRecord[]> {
		try {
			if (taskName) {
				// 获取特定任务的历史记录
				const recordKey = `${REDIS_KEYS.INK_EXECUTION_HISTORY}:${taskName}`;
				const records = await coordinationRedis.lrange(recordKey, 0, limit - 1);
				return records.map((record) => JSON.parse(record) as ExecutionRecord);
			}

			// 获取所有任务的历史记录
			const pattern = `${REDIS_KEYS.INK_EXECUTION_HISTORY}:*`;
			const keys = await coordinationRedis.keys(pattern);

			const allRecords: ExecutionRecord[] = [];
			for (const key of keys) {
				const records = await coordinationRedis.lrange(key, 0, 4); // 每个任务取5条
				allRecords.push(
					...records.map((record) => JSON.parse(record) as ExecutionRecord),
				);
			}

			// 按时间戳排序并限制数量
			return allRecords
				.sort((a, b) => b.timestamp - a.timestamp)
				.slice(0, limit);
		} catch (error) {
			logger.error(error, "Failed to get execution history");
			return [];
		}
	}
}

// 导出单例实例
export const redisManager = new RedisManager();
