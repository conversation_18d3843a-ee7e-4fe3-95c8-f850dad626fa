import type { Job } from "bullmq";
import { createWorker, inkDataSyncQueue, addRepeatedJob } from "./index.js";
import logger from "@/utils/logger.js";
import * as InkApi from "@/api/inkApi.js";
import { coordinationRedis } from "@/lib/redis.js";
import { isMarketDay } from "@/financeUtils/marketTimeManager.js";
import { getChinaDateCompactString } from "@/utils/dateUtils.js";
import * as ConfigService from "@/services/admin/index.js";
import type { BusinessConfig } from "@packages/shared";
import { APP_CONFIG, APP_TYPE } from "@/config/configManager.js";

/**
 * INK Data Synchronization Worker
 *
 * 该模块负责处理所有与INK系统数据同步相关的后台作业。
 * 它通过BullMQ队列接收和处理各种数据更新任务，包括：
 * - INK核心数据（如波动率等）的拉取和缓存。
 * - 多家提供商的期权价格报价数据的拉取、转换和缓存。
 * - 业务配置参数的同步。
 *
 * 主要特性：
 * - 作业调度：通过 `initializeInkDataSyncJobs` 初始化各类定时和周期性任务。
 * - 交易日判断：多数任务会依赖 `isMarketDay` 来判断是否在交易日执行。
 * - 缓存管理：更新的数据通常会存储在Redis中，供应用其他部分使用。
 * - 任务细分：不同的数据同步需求被划分为不同的作业类型（见 `INK_DATA_SYNC_JOBS`）。
 * - 高频更新管理：`MANAGE_MARKET_TIME_UPDATES` 和 `MANAGE_PREMARKET_PRICE_QUOTES`
 *   用于在特定时间段（如交易时段、盘前）动态管理高频数据拉取任务的启停。
 *
 */

// Redis key constants
const INK_SWING_DATA_KEY = "ink:swing:data";
const INK_PRICE_QUOTES_KEY_PREFIX = "ink:quotes";

// Redis锁的配置 - 优化锁过期时间和添加新的锁类型
const INK_SYNC_LOCK_KEY = "ink:sync:lock";
const INK_DATA_UPDATE_LOCK_KEY = "ink:data:update:lock"; // 新增：数据更新锁
const INK_QUOTES_UPDATE_LOCK_KEY = "ink:quotes:update:lock"; // 新增：报价更新锁
const INK_CONFIG_UPDATE_LOCK_KEY = "ink:config:update:lock"; // 新增：配置更新锁
const INK_SYNC_RECORD_KEY = "ink:sync:last_execution";
const LOCK_EXPIRY = 60 * 5; // 优化：锁过期时间改为5分钟
const RECENT_EXECUTION_THRESHOLD = 60 * 3; // 优化：最近执行阈值改为3分钟
const LOCK_RENEWAL_INTERVAL = 60 * 2; // 新增：锁续期间隔2分钟

// 新增：通用的分布式锁管理器
class DistributedLockManager {
	private renewalTimers: Map<string, NodeJS.Timeout> = new Map();

	// 获取锁并自动续期
	async acquireLockWithRenewal(
		lockKey: string,
		expiry: number = LOCK_EXPIRY,
	): Promise<boolean> {
		const appId = getAppIdentifier();
		const now = Math.floor(Date.now() / 1000);
		const lockValue = `${appId}:${now}`;

		const result = await coordinationRedis.set(
			lockKey,
			lockValue,
			"EX",
			expiry,
			"NX",
		);

		if (result === "OK") {
			logger.info(`Lock acquired: ${lockKey} by ${appId}`);
			// 启动自动续期
			this.startLockRenewal(lockKey, lockValue, expiry);
			return true;
		}

		return false;
	}

	// 释放锁并停止续期
	async releaseLock(lockKey: string): Promise<void> {
		const appId = getAppIdentifier();
		const existingLock = await coordinationRedis.get(lockKey);

		if (existingLock?.startsWith(`${appId}:`)) {
			await coordinationRedis.del(lockKey);
			this.stopLockRenewal(lockKey);
			logger.info(`Lock released: ${lockKey} by ${appId}`);
		}
	}

	// 启动锁续期
	private startLockRenewal(
		lockKey: string,
		lockValue: string,
		expiry: number,
	): void {
		const timer = setInterval(async () => {
			try {
				const currentLock = await coordinationRedis.get(lockKey);
				if (currentLock === lockValue) {
					await coordinationRedis.expire(lockKey, expiry);
					logger.debug(`Lock renewed: ${lockKey}`);
				} else {
					this.stopLockRenewal(lockKey);
				}
			} catch (error) {
				logger.error(error, `Failed to renew lock: ${lockKey}`);
				this.stopLockRenewal(lockKey);
			}
		}, LOCK_RENEWAL_INTERVAL * 1000);

		this.renewalTimers.set(lockKey, timer);
	}

	// 停止锁续期
	private stopLockRenewal(lockKey: string): void {
		const timer = this.renewalTimers.get(lockKey);
		if (timer) {
			clearInterval(timer);
			this.renewalTimers.delete(lockKey);
		}
	}

	// 清理所有续期定时器
	cleanup(): void {
		for (const timer of this.renewalTimers.values()) {
			clearInterval(timer);
		}
		this.renewalTimers.clear();
	}
}

const lockManager = new DistributedLockManager();

// 优化：带分布式锁的数据更新函数
async function updateInkDataWithLock(): Promise<void> {
	const lockAcquired = await lockManager.acquireLockWithRenewal(
		INK_DATA_UPDATE_LOCK_KEY,
	);

	if (!lockAcquired) {
		logger.info("INK data update already in progress by another instance");
		return;
	}

	try {
		await updateInkData();
	} finally {
		await lockManager.releaseLock(INK_DATA_UPDATE_LOCK_KEY);
	}
}

// 优化：带分布式锁的报价更新函数
async function updatePriceQuotesWithLock(forceUpdate = false): Promise<void> {
	const lockAcquired = await lockManager.acquireLockWithRenewal(
		INK_QUOTES_UPDATE_LOCK_KEY,
	);

	if (!lockAcquired) {
		logger.info("Price quotes update already in progress by another instance");
		return;
	}

	try {
		await updatePriceQuotes(forceUpdate);
	} finally {
		await lockManager.releaseLock(INK_QUOTES_UPDATE_LOCK_KEY);
	}
}

// 优化：带分布式锁的配置更新函数
async function updateBusinessConfigWithLock(): Promise<void> {
	const lockAcquired = await lockManager.acquireLockWithRenewal(
		INK_CONFIG_UPDATE_LOCK_KEY,
	);

	if (!lockAcquired) {
		logger.info(
			"Business config update already in progress by another instance",
		);
		return;
	}

	try {
		await updateBusinessConfig();
	} finally {
		await lockManager.releaseLock(INK_CONFIG_UPDATE_LOCK_KEY);
	}
}

// 添加优雅关闭处理
process.on("SIGTERM", () => {
	logger.info("Received SIGTERM, cleaning up locks...");
	lockManager.cleanup();
});

process.on("SIGINT", () => {
	logger.info("Received SIGINT, cleaning up locks...");
	lockManager.cleanup();
});

// 定义INK数据同步相关的作业类型
export const INK_DATA_SYNC_JOBS = {
	UPDATE_INK_DATA: "inksync-update-data",
	UPDATE_PRICE_QUOTES: "inksync-update-price-quotes",
	UPDATE_BUSINESS_CONFIG: "inksync-update-business-config",
	MANAGE_MARKET_TIME_UPDATES: "inksync-manage-market-time-updates",
	MANAGE_PREMARKET_PRICE_QUOTES: "inksync-manage-premarket-price-quotes",
	HIGH_FREQ_INK_DATA_UPDATE: "inksync-high-freq-data-update",
	PREMARKET_PRICE_QUOTES_UPDATE: "inksync-premarket-price-quotes-update",
	INITIALIZE_INK_DATA_SCHEDULES: "inksync-initialize-schedules", // 新增：初始化调度任务
};

// 获取应用标识
function getAppIdentifier(): string {
	// 尝试获取交易台ID或通道ID，如果都没有，使用"unknown"加上随机字符串
	const platformId = APP_CONFIG.tradingPlatformId;
	const channelId = APP_CONFIG.channelId;

	if (platformId) return `platform:${platformId}`;
	if (channelId) return `channel:${channelId}`;

	// 生成随机ID作为后备标识
	return `unknown:${Math.random().toString(36).substring(2, 9)}`;
}

// 尝试获取分布式锁
async function tryAcquireInkSyncLock(): Promise<boolean> {
	const appId = getAppIdentifier();

	// 当前时间戳（秒）
	const now = Math.floor(Date.now() / 1000);

	// 锁的值格式：appId:timestamp
	const lockValue = `${appId}:${now}`;

	// 原子操作：如果锁不存在，则设置锁并返回1，否则返回0
	// 使用SETNX命令确保原子性，即使两个实例同时执行也只有一个能成功
	const result = await coordinationRedis.set(
		INK_SYNC_LOCK_KEY,
		lockValue,
		"EX",
		LOCK_EXPIRY,
		"NX",
	);

	// 获取锁成功
	if (result === "OK") {
		logger.info(`INK data sync lock acquired by ${appId}`);
		return true;
	}

	// 获取锁失败，检查现有锁的信息
	const existingLock = await coordinationRedis.get(INK_SYNC_LOCK_KEY);
	if (existingLock) {
		const parts = existingLock.split(":");
		const lockHolderId = parts[0];
		const lockTimestamp = parts.length > 1 ? Number.parseInt(parts[1], 10) : 0;

		logger.info(
			`INK data sync already locked by ${lockHolderId} at ${new Date(lockTimestamp * 1000).toISOString()}`,
		);
	}

	return false;
}

// 释放分布式锁
async function releaseInkSyncLock(): Promise<void> {
	const appId = getAppIdentifier();

	// 先获取锁的当前值
	const existingLock = await coordinationRedis.get(INK_SYNC_LOCK_KEY);

	// 只有当前应用持有锁时才删除，避免删除其他实例的锁
	if (existingLock?.startsWith(`${appId}:`)) {
		await coordinationRedis.del(INK_SYNC_LOCK_KEY);
		logger.info(`INK data sync lock released by ${appId}`);
	}
}

// 记录同步执行信息
async function recordInkSyncExecution(): Promise<void> {
	const appId = getAppIdentifier();
	const now = Math.floor(Date.now() / 1000);

	// 记录格式：{appId: string, timestamp: number}
	const recordData = JSON.stringify({
		appId,
		timestamp: now,
		type: APP_TYPE,
		date: new Date().toISOString(),
	});

	// 记录最后执行信息，不设置过期时间，用于长期追踪
	await coordinationRedis.set(INK_SYNC_RECORD_KEY, recordData);
	logger.info(`INK data sync execution recorded by ${appId}`);
}

// 检查是否有最近的执行记录
async function hasRecentInkExecution(): Promise<boolean> {
	// 获取最近执行记录
	const recordData = await coordinationRedis.get(INK_SYNC_RECORD_KEY);
	if (!recordData) return false;

	try {
		const record = JSON.parse(recordData);
		const now = Math.floor(Date.now() / 1000);

		// 检查执行时间是否在阈值范围内
		if (now - record.timestamp < RECENT_EXECUTION_THRESHOLD) {
			logger.info(
				`Recent INK data sync detected at ${new Date(record.timestamp * 1000).toISOString()} by ${record.appId}`,
			);
			return true;
		}

		return false;
	} catch (error) {
		logger.error(error, "Error parsing recent INK execution record");
		return false;
	}
}

// 处理INK数据同步相关的作业
async function processInkDataSyncJob(job: Job) {
	const { name, data } = job;

	logger.info(`Processing INK data sync job: ${name}`);

	try {
		switch (name) {
			case INK_DATA_SYNC_JOBS.UPDATE_INK_DATA:
				await updateInkDataWithLock();
				break;
			case INK_DATA_SYNC_JOBS.UPDATE_PRICE_QUOTES:
				await updatePriceQuotesWithLock(data?.forceUpdate);
				break;
			case INK_DATA_SYNC_JOBS.UPDATE_BUSINESS_CONFIG:
				await updateBusinessConfigWithLock();
				break;
			case INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES:
				await manageMarketTimeUpdates();
				break;
			case INK_DATA_SYNC_JOBS.MANAGE_PREMARKET_PRICE_QUOTES:
				await managePreMarketPriceQuoteUpdates();
				break;
			case INK_DATA_SYNC_JOBS.HIGH_FREQ_INK_DATA_UPDATE:
				await updateIfMarketDay();
				break;
			case INK_DATA_SYNC_JOBS.PREMARKET_PRICE_QUOTES_UPDATE:
				await updatePriceQuotesIfMarketDay();
				break;
			case INK_DATA_SYNC_JOBS.INITIALIZE_INK_DATA_SCHEDULES:
				// 新增：处理初始化调度任务，使用分布式锁
				await processInitializeInkDataSchedules();
				break;
			default:
				logger.warn(`Unknown INK data sync job type: ${name}`);
		}
	} catch (error) {
		logger.error(error, `Failed to process INK data sync job: ${name}`);
		throw error;
	}
}

// 处理初始化调度任务（带分布式锁）
async function processInitializeInkDataSchedules(): Promise<void> {
	try {
		// 检查是否有最近执行的记录
		if (await hasRecentInkExecution()) {
			logger.info(
				"Skipping INK data schedule initialization due to recent execution",
			);
			return;
		}

		// 尝试获取分布式锁
		if (await tryAcquireInkSyncLock()) {
			try {
				// 执行初始化
				await initializeInkDataSyncJobs();
				// 记录执行信息
				await recordInkSyncExecution();
			} finally {
				// 完成后释放锁
				await releaseInkSyncLock();
			}
		} else {
			logger.info(
				"Skipping INK data schedule initialization due to active lock",
			);
		}
	} catch (error) {
		logger.error(error, "Failed to process INK data schedule initialization");
		throw error;
	}
}

// 将报价对象转换为数组
function optionQuoteToArray(
	quote: InkApi.OptionPriceQuote,
): Array<number | null> {
	// 返回一个包含所有报价字段的数组
	return [
		quote.c100_2w,
		quote.c100_1m,
		quote.c100_2m,
		quote.c100_3m,
		quote.c103_2w,
		quote.c103_1m,
		quote.c103_2m,
		quote.c103_3m,
		quote.c105_2w,
		quote.c105_1m,
		quote.c105_2m,
		quote.c105_3m,
		quote.c110_2w,
		quote.c110_1m,
		quote.c110_2m,
		quote.c110_3m,
	];
}

// 更新INK波动数据
async function updateInkData(): Promise<void> {
	try {
		// Check if it's a market day
		const isTradeDay = await isMarketDay();
		if (!isTradeDay) {
			logger.info("Not a market day, skipping INK data update");
			return;
		}

		logger.info("Fetching new INK API data");

		// Get business date
		const date = getChinaDateCompactString();

		logger.info(`Attempting to fetch swing data for today: ${date}`);

		const swingData = await InkApi.fetchSwingData(date);

		// If no data for today, keep using cached data
		if (!swingData || swingData.size === 0) {
			logger.warn(
				`No data available for today (${date}), keeping existing cache`,
			);
			return;
		}

		logger.info(
			`Successfully fetched swing data for today with ${swingData.size} entries`,
		);

		// Convert Map to Record for caching
		const swingRecord: Record<string, number> = {};
		swingData.forEach((value, key) => {
			swingRecord[key] = value;
		});

		// Store in Redis
		await coordinationRedis.set(
			INK_SWING_DATA_KEY,
			JSON.stringify(swingRecord),
		);

		logger.info("INK swing data updated successfully and stored in Redis");
	} catch (error) {
		logger.error(error, "Failed to update INK swing data");
	}
}

// 更新价格报价数据
async function updatePriceQuotes(forceUpdate = false): Promise<void> {
	try {
		// Check if it's a market day
		const isTradeDay = await isMarketDay();
		// 非交易日则跳过更新，开发模式下可启用 forceUpdate 时不跳过
		if (!isTradeDay) {
			logger.info("Not a market day, skipping price quotes update");
			return;
		}

		logger.info("Fetching external price quotes data");

		// Get business date
		const date = getChinaDateCompactString();

		logger.info(`Attempting to fetch price quotes for today: ${date}`);

		// Get all price providers
		const providers = Object.values(InkApi.PriceProvider);

		// Fetch data from all providers in parallel
		const providerDataPromises = providers.map(async (provider) => {
			try {
				const redisKey = `${INK_PRICE_QUOTES_KEY_PREFIX}:${provider}:${date}`;

				// Check if cache already exists
				const cachedData = await coordinationRedis.get(redisKey);

				// If not forcing update and cache exists, skip update
				if (!forceUpdate && cachedData) {
					logger.info(`Cache already exists for ${provider}, skipping update`);
					return;
				}

				// Get new data
				const quoteData = await InkApi.fetchPriceQuotes(provider, date);

				// 旧版 inkDataSyncCron.ts 中的开发模式回退逻辑 (整个 .then() 结构):
				/*
				const quoteDataFromCronLogic = await InkApi.fetchPriceQuotes(provider, date) // 在旧cron中，date可能是DEV_DATE
				 .then(
				   async (data) => {
				     // 假设 DEV_DATE, useTestData, isMarketOpen, ENV 变量在此作用域内可用或已适配
				     // redis 实例在 worker 中是 coordinationRedis
				     const localDevDateConst = "20250403"; // 示例：DEV_DATE 的本地化
				     let localUseTestData = false;

				     // 如果是强制更新模式获取失败，清除缓存避免使用可能过期的数据
				     if (forceUpdate && (!data || data.size === 0)) {
				       if (
				         ENV.NODE_ENV !== "development" || // ENV.NODE_ENV 可从 @/config/configManager.js 导入
				         ((await isMarketDay()) && isInTradingHours()) // 使用 isInTradingHours 替代 isMarketOpen (isMarketOpen在cron中定义，worker中无)
				       ) {
				         logger.warn(
				           `Force update failed for ${provider}, clearing cache to avoid stale data`,
				         );
				         await coordinationRedis.del(redisKey); // 使用 coordinationRedis
				         return null;
				       }

				       logger.warn(
				         `Force update failed for ${provider}, using test data from ${localDevDateConst}`,
				       );
				       localUseTestData = true;
				       return InkApi.fetchPriceQuotes(provider, localDevDateConst);
				     }
				     // Return the data when it exists!
				     return data;
				   },
				 );
				*/

				// If fetch successful and data exists
				if (quoteData && quoteData.size > 0) {
					// Convert to array format for storage
					const quotesRecord: Record<string, Array<number | null>> = {};
					quoteData.forEach((quote, stockCode) => {
						quotesRecord[stockCode] = optionQuoteToArray(quote);
					});

					// Update cache
					const CACHE_EXPIRATION_TIME = 60 * 60 * 24; // 24 hours in seconds
					await coordinationRedis.set(
						redisKey,
						JSON.stringify(quotesRecord),
						"EX",
						CACHE_EXPIRATION_TIME,
					);
					logger.info(
						`${provider}: Successfully updated with new data - cached ${quoteData.size} quotes for ${date}`,
					);
				} else if (forceUpdate) {
					// If no new data and force update requested, clear cache
					logger.warn(
						`Force update requested but no data available for ${provider}, clearing cache`,
					);
					await coordinationRedis.del(redisKey);
				}
			} catch (error) {
				logger.error(error, `Failed to fetch price quotes from ${provider}`);
				if (forceUpdate) {
					const redisKey = `${INK_PRICE_QUOTES_KEY_PREFIX}:${provider}:${date}`;
					await coordinationRedis.del(redisKey);
				}
			}
		});

		await Promise.all(providerDataPromises);
		logger.info("Price quotes update completed");
	} catch (error) {
		logger.error(error, "Failed to update price quotes data");
	}
}

// 更新业务配置
async function updateBusinessConfig(): Promise<void> {
	try {
		// Check if it's a market day
		const isTradeDay = await isMarketDay();
		if (!isTradeDay) {
			logger.debug("Not a market day, skipping business config update");
			return;
		}

		logger.info("Checking for business config updates from INK API");

		// Get current config
		const config = await ConfigService.getConfig();

		// Prepare updates
		const updates: Partial<BusinessConfig> = {};
		let hasUpdates = false;

		// Business config keys to check
		const configKeys = [
			"OPTION_MULTIPLIER",
			"STOCK_SCALE_LIMIT",
			"TOTAL_SCALE_LIMIT",
			"CHANNEL_CREDIT_LIMIT",
			"DISCOUNT_MULTIPLIER",
		] as const;

		// Check each config item
		for (const key of configKeys) {
			try {
				// Get data from API using key name
				const apiData = await InkApi.fetchBusinessConfig(key);
				const newValue = apiData.get(key);
				const currentValue = config[key];

				// Check if value is valid and needs update
				if (
					newValue !== undefined &&
					typeof newValue === "number" &&
					!Number.isNaN(newValue) &&
					newValue > 0 &&
					currentValue !== newValue
				) {
					updates[key] = newValue;
					hasUpdates = true;
					logger.info(
						`Found new ${key}: ${newValue} (previous: ${currentValue})`,
					);
				}
			} catch (error) {
				// Failure of a single config item should not affect others
				logger.error(error, `Failed to fetch or process ${key}`);
			}
		}

		// If updates exist, apply them
		if (hasUpdates) {
			logger.info("Updating business config with new values:", updates);
			await ConfigService.updateConfig(updates);
			logger.info("Business config updated successfully");
		} else {
			logger.debug("No changes in business config values, config not updated");
		}
	} catch (error) {
		logger.error(error, "Failed to update business config values");
	}
}

// 只有在市场交易日更新INK数据
async function updateIfMarketDay(): Promise<void> {
	try {
		if (await isMarketDay()) {
			await updateInkData();
		} else {
			logger.debug("Not a market day, skipping INK data update from interval");
		}
	} catch (error) {
		logger.error(error, "Failed to update INK data in interval");
	}
}

// 只有在市场交易日更新价格报价
async function updatePriceQuotesIfMarketDay(
	forceUpdate = false,
): Promise<void> {
	try {
		if ((await isMarketDay()) || forceUpdate) {
			await updatePriceQuotes(forceUpdate);
		} else {
			logger.debug(
				"Not a market day, skipping price quotes update from interval",
			);
		}
	} catch (error) {
		logger.error(error, "Failed to update price quotes in interval");
	}
}

// 判断当前是否处于交易时段
function isInTradingHours(): boolean {
	const now = new Date();
	const hours = now.getHours();
	const minutes = now.getMinutes();
	const currentTime = hours * 100 + minutes;

	// 交易时段：9:25-15:00 (不包含9:25和15:00整)
	return currentTime > 925 && currentTime < 1500;
}

// 判断当前是否处于盘前时段
function isInPreMarketHours(): boolean {
	const now = new Date();
	const hours = now.getHours();
	const minutes = now.getMinutes();
	const currentTime = hours * 100 + minutes;

	// 盘前时段：8:30-9:30
	return currentTime >= 830 && currentTime < 930;
}

// 高频更新任务的唯一ID
const HIGH_FREQ_JOB_ID = "high-freq-ink-data-update";
const PREMARKET_JOB_ID = "premarket-price-quotes-update";

// 管理交易时段的INK数据更新
async function manageMarketTimeUpdates(): Promise<void> {
	try {
		const shouldBeRunning = isInTradingHours();
		const repeatableJobs = await inkDataSyncQueue.getJobSchedulers();

		// 清理现有的高频任务 - 使用更彻底的清理方式
		let tasksRemoved = 0;
		for (const job of repeatableJobs) {
			// 通过任务名称或任务ID包含相关标识符的方式检测高频任务
			if (
				job.name === INK_DATA_SYNC_JOBS.HIGH_FREQ_INK_DATA_UPDATE ||
				job.id?.includes(HIGH_FREQ_JOB_ID)
			) {
				await inkDataSyncQueue.removeJobScheduler(job.key);
				tasksRemoved++;
				logger.debug(`已移除高频数据更新任务: ${job.key}`);
			}
		}

		if (tasksRemoved > 0) {
			logger.info(`总共移除了 ${tasksRemoved} 个高频数据更新任务`);
		}

		// 只在交易时段添加高频更新任务
		if (shouldBeRunning) {
			await inkDataSyncQueue.add(
				INK_DATA_SYNC_JOBS.HIGH_FREQ_INK_DATA_UPDATE,
				{},
				{
					repeat: { every: 5000, immediately: true },
					jobId: HIGH_FREQ_JOB_ID,
					removeOnComplete: true,
				},
			);
			logger.debug("已添加高频数据更新任务（每5秒）");
		} else {
			logger.debug("当前非交易时段，未添加高频数据更新任务");
		}
	} catch (error) {
		logger.error(error, "管理交易时段数据更新任务失败");
	}
}

// 管理盘前时段的价格报价更新
async function managePreMarketPriceQuoteUpdates(): Promise<void> {
	try {
		const shouldBeRunning = isInPreMarketHours();

		// 获取所有可重复任务
		const repeatableJobs = await inkDataSyncQueue.getJobSchedulers();

		// 更彻底地清理盘前报价任务
		let tasksRemoved = 0;
		for (const job of repeatableJobs) {
			if (
				job.name === INK_DATA_SYNC_JOBS.PREMARKET_PRICE_QUOTES_UPDATE ||
				job.id === PREMARKET_JOB_ID ||
				job.id?.includes(PREMARKET_JOB_ID)
			) {
				await inkDataSyncQueue.removeJobScheduler(job.key);
				tasksRemoved++;
				logger.debug(`移除盘前报价任务: ${job.key}`);
			}
		}

		if (tasksRemoved > 0) {
			logger.info(`总共移除了 ${tasksRemoved} 个盘前报价任务`);
		}

		// 检查清理后是否还有相关任务
		const remainingJobs = await inkDataSyncQueue.getJobSchedulers();
		const premarketJobExists = remainingJobs.some(
			(job) =>
				job.name === INK_DATA_SYNC_JOBS.PREMARKET_PRICE_QUOTES_UPDATE ||
				job.id === PREMARKET_JOB_ID ||
				job.id?.includes(PREMARKET_JOB_ID),
		);

		if (shouldBeRunning && !premarketJobExists) {
			// 在盘前时段，且报价任务不存在，创建任务
			logger.info("进入盘前时段，启动价格报价更新");

			// 每5分钟执行一次的报价更新任务
			await inkDataSyncQueue.add(
				INK_DATA_SYNC_JOBS.PREMARKET_PRICE_QUOTES_UPDATE,
				{},
				{
					repeat: {
						every: 300000, // 每5分钟
						immediately: true, // 立即开始
					},
					jobId: PREMARKET_JOB_ID,
					removeOnComplete: true,
				},
			);

			// 立即执行一次更新
			await updatePriceQuotesIfMarketDay();
		} else if (!shouldBeRunning) {
			logger.debug("当前非盘前时段，未添加报价更新任务");
		}
	} catch (error) {
		logger.error(error, "管理盘前报价更新任务失败");
	}
}

// 创建Worker实例
export const inkDataSyncWorker = createWorker(
	inkDataSyncQueue,
	processInkDataSyncJob,
);

// 初始化定时作业调度
export async function initializeInkDataSyncJobs() {
	try {
		// Options for one-time initial jobs
		const initialJobOpts = { removeOnComplete: true, removeOnFail: 5 };
		// Options for cron jobs that manage high-frequency tasks (run every minute)
		const highFreqManagerJobOptions = {
			removeOnComplete: true,
			removeOnFail: 20,
		};
		// Default options for other cron jobs
		const defaultCronJobOptions = { removeOnComplete: true, removeOnFail: 50 };

		// 初始更新 (一次性任务，不使用 addRepeatedJob)
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.UPDATE_INK_DATA,
			{},
			initialJobOpts,
		);
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.UPDATE_PRICE_QUOTES,
			{ forceUpdate: true }, // 启动时强制更新一次
			initialJobOpts,
		);
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.UPDATE_BUSINESS_CONFIG,
			{},
			initialJobOpts,
		);

		// 每分钟检查一次是否需要启动或停止交易时段高频更新
		await addRepeatedJob(
			inkDataSyncQueue,
			INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES,
			{},
			"* * * * *",
			highFreqManagerJobOptions,
		);

		// 每分钟检查一次是否需要启动或停止盘前报价更新
		await addRepeatedJob(
			inkDataSyncQueue,
			INK_DATA_SYNC_JOBS.MANAGE_PREMARKET_PRICE_QUOTES,
			{},
			"* * * * *",
			highFreqManagerJobOptions,
		);

		// 每天 9:26 (9:25 + 1分钟) 强制更新价格报价 (由作业处理器判断是否为交易日)
		await addRepeatedJob(
			inkDataSyncQueue,
			INK_DATA_SYNC_JOBS.UPDATE_PRICE_QUOTES,
			{ forceUpdate: true },
			"26 9 * * *", // 每天9:26
			defaultCronJobOptions,
		);

		// 非交易时段更新（每天的0-8点和15-23点，每30分钟）
		await addRepeatedJob(
			inkDataSyncQueue,
			INK_DATA_SYNC_JOBS.UPDATE_INK_DATA,
			{},
			"*/30 0-8,15-23 * * *", // 周一至周日
			defaultCronJobOptions,
		);

		// 业务配置更新（每天每小时的0分）
		await addRepeatedJob(
			inkDataSyncQueue,
			INK_DATA_SYNC_JOBS.UPDATE_BUSINESS_CONFIG,
			{},
			"0 * * * *", // 每天
			defaultCronJobOptions,
		);

		logger.info("INK data sync jobs scheduled successfully");
	} catch (error) {
		logger.error(error, "Failed to schedule INK data sync jobs");
		throw error;
	}
}

// 获取缓存的波动数据
export async function getCachedSwingData(): Promise<Record<
	string,
	number
> | null> {
	try {
		const cachedData = await coordinationRedis.get(INK_SWING_DATA_KEY);
		if (cachedData) {
			return JSON.parse(cachedData);
		}
		return null;
	} catch (error) {
		logger.error(error, "Failed to get cached swing data");
		return null;
	}
}

// 获取缓存的期权价格
export async function getCachedOptionPrice(
	stockCode: string,
	provider: InkApi.PriceProviderValue,
	strikePercent: 100 | 103 | 105 | 110,
	period: "2w" | "1m" | "2m" | "3m",
): Promise<number | null> {
	try {
		const date = getChinaDateCompactString();
		const redisKey = `${INK_PRICE_QUOTES_KEY_PREFIX}:${provider}:${date}`;
		const cachedData = await coordinationRedis.get(redisKey);

		if (!cachedData) {
			return null;
		}

		const quotesData = JSON.parse(cachedData) as Record<
			string,
			Array<number | null>
		>;
		const stockQuotes = quotesData[stockCode];

		if (!stockQuotes) {
			return null;
		}

		// 计算索引
		let index = 0;

		// 先确定行权价百分比的偏移
		switch (strikePercent) {
			case 100:
				index = 0;
				break;
			case 103:
				index = 4;
				break;
			case 105:
				index = 8;
				break;
			case 110:
				index = 12;
				break;
			default:
				return null;
		}

		// 再确定期限的偏移
		switch (period) {
			case "2w":
				index += 0;
				break;
			case "1m":
				index += 1;
				break;
			case "2m":
				index += 2;
				break;
			case "3m":
				index += 3;
				break;
			default:
				return null;
		}

		return stockQuotes[index];
	} catch (error) {
		logger.error(error, "Failed to get cached option price");
		return null;
	}
}

// 用于应用程序启动时初始化所有INK数据同步任务
export async function initializeOnStartup() {
	try {
		// 使用分布式锁机制初始化定时任务调度
		const jobId = "init-ink-data-schedules";
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.INITIALIZE_INK_DATA_SCHEDULES,
			{},
			{ jobId, removeOnComplete: true, removeOnFail: 5 },
		);
		logger.info(
			`Added INK data schedule initialization job to queue with ID: ${jobId}`,
		);
	} catch (error) {
		logger.error(error, "Failed to initialize INK data worker on startup");
	}
}
