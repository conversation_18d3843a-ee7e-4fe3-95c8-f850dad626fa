import prisma from "@/lib/prisma.js";
import { encrypt, decrypt } from "@/utils/encryption.js";
import type { AdminData, AdminPermission } from "@packages/shared";
import type { admin_permission } from "@prisma/client";

interface PrismaAdminData {
	admin_id: number;
	username: string;
	password_hash: string;
	name: string;
	permissions: admin_permission[];
	is_active: boolean | null;
	created_at: Date | null;
}

function transformAdminData(data: PrismaAdminData): AdminData {
	return {
		...data,
		username: decrypt(data.username),
		permissions: data.permissions as AdminPermission[],
		is_active: data.is_active ?? true,
		created_at: data.created_at ?? new Date(),
	};
}

export async function create(
	username: string,
	password_hash: string,
	name: string,
	permissions: admin_permission[],
): Promise<AdminData> {
	const admin = await prisma.admins.create({
		data: {
			username: encrypt(username),
			password_hash,
			name,
			permissions,
		},
	});

	return transformAdminData(admin);
}

export async function getAdmins(): Promise<AdminData[]> {
	const admins = await prisma.admins.findMany({
		orderBy: {
			admin_id: "asc",
		},
	});
	return admins.map(transformAdminData);
}

export async function toggleActiveStatus(admin_id: number): Promise<void> {
	await prisma.$executeRaw`UPDATE admins SET is_active = NOT is_active WHERE admin_id = ${admin_id}`;
}

export async function updatePermissions(
	admin_id: number,
	permissions: admin_permission[],
): Promise<void> {
	await prisma.admins.update({
		where: { admin_id },
		data: { permissions },
	});
}

export async function updatePassword(
	admin_id: number,
	password_hash: string,
): Promise<void> {
	await prisma.admins.update({
		where: { admin_id },
		data: { password_hash },
	});
}

export async function findByUsername(
	username: string,
): Promise<AdminData | null> {
	const admin = await prisma.admins.findUnique({
		where: { username: encrypt(username) },
	});

	if (!admin) return null;

	return transformAdminData(admin);
}

export async function getAdminById(
	admin_id: number,
): Promise<AdminData | null> {
	const admin = await prisma.admins.findUnique({
		where: { admin_id },
	});

	if (!admin) return null;

	return transformAdminData(admin);
}

export async function isAdminActive(admin_id: number): Promise<boolean> {
	const admin = await prisma.admins.findUnique({
		where: { admin_id },
		select: { is_active: true },
	});
	return admin?.is_active ?? true;
}
