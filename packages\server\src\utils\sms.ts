import { ENV } from "@/config/configManager.js";
import SMSClient from "@alicloud/sms-sdk";
import logger from "./logger.js";
import { appRedis } from "@/lib/redis.js";
import { AppError } from "../core/appError.js";
import * as User from "@/models/user.js";
import { notificationThrottle } from "./notificationThrottle.js";

// SMS template code mapping
const SMS_TEMPLATES = {
	VERIFICATION: "SMS_476925204",
	NEW_AUDIT_APPLICATION: "SMS_477560033",
	NEW_AUDIT_APPLICATION_DEMO: "SMS_479955144",
	WITHDRAWAL: "SMS_476675268",
	DEPOSIT: "SMS_476850207",
	FORCE_NOTIFICATION: "SMS_476680201",
	EXPIRY_REMINDER: "SMS_476840236",
	EXPIRY_NOTIFICATION: "SMS_476760228",
	EXERCISE_INSTRUCTION: "SMS_476920230",
	ORDER_INSTRUCTION: "SMS_476800242",
	AUDIT_NOTIFICATION: "SMS_476875219",
	INFO_UPDATE: "SMS_476790226",
	PASSWORD_CHANGE: "SMS_476925208",
	HOLIDAY_NOTIFICATION: "SMS_476925207",
	TRANSFER_OUT: "SMS_478440006",
	TRANSFER_IN: "SMS_478390004",
	PAYMENT_PASSWORD_SET: "SMS_478425005",
} as const;

interface TemplateParams {
	VERIFICATION: { code: string }; // done
	NEW_AUDIT_APPLICATION: { name: string }; // done
	NEW_AUDIT_APPLICATION_DEMO: { name: string }; // done
	WITHDRAWAL: Record<string, never>; // done
	DEPOSIT: Record<string, never>; // done
	FORCE_NOTIFICATION: Record<string, never>; // done
	EXPIRY_REMINDER: { date: string }; // done
	EXPIRY_NOTIFICATION: Record<string, never>; // done
	EXERCISE_INSTRUCTION: Record<string, never>; // done
	ORDER_INSTRUCTION: Record<string, never>; // done
	AUDIT_NOTIFICATION: Record<string, never>; // done
	INFO_UPDATE: Record<string, never>; // done
	PASSWORD_CHANGE: Record<string, never>; // done
	HOLIDAY_NOTIFICATION: { date1: string; date2: string }; // done
	TRANSFER_OUT: Record<string, never>;
	TRANSFER_IN: Record<string, never>;
	PAYMENT_PASSWORD_SET: Record<string, never>;
}

type TemplateType = keyof typeof SMS_TEMPLATES;

// Rate limit thresholds
const RATE_LIMITS =
	ENV.NODE_ENV === "development"
		? {
				MINUTE: 10,
				HOUR: 50,
				DAY: 100,
			}
		: ({
				MINUTE: 1,
				HOUR: 25,
				DAY: 50,
			} as const);

// TTL for rate limit keys (in seconds)
const RATE_LIMIT_TTL = {
	MINUTE: RATE_LIMITS.MINUTE * 60,
	HOUR: RATE_LIMITS.HOUR * 3600,
	DAY: RATE_LIMITS.DAY * 86400,
} as const;

// Add test phone number pattern (6-digit number starting with 100)
const TEST_PHONE_PATTERN = /^100\d{3}$/; // 以100开头的6位数字

class SMSService {
	private smsClient: SMSClient | null = null;

	constructor() {
		if (!ENV.ALIYUN_ACCESS_KEY_ID || !ENV.ALIYUN_ACCESS_KEY_SECRET) {
			throw AppError.create(
				"EXTERNAL_API_ERROR",
				"SMS credentials not configured",
			);
		}

		this.smsClient = new SMSClient({
			accessKeyId: ENV.ALIYUN_ACCESS_KEY_ID,
			secretAccessKey: ENV.ALIYUN_ACCESS_KEY_SECRET,
			endpoint: "https://dysmsapi.aliyuncs.com",
			apiVersion: "2017-05-25",
			timeout: 5000,
		});
	}

	private async checkRateLimit(ip: string): Promise<void> {
		const minuteKey = `sms:limit:minute:${ip}`;
		const hourKey = `sms:limit:hour:${ip}`;
		const dayKey = `sms:limit:day:${ip}`;

		const [minuteCount, hourCount, dayCount] = await appRedis.mget(
			minuteKey,
			hourKey,
			dayKey,
		);

		if (minuteCount && Number.parseInt(minuteCount) >= RATE_LIMITS.MINUTE) {
			logger.warn(`Rate limit exceeded: too frequent requests from IP ${ip}`);
			throw AppError.create(
				"SMS_RATE_LIMIT_IN_MINUTE",
				"Too many requests in one minute",
				{ ip },
			);
		}

		if (hourCount && Number.parseInt(hourCount) >= RATE_LIMITS.HOUR) {
			logger.warn(`Rate limit exceeded: hourly limit reached for IP ${ip}`);
			throw AppError.create(
				"SMS_RATE_LIMIT_IN_HOUR",
				"Too many requests in one hour",
				{ ip },
			);
		}

		if (dayCount && Number.parseInt(dayCount) >= RATE_LIMITS.DAY) {
			logger.warn(`Rate limit exceeded: daily limit reached for IP ${ip}`);
			await appRedis.zadd("suspicious:ips", Date.now(), ip);
			throw AppError.create(
				"SMS_RATE_LIMIT_IN_DAY",
				"Too many requests in one day",
				{
					ip,
				},
			);
		}
	}

	private async updateRateLimits(ip: string): Promise<void> {
		const minuteKey = `sms:limit:minute:${ip}`;
		const hourKey = `sms:limit:hour:${ip}`;
		const dayKey = `sms:limit:day:${ip}`;

		await appRedis
			.multi()
			.incr(minuteKey)
			.expire(minuteKey, RATE_LIMIT_TTL.MINUTE)
			.incr(hourKey)
			.expire(hourKey, RATE_LIMIT_TTL.HOUR)
			.incr(dayKey)
			.expire(dayKey, RATE_LIMIT_TTL.DAY)
			.exec();
	}

	generateCode(): string {
		return Math.floor(100000 + Math.random() * 900000).toString();
	}

	async sendVerificationCode(
		phoneNumber: string,
		code: string,
		ip: string,
	): Promise<void> {
		await this.checkRateLimit(ip);

		try {
			await this.sendSMS(phoneNumber, "VERIFICATION", { code });
			await this.updateRateLimits(ip);
		} catch (error) {
			logger.error(error, `Failed to send verification code to ${phoneNumber}`);
			throw error;
		}
	}

	async sendSMS<T extends TemplateType>(
		phoneNumber: string,
		templateType: T,
		params: TemplateParams[T],
	): Promise<void> {
		// Check for test phone numbers
		if (TEST_PHONE_PATTERN.test(phoneNumber)) {
			logger.info(
				{
					template: templateType,
					params,
				},
				`[TEST] Skipping SMS to test number ${phoneNumber}`,
			);
			return;
		}

		// 对于特定类型的消息进行节流
		if (
			!(await notificationThrottle.shouldSend(
				phoneNumber,
				"SMS",
				`${templateType}`,
				`${JSON.stringify(params)}`,
			))
		) {
			return;
		}

		if (ENV.NODE_ENV === "development" || !this.smsClient) {
			logger.info(
				`[DEV] SMS Params for ${phoneNumber}: ${JSON.stringify(params)}`,
			);
			return;
		}

		try {
			// 如果是 demo 环境且是审核申请消息，使用 demo 专用模板
			const finalTemplateType =
				ENV.IS_DEMO && templateType === "NEW_AUDIT_APPLICATION"
					? "NEW_AUDIT_APPLICATION_DEMO"
					: templateType;

			// 处理参数，确保 name 参数符合阿里云短信模板规范
			const processedParams = {
				...params,
				name:
					"name" in params
						? (params as { name: string }).name.replace(/\s+/g, "_")
						: undefined,
			};

			const smsParams = {
				PhoneNumbers: phoneNumber,
				SignName: ENV.ALIYUN_SMS_SIGN_NAME,
				TemplateCode: SMS_TEMPLATES[finalTemplateType],
				TemplateParam: JSON.stringify(processedParams),
			};

			if (!this.smsClient) {
				throw AppError.create(
					"EXTERNAL_API_ERROR",
					"SMS client not initialized",
				);
			}
			const result = await this.smsClient.sendSMS(smsParams);

			if (result.Code !== "OK") {
				logger.error(result, "SMS sending failed");
				throw new Error(result.Message);
			}

			logger.info(`SMS sent to ${phoneNumber}`);
		} catch (error) {
			logger.error(error, "SMS sending failed");
			throw AppError.create(
				"EXTERNAL_API_ERROR",
				"Failed to send SMS via Aliyun",
				{
					service: "aliyun",
					error: error instanceof Error ? error.message : "Unknown error",
				},
			);
		}
	}

	async sendBulkSMS<T extends TemplateType>(
		phoneNumbers: string[],
		templateType: T,
		params: TemplateParams[T],
	): Promise<{
		success: string[];
		failed: Array<{ phone: string; error: string }>;
	}> {
		if (ENV.NODE_ENV === "development" || !this.smsClient) {
			logger.info(
				`[DEV] Bulk SMS Params for ${phoneNumbers.length} numbers: ${JSON.stringify(params)}`,
			);
			return { success: phoneNumbers, failed: [] };
		}

		const results = {
			success: [] as string[],
			failed: [] as Array<{ phone: string; error: string }>,
		};

		// Process in batches of 100 to avoid overwhelming the API
		const batchSize = 100;
		for (let i = 0; i < phoneNumbers.length; i += batchSize) {
			const batch = phoneNumbers.slice(i, i + batchSize);
			const batchPromises = batch.map(async (phone) => {
				try {
					const smsParams = {
						PhoneNumbers: phone,
						SignName: ENV.ALIYUN_SMS_SIGN_NAME,
						TemplateCode: SMS_TEMPLATES[templateType],
						TemplateParam: JSON.stringify(params),
					};

					if (!this.smsClient) {
						throw AppError.create(
							"EXTERNAL_API_ERROR",
							"SMS client not initialized",
						);
					}

					const result = await this.smsClient.sendSMS(smsParams);

					if (result.Code === "OK") {
						results.success.push(phone);
					} else {
						results.failed.push({
							phone,
							error: result.Message || "Unknown error",
						});
					}
				} catch (error) {
					results.failed.push({
						phone,
						error: error instanceof Error ? error.message : "Unknown error",
					});
				}
			});

			// Wait for current batch to complete before processing next batch
			await Promise.all(batchPromises);
		}

		logger.info(
			`Bulk SMS completed. Success: ${results.success.length}, Failed: ${results.failed.length}`,
		);

		return results;
	}

	// 给所有用户发送
	async sendToAllUsers(
		templateType: TemplateType,
		params: TemplateParams[TemplateType],
	): Promise<void> {
		const users = await User.getAllContactInfo();
		const phoneNumbers = users
			.map((user) => user.phone_number)
			.filter((phone) => phone); // 过滤掉空值

		if (phoneNumbers.length > 0) {
			await this.sendBulkSMS(phoneNumbers, templateType, params);
		}
	}
}

const smsService = new SMSService();

// 禁用短信发送，不导出，保留以备后用
// export default smsService;
