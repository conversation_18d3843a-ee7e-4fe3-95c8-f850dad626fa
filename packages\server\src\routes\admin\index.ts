import { Router } from "express";
import { requirePermission } from "@/middlewares/adminPermission.js";
import { AdminPermission } from "@packages/shared";

import basicRouter from "./basicRoutes.js";
import qualifyRouter from "./qualifyRoutes.js";
import financeRouter from "./financeRoutes.js";
import configRouter from "./configRoutes.js";
import permissionRouter from "./permissonRoutes.js";
import authRouter from "./authRoutes.js";
import fileRouter from "./fileRoutes.js";
import orderRouter from "./orderManagementRoutes.js";
import channelFundRouter from "./channelFundRoutes.js";
import platformRequestRouter from "./platformRequestRoutes.js";
import channelLeadsRouter from "./channelLeadsRoutes.js";
import bankAccountRouter from "./bankAccountRoutes.js";
import templateRouter from "./templateRoutes.js";
import sharedConfigRouter from "./sharedConfigRoutes.js";

const router = Router();

router.use("/basic", requirePermission(AdminPermission.BASIC), basicRouter);
router.use(
	"/qualify",
	requirePermission(AdminPermission.QUALIFY),
	qualifyRouter,
);
router.use(
	"/finance",
	requirePermission(AdminPermission.FINANCE),
	financeRouter,
);
router.use("/config", requirePermission(AdminPermission.CONFIG), configRouter);
router.use(
	"/permission",
	requirePermission(AdminPermission.ADMIN),
	permissionRouter,
);
router.use("/auth", requirePermission(AdminPermission.BASIC), authRouter);
router.use("/file", requirePermission(AdminPermission.BASIC), fileRouter);
router.use(
	"/order",
	requirePermission(AdminPermission.ORDER_MANAGE),
	orderRouter,
);
router.use(
	"/channel",
	requirePermission(AdminPermission.FINANCE),
	channelFundRouter,
);
router.use(
	"/leads",
	requirePermission(AdminPermission.BASIC),
	channelLeadsRouter,
);
router.use(
	"/platform",
	requirePermission(AdminPermission.FINANCE),
	platformRequestRouter,
);

// 银行账户管理路由
router.use(
	"/bank-account",
	requirePermission(AdminPermission.CONFIG),
	bankAccountRouter,
);

// 模板管理路由
router.use(
	"/templates",
	requirePermission(AdminPermission.QUALIFY),
	templateRouter,
);

// 共享配置路由
router.use(
	"/shared-config",
	requirePermission(AdminPermission.FINANCE),
	sharedConfigRouter,
);

export default router;
