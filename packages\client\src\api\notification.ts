import { request } from "./request";
import type { NotificationData } from "@packages/shared";

// 通知相关 API
export const notificationApi = {
	getNotifications: (params: {
		limit: number;
		offset: number;
	}) =>
		request.get<{ notifications: NotificationData[]; total: number }>(
			"/notifications",
			{ params },
		),

	markAsRead: (id: number) => request.post(`/notifications/${id}/read`),

	markAllAsRead: () => request.post("/notifications/read-all"),

	getUnreadCount: () => request.get<number>("/notifications/unread-count"),
} as const;
