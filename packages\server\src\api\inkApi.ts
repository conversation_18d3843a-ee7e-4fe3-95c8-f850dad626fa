import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";
// ! 注意：必须使用 DNS 调制器
import axiosWithDNSFailover from "./dnsFailoverAxios.js";
import type { BusinessConfig } from "@packages/shared";
import { getChinaDateCompactString } from "@/utils/dateUtils.js";

// API endpoints
const API_BASE_URLS = [
	"http://inkdata.w7.luyouxia.net",
	"http://qn4ve7365518.vicp.fun",
	"http://inkdata.k9.luyouxia.net:15711",
];

// API 路径常量对象
const API_PATHS = {
	SWING: "/swing/{date}.htm",
	MULTIPLIER: "/config/multiplier.htm", // OPTION_MULTIPLIER
	SINGLE_SCALE: "/config/single.htm", // STOCK_SCALE_LIMIT
	TOTAL_SCALE: "/config/max.htm", // TOTAL_SCALE_LIMIT
	CREDIT_LIMIT: "/config/credit.htm", // CHANNEL_CREDIT_LIMIT
	DISCOUNT: "/config/discount.htm", // DISCOUNT_MULTIPLIER
	// 外部报价API路径 - 使用通用模板
	PRICE_QUOTES: "/price/{provider}/{date}.htm",
} as const;

// 使用字面量常量对象
export const PriceProvider = {
	HAIYING: "haiying", // 海盈
	YINHE_DERUI: "yinhederui", // 银河德睿
	ZHONGZHENG_ZIBEN: "zhongzhengziben", // 中证资本
	ZHEQI: "zheqi", // 浙期
	YONGAN: "yongan", // 永安
	ZHONGJIN: "zhongjin", // 中金
	GUANGFA: "guangfa", // 广发
	GUOJUNZI: "guojunzi", // 国君子
} as const;

// 定义PriceProvider类型，用于参数类型检查
export type PriceProviderKey = keyof typeof PriceProvider;
export type PriceProviderValue = (typeof PriceProvider)[PriceProviderKey];

// 期权价格结构类型
export interface OptionPriceQuote {
	c100_2w: number | null; // 100% 看涨 2周
	c100_1m: number | null; // 100% 看涨 1月
	c100_2m: number | null; // 100% 看涨 2月
	c100_3m: number | null; // 100% 看涨 3月
	c103_2w: number | null; // 103% 看涨 2周
	c103_1m: number | null; // 103% 看涨 1月
	c103_2m: number | null; // 103% 看涨 2月
	c103_3m: number | null; // 103% 看涨 3月
	c105_2w: number | null; // 105% 看涨 2周
	c105_1m: number | null; // 105% 看涨 1月
	c105_2m: number | null; // 105% 看涨 2月
	c105_3m: number | null; // 105% 看涨 3月
	c110_2w: number | null; // 110% 看涨 2周
	c110_1m: number | null; // 110% 看涨 1月
	c110_2m: number | null; // 110% 看涨 2月
	c110_3m: number | null; // 110% 看涨 3月
}

/**
 * Attempts to fetch data from primary URL, falls back to secondary URLs if needed
 * @param path The API endpoint path
 * @returns The response data as string
 */
async function fetchWithFallback(path: string): Promise<string> {
	let lastError: Error | null = null;

	for (const baseUrl of API_BASE_URLS) {
		try {
			const url = `${baseUrl}${path}`;
			logger.info(`Fetching from: ${url}`);
			const response = await axiosWithDNSFailover.get(url, { timeout: 5000 });

			if (response.status === 200 && response.data) {
				return response.data;
			}
		} catch (error) {
			lastError = error as Error;
			// 对于 warn 级别的日志，只记录错误消息，而不是整个错误对象
			logger.warn(
				`Failed to fetch from ${baseUrl}${path}: ${error instanceof Error ? error.message : String(error)}`,
			);
			// Continue to next URL
		}
	}

	// If we've reached here, all URLs failed
	throw lastError || new Error("All API endpoints failed");
}

/**
 * Gets the effective swing (volatility) for a specific stock
 * @param ts_code Stock code
 * @param date Date in YYYYMMDD format
 * @returns The effective swing value or null if not found
 */
export async function getEffectiveSwing(
	ts_code: string,
	date: string,
): Promise<number | null> {
	try {
		const formattedCode = ts_code.split(".")[0];

		const swingMap = await fetchSwingData(date);
		return swingMap.get(formattedCode) || null;
	} catch (error) {
		logger.error(error, `Error getting effective swing for ${ts_code}`);
		return null;
	}
}

/**
 * Fetches the swing data for a specific date
 * @param date Date in format YYYYMMDD
 * @returns Map of stock codes to swing values
 */
export async function fetchSwingData(
	date: string,
): Promise<Map<string, number>> {
	try {
		const path = API_PATHS.SWING.replace("{date}", date);
		const data = await fetchWithFallback(path);
		return parseSwingData(data);
	} catch (error) {
		logger.error(
			`Error fetching swing data for date ${date}: ${error instanceof Error ? error.message : String(error)}`,
		);
		throw AppError.create(
			"SERVER_ERROR",
			`Failed to fetch swing data for date ${date}`,
		);
	}
}

/**
 * Parse the swing data string into a Map
 * @param data String in format "300084:13.58;301079:11.85;..."
 * @returns Map with ts_code as key and swing value as value, excluding suspended stocks (marked with 'S')
 */
function parseSwingData(data: string): Map<string, number> {
	const swingMap = new Map<string, number>();

	if (!data) return swingMap;

	const pairs = data.split(";");
	for (const pair of pairs) {
		if (!pair) continue;

		const [ts_code, swingValue] = pair.split(":");
		// Skip suspended stocks (marked with 'S')
		if (ts_code && swingValue && swingValue !== "S") {
			swingMap.set(ts_code, Number.parseFloat(swingValue));
		}
	}

	return swingMap;
}

/**
 * 获取特定日期的期权报价数据，获取失败时返回 {} 或 null
 * @param provider 报价提供商
 * @param date 日期格式 YYYYMMDD
 * @returns 股票代码到期权报价的映射
 */
export async function fetchPriceQuotes(
	provider: PriceProviderValue,
	date: string,
): Promise<Map<string, OptionPriceQuote> | null> {
	try {
		// 使用通用模板构建路径，直接使用 provider
		const path = API_PATHS.PRICE_QUOTES.replace("{provider}", provider).replace(
			"{date}",
			date,
		);

		const data = await fetchWithFallback(path);

		const result = parsePriceQuoteData(data);
		return result;
	} catch (error) {
		logger.warn(
			`Error fetching price quotes from ${provider} for date ${date}: ${error instanceof Error ? error.message : String(error)}`,
		);
		// 不抛出错误
		return null;
	}
}

/**
 * 解析期权报价数据字符串为Map对象
 * @param data 格式为 "000001:0.033/0.043/0.0577/0.0719//0.0312/0.0461/0.0606//0.025/0.0396/0.0527//0.0159/0.0297/0.0398;"
 * @returns 股票代码到期权报价的映射
 */
function parsePriceQuoteData(data: string): Map<string, OptionPriceQuote> {
	const quoteMap = new Map<string, OptionPriceQuote>();

	if (!data) return quoteMap;

	const stockEntries = data.split(";");
	for (const entry of stockEntries) {
		if (!entry) continue;

		const [stockCode, quoteString] = entry.split(":");
		if (!stockCode || !quoteString) continue;

		// 解析报价字符串中的所有价格
		const priceValues = quoteString.split("/");

		// 创建空的OptionPriceQuote对象
		const quote: OptionPriceQuote = {
			c100_2w: null,
			c100_1m: null,
			c100_2m: null,
			c100_3m: null,
			c103_2w: null,
			c103_1m: null,
			c103_2m: null,
			c103_3m: null,
			c105_2w: null,
			c105_1m: null,
			c105_2m: null,
			c105_3m: null,
			c110_2w: null,
			c110_1m: null,
			c110_2m: null,
			c110_3m: null,
		};

		// 根据示例格式，填充报价数据
		// 格式: 100C2W/100C1M/100C2M/100C3M/103C2W/103C1M/103C2M/103C3M/105C2W/105C1M/105C2M/105C3M/110C2W/110C1M/110C2M/110C3M
		const priceMapping = [
			{ index: 0, field: "c100_2w" },
			{ index: 1, field: "c100_1m" },
			{ index: 2, field: "c100_2m" },
			{ index: 3, field: "c100_3m" },
			{ index: 4, field: "c103_2w" },
			{ index: 5, field: "c103_1m" },
			{ index: 6, field: "c103_2m" },
			{ index: 7, field: "c103_3m" },
			{ index: 8, field: "c105_2w" },
			{ index: 9, field: "c105_1m" },
			{ index: 10, field: "c105_2m" },
			{ index: 11, field: "c105_3m" },
			{ index: 12, field: "c110_2w" },
			{ index: 13, field: "c110_1m" },
			{ index: 14, field: "c110_2m" },
			{ index: 15, field: "c110_3m" },
		];

		// 填充有效的价格值
		for (const { index, field } of priceMapping) {
			if (
				index < priceValues.length &&
				priceValues[index] &&
				priceValues[index] !== ""
			) {
				quote[field as keyof OptionPriceQuote] = Number.parseFloat(
					priceValues[index],
				);
			}
		}

		// 添加到映射
		quoteMap.set(stockCode, quote);
	}

	return quoteMap;
}

/**
 * 获取特定股票的期权报价
 * @param ts_code 股票代码
 * @param provider 报价提供商
 * @param date 日期格式 YYYYMMDD
 * @returns 期权报价对象或null（如果未找到）
 */
export async function getStockPriceQuote(
	ts_code: string,
	provider: PriceProviderValue,
	date: string,
): Promise<OptionPriceQuote | null> {
	try {
		const formattedCode = ts_code.split(".")[0]; // 去除后缀，如 "000001.SZ" -> "000001"
		const quoteMap = await fetchPriceQuotes(provider, date);
		return quoteMap?.get(formattedCode) || null;
	} catch (error) {
		logger.error(
			error,
			`Error getting price quote for ${ts_code} from ${provider}`,
		);
		return null;
	}
}

/**
 * 获取所有提供商的期权报价
 * @param ts_code 股票代码
 * @param date 日期格式 YYYYMMDD，如果未提供则使用当前日期
 * @returns 所有提供商的期权报价映射
 */
export async function getAllProviderQuotes(
	ts_code: string,
	date?: string,
): Promise<Map<PriceProviderKey, OptionPriceQuote | null>> {
	const formattedCode = ts_code.split(".")[0];
	const results = new Map<PriceProviderKey, OptionPriceQuote | null>();
	const providers = Object.keys(PriceProvider) as PriceProviderKey[];

	// 使用传入的日期或获取当前中国时区的日期
	const dateToUse = date || getChinaDateCompactString();

	// 并行获取所有提供商的报价
	const quoteTasks = providers.map(async (providerKey) => {
		try {
			const providerValue = PriceProvider[providerKey];
			const quote = await getStockPriceQuote(
				formattedCode,
				providerValue,
				dateToUse,
			);
			results.set(providerKey, quote);
		} catch (error) {
			logger.warn(
				`Failed to fetch quote from ${providerKey} for ${formattedCode}: ${error instanceof Error ? error.message : String(error)}`,
			);
			results.set(providerKey, null);
		}
	});

	await Promise.all(quoteTasks);
	return results;
}

// 配置项与API路径的映射表
export const CONFIG_PATHS: Partial<Record<keyof BusinessConfig, string>> = {
	OPTION_MULTIPLIER: API_PATHS.MULTIPLIER,
	STOCK_SCALE_LIMIT: API_PATHS.SINGLE_SCALE,
	TOTAL_SCALE_LIMIT: API_PATHS.TOTAL_SCALE,
	CHANNEL_CREDIT_LIMIT: API_PATHS.CREDIT_LIMIT,
	DISCOUNT_MULTIPLIER: API_PATHS.DISCOUNT,
};

/**
 * 通用业务配置获取函数
 * @param configKey 业务配置键名
 * @returns 包含配置值的Map对象
 */
export async function fetchBusinessConfig(
	configKey: keyof BusinessConfig,
): Promise<Map<string, number>> {
	const path = CONFIG_PATHS[configKey];

	if (!path) {
		throw new Error(`No API path found for config key: ${configKey}`);
	}

	try {
		const data = await fetchWithFallback(path);
		return parseNumericConfigData(data, configKey);
	} catch (error) {
		logger.error(error, `Error fetching ${configKey} data`);
		throw AppError.create("SERVER_ERROR", `Failed to fetch ${configKey} data`);
	}
}

/**
 * 通用配置数据解析函数
 * @param data 来自API的单个数字文本
 * @param configKey 配置键名
 * @returns 包含配置值的Map对象
 */
function parseNumericConfigData(
	data: string,
	configKey: string,
): Map<string, number> {
	const configMap = new Map<string, number>();

	// API返回单个数字字符串，转换为浮点数
	if (data && !Number.isNaN(Number(data))) {
		configMap.set(configKey, Number.parseFloat(data));
	} else {
		// 如果数据无效，抛出错误而不是设置默认值
		const truncatedData =
			data.length > 100
				? `${data.substring(0, 50)}...${data.substring(data.length - 50)}`
				: data;
		logger.error(`Invalid ${configKey} data from API: "${truncatedData}"`);
		throw new Error(`Invalid ${configKey} data from API`);
	}

	return configMap;
}
