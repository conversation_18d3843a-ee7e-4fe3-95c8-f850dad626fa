import axios from "axios";
import type { AxiosRequestConfig } from "axios";
import { useAuthStore } from "@/stores/auth";
import { ElMessage } from "element-plus";
import { systemStatusManager } from "@/core/systemStatusManager";
import {
	auditNotifPaths,
	authCheckPaths,
	inquiryPaths,
	tradePaths,
} from "@packages/shared";
import { messageThrottle } from "@/utils/messageThrottle";

// 创建实例
const http = axios.create({
	baseURL: "/api",
	withCredentials: true,
});

// 初始化认证头
const token = localStorage.getItem("access_token");
if (token) {
	http.defaults.headers.common.authorization = `Bearer ${token}`;
}

// 更新请求头中的 token
export function updateAuthHeader(authHeader: string | null) {
	if (authHeader) {
		http.defaults.headers.common.authorization = authHeader;
	} else {
		http.defaults.headers.common.authorization = undefined;
	}
}

// Axios 检测到名为 XSRF-TOKEN 的 cookie 存在，会自动添加到请求头

// 系统状态检查
const needCheckSystemStatus = (config: AxiosRequestConfig) =>
	tradePaths.some((path) => config.url?.includes(path)) ||
	inquiryPaths.some((path) => config.url?.includes(path));

// 认证状态检查
const needCheckAuth = (config: AxiosRequestConfig) =>
	authCheckPaths.some((path) => config.url?.includes(path));

// 请求拦截器，originalRequest 不包含 baseURL: "/api"
http.interceptors.request.use(async (config) => {
	const auth = useAuthStore();

	// if (auth.initializing && needCheckAuth(config)) {
	// 	await new Promise<void>((resolve) => {
	// 		const checkInitialization = () => {
	// 			if (!auth.initializing) {
	// 				// 初始化完成后，使用全局设置的认证头
	// 				config.headers = config.headers || {};
	// 				config.headers.authorization =
	// 					http.defaults.headers.common.authorization;
	// 				// 这个 resolve 是由 auth.initialize() 中 initializing 状态变化触发的
	// 				// 由于状态变化发生在 auth.initialize() 的 Promise 执行上下文 (App.vue 的 onMounted) 中
	// 				// 这个 resolve 产生的微任务会被插入到同一个 Promise 链中
	// 				// 因此，任何在同一执行上下文中的 Promise 都会阻塞这个请求的执行
	// 				resolve();
	// 			} else {
	// 				setTimeout(checkInitialization, 100);
	// 			}
	// 		};
	// 		checkInitialization();
	// 	});
	// }

	if (!needCheckSystemStatus(config)) {
		return config;
	}

	// 系统状态检查
	await systemStatusManager.updateSystemStatusIfExpired();
	if (!systemStatusManager.checkOperationPermission(config.url || "")) {
		return Promise.reject(
			new Error(`Operation not allowed by system with url: ${config.url}`),
		);
	}

	// 资质状态检查，跳过审核和通知路径
	if (
		!auditNotifPaths.some((path) => config.url?.includes(path)) &&
		!auth.isQualified
	) {
		messageThrottle.show("您暂无操作权限，请先完成资质认证", {
			type: "error",
		});
		return Promise.reject(new Error("Account not qualified for trading"));
	}

	return config;
});

// 请求队列
let isRefreshing = false;
let failedQueue: Array<{
	onSuccess: (value?: unknown) => void;
	onFailure: (reason?: unknown) => void;
}> = [];

// 在重试请求前更新认证头
const retryRequest = (originalRequest: AxiosRequestConfig) => {
	originalRequest.headers = originalRequest.headers || {};
	originalRequest.headers.authorization =
		http.defaults.headers.common.authorization;
	return http(originalRequest);
};

// 响应拦截器，originalRequest 不包含 baseURL: "/api"
http.interceptors.response.use(
	(response) => response,
	async (error) => {
		const { status } = error.response || {};
		const originalRequest = error.config;
		const auth = useAuthStore();

		// 处理 401
		if (status === 401 && !originalRequest._retry) {
			// 先检查是否需要认证检查，避免刷新 token 请求进入重试逻辑
			if (!needCheckAuth(originalRequest)) {
				// return value 会被自动包装成 Promise.resolve(value)。
				// return undefined 会被自动包装成 Promise.resolve(undefined)，而不是 Promise.reject(undefined)。
				// 只有显式地返回 Promise.reject(undefined) 才会导致 Promise 链进入 rejected 状态。
				return Promise.reject(error);
			}

			if (isRefreshing) {
				// Promise.then 会创建一个新 Promise
				// Promise.resolve 是一个静态方法，用于创建一个已经 resolved 的 Promise，不会覆盖对象的方法
				return new Promise(() => {
					failedQueue.push({
						onSuccess: () => retryRequest(originalRequest),
						onFailure: () => undefined,
					});
				});
			}

			isRefreshing = true;
			originalRequest._retry = true;

			try {
				await auth.refreshToken();
				for (const prom of failedQueue) prom.onSuccess();
				failedQueue = [];
				return retryRequest(originalRequest);
			} catch {
				for (const prom of failedQueue) prom.onFailure();
				failedQueue = [];
				ElMessage.error("认证已过期，请重新登录");
				auth.logout();
				return;
			} finally {
				isRefreshing = false;
			}
		}

		// CSRF token 错误处理
		if (
			status === 403 &&
			error.response?.data?.error?.includes("CSRF") &&
			!originalRequest._csrfRetry
		) {
			originalRequest._csrfRetry = true;
			return http(originalRequest);
		}

		return Promise.reject(error);
	},
);

export default http;
