<template>
  <div class="bank-account-view view">
    <div class="page-header">
      <h2>银行账户管理</h2>
      <el-button type="primary" @click="loadData">
        <el-icon>
          <Refresh />
        </el-icon>
        刷新
      </el-button>
    </div>

    <!-- Bank Account Form -->
    <el-card class="account-card">
      <template #header>
        <div class="card-header">
          <span>银行账户设置</span>
          <el-button type="primary" @click="submitForm" :loading="loading.submitForm">保存更改</el-button>
        </div>
      </template>

      <el-form ref="formRef" :model="bankAccount" label-width="120px" :rules="rules">
        <el-form-item label="账户名称">
          <el-input v-model="bankAccount.name" placeholder="请输入账户名称" />
        </el-form-item>

        <el-form-item label="银行名称">
          <el-input v-model="bankAccount.bankName" placeholder="请输入银行名称" />
        </el-form-item>

        <el-form-item label="银行代码">
          <el-input v-model="bankAccount.bankCode" placeholder="请输入银行代码" />
        </el-form-item>

        <el-form-item label="账号">
          <el-input v-model="bankAccount.accountNumber" placeholder="请输入账号" />
        </el-form-item>

        <el-form-item label="分行代码">
          <el-input v-model="bankAccount.branchCode" placeholder="请输入分行代码（可选）" />
        </el-form-item>

        <el-form-item label="港币账号">
          <el-input v-model="bankAccount.accountNumberHKD" placeholder="请输入港币账号（可选）" />
        </el-form-item>

        <el-form-item label="美元账号">
          <el-input v-model="bankAccount.accountNumberUSD" placeholder="请输入美元账号（可选）" />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- History Records -->
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <span>修改历史</span>
        </div>
      </template>

      <el-table :data="accountHistory" style="width: 100%" v-loading="loading.history"
        :default-sort="{ prop: 'created_at', order: 'descending' }" @sort-change="handleSortChange">
        <el-table-column prop="created_at" label="修改时间" sortable="custom" min-width="180"
          :formatter="(row: BankAccountHistoryItem) => formatDate(row.created_at)" />
        <el-table-column prop="config.name" label="账户名称" min-width="150" />
        <el-table-column prop="config.bankName" label="银行名称" min-width="150" />
        <el-table-column prop="config.bankCode" label="银行代码" min-width="120" />
        <el-table-column prop="config.accountNumber" label="账号" min-width="180" />
        <el-table-column prop="admin_id" label="修改人" min-width="120" />
      </el-table>

      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          :total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";
import { bankAccountApi } from "@/api/bankAccount";
import type { BankAccount } from "@packages/shared";
import { formatDate } from "@/utils/format";

const formRef = ref<FormInstance>();

const bankAccount = ref<BankAccount>({
  name: "",
  bankName: "",
  bankCode: "",
  accountNumber: "",
  accountNumberHKD: "",
  accountNumberUSD: "",
  branchCode: "",
});

interface BankAccountHistoryItem {
  config_id: number;
  config: BankAccount;
  created_at: string;
  admin_id: number;
}

const accountHistory = ref<BankAccountHistoryItem[]>([]);

const loading = ref({
  submitForm: false,
  history: false,
});

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const sortBy = ref("created_at");
const sortOrder = ref("DESC");

const rules = ref<FormRules>({
  // 删除所有required验证规则，使字段可以为空
});

const loadData = async () => {
  try {
    await Promise.all([loadBankAccount(), loadHistory()]);
  } catch (error) {
    console.error(error);
    ElMessage.error("加载数据失败");
  }
};

const loadBankAccount = async () => {
  try {
    const data = await bankAccountApi.getBankAccount();
    if (data) {
      bankAccount.value = data;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error("加载银行账户信息失败");
  }
};

const loadHistory = async () => {
  loading.value.history = true;
  try {
    const result = await bankAccountApi.getBankAccountHistory(
      currentPage.value,
      pageSize.value,
      {
        sortBy: sortBy.value,
        sortOrder: sortOrder.value as "ASC" | "DESC",
      },
    );

    accountHistory.value = result?.items || [];
    total.value = result?.total || 0;
  } catch (error) {
    console.error(error);
    ElMessage.error("加载修改历史失败");
  } finally {
    loading.value.history = false;
  }
};

const submitForm = async () => {
  if (!formRef.value) return;

  // 直接提交表单，无需验证
  loading.value.submitForm = true;
  try {
    await bankAccountApi.updateBankAccount(bankAccount.value);
    ElMessage.success("银行账户信息更新成功");
    loadData();
  } catch (error) {
    console.error(error);
    ElMessage.error("更新银行账户信息失败");
  } finally {
    loading.value.submitForm = false;
  }
};

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  loadHistory();
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  loadHistory();
};

const handleSortChange = ({
  prop,
  order,
}: { prop?: string; order?: string }) => {
  sortBy.value = prop || "created_at";
  sortOrder.value = order === "ascending" ? "ASC" : "DESC";
  loadHistory();
};

onMounted(loadData);
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-card__header) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.history-card {
  :deep(.el-table) {
    --el-table-bg-color: var(--el-bg-color-overlay);
    --el-table-tr-bg-color: var(--el-bg-color-overlay);
    --el-table-header-bg-color: var(--el-bg-color-overlay);
    --el-table-border-color: var(--el-border-color-lighter);

    .el-table__header-wrapper th {
      background-color: var(--el-bg-color-overlay);
      color: var(--el-text-color-regular);
    }

    .el-table__body-wrapper td {
      background-color: var(--el-bg-color-overlay);
      color: var(--el-text-color-primary);
    }
  }
}
</style>