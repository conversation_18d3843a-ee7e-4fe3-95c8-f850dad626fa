<template>
  <div class="account-security-view view">
    <div class="header-container">
      <h2>账户安全</h2>
    </div>

    <div v-if="!isLoading">
      <div class="top-cards" :class="{ 'center-unfreeze': !isTradingPlatform && !shouldShowTransferAuth }">
        <!-- 转账授权 -->
        <el-card v-if="isTradingPlatform || shouldShowTransferAuth" class="auth-card">
          <template #header>
            <div class="card-header">
              <span>转账授权</span>
            </div>
          </template>
          <el-form ref="authFormRef" :model="authForm" :rules="rules" label-width="64px" @submit.prevent>
            <el-form-item label="用户ID" prop="user_id">
              <el-input-number v-model="authForm.user_id" :min="1" controls-position="right" style="width: 240px" />
            </el-form-item>
            <div class="form-actions">
              <el-button type="primary" @click="showConfirmAuth(authForm.user_id)">授权</el-button>
              <el-button type="danger" @click="showConfirmRevoke(authForm.user_id)">撤销</el-button>
            </div>
          </el-form>
        </el-card>

        <!-- 解除冻结 -->
        <el-card class="unfreeze-card">
          <template #header>
            <div class="card-header">
              <span>解除冻结</span>
            </div>
          </template>
          <el-form ref="unfreezeFormRef" :model="unfreezeForm" :rules="rules" label-width="64px"
            @submit.prevent="handleUnfreezeSubmit">
            <el-form-item label="用户ID" prop="user_id">
              <el-input-number v-model="unfreezeForm.user_id" :min="1" controls-position="right" style="width: 240px" />
            </el-form-item>
            <div class="form-actions">
              <el-button type="warning" @click="handleUnfreezeSubmit">解冻</el-button>
              <el-button type="danger" @click="handleClearPasswordSubmit">清空支付密码</el-button>
            </div>
          </el-form>
        </el-card>
      </div>

      <!-- 用户列表 -->
      <el-card v-if="isTradingPlatform || shouldShowTransferAuth" class="users-list-card">
        <template #header>
          <div class="card-header">
            <span>用户列表</span>
            <el-button :loading="exporting" @click="exportData">
              <el-icon>
                <Download />
              </el-icon>
              导出数据
            </el-button>
          </div>
        </template>
        <el-table :data="users" style="width: 100%" v-loading="loading" @sort-change="handleSortChange">
          <el-table-column prop="user_id" label="ID" min-width="80" sortable="custom" />
          <el-table-column prop="phone_number" label="手机号" min-width="120" />
          <el-table-column prop="name" label="姓名" min-width="120" sortable="custom">
            <template #default="{ row }">
              {{ row.name || '未设置' }}
            </template>
          </el-table-column>
          <el-table-column prop="can_transfer" label="转账权限" min-width="120" sortable="custom">
            <template #default="{ row }">
              <el-tag :type="row.can_transfer ? 'success' : 'info'">
                {{ row.can_transfer ? '已授权' : '未授权' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="140">
            <template #default="{ row }">
              <el-button v-if="!row.can_transfer" type="primary" size="small" @click="showConfirmAuth(row.user_id)">
                授权
              </el-button>
              <el-button v-else type="danger" size="small" @click="showConfirmRevoke(row.user_id)">
                撤销
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            :total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </el-card>
    </div>

    <!-- 授权确认对话框 -->
    <el-dialog v-model="authDialog.visible" title="转账授权确认">
      <div class="confirm-info">
        <el-alert type="warning" title="确认授权该用户转账权限？" description="授权后该用户可以进行转账操作。" show-icon />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="authDialog.visible = false">取消</el-button>
          <el-button type="primary" :loading="authDialog.loading" @click="confirmAuth">
            确认授权
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 取消授权确认对话框 -->
    <el-dialog v-model="revokeDialog.visible" title="取消转账授权确认">
      <div class="confirm-info">
        <el-alert type="warning" title="确认取消该用户的转账权限？" description="取消后该用户将无法进行转账操作。" show-icon />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="revokeDialog.visible = false">取消</el-button>
          <el-button type="danger" :loading="revokeDialog.loading" @click="confirmRevoke">
            确认取消
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 解冻确认对话框 -->
    <el-dialog v-model="unfreezeDialog.visible" title="解除冻结确认">
      <div class="confirm-info">
        <el-alert type="warning" title="确认解除该用户的支付密码锁定？" description="解除后该用户可以重新尝试输入支付密码。" show-icon />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="unfreezeDialog.visible = false">取消</el-button>
          <el-button type="warning" :loading="unfreezeDialog.loading" @click="confirmUnfreeze">
            确认解除
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 清空支付密码确认对话框 -->
    <el-dialog v-model="clearPasswordDialog.visible" title="清空支付密码确认">
      <div class="confirm-info">
        <el-alert type="warning" title="确认清空该用户的支付密码？" description="清空后该用户需要重新设置支付密码才能进行转账等操作。" show-icon />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="clearPasswordDialog.visible = false">取消</el-button>
          <el-button type="danger" :loading="clearPasswordDialog.loading" @click="confirmClearPassword">
            确认清空
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { transferAuthApi } from "@/api";
import { Download } from "@element-plus/icons-vue";
import { exportToCsv } from "@/utils/export";
import { useAuthStore } from "@/stores/auth";
import { useSharedConfigStore } from "@/stores/sharedConfig";
import { whenever } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { AppType } from "@packages/shared";
import type { UserInfo } from "@packages/shared";

const authStore = useAuthStore();
const sharedConfigStore = useSharedConfigStore();

// 使用 storeToRefs 解构响应式状态
const { app_type } = storeToRefs(authStore);
const { config, isLoading } = storeToRefs(sharedConfigStore);

const users = ref<UserInfo[]>([]);
const total = ref(0);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const sortBy = ref("user_id");
const sortOrder = ref<"ASC" | "DESC">("DESC");

// 判断是否应该显示转账授权功能
const shouldShowTransferAuth = computed(() => {
  return app_type.value === AppType.TRADING_PLATFORM ||
    config.value?.channel_management?.enable_transfer_auth === true;
});

const isTradingPlatform = computed(() => app_type.value === AppType.TRADING_PLATFORM);

// 加载用户列表
const loadUsers = async () => {
  // 如果不是交易台版本，且未启用转账授权，则不加载用户列表
  if (!isTradingPlatform.value && !shouldShowTransferAuth.value) return;

  loading.value = true;
  try {
    const result = await transferAuthApi.getTransferAuthUsers({
      page: currentPage.value,
      pageSize: pageSize.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
    });
    users.value = result?.items || [];
    total.value = result?.total || 0;
  } catch (error) {
    console.error(error);
    ElMessage.error("加载用户列表失败");
  } finally {
    loading.value = false;
  }
};

// 使用 VueUse 的 whenever 自动管理用户列表加载
whenever(
  () => shouldShowTransferAuth.value && !isLoading.value,
  loadUsers,
  { immediate: true }
);

const authDialog = ref({
  visible: false,
  loading: false,
  userId: undefined as number | undefined,
});

const revokeDialog = ref({
  visible: false,
  loading: false,
  userId: undefined as number | undefined,
});

const unfreezeDialog = ref({
  visible: false,
  loading: false,
  userId: undefined as number | undefined,
});

const clearPasswordDialog = ref({
  visible: false,
  loading: false,
  userId: undefined as number | undefined,
});

const exporting = ref(false);

const authForm = ref({
  user_id: undefined as number | undefined,
});
const unfreezeForm = ref({
  user_id: undefined as number | undefined,
});

const authFormRef = ref();
const unfreezeFormRef = ref();

const rules = {
  user_id: [
    { required: true, message: "请输入用户ID" },
    { type: "number", message: "用户ID必须为数字" },
  ],
};

// 处理排序变化
const handleSortChange = ({ prop, order }: { prop?: string; order?: string }) => {
  sortBy.value = prop || "user_id";
  sortOrder.value = order === "ascending" ? "ASC" : "DESC";
  loadUsers();
};

// 处理每页数量变化
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  loadUsers();
};

// 处理页码变化
const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  loadUsers();
};

// 显示授权确认对话框
const showConfirmAuth = (userId?: number) => {
  if (!userId) {
    authFormRef.value?.validateField("user_id");
    return;
  }
  authDialog.value.userId = userId;
  authDialog.value.visible = true;
};

// 显示取消授权确认对话框
const showConfirmRevoke = (userId?: number) => {
  if (!userId) {
    authFormRef.value?.validateField("user_id");
    return;
  }
  revokeDialog.value.userId = userId;
  revokeDialog.value.visible = true;
};

// 显示解冻确认对话框
const showConfirmUnfreeze = (userId?: number) => {
  if (!userId) {
    unfreezeFormRef.value?.validateField("user_id");
    return;
  }
  unfreezeDialog.value.userId = userId;
  unfreezeDialog.value.visible = true;
};

// 显示清空支付密码确认对话框
const showConfirmClearPassword = (userId?: number) => {
  if (!userId) {
    unfreezeFormRef.value?.validateField("user_id");
    return;
  }
  clearPasswordDialog.value.userId = userId;
  clearPasswordDialog.value.visible = true;
};

// 确认授权
const confirmAuth = async () => {
  if (!authDialog.value.userId) return;

  authDialog.value.loading = true;
  try {
    await transferAuthApi.authorizeTransfer(authDialog.value.userId);
    ElMessage.success("授权成功");
    authDialog.value.visible = false;
    loadUsers();
  } catch (error) {
    console.error(error);
    ElMessage.error("授权失败");
  } finally {
    authDialog.value.loading = false;
  }
};

// 确认取消授权
const confirmRevoke = async () => {
  if (!revokeDialog.value.userId) return;

  revokeDialog.value.loading = true;
  try {
    await transferAuthApi.revokeTransferAuth(revokeDialog.value.userId);
    ElMessage.success("取消授权成功");
    revokeDialog.value.visible = false;
    loadUsers();
  } catch (error) {
    console.error(error);
    ElMessage.error("取消授权失败");
  } finally {
    revokeDialog.value.loading = false;
  }
};

// 导出数据
const exportData = async () => {
  exporting.value = true;
  try {
    const response = await transferAuthApi.getTransferAuthUsers({
      page: 1,
      pageSize: 999999,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
    });

    const headers = ["用户ID", "手机号", "姓名", "转账权限"];
    const csvData =
      response?.items.map((item) => [
        item.user_id,
        item.phone_number,
        item.name || "未设置",
        item.can_transfer ? "已授权" : "未授权",
      ]) || [];

    exportToCsv(headers, csvData, "transfer-auth-users");
    ElMessage.success("导出成功");
  } catch (error) {
    console.error("Export failed:", error);
    ElMessage.error("导出失败");
  } finally {
    exporting.value = false;
  }
};

// 确认解冻
const confirmUnfreeze = async () => {
  if (!unfreezeDialog.value.userId) return;

  unfreezeDialog.value.loading = true;
  try {
    await transferAuthApi.unfreezePayment(unfreezeDialog.value.userId);
    ElMessage.success("解除冻结成功");
    unfreezeDialog.value.visible = false;
    unfreezeFormRef.value?.resetFields();
  } catch (error) {
    console.error(error);
    ElMessage.error("解除冻结失败");
  } finally {
    unfreezeDialog.value.loading = false;
  }
};

// 确认清空支付密码
const confirmClearPassword = async () => {
  if (!clearPasswordDialog.value.userId) return;

  clearPasswordDialog.value.loading = true;
  try {
    await transferAuthApi.clearPaymentPassword(
      clearPasswordDialog.value.userId,
    );
    ElMessage.success("清空支付密码成功");
    clearPasswordDialog.value.visible = false;
    unfreezeFormRef.value?.resetFields();
  } catch (error) {
    console.error(error);
    ElMessage.error("清空支付密码失败");
  } finally {
    clearPasswordDialog.value.loading = false;
  }
};

// 提交清空支付密码
const handleClearPasswordSubmit = () => {
  if (!unfreezeForm.value.user_id) {
    unfreezeFormRef.value?.validateField("user_id");
    return;
  }
  showConfirmClearPassword(unfreezeForm.value.user_id);
};

// 修改解冻表单提交处理
const handleUnfreezeSubmit = () => {
  if (!unfreezeForm.value.user_id) {
    unfreezeFormRef.value?.validateField("user_id");
    return;
  }
  showConfirmUnfreeze(unfreezeForm.value.user_id);
};
</script>

<style scoped>
.header-container h2 {
  margin: 0;
}

.users-list-card {
  background: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.confirm-info {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.top-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.auth-card,
.unfreeze-card {
  background: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 添加表单居中样式 */
:deep(.el-form) {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 12px;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin: 8px 0;
}

@media (max-width: 767px) {
  .users-list-card {
    margin-top: 14px;
  }

  .top-cards {
    gap: 14px;
  }
}

.top-cards.center-unfreeze {
  grid-template-columns: minmax(320px, 400px);
  justify-content: center;
}
</style>