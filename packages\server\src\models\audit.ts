import prisma from "@/lib/prisma.js";
import type { Prisma, $Enums } from "@prisma/client";
import { withTransaction } from "@/core/dbTxnManager.js";
import { decrypt } from "@/utils/encryption.js";
import type {
	Currency,
	FundTransactionType,
	QualificationData,
	FundAuditData,
	QualificationAuditData,
	TransactionType,
	UserData,
} from "@packages/shared";
import { AuditStatus, AuditType } from "@packages/shared";

interface PrismaAuditData {
	audit_id: number;
	user_id: number;
	type: $Enums.audit_type;
	status: $Enums.audit_status;
	admin_id: number | null;
	created_at: Date | null;
	comment: string | null;
	data: Prisma.JsonValue;
	operation: string | null;
	amount: Prisma.Decimal | null;
	currency: $Enums.currency_type | null;
}

function convertAuditData(
	audit: PrismaAuditData,
): FundAuditData | QualificationAuditData {
	const base = {
		audit_id: audit.audit_id,
		user_id: audit.user_id,
		status: audit.status as AuditStatus,
		admin_id: audit.admin_id ?? 0,
		created_at: audit.created_at ?? new Date(),
		comment: audit.comment ?? undefined,
	};

	if (audit.type === "fund") {
		return {
			...base,
			type: AuditType.FUND,
			amount: Number(audit.amount),
			operation: audit.operation as FundTransactionType,
			currency: audit.currency as Currency,
		};
	}
	return {
		...base,
		type: AuditType.QUALIFICATION,
		data: audit.data as unknown as QualificationData,
	};
}

interface AuditQueryOptions {
	page?: number;
	pageSize?: number;
	sortBy?: string;
	sortOrder?: "ASC" | "DESC";
	filters?: {
		status?: AuditStatus;
		type?: AuditType;
		user_id?: number;
		operation?: TransactionType;
		// 可以根据需要添加更多过滤条件
	};
}

export async function getAll(options: AuditQueryOptions = {}): Promise<{
	total: number;
	items: Array<(FundAuditData | QualificationAuditData) & Partial<UserData>>;
}> {
	const {
		page = 1,
		pageSize = 10,
		sortBy = "created_at",
		sortOrder = "DESC",
		filters = {},
	} = options;

	// 构建 where 条件
	const where = {
		...(filters.status && { status: filters.status }),
		...(filters.type && { type: filters.type }),
		...(filters.user_id && { user_id: filters.user_id }),
		...(filters.operation && { operation: filters.operation }),
	};

	const [audits, total] = await Promise.all([
		prisma.audits.findMany({
			where,
			include: {
				users: {
					select: {
						name: true,
						bank_name: true,
						bank_code: true,
						bank_account: true,
						premium: true,
						deposit: true,
					},
				},
			},
			orderBy: {
				[sortBy]: sortOrder.toLowerCase(),
			},
			skip: (page - 1) * pageSize,
			take: pageSize,
		}),
		prisma.audits.count({ where }),
	]);

	return {
		total,
		items: audits.map((audit) => {
			const userData = {
				name: audit.users.name ?? "",
				bank_name: audit.users.bank_name ?? "",
				bank_code: audit.users.bank_code ?? "",
				bank_account: audit.users.bank_account
					? decrypt(audit.users.bank_account)
					: "",
				premium: Number(audit.users.premium),
				deposit: Number(audit.users.deposit),
			};

			return {
				...convertAuditData(audit),
				...userData,
			};
		}),
	};
}

export async function updateStatus(
	audit_id: number,
	admin_id: number,
	status: AuditStatus,
	comment?: string,
	client?: Prisma.TransactionClient,
): Promise<FundAuditData | QualificationAuditData> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		const audit = await tx.audits.update({
			where: {
				audit_id,
				status: AuditStatus.PENDING,
			},
			data: {
				status,
				admin_id,
				comment,
			},
		});
		return convertAuditData(audit);
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function getUserHistory(user_id: number) {
	const audits = await prisma.audits.findMany({
		where: {
			user_id,
		},
		orderBy: {
			created_at: "desc",
		},
	});
	return audits;
}

/**
 * 查找用户最新一条通过的资质审核记录
 */
export async function findLatestApprovedQualificationAudit(
	user_id: number,
): Promise<QualificationAuditData | null> {
	const audit = await prisma.audits.findFirst({
		where: {
			user_id,
			type: AuditType.QUALIFICATION,
			status: AuditStatus.APPROVED,
		},
		orderBy: {
			created_at: "desc",
		},
	});

	return audit ? (convertAuditData(audit) as QualificationAuditData) : null;
}

// 增加审核计数
async function incrementAuditCount(
	type: AuditType,
	client: Prisma.TransactionClient,
): Promise<number> {
	const date = new Date();
	const dateStr =
		date.getFullYear().toString() +
		(date.getMonth() + 1).toString().padStart(2, "0") +
		date.getDate().toString().padStart(2, "0");

	const countField =
		type === AuditType.QUALIFICATION ? "qualification_count" : "fund_count";

	const result = await client.audit_sequences.upsert({
		where: { date_str: dateStr },
		create: {
			date_str: dateStr,
			[countField]: 1,
		},
		update: {
			[type === AuditType.QUALIFICATION ? "qualification_count" : "fund_count"]:
				{
					increment: 1,
				},
		},
	});

	return type === AuditType.QUALIFICATION
		? result.qualification_count
		: result.fund_count;
}

export async function createFundAudit(
	user_id: number,
	operation: FundTransactionType,
	amount: number,
	currency: Currency,
	comment?: string,
): Promise<FundAuditData> {
	const createFn = async (tx: Prisma.TransactionClient) => {
		await incrementAuditCount(AuditType.FUND, tx);

		const result = await tx.audits.create({
			data: {
				user_id,
				type: AuditType.FUND,
				status: AuditStatus.PENDING,
				comment,
				operation,
				amount,
				currency,
			},
		});
		return convertAuditData(result) as FundAuditData;
	};

	return withTransaction(createFn);
}

export async function createQualificationAudit(
	user_id: number,
	qualificationData: QualificationData,
	comment?: string,
): Promise<QualificationAuditData> {
	const createFn = async (tx: Prisma.TransactionClient) => {
		await incrementAuditCount(AuditType.QUALIFICATION, tx);

		const result = await tx.audits.create({
			data: {
				user_id,
				type: AuditType.QUALIFICATION,
				status: AuditStatus.PENDING,
				comment,
				data: qualificationData as unknown as Prisma.InputJsonValue,
			},
		});
		return result as unknown as QualificationAuditData;
	};

	return withTransaction(createFn);
}

export async function getPendingByUser(
	user_id: number,
): Promise<Array<QualificationAuditData | FundAuditData>> {
	const audits = await prisma.audits.findMany({
		where: {
			user_id,
			status: AuditStatus.PENDING,
		},
	});
	return audits.map((audit) => convertAuditData(audit));
}
