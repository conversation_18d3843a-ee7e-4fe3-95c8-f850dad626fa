import { defineStore } from "pinia";
import { ref, watch } from "vue";
import { siteConfigApi } from "@/api";
import type { SiteConfig } from "@packages/shared";
import { TinyColor } from "@ctrl/tinycolor";
import { useTheme } from "@/composables/useTheme";

const LOCAL_STORAGE_KEY = "siteConfigCache";

export const useSiteConfigStore = defineStore("siteConfig", () => {
	// 从 localStorage 中获取缓存数据
	const cachedConfig = (() => {
		try {
			const cached = localStorage.getItem(LOCAL_STORAGE_KEY);
			return cached ? JSON.parse(cached) : null;
		} catch (err) {
			console.error("Failed to parse cached site config:", err);
			return null;
		}
	})();

	const config = ref<SiteConfig | null>(cachedConfig);
	const isLoading = ref(false);
	const error = ref<string | null>(null);
	const { isDark } = useTheme();

	// 如果有缓存数据，立即应用主题设置
	if (cachedConfig) {
		// 更新网站标题
		if (cachedConfig.clientSiteName) {
			document.title = cachedConfig.clientSiteName;
		}

		// 更新网站图标
		if (cachedConfig.faviconId) {
			const faviconLink =
				document.querySelector('link[rel="icon"]') ||
				document.createElement("link");

			(faviconLink as HTMLLinkElement).rel = "icon";
			(faviconLink as HTMLLinkElement).href = siteConfigApi.getAssetUrl(
				cachedConfig.faviconId,
			);

			if (!document.querySelector('link[rel="icon"]')) {
				document.head.appendChild(faviconLink);
			}
		}

		// 应用主题色
		if (cachedConfig.clientPrimaryColor) {
			applyPrimaryColor(cachedConfig.clientPrimaryColor);
		}
	}

	// 监听主题模式变化，重新应用主题色
	watch(isDark, () => {
		if (config.value?.clientPrimaryColor) {
			applyPrimaryColor(config.value.clientPrimaryColor);
		}
	});

	// 从缓存加载配置
	const loadFromCache = () => {
		try {
			const cachedData = localStorage.getItem(LOCAL_STORAGE_KEY);
			if (cachedData) {
				const parsedData = JSON.parse(cachedData);
				config.value = parsedData;

				// 更新网站标题
				if (config.value?.clientSiteName) {
					document.title = config.value.clientSiteName;
				}

				// 更新网站图标
				if (config.value?.faviconId) {
					const faviconLink =
						document.querySelector('link[rel="icon"]') ||
						document.createElement("link");

					(faviconLink as HTMLLinkElement).rel = "icon";
					(faviconLink as HTMLLinkElement).href = siteConfigApi.getAssetUrl(
						config.value.faviconId,
					);

					if (!document.querySelector('link[rel="icon"]')) {
						document.head.appendChild(faviconLink);
					}
				}

				// 应用主题色
				if (config.value?.clientPrimaryColor) {
					applyPrimaryColor(config.value.clientPrimaryColor);
				}
			}
		} catch (err) {
			console.error("Failed to load site config from cache:", err);
		}
	};

	async function loadSiteConfig() {
		if (isLoading.value) return;

		isLoading.value = true;
		error.value = null;

		// 先尝试从缓存加载
		loadFromCache();

		try {
			const data = await siteConfigApi.getSiteConfig();
			config.value = data;

			// 更新网站标题
			if (config.value?.clientSiteName) {
				document.title = config.value.clientSiteName;
			}

			// 更新网站图标
			if (config.value?.faviconId) {
				const faviconLink =
					document.querySelector('link[rel="icon"]') ||
					document.createElement("link");

				(faviconLink as HTMLLinkElement).rel = "icon";
				(faviconLink as HTMLLinkElement).href = siteConfigApi.getAssetUrl(
					config.value.faviconId,
				);

				if (!document.querySelector('link[rel="icon"]')) {
					document.head.appendChild(faviconLink);
				}
			}

			// 应用主题色
			if (config.value?.clientPrimaryColor) {
				applyPrimaryColor(config.value.clientPrimaryColor);
			}

			// 更新缓存
			localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(data));

			return config.value;
		} catch (err) {
			console.error("Failed to load site configuration:", err);
			error.value = "Failed to load site configuration";
			return null;
		} finally {
			isLoading.value = false;
		}
	}

	function resetConfig() {
		config.value = null;
		error.value = null;
		localStorage.removeItem(LOCAL_STORAGE_KEY);
	}

	// 应用主题色
	function applyPrimaryColor(color: string) {
		// 设置主色调
		document.documentElement.style.setProperty("--el-color-primary", color);

		// 使用 TinyColor 生成不同深浅的主题色变体
		const tinycolor = new TinyColor(color);

		// 根据明暗模式选择正确的混合方式
		if (isDark.value) {
			// 暗色模式下：light系列混合黑色，dark系列混合白色
			// Element Plus 只使用 light-3, light-5, light-7, light-8, light-9
			const lightColor3 = tinycolor.mix("#000000", 30).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-3",
				lightColor3,
			);

			const lightColor5 = tinycolor.mix("#000000", 50).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-5",
				lightColor5,
			);

			const lightColor7 = tinycolor.mix("#000000", 70).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-7",
				lightColor7,
			);

			const lightColor8 = tinycolor.mix("#000000", 80).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-8",
				lightColor8,
			);

			const lightColor9 = tinycolor.mix("#000000", 90).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-9",
				lightColor9,
			);

			// 生成 dark-2 颜色（混合白色）
			const darkColor = tinycolor.mix("#ffffff", 20).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-dark-2",
				darkColor,
			);

			// 更细腻的表格渐变色
			const lightColor1 = tinycolor.mix("#000000", 10).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-1",
				lightColor1,
			);

			const lightColor2 = tinycolor.mix("#000000", 20).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-2",
				lightColor2,
			);

			const lightColor4 = tinycolor.mix("#000000", 40).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-4",
				lightColor4,
			);
		} else {
			// 亮色模式下：light系列混合白色，dark系列混合黑色
			// Element Plus 只使用 light-3, light-5, light-7, light-8, light-9
			const lightColor3 = tinycolor.mix("#ffffff", 30).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-3",
				lightColor3,
			);

			const lightColor5 = tinycolor.mix("#ffffff", 50).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-5",
				lightColor5,
			);

			const lightColor7 = tinycolor.mix("#ffffff", 70).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-7",
				lightColor7,
			);

			const lightColor8 = tinycolor.mix("#ffffff", 80).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-8",
				lightColor8,
			);

			const lightColor9 = tinycolor.mix("#ffffff", 90).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-light-9",
				lightColor9,
			);

			// 生成 dark-2 颜色（混合黑色）
			const darkColor = tinycolor.mix("#000000", 20).toHexString();
			document.documentElement.style.setProperty(
				"--el-color-primary-dark-2",
				darkColor,
			);
		}
	}

	return {
		config,
		isLoading,
		error,
		loadSiteConfig,
		resetConfig,
		applyPrimaryColor,

		// 常用的getter
		companyName: () => config.value?.companyLegalName || "",
		shortName: () => config.value?.companyShortName || "",
		logoUrl: () =>
			config.value?.logoId
				? siteConfigApi.getAssetUrl(config.value.logoId)
				: null,
	};
});
