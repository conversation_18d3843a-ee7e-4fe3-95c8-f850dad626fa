#!/usr/bin/env tsx

/**
 * 测试脚本：手动触发INK外部报价更新
 * 
 * 用途：
 * - 测试外部报价获取功能
 * - 调试报价更新问题
 * - 验证API连接和数据解析
 */

import { ENV } from "./src/config/configManager.js";
import logger from "./src/utils/logger.js";
import * as InkApi from "./src/api/inkApi.js";
import { getChinaDateCompactString } from "./src/utils/dateUtils.js";
import { coordinationRedis } from "./src/lib/redis.js";

console.log("=== INK外部报价测试脚本 ===");
console.log(`环境: ${ENV.NODE_ENV}`);
console.log(`日期: ${getChinaDateCompactString()}`);

async function testExternalQuotes() {
    try {
        const date = getChinaDateCompactString();
        const providers = Object.values(InkApi.PriceProvider);
        
        console.log(`\n测试日期: ${date}`);
        console.log(`报价提供商数量: ${providers.length}`);
        console.log(`提供商列表: ${providers.join(", ")}`);
        
        // 测试每个提供商
        for (const provider of providers) {
            console.log(`\n--- 测试提供商: ${provider} ---`);
            
            try {
                const startTime = Date.now();
                const quotes = await InkApi.fetchPriceQuotes(provider, date);
                const duration = Date.now() - startTime;
                
                if (quotes && quotes.size > 0) {
                    console.log(`✅ 成功获取 ${quotes.size} 条报价 (耗时: ${duration}ms)`);
                    
                    // 显示前3条报价作为示例
                    let count = 0;
                    for (const [stockCode, quote] of quotes) {
                        if (count >= 3) break;
                        console.log(`   ${stockCode}: c100_2w=${quote.c100_2w}, c103_1m=${quote.c103_1m}`);
                        count++;
                    }
                    
                    if (quotes.size > 3) {
                        console.log(`   ... 还有 ${quotes.size - 3} 条报价`);
                    }
                } else {
                    console.log(`❌ 未获取到报价数据 (耗时: ${duration}ms)`);
                }
            } catch (error) {
                console.log(`❌ 获取失败: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        
        // 检查Redis缓存
        console.log(`\n--- 检查Redis缓存 ---`);
        try {
            const pattern = "ink:quotes:*";
            const keys = await coordinationRedis.keys(pattern);
            console.log(`缓存键数量: ${keys.length}`);
            
            if (keys.length > 0) {
                console.log("缓存键示例:");
                keys.slice(0, 5).forEach(key => console.log(`  ${key}`));
                if (keys.length > 5) {
                    console.log(`  ... 还有 ${keys.length - 5} 个键`);
                }
            }
        } catch (error) {
            console.log(`Redis检查失败: ${error instanceof Error ? error.message : String(error)}`);
        }
        
    } catch (error) {
        console.error("测试过程中发生错误:", error);
    }
}

async function clearRecentExecution() {
    try {
        console.log("\n--- 清除最近执行记录 ---");
        
        // 清除总体执行记录
        await coordinationRedis.del("ink:sync:last_execution");
        console.log("✅ 已清除总体执行记录");
        
        // 清除特定任务执行记录
        const historyPattern = "ink:execution:history:*";
        const historyKeys = await coordinationRedis.keys(historyPattern);
        
        if (historyKeys.length > 0) {
            await coordinationRedis.del(...historyKeys);
            console.log(`✅ 已清除 ${historyKeys.length} 个任务执行历史记录`);
        } else {
            console.log("ℹ️ 没有找到任务执行历史记录");
        }
        
    } catch (error) {
        console.error("清除执行记录失败:", error);
    }
}

async function main() {
    try {
        // 选择操作
        const args = process.argv.slice(2);
        const command = args[0] || "test";
        
        switch (command) {
            case "test":
                await testExternalQuotes();
                break;
            case "clear":
                await clearRecentExecution();
                break;
            case "both":
                await clearRecentExecution();
                await testExternalQuotes();
                break;
            default:
                console.log("用法:");
                console.log("  tsx test-ink-quotes.ts test   # 测试外部报价获取");
                console.log("  tsx test-ink-quotes.ts clear  # 清除最近执行记录");
                console.log("  tsx test-ink-quotes.ts both   # 清除记录并测试");
                break;
        }
        
    } catch (error) {
        console.error("脚本执行失败:", error);
        process.exit(1);
    } finally {
        // 关闭Redis连接
        await coordinationRedis.quit();
        console.log("\n=== 测试完成 ===");
    }
}

// 运行主函数
main().catch(console.error);
