import { Router } from "express";
import { wrapRoute } from "@/utils/routeWrapper.js";
import * as adminService from "@/services/admin/index.js";
import * as OrderManagementService from "@/services/admin/orderManagementService.js";
import * as Order from "@/models/trade/order.js";
import { AppError } from "@/core/appError.js";
import * as positionService from "@/services/positionService.js";
import * as orderService from "@/services/orderService.js";
import * as fundService from "@/services/fundService.js";
import * as inquiryService from "@/services/inquiryService.js";
import * as userService from "@/services/userService.js";
import * as SystemHealthService from "@/services/systemHealthService.js";
import {
	isChannelDbConfigured,
	getChannelDbConnection,
} from "@/lib/channelDatabaseManager.js";
import { getAllConfiguredChannels } from "@/config/defaultParams.js";
import { isTradingPlatform } from "@/config/configManager.js";
import { decrypt } from "@/utils/encryption.js";
import type { StructureType, OrderData } from "@packages/shared";

const router = Router();

// 定义请求类型
interface ApiQueryParams {
	api_token?: string;
}

// Order Management API Endpoints for Control Panel
// ----------------------------------------------

// 人工录单 POST api/open/order/manual-create
router.post(
	"/order/manual-create",
	wrapRoute<{
		api_token: string;
		user_id: number;
		ts_code: string;
		entry_price: number;
		scale: number;
		term: number;
		quote: number;
		structure: StructureType;
		comment: string;
		quote_provider: string;
		quote_diff: number;
	}>(async (req, res) => {
		const { api_token, ...orderData } = req.body;

		// 验证API Token
		if (!api_token || typeof api_token !== "string") {
			throw AppError.create("INVALID_TOKEN", "API token required");
		}

		const isValid = await adminService.verifyApiToken(api_token);
		if (!isValid) {
			throw AppError.create("INVALID_TOKEN", "Invalid API token");
		}

		// 验证必填字段
		if (
			!orderData.user_id ||
			!orderData.ts_code ||
			!orderData.entry_price ||
			!orderData.scale ||
			!orderData.term ||
			!orderData.quote ||
			!orderData.structure ||
			!orderData.comment ||
			!orderData.quote_provider
		) {
			throw AppError.create("BAD_REQUEST", "Missing required fields");
		}

		// 获取管理员ID (使用1作为系统ID，因为是通过API创建的)
		const admin_id = 1;

		// 创建订单
		const order = await OrderManagementService.createOrderManually(
			orderData,
			admin_id,
		);

		res.json(order);
	}),
);

// 修改订单 POST api/open/order/manual-update
router.post(
	"/order/manual-update",
	wrapRoute<{
		api_token: string;
		trade_no: string;
		newValue: Partial<OrderData>;
		comment: string;
	}>(async (req, res) => {
		const { api_token, trade_no, newValue, comment } = req.body;

		// 验证API Token
		if (!api_token || typeof api_token !== "string") {
			throw AppError.create("INVALID_TOKEN", "API token required");
		}

		const isValid = await adminService.verifyApiToken(api_token);
		if (!isValid) {
			throw AppError.create("INVALID_TOKEN", "Invalid API token");
		}

		// 验证必填字段
		if (!trade_no || !newValue || !comment) {
			throw AppError.create("BAD_REQUEST", "Missing required fields");
		}

		// 获取管理员ID (使用1作为系统ID，因为是通过API修改的)
		const admin_id = 1;

		// 更新订单
		const order = await OrderManagementService.updateOrderManually(
			{
				trade_no,
				newValue,
				comment,
			},
			admin_id,
		);

		res.json(order);
	}),
);

// 人工结束订单 POST api/open/order/manual-close
router.post(
	"/order/manual-close",
	wrapRoute<{
		api_token: string;
		trade_no: string;
		settle_price: number;
		comment: string;
	}>(async (req, res) => {
		const { api_token, trade_no, settle_price, comment } = req.body;

		// 验证API Token
		if (!api_token || typeof api_token !== "string") {
			throw AppError.create("INVALID_TOKEN", "API token required");
		}

		const isValid = await adminService.verifyApiToken(api_token);
		if (!isValid) {
			throw AppError.create("INVALID_TOKEN", "Invalid API token");
		}

		// 验证必填字段
		if (!trade_no || !settle_price || !comment) {
			throw AppError.create("BAD_REQUEST", "Missing required fields");
		}

		// 获取管理员ID (使用1作为系统ID，因为是通过API关闭的)
		const admin_id = 1;

		// 结束订单
		const order = await OrderManagementService.closeOrderManually(
			{
				trade_no,
				settle_price,
				comment,
			},
			admin_id,
		);

		res.json(order);
	}),
);

// 获取订单详情 GET api/open/order/details
router.get(
	"/order/details",
	wrapRoute<ApiQueryParams & { trade_no: string }>(async (req, res) => {
		const { api_token, trade_no } = req.query;

		// 验证API Token
		if (!api_token || typeof api_token !== "string") {
			throw AppError.create("INVALID_TOKEN", "API token required");
		}

		const isValid = await adminService.verifyApiToken(api_token);
		if (!isValid) {
			throw AppError.create("INVALID_TOKEN", "Invalid API token");
		}

		// 验证必填字段
		if (!trade_no || typeof trade_no !== "string") {
			throw AppError.create("BAD_REQUEST", "Missing trade_no parameter");
		}

		// 获取订单详情
		const order = await Order.findByTradeNo(trade_no);
		if (!order) {
			throw AppError.create("NOT_FOUND", "Order not found");
		}

		res.json(order);
	}),
);

// 获取持仓列表（需要 API Token）: GET api/open/positions
router.get(
	"/positions",
	wrapRoute<ApiQueryParams>(async (req, res) => {
		const { api_token } = req.query;

		if (!api_token || typeof api_token !== "string") {
			throw AppError.create("INVALID_TOKEN", "API token required");
		}

		const isValid = await adminService.verifyApiToken(api_token);
		if (!isValid) {
			throw AppError.create("INVALID_TOKEN", "Invalid API token");
		}

		// 获取平台自身的持仓数据
		let positionsText = await positionService.getAllPositionsByApi();

		// 如果是交易台环境，获取所有通道的持仓数据并合并
		if (isTradingPlatform()) {
			try {
				// 获取所有已配置的通道
				const channelIds = await getAllConfiguredChannels();

				// 遍历所有通道，获取持仓数据
				for (const channelId of channelIds) {
					if (isChannelDbConfigured(channelId)) {
						try {
							// 获取通道数据库连接
							const channelPrisma = await getChannelDbConnection(channelId);

							// 查询该通道的所有持仓数据
							const channelPositions = await channelPrisma.positions.findMany();

							// 转换并添加通道ID
							const channelPositionsText = channelPositions
								.map((position) => {
									// 处理股票代码：移除字母部分
									const stockCode = position.ts_code.replace(/\.[A-Z]+$/, "");

									// 构建数据字符串，用破折号连接，最后添加通道ID
									const values = [
										position.trade_no || "",
										position.user_id || "",
										stockCode || "",
										position.entry_price || "",
										position.scale?.toString() || "",
										position.term?.toString() || "",
										position.quote || "",
										position.structure || "",
										position.quote_provider || "INK",
										channelId, // 添加通道ID
									].join("~");

									return values;
								})
								.join(";\n");

							// 如果有数据，添加到结果中
							if (channelPositionsText) {
								positionsText = positionsText
									? `${positionsText};\n${channelPositionsText}`
									: channelPositionsText;
							}
						} catch (error) {
							console.error(`获取通道 ${channelId} 持仓数据失败:`, error);
							// 继续处理下一个通道，不中断整个流程
						}
					}
				}
			} catch (error) {
				console.error("获取通道持仓数据失败:", error);
				// 即使获取通道数据失败，仍然返回平台数据
			}
		}

		// 设置为 text/html，这样浏览器就会直接在 body 中显示文本
		res.setHeader("Content-Type", "text/html");
		res.send(positionsText);
	}),
);

// 获取订单列表（需要 API Token）: GET api/open/orders
router.get(
	"/orders",
	wrapRoute<ApiQueryParams>(async (req, res) => {
		const { api_token } = req.query;

		if (!api_token || typeof api_token !== "string") {
			throw AppError.create("INVALID_TOKEN", "API token required");
		}

		const isValid = await adminService.verifyApiToken(api_token);
		if (!isValid) {
			throw AppError.create("INVALID_TOKEN", "Invalid API token");
		}

		// 获取平台自身的订单数据
		let ordersText = await orderService.getAllOrdersByApi();

		// 如果是交易台环境，获取所有通道的订单数据并合并
		if (isTradingPlatform()) {
			try {
				// 获取所有已配置的通道
				const channelIds = await getAllConfiguredChannels();

				// 遍历所有通道，获取订单数据
				for (const channelId of channelIds) {
					if (isChannelDbConfigured(channelId)) {
						try {
							// 获取通道数据库连接
							const channelPrisma = await getChannelDbConnection(channelId);

							// 查询该通道的所有订单数据
							const channelOrders = await channelPrisma.orders.findMany();

							// 转换并添加通道ID
							const channelOrdersText = channelOrders
								.map((order) => {
									// 处理股票代码：移除字母部分
									const stockCode = order.ts_code.replace(/\.[A-Z]+$/, "");

									// 构建数据字符串，用波浪号连接，最后添加通道ID
									const values = [
										order.trade_no || "",
										order.user_id?.toString() || "",
										stockCode || "",
										Number(order.entry_price).toString() || "",
										Number(order.exercise_price).toString() || "",
										Number(order.settle_price).toString() || "",
										order.scale?.toString() || "",
										order.total_scale?.toString() ||
											order.scale?.toString() ||
											"",
										order.term?.toString() || "",
										Number(order.quote).toString() || "",
										order.quote_diff
											? Number(order.quote_diff).toString()
											: "0",
										order.structure || "",
										order.status || "",
										order.created_at
											? new Date(order.created_at).toISOString().split("T")[0]
											: "",
										order.closed_at
											? new Date(order.closed_at).toISOString().split("T")[0]
											: "",
										order.quote_provider || "INK",
										channelId, // 添加通道ID
									].join("~");

									return values;
								})
								.join(";\n");

							// 如果有数据，添加到结果中
							if (channelOrdersText) {
								ordersText = ordersText
									? `${ordersText};\n${channelOrdersText}`
									: channelOrdersText;
							}
						} catch (error) {
							console.error(`获取通道 ${channelId} 订单数据失败:`, error);
							// 继续处理下一个通道，不中断整个流程
						}
					}
				}
			} catch (error) {
				console.error("获取通道订单数据失败:", error);
				// 即使获取通道数据失败，仍然返回平台数据
			}
		}

		// 设置为 text/html，这样浏览器就会直接在 body 中显示文本
		res.setHeader("Content-Type", "text/html");
		res.send(ordersText);
	}),
);

// 获取所有询价记录: GET api/open/inquiries
router.get(
	"/inquiries",
	wrapRoute<ApiQueryParams>(async (req, res) => {
		const { api_token } = req.query;

		if (!api_token || typeof api_token !== "string") {
			throw AppError.create("INVALID_TOKEN", "API token required");
		}

		const isValid = await adminService.verifyApiToken(api_token);
		if (!isValid) {
			throw AppError.create("INVALID_TOKEN", "Invalid API token");
		}

		// 获取平台自身的询价数据
		let inquiriesText = await inquiryService.getAllTextByApi();

		// 如果是交易台环境，获取所有通道的询价数据并合并
		if (isTradingPlatform()) {
			try {
				// 获取所有已配置的通道
				const channelIds = await getAllConfiguredChannels();

				// 遍历所有通道，获取询价数据
				for (const channelId of channelIds) {
					if (isChannelDbConfigured(channelId)) {
						try {
							// 获取通道数据库连接
							const channelPrisma = await getChannelDbConnection(channelId);

							// 查询该通道的所有询价数据
							const channelInquiries = await channelPrisma.inquiries.findMany();

							// 转换为文本格式
							const channelInquiriesText = channelInquiries
								.map((inquiry) => {
									const values = [
										inquiry.inquiry_id?.toString() || "",
										inquiry.user_id?.toString() || "",
										inquiry.ts_code || "",
										inquiry.scale?.toString() || "0",
										inquiry.term?.toString() || "0",
										inquiry.structure || "",
										inquiry.quote?.toString() || "0",
										inquiry.status || "",
										channelId, // 添加通道ID
									].join("~");

									return values;
								})
								.join(";\n");

							// 如果有数据，添加到结果中
							if (channelInquiriesText) {
								inquiriesText = inquiriesText
									? `${inquiriesText};\n${channelInquiriesText}`
									: channelInquiriesText;
							}
						} catch (error) {
							console.error(`获取通道 ${channelId} 询价数据失败:`, error);
							// 继续处理下一个通道，不中断整个流程
						}
					}
				}
			} catch (error) {
				console.error("获取通道询价数据失败:", error);
				// 即使获取通道数据失败，仍然返回平台数据
			}
		}

		res.setHeader("Content-Type", "text/html");
		res.send(inquiriesText);
	}),
);

// 获取所有用户信息: GET api/open/users
router.get(
	"/users",
	wrapRoute<ApiQueryParams>(async (req, res) => {
		const { api_token } = req.query;

		if (!api_token || typeof api_token !== "string") {
			throw AppError.create("INVALID_TOKEN", "API token required");
		}

		const isValid = await adminService.verifyApiToken(api_token);
		if (!isValid) {
			throw AppError.create("INVALID_TOKEN", "Invalid API token");
		}

		// 获取平台自身的用户数据
		let usersText = await userService.getAllTextByApi();

		// 如果是交易台环境，获取所有通道的用户数据并合并
		if (isTradingPlatform()) {
			try {
				// 获取所有已配置的通道
				const channelIds = await getAllConfiguredChannels();

				// 遍历所有通道，获取用户数据
				for (const channelId of channelIds) {
					if (isChannelDbConfigured(channelId)) {
						try {
							// 获取通道数据库连接
							const channelPrisma = await getChannelDbConnection(channelId);

							// 查询该通道的所有用户数据
							const channelUsers = await channelPrisma.users.findMany({
								select: {
									user_id: true,
									phone_number: true,
									email: true,
									name: true,
									bank_name: true,
									bank_code: true,
									bank_account: true,
									contribution: true,
									premium: true,
									created_at: true,
									balance_cny: true,
									balance_hkd: true,
									balance_usd: true,
									is_qualified: true,
								},
							});

							// 转换为文本格式
							const channelUsersText = channelUsers
								.map((user) => {
									// 解密敏感字段
									const decryptedEmail = decrypt(user.email);
									const decryptedPhoneNumber = user.phone_number
										? decrypt(user.phone_number)
										: "";
									const decryptedBankAccount = user.bank_account
										? decrypt(user.bank_account)
										: "";
									const decryptedBalanceCny = Number.parseFloat(
										decrypt(user.balance_cny),
									).toString();
									const decryptedBalanceHkd = Number.parseFloat(
										decrypt(user.balance_hkd),
									).toString();
									const decryptedBalanceUsd = Number.parseFloat(
										decrypt(user.balance_usd),
									).toString();

									// 构建数据字符串，用波浪号连接各字段
									const values = [
										user.user_id?.toString() || "",
										decryptedPhoneNumber,
										decryptedEmail,
										user.name || "",
										user.bank_name || "",
										user.bank_code || "",
										decryptedBankAccount,
										user.contribution?.toString() || "0",
										user.premium?.toString() || "0",
										user.created_at?.toISOString() || "",
										decryptedBalanceCny,
										decryptedBalanceHkd,
										decryptedBalanceUsd,
										user.is_qualified ? "1" : "0",
										channelId, // 添加通道ID
									].join("~");

									return values;
								})
								.join(";\n");

							// 如果有数据，添加到结果中
							if (channelUsersText) {
								usersText = usersText
									? `${usersText};\n${channelUsersText}`
									: channelUsersText;
							}
						} catch (error) {
							console.error(`获取通道 ${channelId} 用户数据失败:`, error);
							// 继续处理下一个通道，不中断整个流程
						}
					}
				}
			} catch (error) {
				console.error("获取通道用户数据失败:", error);
				// 即使获取通道数据失败，仍然返回平台数据
			}
		}

		res.setHeader("Content-Type", "text/html");
		res.send(usersText);
	}),
);

// 获取所有资金记录: GET api/open/funds
router.get(
	"/funds",
	wrapRoute<ApiQueryParams>(async (req, res) => {
		const { api_token } = req.query;

		if (!api_token || typeof api_token !== "string") {
			throw AppError.create("INVALID_TOKEN", "API token required");
		}

		const isValid = await adminService.verifyApiToken(api_token);
		if (!isValid) {
			throw AppError.create("INVALID_TOKEN", "Invalid API token");
		}

		// 获取平台自身的资金数据
		let auditsText = await fundService.getAllTextByApi();

		// 如果是交易台环境，获取所有通道的资金数据并合并
		if (isTradingPlatform()) {
			try {
				// 获取所有已配置的通道
				const channelIds = await getAllConfiguredChannels();

				// 遍历所有通道，获取资金数据
				for (const channelId of channelIds) {
					if (isChannelDbConfigured(channelId)) {
						try {
							// 获取通道数据库连接
							const channelPrisma = await getChannelDbConnection(channelId);

							// 查询该通道的所有资金数据
							const channelTransactions =
								await channelPrisma.transactions.findMany();

							// 转换为文本格式
							const channelTransactionsText = channelTransactions
								.map((transaction) => {
									// 构建数据字符串
									const values = [
										transaction.txn_id?.toString() || "",
										transaction.user_id?.toString() || "",
										transaction.signed_amount?.toString() || "0",
										transaction.type || "",
										transaction.currency || "",
										transaction.trade_no || "",
										transaction.created_at?.toISOString().split("T")[0] || "",
										channelId, // 添加通道ID
									].join("~");

									return values;
								})
								.join(";\n");

							// 如果有数据，添加到结果中
							if (channelTransactionsText) {
								auditsText = auditsText
									? `${auditsText};\n${channelTransactionsText}`
									: channelTransactionsText;
							}
						} catch (error) {
							console.error(`获取通道 ${channelId} 资金数据失败:`, error);
							// 继续处理下一个通道，不中断整个流程
						}
					}
				}
			} catch (error) {
				console.error("获取通道资金数据失败:", error);
				// 即使获取通道数据失败，仍然返回平台数据
			}
		}

		res.setHeader("Content-Type", "text/html");
		res.send(auditsText);
	}),
);

// 获取系统健康检查数据: GET api/open/system-health
/**
 * @api {get} /api/open/system-health Get System Health Check Data
 * @apiName GetSystemHealth
 * @apiGroup System
 * @apiDescription Retrieves system health check data from both the local platform and all connected channels.
 * Optional parameters allow filtering by test type, pagination, and triggering a new health check run.
 *
 * @apiParam {String} api_token Required API authentication token
 * @apiParam {String} [type] Optional test type filter (e.g., "api_connectivity", "database_connectivity")
 *                          If not provided, returns the latest result of each test type
 * @apiParam {Number} [page=1] Page number for pagination
 * @apiParam {Number} [pageSize=20] Items per page
 * @apiParam {Boolean} [run=false] If "true", triggers a new health check run before returning results
 *
 * @apiSuccess {Object[]} items Array of health check results
 * @apiSuccess {Number} total Total number of health check records matching the filter
 *
 * @apiSuccess {Number} items.check_id Unique ID of the health check record
 * @apiSuccess {Date} items.timestamp When the check was performed
 * @apiSuccess {String} items.test_type Type of test that was performed
 * @apiSuccess {String} items.status Status of the test: "success", "partial", or "failure"
 * @apiSuccess {Object} items.details Detailed test results (structure varies by test type)
 * @apiSuccess {String} items.details.app_id Platform ID or channel ID that generated the result
 * @apiSuccess {Number} items.execution_time Test execution time in milliseconds
 * @apiSuccess {String} [items.error_message] Error message if the test failed
 *
 * @apiExample Example Request:
 *     GET /api/open/system-health?api_token=YOUR_API_TOKEN&type=api_connectivity&page=1&pageSize=10
 *
 * @apiSuccessExample {json} Success Response:
 * HTTP/1.1 200 OK
 * {
 *   "items": [
 *     {
 *       "check_id": 123,
 *       "timestamp": "2023-11-15T08:23:45.000Z",
 *       "test_type": "api_connectivity",
 *       "status": "success",
 *       "details": {
 *         "app_id": "platform_main",
 *         "api_results": {
 *           "tushare": { "status": "success", "responseTime": 235 },
 *           "mairui": { "status": "success", "responseTime": 54 },
 *           "ink": { "status": "success", "responseTime": 12 },
 *           "gtimg": { "status": "success", "responseTime": 189 },
 *           "dnsFailover": { "status": "success", "responseTime": 523 },
 *           "stockData": { "status": "success", "responseTime": 43 }
 *         },
 *         "successful_apis": 6,
 *         "total_apis": 6,
 *         "failover_system_status": "success"
 *       },
 *       "execution_time": 1248
 *     }
 *   ],
 *   "total": 1
 * }
 *
 * @apiErrorExample {json} Error Response - Invalid Token:
 * HTTP/1.1 401 Unauthorized
 * {
 *   "code": "INVALID_TOKEN",
 *   "message": "Invalid API token"
 * }
 */
router.get(
	"/system-health",
	wrapRoute<
		ApiQueryParams & {
			type?: string;
			page?: string;
			pageSize?: string;
			run?: string;
		}
	>(async (req, res) => {
		const { api_token, type, page = "1", pageSize = "20", run } = req.query;

		if (!api_token || typeof api_token !== "string") {
			throw AppError.create("INVALID_TOKEN", "API token required");
		}

		const isValid = await adminService.verifyApiToken(api_token);
		if (!isValid) {
			throw AppError.create("INVALID_TOKEN", "Invalid API token");
		}

		// 如果请求要运行健康检查
		if (run === "true") {
			await SystemHealthService.runAllHealthChecks();
		}

		// 使用服务层函数获取健康检查数据（包括通道数据）
		const result = await SystemHealthService.getAllHealthChecksByApi(
			typeof type === "string" ? type : undefined,
			Number.parseInt(typeof page === "string" ? page : "1", 10),
			Number.parseInt(typeof pageSize === "string" ? pageSize : "20", 10),
		);

		res.json(result);
	}),
);

export default router;
