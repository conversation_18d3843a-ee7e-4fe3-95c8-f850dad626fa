import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";
import type { MarketData } from "@packages/shared";
import axiosWithDNSFailover from "./dnsFailoverAxios.js";
import * as backupDataSource from "./mairuiApi.js";
import { fetchSuspensionFromAPI } from "./tushareApi.js";

// 价格缓存相关配置
const priceCache = new Map<string, { price: number; timestamp: number }>();
const PRICE_CACHE_TTL = 2000; // 2秒缓存时间

/**
 * 清理过期的价格缓存条目
 */
function cleanExpiredPriceCache(): void {
	const now = Date.now();
	for (const [key, value] of priceCache.entries()) {
		if (now - value.timestamp >= PRICE_CACHE_TTL) {
			priceCache.delete(key);
		}
	}
}

// 定期清理过期缓存，避免内存泄漏
setInterval(cleanExpiredPriceCache, 30000); // 每30秒清理一次

/**
 * 包装qt.gtimg.cn API请求，处理请求失败逻辑
 * @param apiCall 原始API调用函数
 * @param backupCall 备用数据源调用函数
 * @param context 日志上下文
 * @returns API调用结果
 */
async function withTimeoutHandling<T>(
	apiCall: () => Promise<T>,
	backupCall: () => Promise<T>,
	context: string,
): Promise<T> {
	try {
		return await apiCall();
	} catch (error) {
		logger.warn(
			{
				context,
				errorMessage: error instanceof Error ? error.message : String(error),
			},
			`Failed to fetch data from qt.gtimg.cn for ${context}, trying backup`,
		);
		return backupCall();
	}
}

const BATCH_SIZE = 200; // 设置一个安全的批量大小

/**
 * Fetch current day data for multiple stocks with fallback to backup data source
 * @param ts_codes Array of stock codes
 * @returns Map of stock code to market data
 */
export async function fetchCurrentDayData(
	ts_codes: string[],
): Promise<Map<string, MarketData>> {
	return withTimeoutHandling(
		// 主API调用
		async () => {
			const queries = ts_codes
				.map((code) => code.toLowerCase().split(".").reverse().join(""))
				.join(",");

			// 9:10部分归零，9:15更新市价和昨收价，9:25高低、振幅、成交才非0
			const response = await axiosWithDNSFailover.get(
				`https://qt.gtimg.cn/q=${queries}`,
			);

			if (!response.data) {
				throw AppError.create(
					"STOCK_DATA_NOT_FOUND",
					"Current day data not found",
				);
			}

			// Split response by semicolon for multiple stocks
			const stocksData = response.data
				.trim()
				.split(";")
				.filter((item: string) => item.trim());

			// For multiple stocks, return Map of results
			const resultMap = new Map<string, MarketData>();

			stocksData.forEach((stockData: string, index: number) => {
				const data = stockData.split("~");
				if (data.length >= 44) {
					resultMap.set(ts_codes[index], {
						high: Number.parseFloat(data[33]),
						low: Number.parseFloat(data[34]),
						pre_close: Number.parseFloat(data[4]),
						swing: Number.parseFloat(data[43]),
						amount: Number.parseFloat(data[37]),
					});
				}
			});

			return resultMap;
		},
		// 备用数据源调用
		async () => {
			return await backupDataSource.getCurrentDayData(ts_codes);
		},
		`fetchCurrentDayData(${ts_codes.length} stocks)`,
	);
}

/**
 * Fetch the current stock price with fallback to backup data source.
 * 实现了2秒缓存机制，避免短时间内重复查询同一股票
 * @param ts_code Stock code
 * @returns Current stock price
 */
export async function fetchCurrentPrice(
	ts_code: string,
): Promise<{ price: number }> {
	// 检查缓存
	const cached = priceCache.get(ts_code);
	if (cached && Date.now() - cached.timestamp < PRICE_CACHE_TTL) {
		logger.debug({ ts_code }, "使用缓存的股票价格");
		return { price: cached.price };
	}

	// 缓存未命中，从API获取
	const result = await withTimeoutHandling(
		// 主API调用
		async () => {
			const s_query = `s_${ts_code.toLowerCase().split(".").reverse().join("")}`;
			const response = await axiosWithDNSFailover.get(
				`https://qt.gtimg.cn/q=${s_query}`,
			);
			const data = response.data.split("~");
			return {
				price: Number.parseFloat(data[3]),
			};
		},
		// 备用数据源调用
		async () => {
			const price = await backupDataSource.getCurrentPrice(ts_code);
			return { price };
		},
		`fetchCurrentPrice(${ts_code})`,
	);

	// 更新缓存
	priceCache.set(ts_code, {
		price: result.price,
		timestamp: Date.now(),
	});

	return result;
}

/**
 * Fetch current prices for multiple stocks with fallback to backup data source.
 * 实现了缓存机制，只查询未缓存的股票代码
 * @param ts_codes Array of stock codes
 * @returns Map of stock code to price
 */
export async function fetchCurrentPrices(
	ts_codes: string[],
): Promise<Map<string, number>> {
	const resultMap = new Map<string, number>();
	const uncachedCodes: string[] = [];
	const now = Date.now();

	// 检查缓存，收集未缓存的股票代码
	for (const ts_code of ts_codes) {
		const cached = priceCache.get(ts_code);
		if (cached && now - cached.timestamp < PRICE_CACHE_TTL) {
			resultMap.set(ts_code, cached.price);
			logger.debug({ ts_code }, "使用缓存的股票价格");
		} else {
			uncachedCodes.push(ts_code);
		}
	}

	// 如果所有股票都有缓存，直接返回
	if (uncachedCodes.length === 0) {
		return resultMap;
	}

	// 只查询未缓存的股票
	const fetchedData = await withTimeoutHandling(
		// 主API调用
		async () => {
			const fetchedMap = new Map<string, number>();

			// 将股票代码数组分批处理
			for (let i = 0; i < uncachedCodes.length; i += BATCH_SIZE) {
				const batchCodes = uncachedCodes.slice(i, i + BATCH_SIZE);
				const s_queries = batchCodes
					.map(
						(code) => `s_${code.toLowerCase().split(".").reverse().join("")}`,
					)
					.join(",");

				const response = await axiosWithDNSFailover.get(
					`https://qt.gtimg.cn/q=${s_queries}`,
				);

				// Split response by semicolon for multiple stocks
				const stocksData = response.data.split(";").filter(Boolean);

				stocksData.forEach((stockData: string, index: number) => {
					const data = stockData.split("~");
					fetchedMap.set(batchCodes[index], Number.parseFloat(data[3]));
				});
			}

			return fetchedMap;
		},
		// 备用数据源调用
		async () => {
			return await backupDataSource.getCurrentPrices(uncachedCodes);
		},
		`fetchCurrentPrices(${uncachedCodes.length} uncached stocks)`,
	);

	// 更新缓存并合并结果
	const timestamp = Date.now();
	for (const [ts_code, price] of fetchedData.entries()) {
		priceCache.set(ts_code, { price, timestamp });
		resultMap.set(ts_code, price);
	}

	return resultMap;
}

/**
 * Fetch the current turnover and volume with fallback to backup data source.
 * @param ts_code Stock code
 * @returns Current turnover and volume.
 */
export async function getTurnoverAndVolume(
	ts_code: string,
): Promise<{ volume: number; turnover: number }> {
	return withTimeoutHandling(
		// 主API调用
		async () => {
			const s_query = `s_${ts_code.toLowerCase().split(".").reverse().join("")}`;
			const response = await axiosWithDNSFailover.get(
				`https://qt.gtimg.cn/q=${s_query}`,
			);
			const data = response.data.split("~");
			return {
				volume: Number.parseFloat(data[6]),
				turnover: Number.parseFloat(data[7]),
			};
		},
		// 备用数据源调用
		async () => {
			return await backupDataSource.getTurnoverAndVolume(ts_code);
		},
		`getTurnoverAndVolume(${ts_code})`,
	);
}

/**
 * Fetch today's pre_close price to calculate adjustment factor, since tushare adj_factor api update current day data at 8:40.
 * @param ts_code Stock code
 * @returns Today's pre_close price.
 */
export async function fetchTodayPreClose(
	ts_code: string,
): Promise<{ pre_close: number }> {
	return withTimeoutHandling(
		// 主API调用
		async () => {
			const query = `${ts_code.toLowerCase().split(".").reverse().join("")}`;
			const response = await axiosWithDNSFailover.get(
				`https://qt.gtimg.cn/q=${query}`,
			);
			const data = response.data.split("~");
			return {
				pre_close: Number.parseFloat(data[4]),
			};
		},
		// 备用数据源调用
		async () => {
			const preClose = await backupDataSource.getTodayPreClose(ts_code);
			return { pre_close: preClose };
		},
		`fetchTodayPreClose(${ts_code})`,
	);
}

/**
 * 获取今日停复牌信息
 * @param ts_code 股票代码
 * @returns 是否停牌
 */
export async function fetchTodaySuspension(ts_code: string): Promise<boolean> {
	return withTimeoutHandling(
		// 主API调用
		async () => {
			const s_query = `s_${ts_code.toLowerCase().split(".").reverse().join("")}`;
			const response = await axiosWithDNSFailover.get(
				`https://qt.gtimg.cn/q=${s_query}`,
			);
			const data = response.data.split("~");
			// data[8] 为停复牌信息："S" 停牌，否则为空
			return data[8] === "S";
		},
		// 备用数据源调用
		async () => {
			const today = new Date();
			const suspensionData = await fetchSuspensionFromAPI({
				ts_code,
				trade_date: today,
			});
			// 如果有数据且类型为'S'则表示停牌
			return suspensionData.some(
				(item: { suspend_type: string }) => item.suspend_type === "S",
			);
		},
		`fetchTodaySuspension(${ts_code})`,
	);
}

/**
 * Fetch the highest price, lowest price and yesterday's closing price with fallback to backup data source.
 * @param ts_code Stock code
 * @returns Object containing the highest price, lowest price and yesterday's closing price
 */
export async function fetchPriceDetails(
	ts_code: string,
): Promise<{ high: number; low: number; pre_close: number }> {
	return withTimeoutHandling(
		// 主API调用
		async () => {
			const query = `${ts_code.toLowerCase().split(".").reverse().join("")}`;
			const response = await axiosWithDNSFailover.get(
				`https://qt.gtimg.cn/q=${query}`,
			);
			const data = response.data.split("~");
			return {
				high: Number.parseFloat(data[33]),
				low: Number.parseFloat(data[34]),
				pre_close: Number.parseFloat(data[4]),
			};
		},
		// 备用数据源调用
		async () => {
			return await backupDataSource.getPriceDetails(ts_code);
		},
		`fetchPriceDetails(${ts_code})`,
	);
}

/**
 * 腾讯接口 gtimg 获取行情明细
 *
 * 以五粮液为例，要获取最新行情，访问数据接口：
 * http://qt.gtimg.cn/q=sz000858
 *
 * 返回数据：
 * v_sz000858="51~五 粮 液~000858~27.78~27.60~27.70~417909~190109~227800~27.78~492~27.77~332~27.76~202~27.75~334~27.74~291~27.79~305~27.80~570~27.81~269~27.82~448~27.83~127~15:00:13/27.78/4365/S/12124331/24602|14:56:55/27.80/14/S/38932/24395|14:56:52/27.81/116/B/322585/24392|14:56:49/27.80/131/S/364220/24385|14:56:46/27.81/5/B/13905/24381|14:56:43/27.80/31/B/86199/24375~20121221150355~0.18~0.65~28.11~27.55~27.80/413544/1151265041~417909~116339~1.10~10.14~~28.11~27.55~2.03~1054.39~1054.52~3.64~30.36~24.84~";
 *
 * 以 ~ 分割字符串中内容，下标从0开始，依次为
 * 0: 未知
 * 1: 名字
 * 2: 代码
 * 3: 当前价格
 * 4: 昨收
 * 5: 今开
 * 6: 成交量（手）
 * 7: 外盘
 * 8: 内盘
 * 9: 买一
 * 10: 买一量（手）
 * 11-18: 买二 买五
 * 19: 卖一
 * 20: 卖一量
 * 21-28: 卖二 卖五
 * 29: 最近逐笔成交
 * 30: 时间
 * 31: 涨跌
 * 32: 涨跌%
 * 33: 最高
 * 34: 最低
 * 35: 价格/成交量（手）/成交额
 * 36: 成交量（手）
 * 37: 成交额（万）
 * 38: 换手率
 * 39: 市盈率
 * 40:
 * 41: 最高
 * 42: 最低
 * 43: 振幅
 * 44: 流通市值
 * 45: 总市值
 * 46: 市净率
 * 47: 涨停价
 * 48: 跌停价
 *
 * 获取简要信息：
 * http://qt.gtimg.cn/q=s_sz000858
 *
 * 返回数据：
 * v_s_sz000858="51~五 粮 液~000858~27.78~0.18~0.65~417909~116339~~1054.52";
 *
 * 以 ~ 分割字符串中内容，下标从0开始，依次为：
 * 0: 未知
 * 1: 名字
 * 2: 代码
 * 3: 当前价格
 * 4: 涨跌
 * 5: 涨跌%
 * 6: 成交量（手）
 * 7: 成交额（万）
 * 8: 停复牌
 * 9: 总市值
 */
