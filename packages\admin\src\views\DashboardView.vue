<template>
  <div class="dashboard-view view">
    <h2>仪表盘</h2>
    <el-row :gutter="20">
      <el-col :xs="12" :sm="12" :md="6" :lg="6" :xl="6" v-for="stat in stats" :key="stat.key">
        <el-card class="stat-card" v-loading="loading">
          <h3>
            <el-icon>
              <component :is="stat.icon" />
            </el-icon>
            {{ stat.label }}
          </h3>
          <div class="value">
            {{ stat.formatter ? stat.formatter(stat.value) : stat.value }}
          </div>
          <div :class="['trend', stat.trend > 0 ? 'positive' : stat.trend < 0 ? 'negative' : '']">
            <el-icon v-if="stat.trend !== 0">
              <CaretTop v-if="stat.trend > 0" />
              <CaretBottom v-else />
            </el-icon>
            {{ stat.trend === 0 ? '持平' : `${Math.abs(stat.trend).toFixed(1)}%` }}
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 业务数据卡片 - 响应式布局 -->
    <el-row :gutter="20" class="mt-4">
      <!-- 左列 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <!-- 用户活动卡片 -->
        <el-card class="mb-4">
          <template #header>
            <div class="card-header">
              <span>用户活动</span>
            </div>
          </template>
          <div class="user-stats">
            <div class="stat-item">
              <span class="label">当前在线:</span>
              <span class="value">{{ dashboardStats.online_users }}</span>
            </div>
            <div class="stat-item">
              <span class="label">日活跃:</span>
              <span class="value">{{ dashboardStats.daily_active_users }}</span>
            </div>
            <div class="stat-item">
              <span class="label">周活跃:</span>
              <span class="value">{{ dashboardStats.weekly_active_users }}</span>
            </div>
            <div class="stat-item">
              <span class="label">月活跃:</span>
              <span class="value">{{ dashboardStats.monthly_active_users }}</span>
            </div>
          </div>
        </el-card>

        <!-- 交易概况卡片 -->
        <el-card>
          <template #header>
            <div class="card-header">
              <span>交易概况</span>
            </div>
          </template>
          <div class="stat-group">
            <div class="stat-item">
              <span class="label">日询价数:</span>
              <span class="value">{{ dashboardStats.total_inquiries }}</span>
            </div>
            <div class="stat-item">
              <span class="label">询价转化率:</span>
              <span class="value">{{ formatPercentage(dashboardStats.inquiry_conversion_rate) }}</span>
            </div>
            <div class="stat-item">
              <span class="label">平均订单价值:</span>
              <span class="value">¥{{ formatNumber(dashboardStats.average_order_value) }}</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右列 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <!-- 用户转化卡片 -->
        <el-card class="mb-4">
          <template #header>
            <div class="card-header">
              <span>用户转化</span>
            </div>
          </template>
          <div class="stat-group">
            <div class="stat-item">
              <span class="label">总用户数:</span>
              <span class="value">{{ dashboardStats.total_users }}</span>
            </div>
            <div class="stat-item">
              <span class="label">已验证用户:</span>
              <span class="value">{{ dashboardStats.verified_users }}</span>
            </div>
            <div class="stat-item">
              <span class="label">验证率:</span>
              <span class="value">{{ formatPercentage(dashboardStats.verified_rate) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 财务概况卡片 -->
        <el-card>
          <template #header>
            <div class="card-header">
              <span>财务概况</span>
            </div>
          </template>
          <div class="stat-group">
            <div class="stat-item">
              <span class="label">入金总额:</span>
              <span class="value">¥{{ formatNumber(dashboardStats.deposit_total) }}</span>
            </div>
            <div class="stat-item">
              <span class="label">出金总额:</span>
              <span class="value">¥{{ formatNumber(dashboardStats.withdrawal_total) }}</span>
            </div>
            <div class="stat-item">
              <span class="label">利润率:</span>
              <span class="value">{{ formatPercentage(dashboardStats.profit_margin) }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, type Component, markRaw } from "vue";
import {
  UserFilled,
  ShoppingCart,
  TrendCharts,
  DataLine,
  CaretTop,
  CaretBottom,
} from "@element-plus/icons-vue";
import { basicApi } from "@/api";
import type { DashboardStats } from "@packages/shared";

// 添加dashboardStats引用 - 确保在使用前定义
const dashboardStats = ref<DashboardStats>({} as DashboardStats);

interface StatConfig {
  label: string;
  key: keyof DashboardStats;
  icon: Component;
  value: number;
  trend: number;
  formatter?: (val: number) => string;
}

// 统计数据配置
const stats = ref<StatConfig[]>([
  {
    label: "今日活跃用户",
    key: "active_users",
    icon: markRaw(UserFilled),
    value: 0,
    trend: 0,
  },
  {
    label: "今日订单数",
    key: "todays_orders",
    icon: markRaw(ShoppingCart),
    value: 0,
    trend: 0,
  },
  {
    label: "今日交易额",
    key: "trading_volume",
    icon: markRaw(TrendCharts),
    value: 0,
    trend: 0,
    formatter: (val: number) => `¥${val.toLocaleString()}`,
  },
  {
    label: "今日结算额",
    key: "settle_volume",
    icon: markRaw(DataLine),
    value: 0,
    trend: 0,
    formatter: (val: number) => `¥${val.toLocaleString()}`,
  },
]);

const loading = ref(false);
const STATS_REFRESH_INTERVAL = 60000; // 1分钟刷新一次

// 获取统计数据
const fetchStats = async () => {
  try {
    loading.value = true;
    const data = await basicApi.getDashboardStats();
    if (data) {
      // 存储完整的仪表盘统计数据
      dashboardStats.value = data;

      // 更新显示的卡片
      stats.value = stats.value.map((stat) => ({
        ...stat,
        value: data ? data[stat.key] : 0,
        trend: data ? data[`${stat.key}_trend` as keyof DashboardStats] : 0,
      }));
    }
  } catch (error) {
    console.error("Failed to fetch dashboard stats:", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  // 获取统计数据
  fetchStats();
  const statsTimer = setInterval(fetchStats, STATS_REFRESH_INTERVAL);

  onUnmounted(() => {
    clearInterval(statsTimer);
  });
});

// 格式化为百分比字符串（用于显示）
const formatPercentage = (value: number | undefined | null): string => {
  if (value === undefined || value === null) {
    return "0.0%"; // 返回默认值
  }
  return `${value.toFixed(1)}%`;
};

// 添加新的格式化函数
const formatNumber = (value: number | undefined | null): string => {
  if (value === undefined || value === null) {
    return "0"; // 返回默认值
  }
  return value.toLocaleString();
};
</script>

<style scoped>
.dashboard-view {
  h2 {
    margin: 0;
    padding-bottom: 10px;
  }

  .stat-card {
    background: var(--el-bg-color-overlay);
    color: var(--el-text-color-primary);
    transition: transform 0.2s;
    margin-bottom: 20px;

    &:hover {
      transform: translateY(-2px);
    }

    h3 {
      margin: 0;
      font-size: 14px;
      color: var(--el-text-color-secondary);
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon {
        opacity: 0.5;
      }
    }

    .value {
      font-size: 24px;
      margin: 8px 0;
    }

    .trend {
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;

      &.positive {
        color: var(--el-color-success);
      }

      &.negative {
        color: var(--el-color-danger);
      }
    }
  }

  .stat-item {
    margin-bottom: 8px;

    .label {
      color: var(--el-text-color-secondary);
      margin-right: 8px;
    }

    .value {
      color: var(--el-text-color-primary);
    }
  }

  /* 确保加载动画不会显示在移动端侧边栏上方 */
  :deep(.el-loading-mask) {
    z-index: 900 !important;
  }
}

.stat-group {
  margin-bottom: 16px;

  h4 {
    margin: 0 0 8px;
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }
}

.stat-item {
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;

  .label {
    color: var(--el-text-color-secondary);
  }

  .value {
    font-family: monospace;
    color: var(--el-text-color-primary);
  }
}

:deep(.el-card) {
  background: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-lighter);
  margin-bottom: 16px;

  .el-card__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
}

.mt-4 {
  margin-top: 16px;
}

.el-row {
  margin-bottom: -20px;
}

/* 移动端适配样式 */
@media (max-width: 767px) {
  .dashboard h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .stat-card {
    margin-bottom: 15px;
  }

  .mb-4 {
    margin-bottom: 15px !important;
  }

  .mt-4 {
    margin-top: 15px !important;
  }

  .el-card {
    margin-bottom: 15px;
  }

  .card-header {
    font-size: 1rem;
  }

  .stat-group h4 {
    font-size: 0.9rem;
  }

  .stat-item {
    margin-bottom: 6px;
  }

  .stat-item .label {
    font-size: 0.8rem;
  }

  .stat-item .value {
    font-size: 0.9rem;
  }
}
</style>
