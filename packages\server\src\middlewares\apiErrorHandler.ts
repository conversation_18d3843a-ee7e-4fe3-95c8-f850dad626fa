import type { Request, Response, NextFunction } from "express";
import type { ErrorResponse } from "@packages/shared";
import { ErrorType } from "@packages/shared";
import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";

/**
 * Error handling middleware: handle errors in the application and send appropriate responses.
 * @param err - Error object
 * @param req - Express request object
 * @param res - Express response object
 * @param next - Express next function
 */
export const apiErrorHandler = (
	err: Error | AppError,
	req: Request,
	res: Response,
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	_next: NextFunction,
): void => {
	if (err instanceof AppError) {
		// 401 错误不记录error日志
		// ! 在 JavaScript 中，当你将对象的方法直接赋值给变量时，这个方法会失去它原来的 this 上下文。
		// 这导致所有预期的 401 认证错误变成了未捕获的 500 服务器错误，破坏了前端的令牌刷新机制。
		const logMethod =
			err.statusCode === 401
				? (obj: Record<string, unknown>, msg: string) => logger.warn(obj, msg)
				: (obj: Record<string, unknown>, msg: string) => logger.error(obj, msg);

		logMethod(
			{
				statusCode: err.statusCode,
				code: err.code,
				path: req.path,
				method: req.method,
				...(err.data && { data: err.data }),
			},
			`${err.name}: ${err.message}`,
		);

		const response: ErrorResponse = {
			code: err.code,
			message: err.message,
			...(err.data && { data: err.data }),
		};

		res.status(err.statusCode).json(response);
	} else {
		logger.error(
			{
				stack: err.stack,
				path: req.path,
				method: req.method,
			},
			`Unhandled error: ${err.message}`,
		);

		const response: ErrorResponse = {
			code: ErrorType.SERVER_ERROR.code,
			message: "Internal Server Error",
		};

		res.status(500).json(response);
	}
};
