<template>
  <div class="table-wrapper" ref="wrapperRef">
    <slot></slot>

    <div class="table-footer">
      <div class="settings-wrapper">
        <button class="settings-button" @click="showSettings = !showSettings">
          <el-icon>
            <Setting />
          </el-icon>
        </button>
        <div class="settings-popup" v-if="showSettings">
          <div class="settings-item">
            <button class="sort-button" @click="toggleSort">
              {{ isDescending ? "最新" : "最早" }}
            </button>
          </div>
          <div class="settings-item">
            <el-select v-model="pageSizeValue" class="page-size-select" popper-class="table-wrapper-popper">
              <el-option v-for="size in [10, 20, 50]" :key="size" :value="size" :label="`${size} 条 / 页`" />
            </el-select>
          </div>
        </div>
      </div>

      <div class="pagination">
        <button class="page-button" :disabled="currentPage === 1" @click="handlePage(currentPage - 1)">上一页</button>

        <div class="page-jump">
          <div class="page-input-wrapper">
            <input type="number" :value="currentPage" @keyup.enter="handlePageJump" @blur="handlePageJump"
              @focus="handleFocus" min="1" :max="totalPages" />
            <span class="page-total">&nbsp;/{{ Math.max(1, totalPages) }}</span>
          </div>
        </div>

        <button class="page-button" :disabled="currentPage === totalPages"
          @click="handlePage(currentPage + 1)">下一页</button>
      </div>

      <div class="settings-wrapper"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { Setting } from "@element-plus/icons-vue";

const props = defineProps<{
  pageSize: number;
  currentPage: number;
  totalPages: number;
  isDescending: boolean;
}>();

const emit = defineEmits<{
  (e: "update:pageSize", size: number): void;
  (e: "update:currentPage", page: number): void;
  (e: "update:isDescending", desc: boolean): void;
}>();

const showSettings = ref(false);

const wrapperRef = ref<HTMLElement | null>(null);

// 使用计算属性来同步 pageSize
const pageSizeValue = computed({
  get: () => props.pageSize,
  set: (value) => emit("update:pageSize", value),
});

// 修改点击外部关闭功能
const handleClickOutside = (event: MouseEvent) => {
  if (!wrapperRef.value) return;

  const settingsWrapper = wrapperRef.value.querySelector(".settings-wrapper");
  if (
    showSettings.value &&
    settingsWrapper &&
    !settingsWrapper.contains(event.target as Node)
  ) {
    showSettings.value = false;
  }
};

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});

// 新增页面跳转处理
const handlePageJump = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const page = Number(input.value);

  if (page && page >= 1) {
    if (page > Math.max(1, props.totalPages)) {
      // 如果输入大于总页数，跳转到后一页
      emit("update:currentPage", props.totalPages);
      input.value = String(props.totalPages);
    } else {
      emit("update:currentPage", page);
    }
  } else {
    // 如果输入无效，重置为当前页
    input.value = String(props.currentPage);
  }
};

const handlePage = (page: number) => {
  if (page >= 1 && page <= props.totalPages) {
    emit("update:currentPage", page);
  }
};

const toggleSort = () => {
  emit("update:isDescending", !props.isDescending);
};

// 添加焦点处理，自动全选
const handleFocus = (event: Event) => {
  const input = event.target as HTMLInputElement;
  input.select();
};
</script>

<style>
.table-wrapper-popper .el-select-dropdown__item {
  text-align: center;
}
</style>

<style scoped>
.table-wrapper {
  width: 100%;
}

.table-footer {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  margin-top: 20px;
  position: relative;
  gap: 16px;
}

.pagination {
  justify-self: center;
  display: flex;
  align-items: center;
  gap: 20px;
}

.settings-wrapper {
  position: relative;
  width: 28px;
}

.settings-button {
  width: 28px;
  height: 28px;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  background-color: var(--el-fill-color-light);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.25s ease;
}

.settings-button:hover {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
}

.settings-popup {
  position: absolute;
  bottom: 100%;
  left: 0;
  margin-bottom: 8px;
  background-color: var(--el-fill-color-light);
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  padding: 8px;
  z-index: 1000;
  min-width: 120px;
}

.settings-item {
  margin-bottom: 8px;
}

.settings-item:last-child {
  margin-bottom: 0;
}

.settings-item .sort-button,
.settings-item select {
  width: 100%;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-button {
  min-width: 80px;
  padding: 4px 12px;
  height: 28px;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  color: var(--el-text-color-regular);
  background-color: var(--el-fill-color-light);
  cursor: pointer;
  font-size: 16px;
  line-height: 16px;
  transition: all 0.25s ease;
}

.sort-button:hover {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
}

.page-jump {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.page-input-wrapper {
  position: relative;
  width: 80px;
}

.page-input-wrapper input {
  width: 100%;
  height: 28px;
  text-align: center;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  padding: 0 30px 0 10px;
  font-size: 16px;
  color: var(--el-text-color-primary);
  background-color: var(--el-fill-color-light);
  box-sizing: border-box;

  &:focus,
  &:hover {
    border-color: var(--el-color-primary);
    outline: none;
  }
}

.page-total {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  font-size: 16px;
  pointer-events: none;
  color: var(--el-text-color-secondary);
}

/* 去除输入框的上下箭头 */
.page-input-wrapper input::-webkit-outer-spin-button,
.page-input-wrapper input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.page-input-wrapper input[type='number'] {
  appearance: textfield;
}

.page-button {
  min-width: 80px;
  padding: 4px 12px;
  height: 28px;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  background-color: var(--el-fill-color-light);
  cursor: pointer;
  font-size: 14px;
  color: var(--el-text-color-primary);
  transition: all 0.25s ease;
}

.page-button:hover {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-size-select {
  width: 100%;
}

:deep(.el-select__placeholder) {
  text-align: center;
}

:deep(.el-select__wrapper) {
  border-radius: var(--el-border-radius-base);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .pagination {
    gap: 12px;
  }

  .page-button {
    padding: 4px 8px;
    min-width: 64px;
  }

  .page-input-wrapper {
    width: 64px;

    input {
      padding: 0 26px 0 6px;
    }
  }

  .page-total {
    right: 11px;
  }
}
</style>
