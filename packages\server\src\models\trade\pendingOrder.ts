import prisma from "@/lib/prisma.js";
import type { Prisma, order_status } from "@prisma/client";
import { withTransaction } from "@/core/dbTxnManager.js";
import type {
	PendingOrderData,
	SellingOrderData,
	StructureType,
} from "@packages/shared";
import { OrderStatus } from "@packages/shared";
import { AppError } from "@/core/appError.js";
import logger from "@/utils/logger.js";

interface PrismaPendingOrder {
	pending_id: number;
	trade_no: string | null;
	user_id: number;
	ts_code: string;
	entry_price: Prisma.Decimal;
	exercise_price: Prisma.Decimal;
	settle_price: Prisma.Decimal;
	scale: number;
	total_scale: number;
	term: number;
	quote: Prisma.Decimal;
	status: order_status;
	created_at: Date | null;
	closed_at: Date | null;
	is_split: boolean;
	limit_price: Prisma.Decimal | null;
	structure: string | null;
	expiry_date: Date | null;
	expiry_date_confirmed: boolean | null;
	quote_provider: string | null;
	quote_diff: Prisma.Decimal | null;
}

function transformPendingOrder(po: PrismaPendingOrder): PendingOrderData {
	return {
		...po,
		limit_price: po.limit_price ? Number(po.limit_price) : undefined,
		trade_no: po.trade_no ?? "",
		entry_price: Number(po.entry_price),
		structure: po.structure as StructureType,
		exercise_price: Number(po.exercise_price),
		settle_price: Number(po.settle_price),
		expiry_date: po.expiry_date?.toISOString() ?? "",
		expiry_date_confirmed: po.expiry_date_confirmed ?? false,
		quote: Number(po.quote),
		status: po.status as OrderStatus,
		created_at: po.created_at?.toISOString() ?? "",
		closed_at: po.closed_at?.toISOString() ?? undefined,
		quote_provider: po.quote_provider ?? "", // 用无效值替代融合类型复杂的空值判断
		quote_diff: po.quote_diff ? Number(po.quote_diff) : Number.NaN,
	};
}

export async function create(
	data: Omit<PendingOrderData, "pending_id" | "created_at"> & {
		created_at?: string;
	},
	client?: Prisma.TransactionClient,
): Promise<PendingOrderData> {
	const createFn = async (tx: Prisma.TransactionClient) => {
		const isBuyingOrder = [
			OrderStatus.LIMIT_BUYING,
			OrderStatus.VWAP_BUYING,
		].includes(data.status);

		if (
			data.limit_price === undefined &&
			(data.status === OrderStatus.LIMIT_BUYING ||
				data.status === OrderStatus.LIMIT_SELLING)
		) {
			logger.info("data.limit_price is undefined");
			throw AppError.create(
				"INVALID_LIMIT_PRICE",
				"Limit price is required to Limit Order",
			);
		}

		const pendingOrder = await tx.pending_orders.create({
			data: {
				...data,
				...(data.limit_price !== undefined && {
					limit_price: data.limit_price,
				}),
				...(!isBuyingOrder && {
					trade_no: (data as SellingOrderData).trade_no,
					expiry_date: (data as SellingOrderData).expiry_date,
					expiry_date_confirmed: (data as SellingOrderData)
						.expiry_date_confirmed,
				}),
			},
		});

		return transformPendingOrder(pendingOrder);
	};

	return client ? createFn(client) : withTransaction(createFn);
}

export async function update(
	pending_id: number,
	updates: Partial<PendingOrderData>,
	client?: Prisma.TransactionClient,
): Promise<PendingOrderData> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		const pendingOrder = await tx.pending_orders.update({
			where: { pending_id },
			data: updates,
		});

		return transformPendingOrder(pendingOrder);
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function deletePendingOrder(
	pending_id: number,
	client?: Prisma.TransactionClient,
): Promise<void> {
	const deleteFn = async (tx: Prisma.TransactionClient) => {
		await tx.pending_orders.delete({
			where: { pending_id },
		});
	};

	return client ? deleteFn(client) : withTransaction(deleteFn);
}

export async function close(
	pending_id: number,
	client?: Prisma.TransactionClient,
): Promise<PendingOrderData> {
	const closeFn = async (tx: Prisma.TransactionClient) => {
		const pendingOrder = await tx.pending_orders.update({
			where: { pending_id },
			data: { closed_at: new Date().toISOString() },
		});

		return transformPendingOrder(pendingOrder);
	};

	return client ? closeFn(client) : withTransaction(closeFn);
}

export async function findAllPending(): Promise<PendingOrderData[]> {
	const pendingOrders = await prisma.pending_orders.findMany({
		where: {
			closed_at: null,
			status: {
				in: [
					OrderStatus.VWAP_BUYING,
					OrderStatus.VWAP_SELLING,
					OrderStatus.LIMIT_BUYING,
					OrderStatus.LIMIT_SELLING,
				],
			},
		},
		orderBy: { created_at: "asc" },
	});

	return pendingOrders.map(transformPendingOrder);
}

export async function findAllLimitOrders(): Promise<
	(PendingOrderData & { limit_price: number })[]
> {
	const pendingOrders = await prisma.pending_orders.findMany({
		where: {
			closed_at: null,
			status: {
				in: [OrderStatus.LIMIT_BUYING, OrderStatus.LIMIT_SELLING],
			},
		},
		orderBy: { created_at: "asc" },
	});

	return pendingOrders.map((po) => {
		const { limit_price, ...rest } = transformPendingOrder(po);
		return { ...rest, limit_price: Number(limit_price) };
	});
}

export async function findAllVwapOrders(): Promise<PendingOrderData[]> {
	const pendingOrders = await prisma.pending_orders.findMany({
		where: {
			closed_at: null,
			status: {
				in: [OrderStatus.VWAP_BUYING, OrderStatus.VWAP_SELLING],
			},
		},
		orderBy: { created_at: "asc" },
	});

	return pendingOrders.map(transformPendingOrder);
}

export async function getBuyingList(
	user_id: number,
): Promise<PendingOrderData[]> {
	logger.info(`getBuyingList: user_id=${user_id}`);
	const pendingOrders = await prisma.pending_orders.findMany({
		where: {
			user_id,
			status: {
				in: [OrderStatus.VWAP_BUYING, OrderStatus.LIMIT_BUYING],
			},
		},
	});

	return pendingOrders.map(transformPendingOrder);
}

export async function getSellingList(
	user_id: number,
): Promise<PendingOrderData[]> {
	const pendingOrders = await prisma.pending_orders.findMany({
		where: {
			user_id,
			status: {
				in: [OrderStatus.VWAP_SELLING, OrderStatus.LIMIT_SELLING],
			},
		},
	});

	return pendingOrders.map(transformPendingOrder);
}

/**
 * 获取挂单，支持按类型筛选和分页
 */
export async function getPendingListForAdmin(options: {
	page?: number;
	pageSize?: number;
	statusTypes?: OrderStatus[];
	ts_code?: string;
	user_id?: number;
}): Promise<{ items: PendingOrderData[]; total: number }> {
	const { page = 1, pageSize = 10, statusTypes, ts_code, user_id } = options;

	const where: Prisma.pending_ordersWhereInput = {
		user_id,
		ts_code,
		status: statusTypes ? { in: statusTypes } : undefined,
	};

	// 分页查询
	const [pendingOrders, total] = await Promise.all([
		prisma.pending_orders.findMany({
			where,
			skip: (page - 1) * pageSize,
			take: pageSize,
			orderBy: { created_at: "desc" },
		}),
		prisma.pending_orders.count({ where }),
	]);

	return {
		items: pendingOrders.map(transformPendingOrder),
		total,
	};
}

export async function findById(
	pending_id: number,
): Promise<PendingOrderData | null> {
	const pendingOrder = await prisma.pending_orders.findUnique({
		where: { pending_id },
	});

	return pendingOrder ? transformPendingOrder(pendingOrder) : null;
}

export async function findByTradeNo(
	trade_no: string,
): Promise<PendingOrderData[]> {
	const pendingOrders = await prisma.pending_orders.findMany({
		where: { trade_no },
	});

	return pendingOrders.map(transformPendingOrder);
}

export async function findPendingByTradeNo(
	trade_no: string,
): Promise<PendingOrderData[]> {
	const pendingOrders = await prisma.pending_orders.findMany({
		where: {
			trade_no,
			closed_at: null,
			status: {
				in: [OrderStatus.VWAP_SELLING, OrderStatus.LIMIT_SELLING],
			},
		},
	});

	return pendingOrders.map(transformPendingOrder);
}
