import rateLimit from "express-rate-limit";
import { ENV } from "@/config/configManager.js";
import { AppError } from "@/core/appError.js";

// 通用限制
export const defaultLimiter = rateLimit({
	windowMs: 1 * 60 * 1000, // 1 分钟
	max: ENV.NODE_ENV === "development" ? 1000 : 150, // 每个 IP 每分钟最多 150 个请求
	standardHeaders: true, // 使用标准头
	legacyHeaders: false, // 不使用旧版头
	keyGenerator: (req) => {
		// 获取真实IP，考虑代理情况
		return (
			req.ip ||
			(req.headers["x-forwarded-for"] as string) ||
			req.socket.remoteAddress ||
			"unknown"
		);
	},
	handler: () => {
		throw AppError.create("REQUEST_RATE_LIMIT", "");
	},
});

// 登录接口限制
export const loginLimiter = rateLimit({
	windowMs: 15 * 60 * 1000,
	max: 5,
	standardHeaders: true,
	legacyHeaders: false,
	handler: () => {
		throw AppError.create("LOGIN_REQUEST_RATE_LIMIT", "");
	},
});

export default defaultLimiter;
