import { Router } from "express";
import * as ChannelFundService from "@/services/channelFundService.js";
import * as ChannelOrderService from "@/services/channelOrderService.js";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import { AppError } from "@/core/appError.js";
import { isTradingPlatform } from "@/config/configManager.js";
import {
	getAllConfiguredChannels,
	findChannelNameFromConfig,
} from "@/config/defaultParams.js";
import { isChannelDbConfigured } from "@/lib/channelDatabaseManager.js";
import { ChannelTransactionStatus } from "@packages/shared";
import type { OrderStatus } from "@packages/shared";

const router = Router();

/**
 * 验证交易台环境
 */
function ValidateTradingPlatform() {
	if (!isTradingPlatform()) {
		throw AppError.create("FORBIDDEN", "只有交易台环境才能访问该资源");
	}
}

// 简化版通道列表 GET: /api/admin/channels/all
router.get(
	"/all",
	wrapAdminRoute(async (_req, res) => {
		ValidateTradingPlatform();

		const channels = await ChannelFundService.getAllChannels();
		res.json(channels);
	}),
);

// 获取待审核交易 GET: /api/admin/channel/transactions/pending
router.get(
	"/transactions/pending",
	wrapAdminRoute(async (req, res) => {
		ValidateTradingPlatform();

		const page = req.query.page
			? Number.parseInt(req.query.page as string, 10)
			: 1;
		const size = req.query.size
			? Number.parseInt(req.query.size as string, 10)
			: 10;
		const offset = (page - 1) * size;

		const result = await ChannelFundService.getPendingTransactions(
			size,
			offset,
		);
		res.json({
			items: result.transactions,
			total: result.total,
		});
	}),
);

// 获取所有交易 GET: /api/admin/channel/transactions
router.get(
	"/transactions",
	wrapAdminRoute(async (req, res) => {
		ValidateTradingPlatform();

		const page = req.query.page
			? Number.parseInt(req.query.page as string, 10)
			: 1;
		const size = req.query.size
			? Number.parseInt(req.query.size as string, 10)
			: 10;
		const offset = (page - 1) * size;
		const channelId = req.query.channelId
			? (req.query.channelId as string)
			: undefined;
		const status = req.query.status
			? (req.query.status as ChannelTransactionStatus)
			: undefined;

		// 传递状态参数进行过滤
		const result = await ChannelFundService.getChannelTransactions(
			channelId,
			size,
			offset,
			status,
		);

		res.json({
			items: result.transactions,
			total: result.total,
		});
	}),
);

// 批准出入金审核 POST: /api/admin/channel/transactions/:id/approve
router.post(
	"/transactions/:id/approve",
	wrapAdminRoute<{ review_comment?: string }>(async (req, res) => {
		ValidateTradingPlatform();

		const { review_comment } = req.body;

		const result = await ChannelFundService.updateTransactionStatus({
			transaction_id: Number(req.params.id),
			status: ChannelTransactionStatus.CONFIRMED,
			review_comment,
			admin_id: req.jwt.admin_id,
		});
		res.json(result);
	}),
);

// 拒绝出入金审核 POST: /api/admin/channel/transactions/:id/reject
router.post(
	"/transactions/:id/reject",
	wrapAdminRoute<{ review_comment?: string }>(async (req, res) => {
		ValidateTradingPlatform();

		const { review_comment } = req.body;

		const result = await ChannelFundService.updateTransactionStatus({
			transaction_id: Number(req.params.id),
			status: ChannelTransactionStatus.REJECTED,
			review_comment,
			admin_id: req.jwt.admin_id,
		});
		res.json(result);
	}),
);

// 原有的通用review路由，保留以供后续修改参考
router.post(
	"/transactions/:id/review",
	wrapAdminRoute<{
		status?: ChannelTransactionStatus;
		review_comment?: string;
	}>(async (req, res) => {
		ValidateTradingPlatform();

		const { status, review_comment } = req.body;

		const result = await ChannelFundService.updateTransactionStatus({
			transaction_id: Number(req.params.id),
			status: status as ChannelTransactionStatus,
			review_comment: review_comment,
			admin_id: req.jwt.admin_id,
		});
		res.json(result);
	}),
);

// 获取通道订单 GET: /api/admin/channel/:id/orders
router.get(
	"/:channelId/orders",
	wrapAdminRoute(async (req, res) => {
		ValidateTradingPlatform();

		const { channelId } = req.params;

		if (!channelId) {
			res.status(400).json({
				success: false,
				message: "通道ID不能为空",
			});
			return;
		}

		try {
			const page = Number.parseInt(req.query.page as string) || 1;
			const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
			const sortBy = (req.query.sortBy as string) || "created_at";
			const sortOrder =
				(req.query.sortOrder as string)?.toUpperCase() === "ASC"
					? "ASC"
					: "DESC";

			// 解析筛选条件
			const { ts_codes, startDate, endDate, status } = req.query;

			const result = await ChannelOrderService.getChannelOrders(channelId, {
				page,
				pageSize,
				sortBy,
				sortOrder,
				filters: {
					ts_codes: ts_codes ? (ts_codes as string).split(",") : undefined,
					startDate: startDate as string,
					endDate: endDate as string,
					status: status as OrderStatus,
				},
			});

			res.json(result);
		} catch (error) {
			console.error(`获取通道 ${channelId} 订单失败:`, error);

			// 如果错误包含错误代码和状态码
			if (error instanceof AppError) {
				res.status(error.statusCode).json({
					success: false,
					message: error.message,
					code: error.code,
				});
				return;
			}

			// 处理其他未知错误
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			res.status(500).json({
				success: false,
				message: errorMessage || "获取通道订单失败",
			});
		}
	}),
);

// 获取通道订单汇总信息 GET: /api/admin/channel/:id/orders/summary
router.get(
	"/:channelId/orders/summary",
	wrapAdminRoute(async (req, res) => {
		ValidateTradingPlatform();

		const { channelId } = req.params;

		if (!channelId) {
			res.status(400).json({
				success: false,
				message: "通道ID不能为空",
			});
			return;
		}

		try {
			const result =
				await ChannelOrderService.getChannelOrdersSummary(channelId);
			res.json(result);
		} catch (error) {
			console.error(`获取通道 ${channelId} 订单汇总失败:`, error);

			// 如果错误包含错误代码和状态码
			if (error instanceof AppError) {
				res.status(error.statusCode).json({
					success: false,
					message: error.message,
					code: error.code,
				});
				return;
			}

			// 处理其他未知错误
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			res.status(500).json({
				success: false,
				message: errorMessage || "获取通道订单汇总失败",
			});
		}
	}),
);

// 获取通道数据库配置状态: GET: /api/admin/channel/status
router.get(
	"/status",
	wrapAdminRoute(async (_req, res) => {
		ValidateTradingPlatform();

		// 获取所有配置的通道
		const channelIds = await getAllConfiguredChannels();

		// 检查每个通道的数据库连接状态
		const results = await Promise.all(
			channelIds.map(async (channelId) => {
				const isConfigured = isChannelDbConfigured(channelId);
				let connectionStatus = "未配置";

				if (isConfigured) {
					try {
						// 尝试获取数据库连接
						const { getChannelDbConnection } = await import(
							"@/lib/channelDatabaseManager.js"
						);
						await getChannelDbConnection(channelId);
						connectionStatus = "连接成功";
					} catch (error) {
						connectionStatus = `连接失败: ${error instanceof Error ? error.message : String(error)}`;
					}
				}

				// 从配置中获取通道名称
				const channelName =
					findChannelNameFromConfig(channelId) || `Channel ${channelId}`;

				return {
					channel_id: channelId,
					name: channelName,
					db_url: isConfigured ? "(已配置)" : "(未配置)",
					status: connectionStatus,
				};
			}),
		);

		res.json(results);
	}),
);

export default router;
