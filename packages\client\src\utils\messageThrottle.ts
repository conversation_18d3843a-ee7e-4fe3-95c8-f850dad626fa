import { ElMessage } from "element-plus";

type MessageType = "success" | "warning" | "error" | "info";

interface MessageOptions {
	type?: MessageType;
	interval?: number;
	duration?: number;
	dangerouslyUseHTMLString?: boolean;
}

class MessageThrottle {
	private messageTimers: Map<string, number> = new Map();
	private readonly defaultInterval = 3000;
	private readonly defaultDuration = 3000;
	private readonly defaultType: MessageType = "info";

	show(message: string, options: MessageOptions = {}) {
		const {
			type = this.defaultType,
			interval = this.defaultInterval,
			duration = this.defaultDuration,
			dangerouslyUseHTMLString = false,
		} = options;

		const key = `${type}:${message}`;
		const now = Date.now();
		const lastTime = this.messageTimers.get(key) || 0;

		if (now - lastTime > interval) {
			ElMessage({
				message,
				type,
				duration,
				dangerouslyUseHTMLString,
			});
			this.messageTimers.set(key, now);
		}
	}
}

export const messageThrottle = new MessageThrottle();
