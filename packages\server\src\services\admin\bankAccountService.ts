import { appRedis } from "@/lib/redis.js";
import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";
import * as BankAccountModel from "@/models/bankAccount.js";
import { isChannel } from "@/config/configManager.js";
import type { BankAccount } from "@packages/shared";

const BANK_ACCOUNT_CACHE_KEY = "config:bank_account";
const BANK_ACCOUNT_CACHE_TTL = 3600; // 1小时缓存

/**
 * 设置银行账户缓存
 */
async function setBankAccountCache(bankAccount: BankAccount): Promise<void> {
	try {
		const cacheKey = BANK_ACCOUNT_CACHE_KEY;
		await appRedis.setex(
			cacheKey,
			BANK_ACCOUNT_CACHE_TTL,
			JSON.stringify(bankAccount),
		);
	} catch (error) {
		logger.error(error, "Failed to set bank account cache");
		throw AppError.create(
			"CONFIG_CACHE_FAILED",
			"Failed to cache bank account configuration",
		);
	}
}

/**
 * 验证银行账户信息
 */
function validateBankAccount(bankAccount: Partial<BankAccount>): void {
	// 允许所有字段为空，不进行验证
	return;
}

/**
 * 获取银行账户信息
 * @param usePlatformDb - 是否使用平台数据库,，默认false
 */
export async function getBankAccount(
	usePlatformDb = false,
): Promise<BankAccount> {
	try {
		if (usePlatformDb && !isChannel()) {
			throw AppError.create("FORBIDDEN", "No upstream bank account!");
		}

		// 通道使用平台数据库则不使用缓存
		if (!usePlatformDb) {
			const cached = await appRedis.get(BANK_ACCOUNT_CACHE_KEY);
			if (cached) return JSON.parse(cached);
		}

		// 从数据库获取配置
		const bankAccount = await BankAccountModel.getBankAccount(usePlatformDb);

		if (bankAccount) {
			await setBankAccountCache(bankAccount);
			return bankAccount;
		}

		// 如果数据库中不存在配置，返回空值
		return {
			name: "",
			bankName: "",
			bankCode: "",
			accountNumber: "",
			accountNumberHKD: "",
			accountNumberUSD: "",
			branchCode: "",
		};
	} catch (error) {
		logger.error(error, "Failed to get bank account");
		throw AppError.create(
			"CONFIG_NOT_FOUND",
			"Failed to get bank account configuration",
		);
	}
}

/**
 * 获取指定通道的银行账户信息
 * @param channelId 通道ID
 */
export async function getChannelBankAccount(
	channelId: string,
): Promise<BankAccount> {
	try {
		// 导入需要的模块
		const { PrismaClient } = await import("@prisma/client");
		const { findChannelDBUrlFromConfig, getAllConfiguredChannels } =
			await import("@/config/defaultParams.js");
		const { APP_CONFIG } = await import("@/config/configManager.js");

		// 验证通道ID是否属于当前交易台
		const configuredChannels = await getAllConfiguredChannels();
		if (!configuredChannels.includes(channelId)) {
			logger.warn(
				`尝试访问非当前交易台(${APP_CONFIG.tradingPlatformId})的通道(${channelId})银行账户`,
			);
			throw AppError.create(
				"FORBIDDEN",
				`通道 ${channelId} 不属于当前交易台，无法访问其银行账户信息`,
			);
		}

		// 获取通道的数据库URL
		const channelDbUrl = findChannelDBUrlFromConfig(channelId);

		// 创建临时Prisma客户端连接到通道数据库
		const channelPrisma = new PrismaClient({
			datasources: {
				db: {
					url: channelDbUrl,
				},
			},
		});

		try {
			// 从通道数据库获取银行账户信息
			const bankAccount = await BankAccountModel.getBankAccount(
				false,
				channelPrisma,
			);

			// 如果找到了银行账户信息，返回它
			if (bankAccount) {
				return bankAccount;
			}

			// 如果找不到配置，返回空银行账户
			return {
				name: "",
				bankName: "",
				bankCode: "",
				accountNumber: "",
				accountNumberHKD: "",
				accountNumberUSD: "",
				branchCode: "",
			};
		} finally {
			// 断开临时通道数据库连接
			await channelPrisma.$disconnect();
		}
	} catch (error) {
		logger.error(error, "Failed to get channel bank account");
		throw AppError.create(
			"CONFIG_NOT_FOUND",
			"Failed to get channel bank account configuration",
		);
	}
}

/**
 * 更新银行账户信息
 */
export async function updateBankAccount(
	updates: Partial<BankAccount>,
	admin_id?: number,
) {
	try {
		validateBankAccount(updates);
		const currentBankAccount = await getBankAccount();

		// 合并更新
		const newBankAccount: BankAccount = {
			...currentBankAccount,
			...updates,
		};

		await BankAccountModel.saveBankAccount(newBankAccount, false, admin_id);

		// 清除缓存
		await appRedis.del(BANK_ACCOUNT_CACHE_KEY);

		return { message: "Bank account updated successfully" };
	} catch (error) {
		logger.error(error, "Failed to update bank account");
		throw AppError.create(
			"CONFIG_UPDATE_FAILED",
			"Failed to update bank account configuration",
		);
	}
}

/**
 * 获取银行账户修改历史
 */
export async function getBankAccountHistory(
	page = 1,
	pageSize = 10,
	options: { sortBy?: string; sortOrder?: "ASC" | "DESC" } = {},
) {
	return await BankAccountModel.getBankAccountHistory(page, pageSize, options);
}
