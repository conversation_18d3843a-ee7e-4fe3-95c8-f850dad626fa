import { Router } from "express";
import * as channelFundService from "@/services/channelFundService.js";
import { APP_CONFIG } from "@/config/configManager.js";
import type {
	CreateChannelTransactionRequest,
	ChannelExchangeCurrencyData,
} from "@packages/shared";
import { ChannelTransactionStatus } from "@packages/shared";
import { AppError } from "@/core/appError.js";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import { exchangeRateService } from "@/utils/exchangeRate.js";
import * as ConfigService from "@/services/admin/configService.js";

const router = Router();

/**
 * 验证通道环境
 */
export function ValidateChannel() {
	if (!APP_CONFIG.channelId) {
		throw AppError.create("FORBIDDEN", "只有通道环境才能访问该资源");
	}
}

// 获取余额: GET /api/admin/platform/balance
router.get(
	"/balance",
	wrapAdminRoute(async (_req, res) => {
		ValidateChannel();

		const balance = await channelFundService.getChannelBalance();
		res.json(balance);
	}),
);

// 获取信用额度: GET /api/admin/platform/credit-limit
router.get(
	"/credit-limit",
	wrapAdminRoute(async (_req, res) => {
		ValidateChannel();

		const config = await ConfigService.getConfig();
		const creditLimit = config.CHANNEL_CREDIT_LIMIT;

		res.json({ creditLimit });
	}),
);

// 创建资金交易: POST /api/admin/platform/transaction
router.post(
	"/transaction",
	wrapAdminRoute<Partial<CreateChannelTransactionRequest>>(async (req, res) => {
		ValidateChannel();

		// Validate required fields
		if (!req.body.amount || !req.body.type || !req.body.currency) {
			res.status(400).json({
				error:
					"Missing required fields. Please provide amount, type and currency.",
			});
			return;
		}

		const transactionData = {
			...req.body,
			status: ChannelTransactionStatus.PENDING, // 设置默认状态为待确认
		} as CreateChannelTransactionRequest & {
			status: ChannelTransactionStatus;
		};

		const transaction =
			await channelFundService.createChannelTransaction(transactionData);
		res.json(transaction);
	}),
);

// 获取资金记录: GET /api/admin/platform/transactions
router.get(
	"/transactions",
	wrapAdminRoute(async (req, res) => {
		ValidateChannel();

		const page = req.query.page
			? Number.parseInt(req.query.page as string, 10)
			: 1;
		const size = req.query.size
			? Number.parseInt(req.query.size as string, 10)
			: 10;
		const offset = (page - 1) * size;
		const status = req.query.status as ChannelTransactionStatus | undefined;

		const result = await channelFundService.getChannelTransactions(
			undefined, // 通道版本中channelId会自动从环境变量获取
			size,
			offset,
			status,
			true, // 使用交易台数据库
		);

		res.json({
			items: result.transactions,
			total: result.total,
		});
	}),
);

// 获取当前汇率: GET /api/admin/platform/rates
router.get(
	"/rates",
	wrapAdminRoute(async (_req, res) => {
		ValidateChannel();

		const rates = await exchangeRateService.getCurrentRates();
		res.json(rates);
	}),
);

// 执行货币兑换: POST /api/admin/platform/exchange
router.post(
	"/exchange",
	wrapAdminRoute<ChannelExchangeCurrencyData>(async (req, res) => {
		ValidateChannel();

		const { fromCurrency, toCurrency, amount } = req.body;

		// Validate required fields
		if (!fromCurrency || !toCurrency || !amount || Number(amount) <= 0) {
			throw AppError.create(
				"INVALID_REQUEST_DATA",
				"缺少必要字段或金额无效: fromCurrency, toCurrency, amount",
			);
		}

		// Execute currency exchange
		const result = await channelFundService.exchangeChannelCurrency(
			fromCurrency,
			toCurrency,
			amount,
		);

		res.json(result);
	}),
);

export default router;
