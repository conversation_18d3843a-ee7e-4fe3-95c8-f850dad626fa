import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";
import { appRedis } from "@/lib/redis.js";
import type {
	StockInfo,
	TradeCalendarRange,
	PlatformConfig,
	StructureType,
} from "@packages/shared";
import type { DailyData, LimitData } from "@/types/api.js";
import { pool } from "@/lib/mysql.js";
import * as tushareApi from "../api/tushareApi.js";
import * as gtimgApi from "../api/gtimgApi.js";
import * as inkApi from "../api/inkApi.js";
import {
	getCachedSwingData,
	getCachedOptionPrice,
} from "../queue/inkDataSyncWorker.js";
import { calculateFinalQuote, calculateQuoteMarkupOffset } from "./quote.js";
import {
	dateToISOString,
	getChinaDateCompactString,
	dateToCompactString,
} from "@/utils/dateUtils.js";
import { isChannel } from "@/config/configManager.js";

const STOCK_LIST_KEY = "stock:list:hash";
const LIST_CACHE_DURATION = 6 * 60 * 60; // 6 hours，凌晨更新股票列表

// Re-export functions from tushareApi.ts
export const fetchStockListFromAPI = tushareApi.fetchStockListFromAPI;
export const fetchTodayExDateListFromAPI = tushareApi.fetchExDateListFromAPI;
export const fetchUpDownLimitFromAPI = tushareApi.fetchUpDownLimitFromAPI;
export const fetchTradeCalendarRangeFromAPI =
	tushareApi.fetchTradeCalendarRangeFromAPI;
export const fetchDailyDataFromAPI = tushareApi.fetchDailyDataFromAPI;
export const fetchSuspensionFromAPI = tushareApi.fetchSuspensionFromAPI;

// Re-export functions from gtimgApi.ts
export const fetchCurrentDayData = gtimgApi.fetchCurrentDayData;
export const fetchCurrentPrice = gtimgApi.fetchCurrentPrice;
export const fetchCurrentPrices = gtimgApi.fetchCurrentPrices;
export const getTurnoverAndVolume = gtimgApi.getTurnoverAndVolume;
export const fetchTodayPreClose = gtimgApi.fetchTodayPreClose;
export const fetchTodaySuspension = gtimgApi.fetchTodaySuspension;
export const fetchPriceDetails = gtimgApi.fetchPriceDetails;

/**
 * 获取股票列表。
 * @param forceRefresh 可选。如果为true，则绕过Redis缓存强制从数据库获取
 * @returns 股票列表。market: "主板" | "创业板" | "科创板" | "CDR" | "北交所"
 */
export async function fetchStockList(
	forceRefresh?: boolean,
): Promise<StockInfo[]> {
	try {
		// 优先检查Redis缓存，除非强制刷新
		if (!forceRefresh) {
			console.time("Redis fetchStockList");
			const redisData = await appRedis.hgetall(STOCK_LIST_KEY);
			console.timeEnd("Redis fetchStockList");
			// Redis 需要转换
			if (Object.keys(redisData).length > 0) {
				return Object.entries(redisData).map(([ts_code, data]) => {
					const [name, market] = data.split(":");
					return { ts_code, name, market };
				});
			}
		}

		// 从MySQL获取 (强制刷新或Redis缓存未命中时)
		console.time("MySQL fetchStockList");
		const [rows] = await pool.query(
			"SELECT ts_code, name, market FROM stock_basic",
		);
		const cachedData = rows as StockInfo[];
		console.timeEnd("MySQL fetchStockList");

		// 存入 Redis hash
		const pipeline = appRedis.pipeline();
		for (const item of cachedData) {
			pipeline.hset(
				STOCK_LIST_KEY,
				item.ts_code,
				`${item.name}:${item.market}`,
			);
		}
		pipeline.expire(STOCK_LIST_KEY, LIST_CACHE_DURATION);
		await pipeline.exec();

		return cachedData;
	} catch (error) {
		logger.error(error, "Error fetching stock list");
		throw AppError.create(
			"FETCH_STOCK_LIST_FAILED",
			"Failed to fetch stock list",
		);
	}
}

/**
 * 从数据库获取今天除权除息的股票列表。
 * @returns 除权除息股票列表
 */
export async function fetchTodayExDateList(): Promise<
	Array<{ ts_code: string; ex_date: string }>
> {
	try {
		const today = dateToISOString(new Date());
		const [rows] = await pool.query(`
			SELECT ts_code, ex_date
			FROM dividend
			WHERE ex_date = '${today}'
		`);
		return rows as Array<{ ts_code: string; ex_date: string }>;
	} catch (error) {
		logger.error(error, "Error fetching today's ex_date list");
		throw AppError.create(
			"FETCH_EX_DATE_LIST_FAILED",
			"Failed to fetch today's ex_date list",
		);
	}
}

/**
 * 从数据库获取股票涨跌停信息。
 * @returns 涨跌停数据
 */
export async function fetchUpDownLimit(params: {
	date?: Date;
	ts_code?: string;
}): Promise<LimitData> {
	try {
		const trade_date = params.date ? dateToISOString(params.date) : undefined;
		let query_str = `
			SELECT trade_date, ts_code, pre_close, up_limit, down_limit
			FROM stk_limit
			WHERE 1=1
		`;

		if (trade_date) {
			query_str += ` AND trade_date = '${trade_date}'`;
		}
		if (params.ts_code) {
			query_str += ` AND ts_code = '${params.ts_code}'`;
		}

		const [rows] = await pool.query(query_str);
		return rows as LimitData;
	} catch (error) {
		logger.error(error, "Error fetching up down limit");
		throw AppError.create(
			"FETCH_UP_DOWN_LIMIT_FAILED",
			"Failed to fetch up down limit",
		);
	}
}

/**
 * 从数据库获取交易日历范围。
 */
export async function fetchTradeCalendarRange(
	startDate: Date,
	endDate: Date,
): Promise<TradeCalendarRange[]> {
	try {
		const start = dateToISOString(startDate);
		const end = dateToISOString(endDate);

		const [rows] = await pool.query(`
			SELECT cal_date, is_open, pretrade_date
			FROM trade_calendar
			WHERE cal_date BETWEEN '${start}' AND '${end}'
			ORDER BY cal_date ASC
		`);
		return rows as TradeCalendarRange[];
	} catch (error) {
		logger.error(error, "Error fetching trade calendar range");
		throw error;
	}
}

export async function fetchTradeCalendar(
	tradeDate: Date,
): Promise<TradeCalendarRange> {
	try {
		const dateStr = dateToISOString(tradeDate);

		const [rows] = await pool.query(`
			SELECT cal_date, is_open, pretrade_date
			FROM trade_calendar
			WHERE cal_date = '${dateStr}'
		`);

		const data = rows as TradeCalendarRange[];
		return data[0];
	} catch (error) {
		logger.error(error, `Error fetching trade calendar for ${tradeDate}`);
		throw error;
	}
}

/**
 * 从MySQL获取收盘价数据。
 * @param params 查询参数
 * @param fields 返回字段
 * @returns 收盘价数据
 */
export async function fetchDailyData(
	params: {
		date?: Date;
		ts_code?: string;
		limit?: number;
	},
	fields: ["ts_code"?, "trade_date"?, "close"?],
): Promise<DailyData> {
	try {
		// 构建查询条件
		let whereClause = "1=1";
		const queryParams: string[] = [];

		if (params.date) {
			const trade_date = dateToISOString(params.date);
			whereClause += " AND trade_date = ?";
			queryParams.push(trade_date);
		}

		if (params.ts_code) {
			whereClause += " AND ts_code = ?";
			queryParams.push(params.ts_code);
		}

		// 构建查询字段
		const selectFields = fields.join(", ");

		// 构建查询语句
		let queryStr = `
			SELECT ${selectFields}
			FROM daily
			WHERE ${whereClause}
			ORDER BY trade_date DESC
		`;

		if (params.limit) {
			queryStr += ` LIMIT ${params.limit}`; // limit是数字，无需转义
		}

		// 执行查询
		const [rows] = await pool.query(queryStr, queryParams);
		const data = rows as Array<Record<string, string | number | Date>>;

		// 转换为 DailyData 格式
		return data.map((item) => {
			const record: [string?, string?, number?] = [];
			if (fields.includes("ts_code")) record.push(String(item.ts_code));
			if (fields.includes("trade_date")) {
				// 处理日期值 - MySQL返回Date对象
				const dateValue = dateToCompactString(item.trade_date as Date);
				record.push(dateValue);
			}
			if (fields.includes("close")) record.push(Number(item.close));
			return record;
		});
	} catch (error) {
		logger.error(error, "Error fetching daily data from database");
		throw error;
	}
}

/**
 * 获取每日数据的总数量
 * @returns 每日数据的总数量
 */
export async function countDailyData(): Promise<number> {
	try {
		const [rows] = await pool.query(`
      SELECT count(*) as count 
      FROM daily 
      LIMIT 1
    `);
		const data = rows as { count: number }[];
		return Number(data[0].count);
	} catch (error) {
		logger.error(error, "Error counting daily data from database");
		throw error;
	}
}

/**
 * 获取指定日期的所有停牌股票
 * @param date 交易日期
 * @returns 停牌股票代码数组
 */
export async function fetchSuspendedStocks(date: Date): Promise<string[]> {
	try {
		const dateStr = dateToISOString(date);
		const [rows] = await pool.query(
			`
      SELECT ts_code
      FROM suspend_d
      WHERE trade_date = ?
      AND suspend_type = 'S'
    `,
			[dateStr],
		);

		const data = rows as Array<{ ts_code: string }>;
		return data.map((item: { ts_code: string }) => item.ts_code);
	} catch (error) {
		logger.error(error, `Error fetching suspended stocks for ${date}`);
		throw error;
	}
}

/**
 * 获取(retrieve)股票缓存的有效振幅值。
 * 如果缓存未命中，则回退到直接API调用。
 * Cron job确保在需要时获取前几日数据以保证数据可用性。
 *
 * @param ts_code 股票代码
 * @returns 有效振幅值
 * @throws AppError 如果无法获取数据
 */
export async function getEffectiveSwingWithCache(
	ts_code: string,
): Promise<number | null> {
	try {
		// 格式化股票代码，仅保留数字部分（去掉小数点和交易所代码）
		// 例如将 "600001.SH" 转换为 "600001"
		const formattedCode = ts_code.split(".")[0];

		// 尝试从内存缓存获取
		const swingMap = await getCachedSwingData();

		if (swingMap && swingMap[formattedCode] !== undefined) {
			logger.info(`Using cached swing data for ${ts_code}`);
			return swingMap[formattedCode];
		}

		// 如果缓存没有，则从API获取
		logger.info(
			`Swing data not found in cache for ${ts_code}, fetching from API`,
		);
		const currentDate = getChinaDateCompactString();

		return inkApi.getEffectiveSwing(ts_code, currentDate);
	} catch (error) {
		logger.error(
			error,
			`Error getting effective swing with cache for ${ts_code}`,
		);
		throw AppError.create(
			"SERVER_ERROR",
			"Failed to fetch effective swing data",
		);
	}
}

/**
 * 获取股票的所有外部期权报价以及内部计算报价。
 * 根据指定的结构和期限，获取所有外部提供商的报价数据。
 * 优先使用缓存数据，如果缓存不存在则直接获取API数据。
 *
 * @param ts_code 股票代码
 * @param structure 期权结构类型 (例如 "100C", "103C", "105C", "110C")
 * @param term 期限 (14表示两周, 1、2或3表示月)
 * @param platformConfig 平台配置 (可选)
 * @param notional 名义本金 (可选)
 * @param userCustomQuoteDiffs 用户自定义报价差异配置 (可选)
 * @returns 包含内部计算报价和所有外部报价、以及实际应用的报价差异的对象
 */
export async function getExternalOptionQuotes(
	ts_code: string,
	structure: StructureType,
	term: number,
	platformConfig?: PlatformConfig,
	notional?: number,
	userCustomQuoteDiffs?: Record<string, number> | null, // 添加的用户自定义报价差异参数
): Promise<{
	calculatedQuote: number | null;
	externalQuotes: Record<string, number | null>;
	allExternalQuotes: Record<string, number | null>;
	quoteDiffs: Record<string, number | null>;
}> {
	try {
		// 格式化股票代码，仅保留数字部分
		const formattedCode = ts_code.split(".")[0];

		// 从structure解析出行权价百分比
		const structureMatch = /^(\d+)/.exec(structure);
		if (!structureMatch) {
			logger.error(`Invalid structure format: ${structure}`);
			return {
				calculatedQuote: null,
				externalQuotes: {},
				allExternalQuotes: {},
				quoteDiffs: {},
			};
		}

		const strikePercent = Number.parseInt(structureMatch[1]) as
			| 100
			| 103
			| 105
			| 110;

		// 将term转换为期限格式
		let period: "2w" | "1m" | "2m" | "3m";
		switch (term) {
			case 14:
				period = "2w";
				break;
			case 1:
				period = "1m";
				break;
			case 2:
				period = "2m";
				break;
			case 3:
				period = "3m";
				break;
			default:
				logger.error(`Invalid term: ${term}`);
				return {
					calculatedQuote: null,
					externalQuotes: {},
					allExternalQuotes: {},
					quoteDiffs: {},
				};
		}

		// 1. 获取内部计算的报价
		const calculatedQuote = await calculateFinalQuote(ts_code, structure, term);

		// 2. 初始化报价记录对象
		const externalQuotes: Record<string, number | null> = {}; // 仅包含启用的提供商报价
		const allExternalQuotes: Record<string, number | null> = {}; // 包含所有提供商报价
		const providers = Object.keys(
			inkApi.PriceProvider,
		) as inkApi.PriceProviderKey[];

		// 3. 初始化报价差值对象
		const quoteDiffs: Record<string, number | null> = {};

		// 只有通道版本才计算报价差值
		const isChannelVersion = isChannel();

		// 只在通道版本下计算INK的报价差值
		if (isChannelVersion && calculatedQuote !== null && notional) {
			const inkProviderConfig = platformConfig?.quote_providers?.INK;
			if (inkProviderConfig) {
				// 对于INK，价格调整（加价）优先使用用户自定义设置，如果不存在则使用平台配置。
				const inkPriceAdjustment =
					userCustomQuoteDiffs?.INK !== undefined &&
					userCustomQuoteDiffs?.INK !== null
						? userCustomQuoteDiffs.INK
						: inkProviderConfig.price_adjustment;

				// INK提供商强制启用，如果有有效的价格调整值，则计算报价差值
				if (inkPriceAdjustment !== undefined) {
					const quoteMarkupOffset = calculateQuoteMarkupOffset(
						notional,
						calculatedQuote,
						inkPriceAdjustment,
					);
					quoteDiffs.INK = quoteMarkupOffset;
				} else {
					quoteDiffs.INK = null;
				}
			} else {
				quoteDiffs.INK = null;
			}
		} else {
			quoteDiffs.INK = null;
		}

		// 4. 对于看跌期权，直接返回不包含外部报价的结果
		if (structure.endsWith("P")) {
			// 如果是看跌期权，不返回外部报价，但保留INK的quoteDiffs
			return {
				calculatedQuote,
				// 看跌期权没有外部报价
				externalQuotes: {},
				allExternalQuotes: {},
				quoteDiffs, // 保留INK的quoteDiffs！！！
			};
		}

		// 5. 并行获取所有外部提供商的报价
		const quoteTasks = providers.map(async (providerKey) => {
			try {
				// 获取提供商配置
				const currentProviderConfig =
					platformConfig?.quote_providers?.[providerKey];
				const providerValue = inkApi.PriceProvider[providerKey];

				// 确定此提供商的有效价格调整
				let effectivePriceAdjustment: number | undefined = undefined;
				if (
					userCustomQuoteDiffs &&
					userCustomQuoteDiffs[providerKey] !== undefined &&
					userCustomQuoteDiffs[providerKey] !== null
				) {
					effectivePriceAdjustment = userCustomQuoteDiffs[providerKey];
				} else if (currentProviderConfig?.price_adjustment !== undefined) {
					effectivePriceAdjustment = currentProviderConfig.price_adjustment;
				}

				// 6. 从缓存获取此提供商的报价 (这里才是实际使用缓存的地方)
				const price = await getCachedOptionPrice(
					formattedCode,
					providerValue,
					strikePercent,
					period,
				);

				if (price !== null) {
					// 7a. 无论提供商是否启用，都添加到allExternalQuotes (用于限价逻辑)
					allExternalQuotes[providerKey] = price;

					// 7b. 只有启用的提供商才添加到externalQuotes (用于业务展示)
					if (!currentProviderConfig || currentProviderConfig.enabled) {
						externalQuotes[providerKey] = price;
					}

					// 7c. 计算报价差值，考虑price_adjustment
					// 仅当是通道版本且提供商已启用，并且有有效的价格调整值时，才计算差值
					if (
						isChannelVersion &&
						notional &&
						currentProviderConfig &&
						currentProviderConfig.enabled && // 检查提供商是否启用
						effectivePriceAdjustment !== undefined
					) {
						// 使用 effectivePriceAdjustment
						const quoteMarkupOffset = calculateQuoteMarkupOffset(
							notional,
							price,
							effectivePriceAdjustment, // 使用确定的调整值
						);
						quoteDiffs[providerKey] = quoteMarkupOffset;
					}
				}
			} catch (error) {
				logger.error(
					error,
					`Error getting option price for ${ts_code} from ${providerKey}`,
				);
				externalQuotes[providerKey] = null;
				allExternalQuotes[providerKey] = null;
			}
		});

		// 8. 等待所有报价获取完成
		await Promise.all(quoteTasks);

		// 9. 返回计算结果
		return {
			calculatedQuote,
			externalQuotes,
			allExternalQuotes,
			quoteDiffs,
		};
	} catch (error) {
		logger.error(
			error,
			`Error getting external option quotes for ${ts_code}, structure: ${structure}, term: ${term}`,
		);
		return {
			calculatedQuote: null,
			// 错误时确保返回空对象
			externalQuotes: {},
			allExternalQuotes: {},
			quoteDiffs: {},
		};
	}
}
