// ! 环境变量全局加载，会加载到 Node.js 的 process.env 中，污染全局变量
// require("dotenv").config({ path: "./packages/server/.env" });

// 创建日志目录
const fs = require('node:fs');
const path = require('node:path');

// 确保 PM2 日志目录存在
const logDir = path.join(__dirname, 'logs');
const pm2LogDir = path.join(__dirname, 'logs/pm2');

try {
	if (!fs.existsSync(logDir)) {
		fs.mkdirSync(logDir, { recursive: true });
		console.log(`Created log directory: ${logDir}`);
	}
	if (!fs.existsSync(pm2LogDir)) {
		fs.mkdirSync(pm2LogDir, { recursive: true });
		console.log(`Created PM2 log directory: ${pm2LogDir}`);
	}
} catch (err) {
	console.error(`Failed to create log directories: ${err.message}`);
}

// 设置临时环境变量应用名称: APP_NAME=ink-server-b1 pm2 start ecosystem.config.cjs
const appName = process.env.APP_NAME;
if (!appName) {
    console.error('Error: APP_NAME environment variable is required');
    process.exit(1);
}

module.exports = {
	apps: [
		{
			name: appName,
			script: "./packages/server/dist/app.js", // 编译后的入口文件
			instances: "max", // 根据 CPU 核心数启动实例
			exec_mode: "cluster", // 使用集群模式
			watch: false, // 生产环境关闭文件监听
			max_memory_restart: "1G", // 超过内存限制自动重启
			time: true, // 添加时间戳
			// pm2 环境变量注入依赖配置文件: pm2 reload ecosystem.config.cjs --env sync
			env: {
				// 默认环境，因为 PM2 的环境变量持久化机制，用于刷新数据后显式恢复（覆盖）配置
				FORCE_SYNC: "false",
			},
			env_sync: {
				// 带同步的环境配置
				FORCE_SYNC: "true",
			},
			// 日志配置
			log_date_format: "YYYY-MM-DD HH:mm:ss",
			error_file: "./logs/pm2/error.log", // 使用相对路径
			out_file: "./logs/pm2/out.log",     // 使用相对路径
		},
	],
};
