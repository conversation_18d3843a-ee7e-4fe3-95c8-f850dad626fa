import type { StructureType } from "./inquiry.js";

// Unified trade type definition
export enum OrderType {
	MARKET = "market",
	LIMIT = "limit",
	VWAP = "vwap",
}

export enum TradeDirection {
	BUY = "buy",
	SELL = "sell",
}

interface BaseRequest {
	type: OrderType;
	user_id: number;
	ts_code: string;
	scale: number;
	quote_provider: string;
	quote_diff: number;
}

export interface BuyRequest extends BaseRequest {
	direction: TradeDirection.BUY;
	quote: number;
	structure: StructureType;
	term: number;
	entry_price?: number;
	limit_price?: number;

	inquiry_id?: number;
}

export interface QuoteBuyRequest extends BuyRequest {
	inquiry_id: number;
}

export interface SellRequest extends BaseRequest {
	direction: TradeDirection.SELL;
	trade_no: string;
	settle_price?: number;
	limit_price?: number;
	scale: number;
}

export type OrderRequest = BuyRequest | SellRequest;

// 订单相关类型
export enum OrderStatus {
	HOLDING = "holding",
	SOLD = "sold",
	LIMIT_BUYING = "limit_buying",
	LIMIT_SELLING = "limit_selling",
	VWAP_BUYING = "vwap_buying",
	VWAP_SELLING = "vwap_selling",
}

export interface OrderData {
	trade_no: string;
	user_id: number;
	ts_code: string;
	entry_price: number;
	structure: StructureType;
	exercise_price: number;
	settle_price: number;
	scale: number;
	total_scale: number;
	term: number;
	expiry_date: string;
	expiry_date_confirmed: boolean;
	quote: number;
	quote_provider: string;
	quote_diff: number;
	status: OrderStatus;
	created_at: string;
	closed_at?: string;
	is_split?: boolean;
}

export interface SettledOrderData extends OrderData {
	closed_at: string;
}

export interface OrderResponse {
	message: string;
	data: Omit<
		OrderData,
		"trade_no" | "expiry_date" | "expiry_date_confirmed"
	> & {
		trade_no?: string;
		expiry_date?: string;
		expiry_date_confirmed?: boolean;
	};
}

export type PendingOrderData = BuyingOrderData | SellingOrderData;

export interface SellingOrderData extends OrderData {
	pending_id: number;
	limit_price?: number;
}

export interface BuyingOrderData
	extends Omit<
		SellingOrderData,
		"trade_no" | "expiry_date" | "expiry_date_confirmed"
	> {}

export enum OrderModificationType {
	MANUAL_CREATE = "manual_create",
	ORDER_CHANGE = "order_change",
	MANUAL_CLOSE = "manual_close",
}

export interface OrderModification {
	id: number;
	trade_no: string;
	user_id: number;
	type: OrderModificationType;
	data: OrderModificationData;
	comment?: string;
	created_at: string;
	admin_id?: number;
}

export interface OrderModificationData {
	oldValue?: Partial<OrderData>;
	newValue: Partial<OrderData>;
}
