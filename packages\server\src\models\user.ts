import prisma from "@/lib/prisma.js";
import { Prisma } from "@prisma/client";
import { withTransaction } from "@/core/dbTxnManager.js";
import { encrypt, decrypt } from "@/utils/encryption.js";
import { AppError } from "@/core/appError.js";
import type {
	Currency,
	Balances,
	UserData,
	UserInfo,
	UserFilters,
} from "@packages/shared";
import type { UpdateQualificationInfoData } from "@packages/shared";
import EmailService from "@/utils/email.js";

// 业务上可能为空，保持数据库灵活性，符合最小约束原则
interface PrismaUser {
	user_id: number;
	email: string;
	password_hash: string;
	payment_password_hash: string | null;

	is_qualified: boolean | null;
	phone_number: string | null;
	balance_cny: string;
	balance_hkd: string;
	balance_usd: string;
	created_at: Date | null;
	updated_at: Date | null;
	contribution: Prisma.Decimal | null;
	name: string | null;
	id_number: string | null;
	bank_name: string | null;
	bank_code: string | null;
	bank_account: string | null;
	premium: Prisma.Decimal | null;
	deposit: Prisma.Decimal | null;
	can_transfer: boolean | null;
	custom_quote_diffs: Prisma.JsonValue | null;
	custom_profit_sharing_percentage: Prisma.Decimal | null;
}

function transformUserData(prismaUser: PrismaUser): UserInfo {
	const { password_hash, payment_password_hash, ...safeDataToTransform } =
		prismaUser;

	const transformedBase = {
		...safeDataToTransform,
		is_qualified: prismaUser.is_qualified ?? false,
		created_at: prismaUser.created_at ?? new Date(),
		updated_at: prismaUser.updated_at ?? new Date(),
		contribution: prismaUser.contribution?.toNumber() ?? 0,
		name: prismaUser.name ?? "",
		bank_name: prismaUser.bank_name ?? "",
		bank_code: prismaUser.bank_code ?? "",
		premium: prismaUser.premium?.toNumber() ?? 0,
		deposit: prismaUser.deposit?.toNumber() ?? 0,
		can_transfer: prismaUser.can_transfer ?? false,
		custom_profit_sharing_percentage:
			prismaUser.custom_profit_sharing_percentage?.toNumber() ?? undefined,
		custom_quote_diffs:
			(prismaUser.custom_quote_diffs as Record<string, number>) ?? undefined,
	};

	return {
		...transformedBase,
		email: decrypt(prismaUser.email),
		balance_cny: Number.parseFloat(decrypt(prismaUser.balance_cny)),
		balance_hkd: Number.parseFloat(decrypt(prismaUser.balance_hkd)),
		balance_usd: Number.parseFloat(decrypt(prismaUser.balance_usd)),
		id_number: prismaUser.id_number ? decrypt(prismaUser.id_number) : "",
		phone_number: prismaUser.phone_number
			? decrypt(prismaUser.phone_number)
			: "",
		bank_account: prismaUser.bank_account
			? decrypt(prismaUser.bank_account)
			: "",
	} as UserInfo;
}

export async function create(
	email: string,
	password_hash: string,
): Promise<UserInfo> {
	const user = await prisma.users.create({
		data: {
			// EMAIL_ONLY: 避免邮箱模式加密后的 "" 触发唯一性约束
			email: encrypt(email),
			password_hash,
			balance_cny: encrypt("0"),
			balance_hkd: encrypt("0"),
			balance_usd: encrypt("0"),
		},
	});

	return transformUserData(user);
}

export async function updatePassword(
	user_id: number,
	new_password_hash: string,
): Promise<void> {
	const user = await prisma.users.update({
		where: { user_id },
		data: {
			password_hash: new_password_hash,
			updated_at: new Date(),
		},
	});

	if (!user) {
		throw AppError.create(
			"USER_UPDATE_FAILED",
			`Failed to update password for user ${user_id}`,
		);
	}

	if (user.email) {
		EmailService.sendEmail(decrypt(user.email), "PASSWORD_CHANGE", {});
	}
}

export async function getBalances(user_id: number): Promise<Balances> {
	const user = await prisma.users.findUnique({
		where: { user_id },
		select: {
			balance_cny: true,
			balance_hkd: true,
			balance_usd: true,
		},
	});

	if (!user) {
		throw AppError.create(
			"USER_NOT_FOUND",
			`User with ID ${user_id} not found when getting balances`,
		);
	}

	return {
		balance_cny: Number.parseFloat(decrypt(user.balance_cny)),
		balance_hkd: Number.parseFloat(decrypt(user.balance_hkd)),
		balance_usd: Number.parseFloat(decrypt(user.balance_usd)),
	};
}

export async function getBalance(
	user_id: number,
	currency: Currency,
): Promise<number> {
	const balanceType = `balance_${currency.toLowerCase()}` as keyof Balances;
	const user = (await prisma.users.findUnique({
		where: { user_id },
		select: {
			[balanceType]: true,
		},
	})) as unknown as { [key: string]: string };

	if (!user) {
		throw AppError.create(
			"USER_NOT_FOUND",
			`User with ID ${user_id} not found when getting balance of ${currency}`,
		);
	}

	return Number.parseFloat(decrypt(user[balanceType]));
}

export async function updateBalance(
	user_id: number,
	newBalance: number,
	balanceType: Currency,
	client: Prisma.TransactionClient,
): Promise<UserInfo | null> {
	const result = await client.users.update({
		where: { user_id },
		data: {
			[`balance_${balanceType.toLowerCase()}`]: encrypt(
				Number(newBalance).toFixed(2),
			),
			updated_at: new Date(),
		},
	});

	if (!result) {
		throw AppError.create(
			"USER_BALANCE_UPDATE_FAILED",
			`Failed to update balance for user ${user_id}`,
		);
	}

	return transformUserData(result);
}

interface UserQueryOptions {
	page?: number;
	pageSize?: number;
	sortBy?: string;
	sortOrder?: "ASC" | "DESC";
	filters?: UserFilters;
	select?: (keyof UserInfo)[];
}

/**
 * 获取用户列表(已包含加解密逻辑)
 */
export async function getAll(
	options: UserQueryOptions = {},
): Promise<{ total: number; items: UserInfo[] }> {
	const {
		page = 1,
		pageSize = 10,
		sortBy = "created_at",
		sortOrder = "DESC",
		filters = {},
	} = options;

	const where: Prisma.usersWhereInput = {
		...(filters.user_id && { user_id: filters.user_id }),
		...(filters.phone_number && {
			phone_number: encrypt(filters.phone_number),
		}),
		...(filters.email && { email: encrypt(filters.email) }),
		...(filters.name && { name: filters.name }),
		...(filters.is_qualified !== undefined && {
			is_qualified: filters.is_qualified,
		}),
	};

	const [total, users] = await Promise.all([
		prisma.users.count({ where }),
		prisma.users.findMany({
			where,
			orderBy: { [sortBy]: sortOrder.toLowerCase() },
			skip: (page - 1) * pageSize,
			take: pageSize,
		}),
	]);

	return {
		total,
		items: users.map(transformUserData),
	};
}

// 通过id查找用户(调用getAll方法)
export async function findById(user_id: number): Promise<UserInfo | null> {
	const result = await getAll({ filters: { user_id } });
	return result.items[0] || null;
}

// 通过手机号查找用户(调用getAll方法)
export async function findByPhoneNumber(
	phone_number: string,
): Promise<UserInfo | null> {
	const result = await getAll({ filters: { phone_number } });
	return result.items[0] || null;
}

// 通过邮箱查找用户(调用getAll方法)
export async function findByEmail(email: string): Promise<UserInfo | null> {
	const result = await getAll({ filters: { email } });
	return result.items[0] || null;
}

export async function findByName(name: string): Promise<UserInfo | null> {
	const result = await getAll({ filters: { name } });
	return result.items[0] || null;
}

export async function getPasswordHashByPhoneNumber(
	phone_number: string,
): Promise<string | null> {
	const user = await prisma.users.findUnique({
		where: { phone_number: encrypt(phone_number) },
		select: { password_hash: true },
	});
	return user?.password_hash || null;
}

// 新增通过邮箱获取密码哈希的功能
export async function getPasswordHashByEmail(
	email: string,
): Promise<string | null> {
	const user = await prisma.users.findUnique({
		where: { email: encrypt(email) },
		select: { password_hash: true },
	});
	return user?.password_hash || null;
}

export async function getTotalCount(): Promise<number> {
	return await prisma.users.count();
}

export async function getVerifiedCount(): Promise<number> {
	return await prisma.users.count({
		where: {
			is_qualified: true,
		},
	});
}

export async function addContributionPoints(
	user_id: number,
	points: number,
	client?: Prisma.TransactionClient,
): Promise<void> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		const result = await tx.users.update({
			where: { user_id },
			data: {
				contribution: { increment: points },
			},
		});

		if (!result) {
			throw AppError.create(
				"USER_UPDATE_FAILED",
				`Failed to update contribution points for user ${user_id}`,
			);
		}
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function addAccumulatedPremium(
	user_id: number,
	amount: number,
	client?: Prisma.TransactionClient,
): Promise<void> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		const result = await tx.users.update({
			where: { user_id },
			data: {
				premium: { increment: amount },
			},
		});

		if (!result) {
			throw AppError.create(
				"USER_UPDATE_FAILED",
				`Failed to update premium for user ${user_id}`,
			);
		}
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function addAccumulatedDeposit(
	user_id: number,
	amount: number,
	client?: Prisma.TransactionClient,
): Promise<void> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		const result = await tx.users.update({
			where: { user_id },
			data: {
				deposit: { increment: amount },
			},
		});

		if (!result) {
			throw AppError.create(
				"USER_UPDATE_FAILED",
				`Failed to update deposit for user ${user_id}`,
			);
		}
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function getQualificationStatus(user_id: number) {
	const user = await prisma.users.findUnique({
		where: { user_id },
		select: { is_qualified: true },
	});
	return user?.is_qualified;
}

export async function updateQualificationInfo(
	user_id: number,
	data: UpdateQualificationInfoData,
	client?: Prisma.TransactionClient,
): Promise<UserInfo> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		const result = await tx.users.update({
			where: { user_id },
			data: {
				is_qualified: true,
				name: data.name,
				id_number: encrypt(data.id_number),
				phone_number: encrypt(data.phone_number),
				bank_name: data.bank_name,
				bank_code: data.bank_code,
				bank_account: encrypt(data.bank_account),
				updated_at: new Date(),
			},
		});

		if (!result) {
			throw AppError.create(
				"USER_UPDATE_FAILED",
				`Failed to update qualification info for user ${user_id}`,
			);
		}

		return transformUserData(result);
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

// 添加获取转账授权用户的方法
export async function getTransferAuthUsers(
	options: UserQueryOptions = {},
): Promise<{ total: number; items: Partial<UserInfo>[] }> {
	const result = await getAll({
		page: options.page,
		pageSize: options.pageSize,
		sortBy: options.sortBy,
		sortOrder: options.sortOrder,
	});

	// 如果指定了select，只返回选中的字段
	if (options.select?.length) {
		const selectedFields = options.select; // 创建一个确定非空的引用
		return {
			total: result.total,
			items: result.items.map((user) => {
				const filteredFields = Object.fromEntries(
					selectedFields.map((key) => [key, user[key as keyof UserInfo]]),
				) as Partial<UserInfo>;
				return filteredFields;
			}),
		};
	}

	// 否则返回所有字段
	return result;
}

// 添加更新用户信息的方法
export async function updateById(
	user_id: number,
	updates: Partial<UserData>,
	client?: Prisma.TransactionClient,
): Promise<void> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		// 处理需要加密的字段
		const processedUpdates = Object.entries(updates).reduce(
			(acc, [key, value]) => {
				if (value === undefined) return acc;

				if (
					["phone_number", "email", "id_number", "bank_account"].includes(
						key,
					) &&
					typeof value === "string"
				) {
					acc[key] = encrypt(value);
				} else if (key === "custom_quote_diffs") {
					acc[key] = value === null ? Prisma.JsonNull : value;
				} else {
					acc[key] = value;
				}
				return acc;
			},
			{} as Record<string, unknown>,
		);

		if (Object.keys(processedUpdates).length === 0) return;

		const result = await tx.users.update({
			where: { user_id },
			data: {
				...processedUpdates,
				updated_at: new Date(),
			},
		});

		if (!result) {
			throw AppError.create(
				"USER_UPDATE_FAILED",
				`Failed to update user ${user_id}`,
			);
		}
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function getPaymentPasswordHash(
	user_id: number,
): Promise<string | null> {
	const user = await prisma.users.findUnique({
		where: { user_id },
		select: { payment_password_hash: true },
	});
	return user?.payment_password_hash || null;
}

export async function setPaymentPassword(
	user_id: number,
	password_hash: string,
	client?: Prisma.TransactionClient,
): Promise<void> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		const result = await tx.users.update({
			where: { user_id },
			data: {
				payment_password_hash: password_hash,
				updated_at: new Date(),
			},
		});

		if (!result) {
			throw AppError.create(
				"USER_UPDATE_FAILED",
				`Failed to set payment password for user ${user_id}`,
			);
		}
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function removePaymentPassword(
	user_id: number,
	client?: Prisma.TransactionClient,
): Promise<void> {
	const updateFn = async (tx: Prisma.TransactionClient) => {
		const result = await tx.users.update({
			where: { user_id },
			data: {
				payment_password_hash: null,
				updated_at: new Date(),
			},
		});

		if (!result) {
			throw AppError.create(
				"USER_UPDATE_FAILED",
				`Failed to remove payment password for user ${user_id}`,
			);
		}
	};

	return client ? updateFn(client) : withTransaction(updateFn);
}

export async function getAllContactInfo(): Promise<
	Array<{ email: string; phone_number: string }>
> {
	const users = await prisma.users.findMany({
		select: {
			phone_number: true,
			email: true,
		},
	});

	return users.map((user) => ({
		email: decrypt(user.email),
		phone_number: user.phone_number ? decrypt(user.phone_number) : "",
	}));
}

export async function getAllByApi(): Promise<UserInfo[]> {
	const users = await prisma.users.findMany({
		orderBy: { created_at: "asc" },
	});

	return users.map(transformUserData);
}
