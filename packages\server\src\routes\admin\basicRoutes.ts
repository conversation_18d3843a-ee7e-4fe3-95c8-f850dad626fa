import { Router } from "express";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import * as basicService from "@/services/admin/basicService.js";
import * as userModel from "@/models/user.js";
import * as positionService from "@/services/positionService.js";
import * as notificationService from "@/services/notifService.js";
import type {
	NotificationOptions,
	OrderStatus,
	UserFilters,
} from "@packages/shared";
import * as orderModel from "@/models/trade/order.js";
import * as inquiryService from "@/services/inquiryService.js";
import * as pendingOrderModel from "@/models/trade/pendingOrder.js";

const router = Router();

// Get provider discounts: GET /api/admin/basic/provider-discounts
router.get(
	"/provider-discounts",
	wrapAdminRoute(async (_, res) => {
		const { PROVIDER_DISCOUNT_CONFIG } = await import(
			"@/config/defaultParams.js"
		);
		res.status(200).json(PROVIDER_DISCOUNT_CONFIG);
	}),
);

// Get dashboard stats: GET /api/admin/basic/dashboard-stats
router.get(
	"/dashboard-stats",
	wrapAdminRoute(async (_, res) => {
		const stats = await basicService.getDashboardStats();
		res.status(200).json(stats);
	}),
);

// Get user profile: GET /api/admin/basic/user/user_id/:user_id
router.get(
	"/user/user_id/:user_id",
	wrapAdminRoute(async (req, res) => {
		const user_id = Number.parseInt(req.params.user_id);
		const result = await userModel.getAll({ filters: { user_id } });
		res.status(200).json(result.items[0]);
	}),
);

// Get user profile: GET /api/admin/basic/user/phone_number/:phone_number
router.get(
	"/user/phone_number/:phone_number",
	wrapAdminRoute(async (req, res) => {
		const phone_number = req.params.phone_number;
		const result = await userModel.findByPhoneNumber(phone_number);
		res.status(200).json(result);
	}),
);

// Get user profile: GET /api/admin/basic/user/email/:email
router.get(
	"/user/email/:email",
	wrapAdminRoute(async (req, res) => {
		const email = req.params.email;
		const result = await userModel.findByEmail(email);
		res.status(200).json(result);
	}),
);

// Get user profile: GET /api/admin/basic/user/username/:username
router.get(
	"/user/username/:username",
	wrapAdminRoute(async (req, res) => {
		const username = req.params.username;
		const result = await userModel.findByName(username);
		res.status(200).json(result);
	}),
);

// Get all users list: GET /api/admin/basic/users
router.get(
	"/users",
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder =
			(req.query.sortOrder as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";

		// 构建过滤条件
		const filters: UserFilters = {};

		if (req.query.user_id) {
			filters.user_id = Number.parseInt(req.query.user_id as string);
		}

		if (req.query.email) {
			filters.email = req.query.email as string;
		}

		if (req.query.phone_number) {
			filters.phone_number = req.query.phone_number as string;
		}

		if (req.query.name) {
			filters.name = req.query.name as string;
		}

		if (req.query.is_qualified !== undefined) {
			filters.is_qualified = req.query.is_qualified === "true";
		}

		const result = await userModel.getAll({
			page,
			pageSize,
			sortBy,
			sortOrder,
			filters,
		});

		res.status(200).json(result);
	}),
);

// Get orders for specific user: GET /api/admin/basic/orders/:user_id
router.get(
	"/orders/:user_id",
	wrapAdminRoute(async (req, res) => {
		const user_id = Number.parseInt(req.params.user_id);
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder =
			(req.query.sortOrder as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";

		// 构建筛选条件
		const filters: {
			user_id: number;
			status?: OrderStatus;
			startDate?: string;
			endDate?: string;
		} = { user_id };

		if (req.query.status && req.query.status !== "all") {
			filters.status = req.query.status as OrderStatus;
		}
		if (req.query.startDate) {
			filters.startDate = req.query.startDate as string;
		}
		if (req.query.endDate) {
			filters.endDate = req.query.endDate as string;
		}

		const result = await orderModel.getAll({
			page,
			pageSize,
			sortBy,
			sortOrder,
			filters,
		});
		res.status(200).json(result);
	}),
);

// Get all orders: GET /api/admin/basic/orders
router.get(
	"/orders",
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder =
			(req.query.sortOrder as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";

		// 构建筛选条件
		const filters: {
			status?: OrderStatus;
			startDate?: string;
			endDate?: string;
		} = {};
		if (req.query.status && req.query.status !== "all") {
			filters.status = req.query.status as OrderStatus;
		}
		if (req.query.startDate) {
			filters.startDate = req.query.startDate as string;
		}
		if (req.query.endDate) {
			filters.endDate = req.query.endDate as string;
		}

		const result = await orderModel.getAll({
			page,
			pageSize,
			sortBy,
			sortOrder,
			filters,
		});
		res.status(200).json(result);
	}),
);

// Get positions for specific user: GET /api/admin/basic/positions/:user_id
router.get(
	"/positions/:user_id",
	wrapAdminRoute(async (req, res) => {
		const user_id = Number.parseInt(req.params.user_id);
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const positions = await positionService.getUserPositionsByAdmin(
			user_id,
			page,
			pageSize,
		);
		res.status(200).json(positions);
	}),
);

// Get all positions: GET /api/admin/basic/positions
router.get(
	"/positions",
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const result = await positionService.getAllPositionsByAdmin(page, pageSize);
		res.status(200).json(result);
	}),
);

// Get all inquiries: GET /api/admin/basic/inquiries
router.get(
	"/inquiries",
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder =
			(req.query.sortOrder as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";

		const result = await inquiryService.getAdminInquiries({
			page,
			pageSize,
			sortBy,
			sortOrder,
		});
		res.status(200).json(result);
	}),
);

// Get inquiries for specific user: GET /api/admin/basic/inquiries/:user_id
router.get(
	"/inquiries/:user_id",
	wrapAdminRoute(async (req, res) => {
		const user_id = Number.parseInt(req.params.user_id);
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder =
			(req.query.sortOrder as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";

		const result = await inquiryService.getAdminInquiries({
			page,
			pageSize,
			sortBy,
			sortOrder,
			filters: { user_id },
		});
		res.status(200).json(result);
	}),
);

// Send system notification: POST /api/admin/basic/system
router.post(
	"/system",
	wrapAdminRoute<NotificationOptions>(async (req, res) => {
		const { title, content, type, metadata } = req.body;
		const notification = await notificationService.sendNotification(null, {
			title,
			content,
			type,
			metadata,
		});
		res.status(200).json(notification);
	}),
);

/**
 * Get all pending orders with filter: GET /api/trade/list/pending
 */
router.get(
	"/list/pending",
	wrapAdminRoute(async (req, res) => {
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;

		const types = req.query.types
			? (req.query.types as string).split(",")
			: undefined;
		const ts_code = req.query.ts_code as string;
		const user_id = req.query.user_id
			? Number.parseInt(req.query.user_id as string)
			: undefined;

		// 将类型字符串转换为OrderStatus数组
		const statusTypes = types?.map((type) => type as OrderStatus);

		const result = await pendingOrderModel.getPendingListForAdmin({
			page,
			pageSize,
			statusTypes,
			ts_code,
			user_id,
		});

		res.status(200).json(result);
	}),
);

export default router;
