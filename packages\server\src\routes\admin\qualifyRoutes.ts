import { Router } from "express";
import * as auditService from "@/services/admin/auditService.js";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import type { AuditStatus, UserInfo } from "@packages/shared";
import { AuditType } from "@packages/shared";
import * as auditModel from "@/models/audit.js";
import * as userService from "@/services/userService.js";

const router = Router();

// Get qualification audits: GET /api/admin/qualify/audits
router.get(
	"/audits",
	wrapAdminRoute(async (req, res) => {
		const status = req.query.status as AuditStatus;
		const user_id = Number.parseInt(req.query.user_id as string);
		const page = Number.parseInt(req.query.page as string) || 1;
		const pageSize = Number.parseInt(req.query.pageSize as string) || 10;
		const sortBy = req.query.sortBy as string;
		const sortOrder =
			(req.query.sortOrder as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";

		const result = await auditModel.getAll({
			page,
			pageSize,
			sortBy,
			sortOrder,
			filters: {
				status,
				type: AuditType.QUALIFICATION,
				user_id,
			},
		});

		res.status(200).json(result);
	}),
);

// Process qualification audits: POST /api/admin/qualify/audits
router.post(
	"/audits",
	wrapAdminRoute<{ audit_id: number; status: AuditStatus; comment?: string }>(
		async (req, res) => {
			const { audit_id, status, comment } = req.body;
			const admin_id = req.jwt.admin_id;

			const result = await auditService.processAudit(
				audit_id,
				admin_id,
				status,
				comment,
			);
			res.status(200).json(result);
		},
	),
);

// Update user profile: POST /api/admin/qualify/user-profile
router.post(
	"/user-profile",
	wrapAdminRoute<UserInfo>(async (req, res) => {
		const user = req.body;
		const result = await userService.updateUserProfile(user);
		res.status(200).json(result);
	}),
);

// Get user qualification audit: GET /api/admin/qualify/signature/:user_id
router.get(
	"/signature/:user_id",
	wrapAdminRoute(async (req, res) => {
		const user_id = Number.parseInt(req.params.user_id);
		const result =
			await auditModel.findLatestApprovedQualificationAudit(user_id);
		if (!result) {
			res.status(404).json({ error: "No approved qualification audit found" });
			return;
		}
		res.status(200).json(result.data.signature);
	}),
);
export default router;
