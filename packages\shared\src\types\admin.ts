import type { UserInfo } from "./user";

// 管理员权限
export const AdminPermission = {
	BASIC: "basic", // 基础权限：查看订单、持仓等数据
	QUALIFY: "qualify", // 资质审核权限：处理用户账户、身份等审核
	FINANCE: "finance", // 财务权限：处理用户出入金等资金相关审核
	CONFIG: "config", // 配置权限：修改系统配置
	ADMIN: "admin", // 管理员权限：管理其他管理员账号
	ORDER_MANAGE: "order_manage", // 订单增改权限：管理订单
} as const;

export type AdminPermission =
	(typeof AdminPermission)[keyof typeof AdminPermission];

// 权限说明文本
export const PERMISSION_DESCRIPTIONS: Record<AdminPermission, string> = {
	basic: "基础权限",
	qualify: "资质审核权限",
	finance: "出入金审核权限",
	config: "系统配置权限",
	admin: "管理员权限",
	order_manage: "订单增改权限",
} as const;

// 管理员数据
export interface AdminData {
	admin_id: number;
	username: string;
	password_hash: string;
	name: string;
	permissions: AdminPermission[];
	is_active: boolean;
	created_at: Date;
}

// 用户活动
export interface UserActivity {
	online: number; // 当前在线（未过期）
	daily: number; // 日活（24小时内）
	weekly: number; // 周活（7天内）
	monthly: number; // 月活（30天内）
}

export interface RedisMetrics {
	usedMemory: number; // Redis使用的内存
	peakMemory: number; // Redis峰值内存
	keyCount: number; // 键总数
	hitRate: number; // 缓存命中率
	connectedClients: number; // 当前连接数
	maxMemory: number; // 最大内存限制
}

// PostgreSQL数据库指标
export interface PostgresMetrics {
	activeConnections: number; // 当前活跃连接数
	maxConnections: number; // 最大连接数
	connectionUtilization: number; // 连接池使用率
	idleConnections: number; // 空闲连接数
	queryLatency: number; // 查询延迟(ms)
	databaseSize: number; // 数据库大小(bytes)
	transactionsPerSecond: number; // 每秒事务数
}

// API Token
export interface ApiTokenData {
	token: string;
	expires_at: string;
}

// API 请求/响应类型
export interface AdminLoginRequest {
	username: string;
	password: string;
}

export interface AdminLoginResponse {
	token: string;
	permissions: AdminPermission[];
}

export interface UpdatePasswordRequest {
	oldPassword: string;
	newPassword: string;
}

export interface CreateAdminRequest {
	username: string;
	password: string;
	permissions: AdminPermission[];
}

export interface DashboardStats {
	active_users: number; // 活跃用户数
	active_users_trend: number; // 活跃用户数趋势
	todays_orders: number; // 今日订单数
	todays_orders_trend: number; // 今日订单数趋势
	trading_volume: number; // 交易量
	trading_volume_trend: number; // 交易量趋势
	settle_volume: number; // 结算额
	settle_volume_trend: number; // 结算额趋势
	// 新增业务统计属性
	total_users: number;
	verified_users: number;
	verified_rate: number;
	total_inquiries: number;
	inquiry_conversion_rate: number;
	total_orders: number;
	completed_orders: number;
	completion_rate: number;
	total_trading_volume: number;
	average_order_value: number;
	deposit_total: number;
	withdrawal_total: number;
	profit_margin: number;
	// 新增用户活动统计属性
	online_users: number;
	daily_active_users: number;
	weekly_active_users: number;
	monthly_active_users: number;
}
