import { ENV } from "@/config/configManager.js";
import {
	getTurnoverAndVolume,
	fetchCurrentPrice,
} from "@/financeUtils/marketData.js";
import { calculateVWAP } from "@/financeUtils/calculator.js";
import { OrderStatus, NotificationType } from "@packages/shared";
import type { PendingOrderData, SellingOrderData } from "@packages/shared";
import * as ConfirmOrder from "./confirmOrder.js";
import * as PendingOrder from "@/models/trade/pendingOrder.js";
import { AppError } from "@/core/appError.js";
import logger from "@/utils/logger.js";
import * as NotifService from "@/services/notifService.js";
import redis from "@/config/redis.js";

const BASE_INTERVAL =
	ENV.NODE_ENV !== "development" ? 15 * 60 * 1000 : 60 * 1000; // 15 minutes

// 添加 Redis 相关常量
const VWAP_ORDER_PREFIX = "vwap_order:";
// 为减少对业务影响，预留10秒缓冲时间
const VWAP_EXPIRY = BASE_INTERVAL + 10 * 1000; // 15分10秒

interface VWAPOrderCache {
	pending_id: number;
	ts_code: string;
	start_time: number;
	initial_turnover: number;
	initial_volume: number;
}

class VWAPStrategy {
	private activeOrders = new Map<number, NodeJS.Timeout>();
	private isInitialized = false;

	private constructor() {}
	private static instance: VWAPStrategy;

	static getInstance(): VWAPStrategy {
		if (!VWAPStrategy.instance) {
			VWAPStrategy.instance = new VWAPStrategy();
		}
		return VWAPStrategy.instance;
	}

	async initialize(): Promise<void> {
		if (this.isInitialized) return;

		const isPrimaryProcess =
			ENV.NODE_ENV === "production" &&
			Number(process.env.pm_id) === ENV.PRIMARY_PROCESS_ID;

		if (!isPrimaryProcess) {
			logger.info("Skipping VWAP orders processing in non-primary process");
			this.isInitialized = true;
			return;
		}

		try {
			const pendingOrders = await PendingOrder.findAllVwapOrders();

			for (const order of pendingOrders) {
				const cacheKey = `${VWAP_ORDER_PREFIX}${order.pending_id}`;
				const cachedData = await redis.get(cacheKey);

				if (cachedData) {
					const cache: VWAPOrderCache = JSON.parse(cachedData);
					const now = Date.now();
					const timeLeft = cache.start_time + BASE_INTERVAL - now;

					if (timeLeft <= 0) {
						// 时间已经过了，但在10秒缓冲时间内，直接执行
						const finalData = await getTurnoverAndVolume(order.ts_code);
						const isSTAR = order.ts_code.split(".")[0].startsWith("68");
						const vwapPrice = calculateVWAP(
							{
								turnover: cache.initial_turnover,
								volume: cache.initial_volume,
							},
							finalData,
							isSTAR,
						);

						if (vwapPrice) {
							await this.executeVwapOrder(order, vwapPrice);
						} else {
							const { price } = await fetchCurrentPrice(order.ts_code);
							await this.executeVwapOrder(order, price);
						}
					} else {
						// 重新设置定时器
						this.execute(order, {
							startTime: cache.start_time,
							initialTurnover: cache.initial_turnover,
							initialVolume: cache.initial_volume,
						});
					}
				} else {
					// Redis 中没有缓存，关闭订单并通知用户
					await this.handleOrderFailure(order, {
						reason:
							"服务器重启后VWAP挂单已超过时限，为确保交易安全，挂单已自动关闭",
					});
				}
			}

			this.isInitialized = true;
			logger.info(
				`Processed ${pendingOrders.length} VWAP orders after restart`,
			);
		} catch (error) {
			logger.error(error, "Failed to process VWAP orders");
			throw error;
		}
	}

	async executeVwapOrder(
		order: PendingOrderData,
		vwapPrice: number,
	): Promise<void> {
		try {
			// 只有卖出订单需要验证价格条件
			if (order.status === OrderStatus.VWAP_SELLING) {
				const isCall = order.structure.endsWith("C");
				const isPriceValid = isCall
					? vwapPrice > order.exercise_price
					: vwapPrice < order.exercise_price;

				if (!isPriceValid) {
					// 如果价格条件不满足，作为失败处理
					await this.handleOrderFailure(order, {
						reason: isCall
							? "VWAP均价低于执行价，不满足看涨期权卖出条件"
							: "VWAP均价高于执行价，不满足看跌期权卖出条件",
						vwap_price: vwapPrice,
						exercise_price: order.exercise_price,
					});
					return;
				}
			}

			await ConfirmOrder.executeVwap(order, vwapPrice);
		} catch (error) {
			logger.error(error, `Failed to execute VWAP order ${order.pending_id}`);
			await this.handleOrderFailure(order, {
				reason: error instanceof AppError ? error.name : "",
			});
		}
	}

	async handleOrderFailure(
		order: PendingOrderData,
		details?: {
			reason: string;
			message?: string;
			vwap_price?: number;
			exercise_price?: number;
		},
	): Promise<void> {
		try {
			// 关闭订单
			await PendingOrder.close(order.pending_id);
			await PendingOrder.deletePendingOrder(order.pending_id);

			// 构建通知内容
			let notificationContent = `您的VWAP挂单 ${order.ts_code} 执行失败。`;
			if (details?.reason) {
				notificationContent += `\n原因：${details.reason}`;
			}
			if (details?.message) {
				notificationContent += `\n${details.message}`;
			}

			// 使用 NotifService 发送失败通知
			await NotifService.sendNotification(order.user_id, {
				title: "VWAP挂单执行失败",
				content: notificationContent,
				type: NotificationType.ORDER,
				metadata: {
					type: "pending_failed",
					trade_no: (order as SellingOrderData).trade_no,
					ts_code: order.ts_code,
					status: order.status,
					vwap_price: details?.vwap_price,
					exercise_price: details?.exercise_price,
				},
			});
		} catch (error) {
			logger.error(
				error,
				`Failed to handle order failure for ${order.pending_id}`,
			);
		}
	}

	async execute(
		pendingOrder: PendingOrderData,
		cache?: {
			startTime: number;
			initialTurnover: number;
			initialVolume: number;
		},
	): Promise<void> {
		let initialData: { turnover: number; volume: number };
		const startTime = cache?.startTime || Date.now();

		if (cache) {
			initialData = {
				turnover: cache.initialTurnover,
				volume: cache.initialVolume,
			};
		} else {
			initialData = await getTurnoverAndVolume(pendingOrder.ts_code);
			// 存储订单信息到 Redis
			const cacheData: VWAPOrderCache = {
				pending_id: pendingOrder.pending_id,
				ts_code: pendingOrder.ts_code,
				start_time: startTime,
				initial_turnover: initialData.turnover,
				initial_volume: initialData.volume,
			};

			await redis.setex(
				`${VWAP_ORDER_PREFIX}${pendingOrder.pending_id}`,
				Math.ceil(VWAP_EXPIRY / 1000), // Redis expiry in seconds
				JSON.stringify(cacheData),
			);
		}

		const timer = setTimeout(
			async () => {
				try {
					// 添加数据库状态检查
					const freshOrder = await PendingOrder.findById(
						pendingOrder.pending_id,
					);
					if (!freshOrder) {
						logger.info(
							`VWAP order ${pendingOrder.pending_id} no longer exists, skipping execution`,
						);
						this.activeOrders.delete(pendingOrder.pending_id);
						return;
					}

					const finalData = await getTurnoverAndVolume(pendingOrder.ts_code);
					// 68开头的是科创板（一个689，其余688）
					const isSTAR = pendingOrder.ts_code.split(".")[0].startsWith("68");
					const vwapPrice = calculateVWAP(initialData, finalData, isSTAR);
					if (vwapPrice) {
						await this.executeVwapOrder(pendingOrder, vwapPrice);
					} else {
						const { price } = await fetchCurrentPrice(pendingOrder.ts_code);
						await this.executeVwapOrder(pendingOrder, price);
					}
				} catch (error) {
					await this.handleOrderFailure(pendingOrder);
				} finally {
					// 清理 Redis 缓存和定时器
					await redis.del(`${VWAP_ORDER_PREFIX}${pendingOrder.pending_id}`);
					this.activeOrders.delete(pendingOrder.pending_id);
				}
			},
			cache ? BASE_INTERVAL - (Date.now() - startTime) : BASE_INTERVAL,
		);

		this.activeOrders.set(pendingOrder.pending_id, timer);
	}

	// 添加清理函数
	async cleanupOrder(orderId: number): Promise<void> {
		const timer = this.activeOrders.get(orderId);
		if (timer) {
			clearTimeout(timer);
			this.activeOrders.delete(orderId);
		}
		await PendingOrder.close(orderId);
		await PendingOrder.deletePendingOrder(orderId);
	}
}

export const vwapStrategy = VWAPStrategy.getInstance();
