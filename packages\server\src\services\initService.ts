import { ENV } from "@/config/configManager.js";
import * as Admin from "@/models/admins.js";
import { hashPassword } from "@/utils/encryption.js";
import logger from "@/utils/logger.js";
import { AdminPermission } from "@packages/shared";
import {
	APP_CONFIG,
	isTradingPlatform,
	isChannel,
} from "@/config/configManager.js";
import { TRADING_PLATFORMS } from "@/config/defaultParams.js";

async function initializeAdmin() {
	try {
		const superAdmin = await Admin.findByUsername("admin");
		if (!superAdmin) {
			logger.warn("No super admin found, creating default admin account");
			const username = ENV.ADMIN_USERNAME || "admin";
			const password = ENV.ADMIN_PASSWORD || "admin123";
			const passwordHash = await hashPassword(password);

			const permissions = Object.values(AdminPermission);

			await Admin.create(username, passwordHash, "Administrator", permissions);
			logger.info("Default admin account created");
			logger.warn(
				`Please change default admin password for account: ${username}`,
			);
		} else {
			logger.info("Admin account exists");
		}
	} catch (error) {
		logger.error(error, "Failed to initialize admin");
		throw error;
	}
}

/**
 * 验证平台ID和通道ID配置
 */
async function validatePlatformAndChannelConfig() {
	try {
		// 验证交易平台ID
		if (isTradingPlatform()) {
			const platformId = APP_CONFIG.tradingPlatformId;
			if (!(platformId in TRADING_PLATFORMS)) {
				logger.error(
					`Trading platform ID '${platformId}' is not defined in TRADING_PLATFORMS`,
				);
			} else {
				logger.info(`Validated trading platform ID: ${platformId}`);
			}
		}

		// 验证通道ID
		if (isChannel()) {
			const channelId = APP_CONFIG.channelId;
			let channelFound = false;

			// 遍历所有平台查找通道
			for (const platformId in TRADING_PLATFORMS) {
				const platform =
					TRADING_PLATFORMS[platformId as keyof typeof TRADING_PLATFORMS];
				if (
					platform.channels &&
					Object.keys(platform.channels).includes(channelId)
				) {
					channelFound = true;
					break;
				}
			}

			if (!channelFound) {
				logger.error(
					`Channel ID '${channelId}' is not defined in any platform's channels in TRADING_PLATFORMS`,
				);
			} else {
				logger.info(`Validated channel ID: ${channelId}`);
			}
		}
	} catch (error) {
		logger.error(
			error,
			"Failed to validate platform and channel configuration",
		);
	}
}

export async function initialize() {
	await initializeAdmin();
	await validatePlatformAndChannelConfig();
}
