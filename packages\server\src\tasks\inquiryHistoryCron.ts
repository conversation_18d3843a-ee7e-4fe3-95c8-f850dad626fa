import cron from "node-cron";
import * as InquiryModel from "@/models/inquiry.js";
import logger from "@/utils/logger.js";

export class InquiryHistoryCron {
	private static instance: InquiryHistoryCron;

	private constructor() {}

	public static getInstance(): InquiryHistoryCron {
		if (!InquiryHistoryCron.instance) {
			InquiryHistoryCron.instance = new InquiryHistoryCron();
		}
		return InquiryHistoryCron.instance;
	}

	public start(): void {
		try {
			// 每天凌晨00:00执行
			cron.schedule(
				"0 0 * * *",
				async () => {
					await this.clearInquiries();
				},
				{
					timezone: "Asia/Shanghai",
				},
			);

			logger.info("Inquiry cleanup cron job started");
		} catch (error) {
			logger.error(error, "Failed to start inquiry cleanup cron job");
			throw error;
		}
	}

	private async clearInquiries(): Promise<void> {
		try {
			const startTime = Date.now();
			await InquiryModel.deleteAll();
			const duration = Date.now() - startTime;

			logger.info(`Daily inquiry cleanup completed in ${duration}ms`);
		} catch (error) {
			logger.error(error, "Error during inquiry cleanup");
		}
	}
}

// 导出单例实例
export const inquiryHistoryCron = InquiryHistoryCron.getInstance();
