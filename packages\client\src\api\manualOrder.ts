import { request } from "./request";
import type { ManualOrderData, ManualOrderRequest } from "@packages/shared";

export const manualOrderApi = {
	createManualOrder: (data: ManualOrderRequest) =>
		request.post("/manual-order", data),

	getManualOrders: (
		page: number,
		pageSize: number,
		isDescending = true,
		filters?: {
			ts_codes?: string[];
			status?: string[];
			startDate?: string;
			endDate?: string;
		},
	) =>
		request.get<{
			items: ManualOrderData[];
			total: number;
			ts_codes: string[];
		}>("/manual-order/list", {
			params: {
				page,
				pageSize,
				isDescending,
				...filters,
				ts_codes: filters?.ts_codes?.join(","),
				status: filters?.status?.join(","),
			},
		}),

	updateManualOrder: (id: number, data: Partial<ManualOrderData>) =>
		request.put(`/manual-order/${id}`, data),

	deleteManualOrder: (id: number) => request.delete(`/manual-order/${id}`),
} as const;

export default manualOrderApi;
