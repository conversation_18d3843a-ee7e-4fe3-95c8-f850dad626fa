import cron from "node-cron";
import {
	syncStockList,
	syncDividend,
	syncUpDownLimit,
	syncTradeCalendar,
	syncDailyData,
	syncAllData,
	syncSuspension,
} from "../services/dataSync.js";
import { ENV } from "@/config/configManager.js";
import logger from "@/utils/logger.js";

export class StockDataCron {
	private static instance: StockDataCron;

	// typescript will auto privatize constructor

	public static getInstance(): StockDataCron {
		if (!StockDataCron.instance) {
			StockDataCron.instance = new StockDataCron();
		}
		return StockDataCron.instance;
	}

	public async start(): Promise<void> {
		if (!ENV.PRIMARY_APPLICATION) {
			logger.info("Not primary application, skipping stock data sync");
			return;
		}

		try {
			// 初始化数据
			await syncAllData();

			// 0:01 更新股票列表
			cron.schedule(
				"1 0 * * *",
				async () => {
					await syncStockList();
				},
				{ timezone: "Asia/Shanghai" },
			);

			// 0:30 更新交易日历
			cron.schedule(
				"30 0 * * *",
				async () => {
					await syncTradeCalendar();
				},
				{ timezone: "Asia/Shanghai" },
			);

			// 0:30 更新昨日开始的每日收盘数据
			cron.schedule(
				"30 0 * * *",
				async () => {
					await syncDailyData();
				},
				{ timezone: "Asia/Shanghai" },
			);

			// 9:00 更新涨跌停限制
			cron.schedule(
				"0 9 * * *",
				async () => {
					await syncUpDownLimit();
				},
				{ timezone: "Asia/Shanghai" },
			);

			// 9:00 更新除权除息
			cron.schedule(
				"0 9 * * *",
				async () => {
					await syncDividend();
				},
				{ timezone: "Asia/Shanghai" },
			);

			// 9:30 更新停复牌数据
			cron.schedule(
				"30 9 * * *",
				async () => {
					await syncSuspension();
				},
				{ timezone: "Asia/Shanghai" },
			);

			logger.info("Stock data cron jobs started");
		} catch (error) {
			logger.error(error, "Failed to start stock data cron jobs");
			throw error;
		}
	}
}

// 导出单例实例
export const stockDataCron = StockDataCron.getInstance();
