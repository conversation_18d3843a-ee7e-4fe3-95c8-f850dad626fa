<template>
  <div class="inquiries-view view">
    <div class="header-container">
      <h2>询价记录</h2>
      <div class="controls">
        <el-button :loading="exporting" @click="exportData">
          <el-icon>
            <Download />
          </el-icon>
          导出数据
        </el-button>
      </div>
    </div>
    <div class="search-bar">
      <el-input v-model="searchQuery" placeholder="输入用户ID搜索" class="search-input" clearable
        @keydown.enter="handleSearch" @clear="handleClear">
        <template #prefix>
          <el-icon>
            <Search />
          </el-icon>
        </template>
      </el-input>
    </div>

    <el-table :data="inquiries" style="width: 100%" @sort-change="handleSortChange">
      <el-table-column prop="inquiry_id" label="ID" min-width="60" sortable="custom" />
      <el-table-column prop="user_id" label="用户ID" min-width="80" sortable="custom" />
      <el-table-column prop="ts_code" label="标的" min-width="100" sortable="custom">
        <template #default="{ row }">
          {{ row.ts_code }}
        </template>
      </el-table-column>
      <el-table-column prop="scale" label="规模" min-width="80" sortable="custom">
        <template #default="{ row }">
          {{ row.scale }}万
        </template>
      </el-table-column>
      <el-table-column prop="structure" label="结构" min-width="80" sortable="custom">
        <template #default="{ row }">
          {{ formatStructure(row.structure) }}
        </template>
      </el-table-column>
      <el-table-column prop="term" label="期限" min-width="80" sortable="custom">
        <template #default="{ row }">
          {{ row.term === 14 ? '2周' : row.term + '个月' }}
        </template>
      </el-table-column>
      <el-table-column prop="quote" label="基准报价" min-width="100" sortable="custom">
        <template #default="{ row }">
          <div class="quote-group">
            <el-tag type="success" v-if="row.quote">{{ row.quote }}%</el-tag>
            <span v-else>待报价</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="external_quotes" label="外部报价" min-width="140">
        <template #default="{ row }">
          <div v-if="row.external_quotes && Object.keys(row.external_quotes).length > 0" class="external-quotes">
            <el-popover placement="right" trigger="click" width="300">
              <template #reference>
                <el-button type="info" size="small">查看外部报价</el-button>
              </template>
              <div class="external-quotes-container">
                <div v-for="(quote, provider) in row.external_quotes" :key="provider" class="external-quote-item">
                  <span class="provider-name">{{ formatProvider(String(provider)) }}</span>
                  <div class="quote-info">
                    <span class="provider-quote" v-if="quote !== null">{{ getCalculatedQuote(quote,
                      row.quote_diffs?.[provider])
                    }}%</span>
                    <span class="provider-quote no-quote" v-else>无报价</span>
                    <span class="quote-diff"
                      v-if="row.quote_diffs?.[provider] !== null && row.quote_diffs?.[provider] !== undefined">
                      ({{ row.quote_diffs[provider] >= 0 ? '+' : '' }}{{ row.quote_diffs[provider] }}%)
                    </span>
                  </div>
                </div>
              </div>
            </el-popover>
          </div>
          <span v-else class="no-external-quotes">无外部报价</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" min-width="80" sortable="custom">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ formatInquiryStatus(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="时间" min-width="160" sortable="custom">
        <template #default="{ row }">
          <div class="time-group">
            <div>创建: {{ formatDate(row.created_at) }}</div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        :total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { Search, Download } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { basicApi } from "@/api";
import { InquiryStatus, PriceProviderNames } from "@packages/shared";
import type { InquiryData } from "@packages/shared";
import { formatDate, formatStructure } from "@/utils/format";
import { exportToCsv } from "@/utils/export";
import { useSiteConfigStore } from "@/stores/siteConfig";

const siteConfigStore = useSiteConfigStore();

const searchQuery = ref("");
const inquiries = ref<InquiryData[]>([]);

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const sortBy = ref("created_at");
const sortOrder = ref<"DESC" | "ASC" | undefined>("DESC");

const statusTypes: Record<InquiryStatus, string> = {
  [InquiryStatus.PROCESSING]: "success",
  [InquiryStatus.APPROVED]: "warning",
  [InquiryStatus.REJECTED]: "danger",
  [InquiryStatus.EXPIRED]: "info",
};

const getStatusType = (status: InquiryStatus) => statusTypes[status] || "info";
const formatInquiryStatus = (status: InquiryStatus) => {
  const statusMap: Record<InquiryStatus, string> = {
    [InquiryStatus.PROCESSING]: "生效中",
    [InquiryStatus.APPROVED]: "已使用",
    [InquiryStatus.REJECTED]: "已拒绝",
    [InquiryStatus.EXPIRED]: "已过期",
  };
  return statusMap[status] || status;
};

const exporting = ref(false);

const exportData = async () => {
  exporting.value = true;
  try {
    const response = await basicApi.getInquiries(1, 999999, {
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
    });

    const headers = [
      "询价ID",
      "用户ID",
      "标的",
      "规模",
      "结构",
      "期限",
      "原始报价",
      "最终报价",
      "状态",
      "创建时间",
      "外部报价",
    ];
    const csvData =
      response?.items.map((item) => {
        // 格式化外部报价
        let externalQuotesText = "";
        if (
          item.external_quotes &&
          Object.keys(item.external_quotes).length > 0
        ) {
          // 使用显式类型断言和转换
          const externalQuotes = item.external_quotes as Record<
            string,
            number | null
          >;
          externalQuotesText = Object.entries(externalQuotes)
            .map(([provider, quote]) => {
              // 显式转换为字符串
              if (quote !== null) {
                const finalQuote = getCalculatedQuote(quote, item.quote_diffs?.[provider]);
                return `${formatProvider(String(provider))}: ${finalQuote}%`;
              }
              return `${formatProvider(String(provider))}: 无报价`;
            })
            .join("; ");
        }

        return [
          item.inquiry_id,
          item.user_id,
          item.ts_code,
          `${item.scale}万`,
          formatStructure(item.structure),
          item.term === 14 ? "2周" : `${item.term}个月`,
          item.quote ? `${item.quote}%` : "",
          formatInquiryStatus(item.status),
          formatDate(item.created_at),
          externalQuotesText,
        ];
      }) || [];

    exportToCsv(headers, csvData, "inquiries");
    ElMessage.success("导出成功");
  } catch (error) {
    console.error("Export failed:", error);
    ElMessage.error("导出失败");
  } finally {
    exporting.value = false;
  }
};

const loadData = async (userId?: number) => {
  try {
    const response = await (userId
      ? basicApi.getUserInquiries(userId, currentPage.value, pageSize.value, {
        sortBy: sortBy.value,
        sortOrder: sortOrder.value as "ASC" | "DESC",
      })
      : basicApi.getInquiries(currentPage.value, pageSize.value, {
        sortBy: sortBy.value,
        sortOrder: sortOrder.value as "ASC" | "DESC",
      }));

    inquiries.value = response?.items || [];
    total.value = response?.total || 0;
  } catch (error) {
    console.error("Failed to fetch data:", error);
    ElMessage.error("获取数据失败");
  }
};

const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    await loadData();
    return;
  }
  const userId = Number.parseInt(searchQuery.value);
  if (Number.isNaN(userId)) {
    ElMessage.warning("请输入有效的用户ID");
    return;
  }
  await loadData(userId);
};

const handleClear = () => {
  loadData();
};

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  loadData(Number.parseInt(searchQuery.value) || undefined);
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  loadData(Number.parseInt(searchQuery.value) || undefined);
};

const handleSortChange = ({
  prop,
  order,
}: { prop?: string; order?: string }) => {
  sortBy.value = prop || "created_at";
  sortOrder.value = order === "ascending" ? "ASC" : "DESC";
  loadData(Number.parseInt(searchQuery.value) || undefined);
};

const formatProvider = (provider: string) => {
  if (provider === "INK") return siteConfigStore.shortName();

  return (
    PriceProviderNames[provider as keyof typeof PriceProviderNames] || provider
  );
};

const getCalculatedQuote = (baseQuote: number, quoteDiff: number | null | undefined) => {
  // 如果quoteDiff为null或undefined，只返回baseQuote
  if (quoteDiff === null || quoteDiff === undefined) {
    return baseQuote;
  }
  return baseQuote + quoteDiff;
};

onMounted(() => loadData());
</script>

<style scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

h2 {
  margin: 0;
}

.search-input {
  width: 300px;
}

.price-group,
.time-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quote-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

:deep(.el-table) {
  --el-table-bg-color: var(--el-bg-color-overlay);
  --el-table-tr-bg-color: var(--el-bg-color-overlay);
  --el-table-header-bg-color: var(--el-bg-color-overlay);
  --el-table-border-color: var(--el-border-color-lighter);

  background-color: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-overlay);
  color: var(--el-text-color-regular);
}

:deep(.el-table td) {
  color: var(--el-text-color-primary);
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: var(--el-table-row-hover-bg-color);
}

:deep(.el-input__wrapper) {
  background-color: var(--el-bg-color-overlay);
}

:deep(.el-input__inner) {
  color: var(--el-text-color-primary);
}

:deep(.el-input__prefix-icon) {
  color: var(--el-text-color-placeholder);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.external-quotes-container {
  max-height: 300px;
  overflow-y: auto;
}

.external-quote-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.external-quote-item:last-child {
  border-bottom: none;
}

.provider-name {
  font-weight: 500;
}

.quote-info {
  display: flex;
  gap: 4px;
  align-items: center;
}

.provider-quote {
  color: var(--el-color-success);
}

.provider-quote.no-quote {
  color: var(--el-text-color-secondary);
}

.quote-diff {
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.no-external-quotes {
  color: var(--el-text-color-secondary);
  font-size: 13px;
}
</style>