#ink #deploy #server 
# ink 项目部署指南

### 1. 解析域名
- 在域名服务商处添加A记录：
  - `test.ink.hk.cn` → 服务器IP
  - `admin.test.ink.hk.cn` → 服务器IP
- 等待DNS生效（通常需要5-10分钟）
- 验证解析是否成功：
```bash
nslookup ink.hk.cn
nslookup www.ink.hk.cn
nslookup admin.ink.hk.cn
```

### 2. certbot生成证书
```bash
sudo certbot certonly --nginx -d ink.hk.cn -d www.ink.hk.cn -d admin.ink.hk.cn
```
- 确保已安装nginx和certbot
- 证书默认存储在`/etc/letsencrypt/live/ink.hk.cn/`
- 测试自动续期：
```bash
sudo certbot renew --dry-run
```

### 3. 克隆远程仓库

- 创建目录：`mkdir ink-test`
- 修改目录所有者：`sudo chown admin:admin ink-test`
- 调整目录权限：`sudo chmod -R 755 ink-test`

```bash
<NAME_EMAIL>:jk278/ink-dev.git ink-test
```
- 确保服务器已配置SSH key
- 指定分支克隆：`git clone -b <分支名> <仓库地址> [本地目录名]`
- ~~切换分支：`git checkout -b branch-name origin/branch-name`~~

### 4. 创建 [[Nginx Config|Nginx 配置]]

注意**端口**和文件路径与实际一致！

- 启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/ink.conf /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl restart nginx
```

### 5. 新建数据库
- 创建Postgresql数据库：
```sql
CREATE DATABASE ink_prod
```

### 6. 配置环境变量
```ini
PRIMARY_PROCESS_ID
PRIMARY_APPLICATION
REDIS_DB
PORT
TRADING_PLATFORM_ID
CHANNEL_ID
DATABASE_URL
PLATFORM_DATABASE_URL
# 其他必要变量...
```

### 7. 安装依赖包
```shell
pnpm -r install
```

### 8. [[Prisma 数据库迁移|prisma]]迁移数据库
```bash
pnpm prisma migrate dev --name init
pnpm prisma generate
```
- 检查迁移文件是否完整
- 添加初始化序列
```sql
ALTER SEQUENCE users_user_id_seq RESTART WITH 10001;
INSERT INTO stocks (ts_code, scale) VALUES ('all', 0);
```
- 删除 prisma/migrations 目录避免影响下次部署

### 9. 构建项目
```shell
pnpm build
```

### 10. 运行pm2进程
```bash
APP_NAME=ink-server pm2 start ecosystem.config.cjs
pm2 save
pm2 startup
```
