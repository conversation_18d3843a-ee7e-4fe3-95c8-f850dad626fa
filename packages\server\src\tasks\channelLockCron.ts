import * as cron from "node-cron";
import * as ChannelFund from "@/models/channelFund.js";
import logger from "@/utils/logger.js";
import { isTradingPlatform } from "@/config/configManager.js";

export class ChannelLockCron {
	private static instance: ChannelLockCron;

	// 私有构造函数，确保单例模式
	private constructor() {}

	/**
	 * 获取单例实例
	 */
	public static getInstance(): ChannelLockCron {
		if (!ChannelLockCron.instance) {
			ChannelLockCron.instance = new ChannelLockCron();
		}
		return ChannelLockCron.instance;
	}

	/**
	 * 启动定时任务
	 */
	public start(): void {
		// 交易台才需要设置资金锁定相关任务
		if (!isTradingPlatform()) {
			logger.info("非交易台，跳过通道锁定定时任务");
			return;
		}

		try {
			// 每日零点检查通道余额，如有负值则锁定
			cron.schedule("0 0 * * *", async () => {
				logger.info("启动定时任务: 检查通道负余额并锁定");
				try {
					const result = await ChannelFund.checkAndLockNegativeBalances();
					logger.info(
						{
							locked: result.locked,
							notLocked: result.notLocked,
						},
						"通道余额检查完成",
					);

					// 如果有被锁定的通道，记录警告
					if (result.locked.length > 0) {
						logger.warn(
							`${result.locked.length}个通道因负余额被锁定: ${result.locked.join(", ")}`,
						);
					}
				} catch (error) {
					logger.error(error, "执行通道余额检查定时任务失败");
				}
			});

			logger.info("通道锁定定时任务初始化完成");
		} catch (error) {
			logger.error(error, "启动通道锁定定时任务失败");
			throw error;
		}
	}
}

// 导出单例实例
export const channelLockCron = ChannelLockCron.getInstance();
