import { computed, ref } from "vue";
import router from "@/router";
import { defineStore } from "pinia";
import { jwtDecode } from "jwt-decode";
import type { AdminJWTPayload, AdminLoginRequest } from "@packages/shared";
import { authApi, type AuthResponse } from "@/api";
import { updateAuthHeader } from "@/plugins/axios";
import type { AxiosResponse } from "axios";

export const useAuthStore = defineStore("auth", () => {
	const token = ref<string | null>(localStorage.getItem("admin_token"));

	// 初始化时设置 token
	if (token.value) {
		updateAuthHeader(`Bearer ${token.value}`);
	}

	const isLoggedIn = computed(() => !!token.value);
	const adminId = computed(() => {
		if (!token.value) return null;
		try {
			const decoded = jwtDecode<AdminJWTPayload>(token.value);
			return decoded.admin_id;
		} catch {
			return null;
		}
	});
	const permissions = computed(() => {
		if (!token.value) return [];
		try {
			const decoded = jwtDecode<AdminJWTPayload>(token.value);
			return decoded.permissions || [];
		} catch {
			return [];
		}
	});

	const app_type = computed(() => {
		if (!token.value) return null;
		try {
			const decoded = jwtDecode<AdminJWTPayload>(token.value);
			return decoded.app_type;
		} catch {
			return null;
		}
	});

	function setToken(res: AxiosResponse<AuthResponse>) {
		const authHeader = res.headers?.authorization;
		if (authHeader?.startsWith("Bearer ")) {
			const newToken = authHeader.slice(7); // 提取纯 token
			localStorage.setItem("admin_token", newToken);
			token.value = newToken;
			updateAuthHeader(authHeader); // axios 使用完整认证头
		}
	}

	function clearToken() {
		localStorage.removeItem("admin_token");
		token.value = null;
		updateAuthHeader(null); // 清除 axios header
	}

	async function refreshToken() {
		const res = await authApi.refreshToken();
		setToken(res);
	}

	async function login(credentials: AdminLoginRequest) {
		const res = await authApi.login(credentials);
		setToken(res);
		router.push("/");
	}

	async function logout() {
		await authApi.logout();
		clearToken();
		router.push("/auth");
	}

	return {
		token,
		adminId,
		permissions,
		app_type,
		isLoggedIn,
		login,
		logout,
		refreshToken,
	};
});
