<template>
  <div class="auth-page">
    <div class="auth-container">
      <el-card class="dark-card">
        <template #header>
          <div class="card-header">
            <span>管理员登录</span>
          </div>
        </template>

        <el-form ref="loginForm" :model="formData" :rules="loginRules" :label-width="isMobile ? '70' : '100'" @keyup.enter="handleLogin"
          :validate-on-rule-change="false">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="formData.username" placeholder="请输入用户名" :prefix-icon="User" />
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input v-model="formData.password" type="password" show-password placeholder="请输入密码"
              :prefix-icon="Lock" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleLogin" :loading="loading" class="login-button">
              登录
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useAuthStore } from "@/stores/auth";
import { ElMessage } from "element-plus";
import { User, Lock } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import type { LoginParams } from "@/api";
import { getErrorMessage } from "@packages/shared";
const loading = ref(false);
const loginForm = ref<FormInstance>();

const isMobile = ref(window.innerWidth < 768);

const formData = ref({
	username: "",
	password: "",
});

const loginRules = {
	username: [{ required: true, trigger: "blur", message: "请输入用户名" }],
	password: [{ required: true, trigger: "blur", message: "请输入密码" }],
};

const handleLogin = async () => {
	if (!loginForm.value) return;

	try {
		await loginForm.value.validate();
		loading.value = true;

		const loginData: LoginParams = {
			username: formData.value.username,
			password: formData.value.password,
		};
		await useAuthStore().login(loginData);
		ElMessage.success("登录成功");
	} catch (error) {
		console.error("Login failed:", error);
		ElMessage.error(`登录失败：${getErrorMessage(error)}`);
	} finally {
		loading.value = false;
	}
};
</script>

<style scoped>
.auth-page {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--el-bg-color);
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 500px;
}

.dark-card {
  background: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
  border: 1px solid var(--el-border-color);
  box-shadow: var(--el-box-shadow-light);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-form-item__label) {
  color: var(--el-text-color-primary) !important;
}

:deep(.el-input__wrapper) {
  background-color: var(--el-input-bg-color) !important;
  box-shadow: none !important;
  border: 1px solid var(--el-border-color);
}

:deep(.el-input__wrapper:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-input__inner) {
  color: var(--el-text-color-primary) !important;
}

:deep(.el-input__prefix-icon) {
  color: var(--el-text-color-placeholder);
}

.login-button {
  width: 100%;
  margin-top: 10px;
}

:deep(.el-card__header) {
  border-bottom: 1px solid var(--el-border-color);
}

:deep(.el-button--primary) {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

:deep(.el-button--primary:hover) {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
}
</style>
