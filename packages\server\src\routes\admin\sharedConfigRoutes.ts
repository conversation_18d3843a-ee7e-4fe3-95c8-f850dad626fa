import { Router } from "express";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import * as sharedConfigService from "@/services/admin/sharedConfigService.js";
import type { SharedConfig } from "@packages/shared";
import { isChannel } from "@/config/configManager.js";
import { AppError } from "@/core/appError.js";

const router = Router();

// 获取共享配置: GET /api/admin/shared-config
router.get(
	"/",
	wrapAdminRoute(async (_, res) => {
		const config = await sharedConfigService.getSharedConfig();
		res.status(200).json(config);
	}),
);

// 更新共享配置: POST /api/admin/shared-config
router.post(
	"/",
	wrapAdminRoute<SharedConfig>(async (req, res) => {
		// 通道环境不能修改共享配置
		if (isChannel()) {
			throw AppError.create("FORBIDDEN", "通道环境不能修改共享配置");
		}

		const result = await sharedConfigService.updateSharedConfig(
			req.body,
			req.jwt.admin_id,
		);
		res.status(200).json(result);
	}),
);

export default router;
