# Cursor 规则限制与应对方案 (v0.49.5)

本文档概述了在 Cursor v0.49.5 版本中观察到的项目规则（`.cursor/rules/*.mdc` 文件）的限制，以及本项目当前采用的应对策略。

## 观察到的问题

1.  **元数据字段互斥:** 在单个 `.mdc` 文件的 YAML 前置元数据块 (`--- ... ---`) 中同时设置多个激活机制相关的字段（例如 `description`, `globs`, `alwaysApply: true`）时，似乎会导致问题。规则可能无法正确加载或按预期工作。实际上，似乎一次只能有一个主要的激活机制字段（`description` 用于 Agent Requested 类型，`globs` 用于 Auto Attached 类型，`alwaysApply` 用于 Always 类型）能有效工作。
2.  **Agent 编辑 Bug:** AI Agent 可能无法通过其工具可靠地修改 `.mdc` 文件的元数据。为了确保准确性，需要手动编辑。
3.  **元数据块限制:** 在单个 `.mdc` 文件中包含多个 YAML 前置元数据块 (`--- ... ---`) 会导致规则文件无法正常加载或出错。
4.  **`globs` 格式要求:** `globs` 字段需要遵循特定格式：
    *   多个路径模式必须用逗号 `,` 分隔。
    *   逗号后面**不能**有空格。
    *   路径模式本身**不能**被引号包裹。

## 当前采用的应对策略

基于以上限制，并且为了让规则能够自动应用于代码库的特定部分：

*   我们在 `.mdc` 规则文件的元数据块中**主要使用 `globs` 字段**来定义规则的应用范围。
*   `description` 和 `alwaysApply` 字段已被移除或留空。
*   严格遵循 `globs` 的格式要求。

**正确格式示例:**
```yaml
---
# 单个路径
globs: packages/server/src/**
---

## 后端规范...
```

```yaml
---
# 多个路径，注意逗号后无空格，路径无引号
globs: packages/client/src/**,packages/admin/src/**
---

## 前端规范...
```

## 潜在影响

*   **激活:** 现在规则应该能更可靠地在编辑或引用匹配其 `globs` 模式的文件时自动激活。
*   **AI 判断:** 由于明确的 `description` 字段（用于 Agent Requested 类型）缺失，AI 在判断是否应用规则时，可能不会像预期那样有效地利用规则的描述信息（尽管规则内容本身可以提供一些线索）。规则的激活主要由文件路径驱动。

## 建议

*   **手动编辑元数据:** 在 Agent 编辑 Bug 修复之前，如果需要更改 `.mdc` 文件的元数据，请手动进行编辑。
*   **关注 Cursor 更新:** 留意 Cursor 的未来版本。如果与元数据字段相关的 Bug 被修复，我们可以重新评估此策略，并可能重新引入 `description` 字段与 `globs` 同时使用，以更好地符合 `Auto Attached` 规则类型的完整设计，允许 AI 同时使用路径和描述来判断相关性。 