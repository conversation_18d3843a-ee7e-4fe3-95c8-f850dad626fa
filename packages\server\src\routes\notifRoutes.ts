import { Router } from "express";
import { wrapUserRoute } from "@/utils/routeWrapper.js";
import * as notificationModel from "@/models/notification.js";
import logger from "@/utils/logger.js";

const router = Router();

// 获取通知列表
router.get(
	"/",
	wrapUserRoute(async (req, res) => {
		const { limit = 20, offset = 0 } = req.query;
		const { notifications, total } =
			await notificationModel.getUserNotifications(req.jwt.user_id, {
				limit: Number(limit),
				offset: Number(offset),
			});
		res.json({ notifications, total });
	}),
);

// 获取未读通知数量
router.get(
	"/unread-count",
	wrapUserRoute(async (req, res) => {
		try {
			const count = await notificationModel.getUnreadCount(req.jwt.user_id);

			res.json(count);
		} catch (error) {
			logger.error(
				`Error getting unread notifications count: ${
					error instanceof Error ? error.message : String(error)
				}`,
			);
			throw error;
		}
	}),
);

// 标记通知为已读
router.post(
	"/:notif_id/read",
	wrapUserRoute(async (req, res) => {
		const { notif_id } = req.params;
		await notificationModel.markAsRead(Number(notif_id), req.jwt.user_id);
		res.json({ success: true });
	}),
);

// 标记所有通知为已读
router.post(
	"/read-all",
	wrapUserRoute(async (req, res) => {
		await notificationModel.markAllAsRead(req.jwt.user_id);
		res.json({ success: true });
	}),
);

export default router;
