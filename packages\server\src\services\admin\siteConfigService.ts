import { appRedis } from "@/lib/redis.js";
import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";
import * as SiteConfigModel from "@/models/siteConfig.js";
import { isChannel } from "@/config/configManager.js";
import type { SiteConfig } from "@packages/shared";
import prisma from "@/lib/prisma.js";
import fs from "node:fs/promises";
import path from "node:path";

// 确定server目录路径
const cwd = process.cwd();
const serverDir = cwd.endsWith("server")
	? cwd
	: path.join(cwd, "packages", "server");

const SITE_CONFIG_CACHE_KEY = "config:site_config";
const SITE_CONFIG_CACHE_TTL = 3600; // 1小时缓存

/**
 * 设置网站配置缓存
 */
async function setSiteConfigCache(siteConfig: SiteConfig): Promise<void> {
	try {
		await appRedis.setex(
			SITE_CONFIG_CACHE_KEY,
			SITE_CONFIG_CACHE_TTL,
			JSON.stringify(siteConfig),
		);
	} catch (error) {
		logger.error(error, "Failed to set site config cache");
		throw AppError.create(
			"CONFIG_CACHE_FAILED",
			"Failed to cache site configuration",
		);
	}
}

/**
 * 创建并保存默认图标
 * @returns 创建的资源ID
 */
async function createDefaultIcon(
	type: "favicon" | "logo",
	admin_id?: number,
): Promise<string> {
	try {
		// 生成资源ID
		const assetId = `${type}_default_${Date.now()}`;

		// 读取默认图标文件
		const defaultIconPath = path.join(
			serverDir,
			"public",
			type === "favicon" ? "favicon" : "logo",
			"default.svg",
		);

		logger.info(`Loading default ${type} from: ${defaultIconPath}`);

		const fileContent = await fs.readFile(defaultIconPath);
		const fileStats = await fs.stat(defaultIconPath);

		// 存储到数据库 (使用 Prisma Client API)
		await prisma.assets.create({
			data: {
				asset_id: assetId,
				content: fileContent,
				mime_type: "image/svg+xml",
				file_size: fileStats.size,
				type: type,
				admin_id: admin_id,
			},
		});

		// 存入Redis缓存
		const cacheKey = `asset:${assetId}`;
		await appRedis.set(cacheKey, fileContent);
		await appRedis.expire(cacheKey, 86400); // 24小时缓存

		// 缓存MIME类型
		await appRedis.set(`${cacheKey}:mime`, "image/svg+xml");
		await appRedis.expire(`${cacheKey}:mime`, 86400);

		return assetId;
	} catch (error) {
		logger.error(error, `Failed to create default ${type}`);
		throw AppError.create(
			"CONFIG_UPDATE_FAILED",
			`Failed to create default ${type}`,
		);
	}
}

/**
 * 创建默认网站配置
 */
async function createDefaultSiteConfig(admin_id?: number): Promise<SiteConfig> {
	try {
		// 创建默认图标
		const faviconId = await createDefaultIcon("favicon", admin_id);
		const logoId = await createDefaultIcon("logo", admin_id);

		// 根据应用类型设置默认名称
		const isChannelApp = isChannel();
		const defaultConfig: SiteConfig = {
			companyLegalName: isChannelApp ? "默认通道公司" : "默认交易平台",
			companyShortName: "OPTION",
			clientSiteName: isChannelApp ? "通道交易系统" : "交易平台",
			adminSiteName: isChannelApp ? "通道管理系统" : "交易平台管理系统",
			faviconId: faviconId,
			logoId: logoId,
			clientPrimaryColor: "#e88234", // 用户端默认橙色
			adminPrimaryColor: "#409eff", // 管理端默认蓝色
		};

		// 保存到数据库
		await SiteConfigModel.saveSiteConfig(defaultConfig, admin_id);

		return defaultConfig;
	} catch (error) {
		logger.error(error, "Failed to create default site config");
		throw AppError.create(
			"CONFIG_UPDATE_FAILED",
			"Failed to create default site configuration",
		);
	}
}

/**
 * 获取网站配置信息
 */
export async function getSiteConfig(): Promise<SiteConfig> {
	try {
		const cached = await appRedis.get(SITE_CONFIG_CACHE_KEY);
		if (cached) {
			return JSON.parse(cached);
		}

		// 从数据库获取配置
		const siteConfig = await SiteConfigModel.getSiteConfig();

		if (siteConfig) {
			await setSiteConfigCache(siteConfig);
			return siteConfig;
		}

		// 如果数据库中不存在配置，创建默认配置
		logger.info("No site configuration found, creating default configuration");
		const defaultConfig = await createDefaultSiteConfig();

		await setSiteConfigCache(defaultConfig);
		return defaultConfig;
	} catch (error) {
		logger.error(error, "Failed to get site config");
		throw AppError.create(
			"CONFIG_NOT_FOUND",
			"Failed to get site configuration",
		);
	}
}

/**
 * 更新网站配置信息
 */
export async function updateSiteConfig(
	updates: Partial<SiteConfig>,
	admin_id?: number,
) {
	try {
		const currentConfig = await getSiteConfig();

		// 合并更新
		const newConfig: SiteConfig = {
			...currentConfig,
			...updates,
		};

		await SiteConfigModel.saveSiteConfig(newConfig, admin_id);

		// 清除缓存
		await appRedis.del(SITE_CONFIG_CACHE_KEY);

		return { message: "Site configuration updated successfully" };
	} catch (error) {
		logger.error(error, "Failed to update site config");
		throw AppError.create(
			"CONFIG_UPDATE_FAILED",
			"Failed to update site configuration",
		);
	}
}

/**
 * 获取网站配置修改历史
 */
export async function getSiteConfigHistory(
	page = 1,
	pageSize = 10,
	options: { sortBy?: string; sortOrder?: "ASC" | "DESC" } = {},
) {
	return await SiteConfigModel.getSiteConfigHistory(page, pageSize, options);
}

/**
 * 处理图标上传
 * @param file 上传的文件
 * @param type 图标类型 favicon 或 logo
 * @param admin_id 管理员ID
 */
export async function uploadIcon(
	file: Express.Multer.File,
	type: "favicon" | "logo",
	admin_id?: number,
) {
	try {
		// 验证文件大小
		if (file.size > 200 * 1024) {
			// 100KB 限制
			throw AppError.create(
				"FILE_SIZE_EXCEEDED",
				`${type === "favicon" ? "图标" : "Logo"}文件大小为 ${(file.size / 1024).toFixed(2)}KB，超出200KB限制`,
			);
		}

		// 验证文件类型
		if (file.mimetype !== "image/svg+xml" && file.mimetype !== "image/png") {
			throw AppError.create("BAD_REQUEST", "只支持SVG和PNG格式");
		}

		// 生成资源ID
		const assetId = `${type}_${Date.now()}`;

		// 存储到数据库 (使用 Prisma Client API)
		await prisma.assets.create({
			data: {
				asset_id: assetId,
				content: file.buffer,
				mime_type: file.mimetype,
				file_size: file.size,
				type: type,
				admin_id: admin_id,
			},
		});

		// 存入Redis缓存
		const cacheKey = `asset:${assetId}`;
		await appRedis.set(cacheKey, file.buffer);
		await appRedis.expire(cacheKey, 86400); // 24小时缓存

		// 缓存MIME类型
		await appRedis.set(`${cacheKey}:mime`, file.mimetype);
		await appRedis.expire(`${cacheKey}:mime`, 86400);

		// 更新网站配置
		const currentConfig = await getSiteConfig();
		const updates: Partial<SiteConfig> = {};

		if (type === "favicon") {
			updates.faviconId = assetId;
		} else {
			updates.logoId = assetId;
		}

		await updateSiteConfig(updates, admin_id);

		return {
			message: `${type === "favicon" ? "Favicon" : "Logo"} uploaded successfully`,
			assetId,
		};
	} catch (error) {
		logger.error(error, `Failed to upload ${type}`);
		throw error instanceof AppError
			? error
			: AppError.create("CONFIG_UPDATE_FAILED", `Failed to upload ${type}`);
	}
}

/**
 * 获取资源内容
 * @param assetId 资源ID
 */
export async function getAsset(assetId: string) {
	try {
		// 先检查Redis缓存
		const cacheKey = `asset:${assetId}`;
		const cachedAsset = await appRedis.getBuffer(cacheKey);

		if (cachedAsset) {
			// 从缓存中获取资源类型
			const cachedMimeType = await appRedis.get(`${cacheKey}:mime`);

			return {
				content: cachedAsset,
				mimeType: cachedMimeType || "application/octet-stream",
			};
		}

		// 缓存未命中，查询数据库 (使用 Prisma Client API)
		const asset = await prisma.assets.findUnique({
			where: { asset_id: assetId },
			select: { content: true, mime_type: true },
		});

		if (!asset || !asset.content) {
			// Check for asset.content as well, as it might be null
			throw AppError.create("NOT_FOUND", "Resource not found");
		}

		// 更新缓存 - 存入Redis和返回客户端使用同一个处理过的Buffer
		const contentBuffer = Buffer.isBuffer(asset.content)
			? asset.content
			: Buffer.from(asset.content);
		await appRedis.set(cacheKey, contentBuffer);
		await appRedis.expire(cacheKey, 86400); // 24小时缓存

		await appRedis.set(`${cacheKey}:mime`, asset.mime_type);
		await appRedis.expire(`${cacheKey}:mime`, 86400);

		return {
			content: contentBuffer,
			mimeType: asset.mime_type,
		};
	} catch (error) {
		logger.error(error, `Failed to get asset: ${assetId}`);
		throw error instanceof AppError
			? error
			: AppError.create("ASSET_RETRIEVAL_FAILED", "Failed to retrieve asset");
	}
}
