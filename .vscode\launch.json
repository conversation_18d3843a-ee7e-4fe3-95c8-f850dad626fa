{"version": "0.3.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug Vitest Tests", "autoAttachChildProcesses": true, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "program": "${workspaceRoot}/packages/server/node_modules/vitest/vitest.mjs", "args": ["run", "tests/app.test.ts"], "smartStep": true, "envFile": "${workspaceFolder}/packages/server/.env", "cwd": "${workspaceFolder}/packages/server", "console": "integratedTerminal"}]}