import type { NotificationData, NotificationGroup } from "@packages/shared";

export const formatRelativeTime = (date: Date | string) => {
	const now = new Date();
	const then = new Date(date);
	const diff = now.getTime() - then.getTime();

	const minutes = Math.floor(diff / 60000);
	if (minutes < 1) return "刚刚";
	if (minutes < 60) return `${minutes}分钟前`;

	const hours = Math.floor(minutes / 60);
	if (hours < 24) return `${hours}小时前`;

	const days = Math.floor(hours / 24);
	if (days < 30) return `${days}天前`;

	const months = Math.floor(days / 30);
	if (months < 12) return `${months}个月前`;

	return `${Math.floor(months / 12)}年前`;
};

export const groupByDate = (
	notifications: NotificationData[],
	dateField: keyof NotificationData,
): NotificationGroup[] => {
	const groups: Record<string, NotificationData[]> = {};

	for (const notification of notifications) {
		const date = new Date(notification[dateField] as string);
		const dateStr = date.toLocaleDateString();

		if (!groups[dateStr]) {
			groups[dateStr] = [];
		}
		groups[dateStr].push(notification);
	}

	return Object.entries(groups)
		.map(([date, items]) => ({
			date,
			notifications: items,
		}))
		.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
};
