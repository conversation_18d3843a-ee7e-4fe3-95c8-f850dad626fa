# INK Trading Platform - Backend

Backend services for the INK Trading Platform.

## Core Components
- RESTful API server
- WebSocket server
- Database management
- Authentication services
- Business logic implementation

## Project Structure

- `src/`: Source code
  - `core/`: Core business logic
  - `middlewares/`: Express middlewares
  - `utils/`: Utility functions
  - `config/`: Configuration files
  - `models/`: Database models
  - `services/`: Business services
  - `routes/`: API routes
- `tests/`: Test files

## API Documentation

[Link to API documentation]

## SQL脚本

项目包含了一些SQL脚本文件，用于数据库迁移和维护操作。这些脚本位于`sql/`目录下：

```
sql/
├── migrations/     # 数据库结构迁移脚本
├── queries/        # 常用查询脚本
└── maintenance/    # 数据库维护脚本
```

### 执行邮箱迁移

邮箱迁移是将系统从基于手机号的认证迁移到基于邮箱的认证。完整迁移流程如下：

1. **解密敏感字段**
   ```bash
   # 先解密email和phone_number字段
   pnpm field-crypto -- decrypt users email
   pnpm field-crypto -- decrypt users phone_number
   ```

2. **执行SQL迁移脚本**
   ```bash
   # 在PostgreSQL中执行SQL脚本
   psql -d 数据库名 -f sql/migrations/email_migration.sql
   ```

3. **重新加密敏感字段**
   ```bash
   # 重新加密email和phone_number字段
   pnpm field-crypto -- encrypt users email
   pnpm field-crypto -- encrypt users phone_number
   ```

### 字段加解密工具

项目提供了一个工具脚本用于加密/解密数据库字段：

```bash
# 加密字段
pnpm field-crypto -- encrypt <表名> <字段名>

# 解密字段
pnpm field-crypto -- decrypt <表名> <字段名>
```

更多信息请参考项目文档。
