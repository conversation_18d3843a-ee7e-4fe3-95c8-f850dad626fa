import { Router } from "express";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import * as adminService from "@/services/admin/index.js";
import { AdminPermission } from "@packages/shared";
import { AppError } from "@/core/appError.js";

const router = Router();

// 需要管理员权限

// 获取所有管理员列表
router.get(
	"/admins",
	wrapAdminRoute(async (_, res) => {
		const admins = await adminService.getAllAdmins();
		res.status(200).json(admins);
	}),
);

// 创建新管理员
router.post(
	"/admins",
	wrapAdminRoute<CreateAdminBody>(async (req, res) => {
		const { username, password, name, permissions } = req.body;

		// Forbidden to create admin with admin permission
		if (permissions.includes(AdminPermission.ADMIN)) {
			throw AppError.create(
				"ADMIN_PERMISSION_DENIED",
				"Admin permission is not allowed",
			);
		}

		const admin = await adminService.createAdmin(
			username,
			password,
			name,
			permissions,
		);
		res.status(201).json(admin);
	}),
);

// Toggle admin active status
router.post(
	"/admins/:admin_id",
	wrapAdminRoute(async (req, res) => {
		const admin_id = Number.parseInt(req.params.admin_id);
		const result = await adminService.toggleAdminActiveStatus(admin_id);
		res.status(200).json(result);
	}),
);

// Update admin permissions
router.put(
	"/admins/:admin_id/permissions",
	wrapAdminRoute<UpdatePermissionsBody>(async (req, res) => {
		const admin_id = Number.parseInt(req.params.admin_id);
		const { permissions } = req.body;

		// Forbidden to update to admin permission
		if (permissions.includes(AdminPermission.ADMIN)) {
			throw AppError.create(
				"ADMIN_PERMISSION_DENIED",
				"Admin permission is not allowed",
			);
		}

		const result = await adminService.updateAdminPermissions(
			admin_id,
			permissions,
		);
		res.status(200).json(result);
	}),
);

export default router;

interface CreateAdminBody {
	username: string;
	password: string;
	name: string;
	permissions: AdminPermission[];
}

interface UpdatePermissionsBody {
	permissions: AdminPermission[];
}
