import * as FundService from "../fundService.js";
import { executeOrder } from "./executeOrder.js";
import type {
	OrderData,
	PendingOrderData,
	OrderRequest,
	BuyRequest,
	SellRequest,
	BuyingOrderData,
	SellingOrderData,
	QuoteBuyRequest,
	OrderResponse,
	InquiryData,
} from "@packages/shared";
import {
	TradeDirection,
	OrderStatus,
	OrderType,
	InquiryStatus,
	WebSocketMessageType,
	ProviderTradingRestrictions,
	Currency,
	QUOTE_REJECTED,
} from "@packages/shared";
import * as PendingOrder from "@/models/trade/pendingOrder.js";
import logger from "@/utils/logger.js";
import * as OrderModel from "@/models/trade/order.js";
import { appRedis } from "@/lib/redis.js";
import * as ConfigService from "@/services/admin/configService.js";
import * as SharedConfigService from "@/services/admin/sharedConfigService.js";
import { AppError } from "@/core/appError.js";
import { startMonitoring as startLimitMonitoring } from "@/queue/limitOrderWorker.js";
import { startVwapOrder } from "@/queue/vwapOrderWorker.js";
import { dateToISOString } from "@/utils/dateUtils.js";
import { fetchPriceDetails } from "@/financeUtils/marketData.js";
import { calcBuyAmount } from "@/financeUtils/calculator.js";
import { notifyUser } from "@/utils/notify.js";
import { getLastNTradingDays } from "@/financeUtils/marketTimeManager.js";
import * as InquiryModel from "@/models/inquiry.js";

const TRADING_END = 14 * 60 * 60 * 1000 + 57 * 60 * 1000; // 14:57
const VWAP_CUTOFF = 15 * 60 * 1000; // 15 minutes

// Add a new interface for validation result
export interface ValidationResult {
	valid: boolean;
	stage: string;
	details: Record<string, unknown>;
}

// Add a type definition for our function response
export type PlaceOrderResponse = {
	message: string;
	data: OrderData | PendingOrderData | ValidationResult;
};

// 验证订单创建时间
function validateOrderTime(orderType: OrderType): {
	valid: boolean;
	message?: string;
} {
	const now = new Date();
	const currentMs =
		now.getHours() * 60 * 60 * 1000 + now.getMinutes() * 60 * 1000;
	const timeToClose = TRADING_END - currentMs;

	// VWAP订单额外验证15分钟限制
	if (
		orderType === OrderType.VWAP &&
		timeToClose <= VWAP_CUTOFF &&
		timeToClose > 0
	) {
		return {
			valid: false,
			message: "Cannot create VWAP orders 15 minutes before market close",
		};
	}

	return { valid: true };
}

/**
 * 检查价格波动是否超过限制
 * 对于非INK提供商，如果股票价格波动超过5%，则报价失效
 * @param ts_code 股票代码
 * @param provider 报价提供商
 * @returns Promise<void>
 * @throws AppError 如果价格波动超过限制
 */
async function checkPriceVolatility(
	ts_code: string,
	provider: string,
): Promise<void> {
	// 只对非INK提供商检查价格波动
	if (provider === "INK") {
		return;
	}

	// 获取价格详情
	const priceDetails = await fetchPriceDetails(ts_code);
	const { high, low, pre_close } = priceDetails;

	// 计算价格波动百分比
	const highVolatility = Math.abs(high - pre_close) / pre_close;
	const lowVolatility = Math.abs(low - pre_close) / pre_close;
	const volatilityThreshold = 0.05; // 5%

	logger.debug(
		`Price volatility check for ${ts_code}: high=${high}, low=${low}, pre_close=${pre_close}`,
	);
	// 如果价格波动超过5%，抛出错误
	if (
		highVolatility > volatilityThreshold ||
		lowVolatility > volatilityThreshold
	) {
		throw AppError.create(
			"PRICE_VOLATILITY_EXCEEDED",
			"股票价格波动超过限制，当前报价已失效",
			{
				provider,
				ts_code,
				high,
				low,
				pre_close,
			},
		);
	}
}

export async function placeOrder(
	// 注意：当对联合类型(Union Type)使用 Omit 时，结果类型与对每个类型分别使用 Omit 再联合的结果不同
	// 这是因为 TypeScript 的类型分发(Distribution)规则：泛型操作符对联合类型整体起作用，而不会分发到联合类型的每个成员
	orderRequest: OrderRequest,
	options?: {
		isExpiry?: boolean;
		// 添加健康检查标记
		dryRun?: boolean;
	},
): Promise<OrderResponse> {
	logger.info(orderRequest, "置单中");

	// 如果是健康检查模式，使用验证函数
	if (options?.dryRun) {
		const validationResult = await validateOrderForHealthCheck(orderRequest);
		return {
			message: validationResult.valid
				? "Order validation successful"
				: "Order validation failed",
			data: validationResult as unknown as Omit<
				OrderData,
				"trade_no" | "expiry_date" | "expiry_date_confirmed"
			> & {
				trade_no?: string;
				expiry_date?: string;
				expiry_date_confirmed?: boolean;
			},
		};
	}

	let inquiry_id: number | undefined;

	// 价格调整单位为万！
	// 对于买单，orderRequest.quote_diff 应该已经是询价时确定的最终提价差额（已验证）
	// 对于卖单，orderRequest.quote_diff 通常为 undefined 或 0
	// 不再无条件覆盖买单的 quote_diff，直接使用请求中传递的 quote_diff

	// 验证订单创建时间
	const timeValidation = validateOrderTime(orderRequest.type);
	if (!timeValidation.valid) {
		throw AppError.create(
			"ORDER_TIME_EXCEEDED",
			timeValidation.message || "Invalid order time",
		);
	}

	// Validate quote for buy orders
	if (orderRequest.direction === TradeDirection.BUY) {
		const buyRequest = orderRequest as QuoteBuyRequest;
		inquiry_id = buyRequest.inquiry_id;
		await validateBuyRequest(buyRequest);
	} else {
		await validateSellRequest(orderRequest as SellRequest);

		if (orderRequest.type === OrderType.LIMIT) {
			await validateLimitSellRequest(orderRequest as SellRequest);
		}
	}

	// Handle market orders
	if (orderRequest.type === OrderType.MARKET) {
		// 市价单在下单后会立即在executeOrder中验证价格波动
		const order = await executeOrder(orderRequest, {
			isExpiry: options?.isExpiry,
		});
		if (inquiry_id) {
			await InquiryModel.updateStatus(inquiry_id, InquiryStatus.APPROVED);
		}
		return { message: "Order executed successfully", data: order };
	}

	// Create order and pending order
	const pendingOrder = await createStrategyOrder(orderRequest);

	// Execute strategy
	executeStrategy(orderRequest.type, pendingOrder, orderRequest).catch(
		(error) => {
			logger.error(error, "Strategy execution failed");
		},
	);

	// Update pending order after successful create pending order
	notifyUser(pendingOrder.user_id, {
		type: WebSocketMessageType.ORDER_UPDATE,
	});

	// Update inquiry status after successful order placement
	if (inquiry_id) {
		await InquiryModel.updateStatus(inquiry_id, InquiryStatus.APPROVED);
	}
	return { message: "Order placed successfully", data: pendingOrder };
}

async function validateQuote(buyRequest: QuoteBuyRequest): Promise<void> {
	const { inquiry_id, quote: requestedQuote, quote_provider } = buyRequest;

	// 1. 验证请求中的报价是否为正数
	if (requestedQuote <= 0) {
		throw AppError.create(
			"INVALID_QUOTE",
			"Requested quote price must be a positive value.",
		);
	}

	// 2. 如果使用外部报价提供商，检查是否允许一键下单功能
	if (quote_provider !== "INK") {
		const sharedConfig = await SharedConfigService.getSharedConfig();
		if (!sharedConfig?.channel_management?.enable_external_order) {
			throw AppError.create(
				"EXTERNAL_ORDER_DISABLED",
				"外部报价下单功能已被禁用",
				{ provider: quote_provider },
			);
		}
	}

	const inquiryKey = `inquiry:${inquiry_id}`;
	const inquiryData = await appRedis.get(inquiryKey);

	if (!inquiryData) {
		throw AppError.create(
			"QUOTE_EXPIRED",
			"No valid quote found. Please request a new quote.",
		);
	}

	const storedInquiry = JSON.parse(inquiryData) as InquiryData;

	if (storedInquiry.status === InquiryStatus.REJECTED) {
		throw AppError.create(
			"QUOTE_REJECTED",
			"Inquiry was rejected. Please request a new quote.",
		);
	}

	if (
		storedInquiry.scale !== buyRequest.scale ||
		storedInquiry.structure !== buyRequest.structure ||
		storedInquiry.term !== buyRequest.term ||
		storedInquiry.user_id !== buyRequest.user_id ||
		storedInquiry.ts_code !== buyRequest.ts_code
	) {
		throw AppError.create(
			"INVALID_INQUIRY_INFO",
			"Quote mismatched with stored inquiry details. Invalid quote.",
		);
	}

	logger.debug({ storedInquiry, buyRequest }, "validateQuote");

	let effectiveStoredQuote: number | null = null;

	if (quote_provider === "INK") {
		effectiveStoredQuote = storedInquiry.quote;
	} else if (
		storedInquiry.external_quotes &&
		storedInquiry.external_quotes[quote_provider] !== undefined
	) {
		effectiveStoredQuote = storedInquiry.external_quotes[quote_provider];
	} else {
		throw AppError.create(
			"INVALID_QUOTE",
			`Quote provider '${quote_provider}' not found or no quote available for this provider.`,
		);
	}

	if (
		effectiveStoredQuote === null ||
		effectiveStoredQuote === QUOTE_REJECTED
	) {
		throw AppError.create(
			"QUOTE_MISMATCH",
			`The selected quote from provider '${quote_provider}' is currently unavailable or rejected.`,
		);
	}

	const quoteDiffForProvider = storedInquiry.quote_diffs?.[quote_provider] ?? 0;
	const expectedAdjustedQuote = Number(
		(effectiveStoredQuote + quoteDiffForProvider).toFixed(2),
	);

	if (Number(requestedQuote.toFixed(2)) !== expectedAdjustedQuote) {
		logger.error("Quote mismatch after adjustment", {
			requestedQuote,
			effectiveStoredQuote,
			quoteDiffForProvider,
			expectedAdjustedQuote,
			storedInquiryQuoteDiffs: storedInquiry.quote_diffs,
		});
		throw AppError.create(
			"QUOTE_MISMATCH",
			`Requested quote ${requestedQuote} does not match expected adjusted quote ${expectedAdjustedQuote} for provider '${quote_provider}'.`,
		);
	}
}

const validateScale = (scale: number): void => {
	if (scale <= 0) {
		throw AppError.create("INVALID_SCALE", "Scale must be greater than 0.");
	}

	// 验证是否为 100 的倍数
	if (scale % 100 !== 0) {
		throw AppError.create("INVALID_SCALE", "Scale must be a multiple of 100.");
	}
};

async function validateBuyRequest(buyRequest: BuyRequest): Promise<void> {
	await validateQuote(buyRequest as QuoteBuyRequest);

	const { scale } = buyRequest;
	validateScale(scale);
}

async function validateSellRequest(sellRequest: SellRequest): Promise<null> {
	const order = await OrderModel.findByTradeNo(sellRequest.trade_no);
	if (order.user_id !== sellRequest.user_id) {
		throw AppError.create("ORDER_NOT_OWNED", "Order not owned by user.");
	}

	const { scale } = sellRequest;
	validateScale(scale);

	// 检查T+N交易限制
	if (order.quote_provider) {
		const restriction =
			ProviderTradingRestrictions[
				order.quote_provider as keyof typeof ProviderTradingRestrictions
			];

		if (restriction > 0) {
			// 获取最近N个交易日 (加上今天)
			const recentTradingDays = await getLastNTradingDays(restriction + 1);
			const orderDate = new Date(order.created_at);

			// 修复：使用时间戳比较而非字符串比较，并确保orderDate是午夜时间，以便正确比较
			// 将订单日期设置为当天的开始时间（00:00:00）以便进行准确比较
			const orderDateStart = new Date(orderDate);
			orderDateStart.setHours(0, 0, 0, 0);

			// 找到最早的交易日（交易日是倒序排列的，所以是最后一个）
			if (recentTradingDays.length > 0) {
				// 注意：这里获取的是第 N+1 个交易日，因为 restriction + 1
				const earliestTradingDay =
					recentTradingDays[recentTradingDays.length - 1]; // 获取数组最后一个元素，索引从零开始
				const nthTradingDay = new Date(earliestTradingDay);
				nthTradingDay.setHours(0, 0, 0, 0);

				/**
				 * T+N 交易限制核心逻辑：
				 * - 今天买，第N个交易日当天（含）即可卖出
				 * - 变量：
				 *   - orderDateStart: 订单创建日的00:00:00
				 *   - nthTradingDay: 从今天往前数第N个交易日的00:00:00
				 * - 判断条件：
				 *   - 如果 orderDateStart > nthTradingDay，则未满足T+N，抛出错误
				 *   - 只有 orderDateStart <= nthTradingDay 时，才允许卖出
				 *
				 * ASCII时间轴（T+1示例）:
				 *   T日(下单)         T+1日           T+2日
				 *   00:00  ... 23:59 00:00 ... 23:59 00:00
				 *   |----------------|----------------|
				 *   orderDateStart   nthTradingDay (这里应该是T+1日00:00)
				 *
				 *   T日下单，T+1日00:00后即可卖出
				 */
				if (orderDateStart > nthTradingDay) {
					throw AppError.create(
						"TRADING_RESTRICTION_VIOLATION",
						`未满足T+${restriction}限制，订单起始日：${dateToISOString(orderDate)}`,
						{
							order_date: dateToISOString(orderDate),
							restriction,
						},
					);
				}
			}
		}
	}

	// 检查是否有待处理的挂单
	const pendingOrders = await PendingOrder.findByTradeNo(sellRequest.trade_no);
	const pendingScale = pendingOrders.reduce(
		(sum, order) => sum + order.scale,
		0,
	);
	const availableScale = order.scale - pendingScale;

	if (scale > availableScale) {
		throw AppError.create(
			"PENDING_ORDERS_INSUFFICIENT_SCALE",
			`Insufficient available scale. Required: ${scale}, Available: ${availableScale}`,
		);
	}

	// When no validation issues, return null
	return null;
}

async function validateLimitSellRequest(
	sellRequest: SellRequest,
): Promise<void> {
	const { limit_price } = sellRequest;
	const order = await OrderModel.findByTradeNo(sellRequest.trade_no);
	const isCall = order.structure.endsWith("C");
	if (!limit_price) {
		throw AppError.create("INVALID_LIMIT_PRICE", "Limit price is required");
	}

	logger.info(
		{
			limit_price,
			exercise_price: order.exercise_price,
			isLimitLager: limit_price > order.exercise_price,
		},
		"validateLimitSellRequest",
	);
	if (isCall && limit_price <= order.exercise_price) {
		throw AppError.create(
			"INVALID_LIMIT_PRICE",
			"Limit price must be greater than exercise price for call options",
		);
	}

	if (!isCall && limit_price >= order.exercise_price) {
		throw AppError.create(
			"INVALID_LIMIT_PRICE",
			"Limit price must be less than exercise price for put options",
		);
	}
}

async function createStrategyOrder(
	orderRequest: OrderRequest,
): Promise<PendingOrderData> {
	// 对限价单和均价单在创建策略订单时检查价格波动
	// 启发性设计注释：验证逻辑应放在流程中最合理的地方
	// 我以前的思维误区是"应该在入口处就验证所有请求"，这会导致重复验证
	// 正确做法是：验证的位置取决于业务逻辑和用户体验
	// - 市价单：在executeOrder中验证，因为立即执行
	// - 限价单和均价单：在createStrategyOrder中验证，在订单挂起前确保价格合理
	if (orderRequest.quote_provider && orderRequest.quote_provider !== "INK") {
		await checkPriceVolatility(
			orderRequest.ts_code,
			orderRequest.quote_provider,
		);
	}

	if (orderRequest.direction === TradeDirection.BUY) {
		return buyingOrder(orderRequest as BuyRequest);
	}
	return sellingOrder(orderRequest as SellRequest);
}

async function buyingOrder(buyRequest: BuyRequest): Promise<PendingOrderData> {
	const sufficientBalance = await FundService.checkBalance(
		buyRequest.user_id,
		calcBuyAmount(buyRequest.scale, buyRequest.quote),
		Currency.CNY,
	);
	logger.info({ sufficientBalance }, "sufficientBalance");
	if (!sufficientBalance) {
		throw AppError.create("INSUFFICIENT_BALANCE", "Insufficient balance");
	}

	logger.info(buyRequest, "创建挂单");

	// 确定订单状态
	let status: OrderStatus;
	switch (buyRequest.type) {
		case OrderType.LIMIT:
			status = OrderStatus.LIMIT_BUYING;
			break;
		case OrderType.VWAP:
			status = OrderStatus.VWAP_BUYING;
			break;
		default:
			status = OrderStatus.LIMIT_BUYING;
	}

	// 创建挂单
	return PendingOrder.create({
		user_id: buyRequest.user_id,
		ts_code: buyRequest.ts_code,
		entry_price: 0,
		exercise_price: 0,
		settle_price: 0,
		scale: buyRequest.scale,
		total_scale: buyRequest.scale,
		term: buyRequest.term,
		structure: buyRequest.structure,
		quote: buyRequest.quote,
		quote_provider: buyRequest.quote_provider,
		quote_diff: buyRequest.quote_diff,
		status,
		limit_price:
			buyRequest.type === OrderType.LIMIT ? buyRequest.limit_price : undefined,
	} as BuyingOrderData);
}

async function sellingOrder(
	sellRequest: SellRequest,
): Promise<PendingOrderData> {
	const order = await OrderModel.findByTradeNo(sellRequest.trade_no);
	logger.info(order, "创建挂单");

	if (sellRequest.scale > order.scale) {
		throw AppError.create(
			"INVALID_SCALE",
			"Scale must be less than or equal to the order scale.",
		);
	}

	// 确定订单状态
	let status: OrderStatus;
	switch (sellRequest.type) {
		case OrderType.LIMIT:
			status = OrderStatus.LIMIT_SELLING;
			break;
		case OrderType.VWAP:
			status = OrderStatus.VWAP_SELLING;
			break;
		default:
			status = OrderStatus.LIMIT_SELLING;
	}

	// 创建挂单
	return PendingOrder.create({
		user_id: order.user_id,
		ts_code: order.ts_code,
		entry_price: order.entry_price,
		exercise_price: order.exercise_price,
		settle_price: order.settle_price,
		scale: sellRequest.scale,
		total_scale: order.total_scale,
		structure: order.structure,
		term: order.term,
		expiry_date: order.expiry_date,
		expiry_date_confirmed: order.expiry_date_confirmed,
		quote: order.quote,
		status,
		trade_no: sellRequest.trade_no,
		limit_price:
			sellRequest.type === OrderType.LIMIT
				? sellRequest.limit_price
				: undefined,
	} as SellingOrderData);
}

async function executeStrategy(
	type: Exclude<OrderType, OrderType.MARKET>,
	pendingOrder: PendingOrderData,
	orderRequest: OrderRequest,
): Promise<void> {
	if (type === OrderType.LIMIT) {
		const limitPrice = (orderRequest as BuyRequest).limit_price;
		if (!limitPrice) {
			throw AppError.create(
				"INVALID_LIMIT_PRICE",
				"Limit price is required for limit orders",
			);
		}
		await startLimitMonitoring({
			...pendingOrder,
			limit_price: limitPrice,
		});
	} else if (type === OrderType.VWAP) {
		await startVwapOrder(pendingOrder);
	}
}

export async function getOrderHistory(
	user_id: number,
	page: number,
	pageSize: number,
	isDescending: boolean,
	filters?: {
		ts_codes?: string[];
		startDate?: string;
		endDate?: string;
	},
): Promise<{
	items: OrderData[];
	total: number;
	ts_codes: string[];
}> {
	const offset = (page - 1) * pageSize;

	const [ordersResult, ts_codes] = await Promise.all([
		OrderModel.getOrderHistory(
			user_id,
			offset,
			pageSize,
			isDescending,
			filters,
		),
		OrderModel.getAllOrderTsCodes(user_id),
	]);

	return {
		items: ordersResult.items,
		total: ordersResult.total,
		ts_codes,
	};
}

export async function getSettleHistory(
	user_id: number,
	page: number,
	pageSize: number,
	isDescending: boolean,
	filters?: {
		ts_codes?: string[];
		startDate?: string;
		endDate?: string;
	},
): Promise<{
	items: OrderData[];
	total: number;
	ts_codes: string[];
}> {
	const offset = (page - 1) * pageSize;

	const [settleResult, ts_codes] = await Promise.all([
		OrderModel.getSettleHistory(
			user_id,
			offset,
			pageSize,
			isDescending,
			filters,
		),
		OrderModel.getAllSettleTsCodes(user_id),
	]);

	return {
		items: settleResult.items,
		total: settleResult.total,
		ts_codes,
	};
}

export async function getBuyingList(
	user_id: number,
): Promise<PendingOrderData[] | null> {
	return PendingOrder.getBuyingList(user_id);
}

export async function getSellingList(
	user_id: number,
): Promise<PendingOrderData[] | null> {
	return PendingOrder.getSellingList(user_id);
}

/**
 * 健康检查专用的交易验证函数，进行完整的流程验证但不执行实际交易
 *
 * 此函数验证完整的交易流程，包括：
 * - 询价验证
 * - 用户余额检查
 * - 交易时间检查
 * - 参数验证
 * - 交易策略检查
 *
 * 不会：
 * - 创建真实订单
 * - 执行实际交易
 * - 变更用户余额
 * - 更新询价状态
 *
 * @param orderRequest 订单请求
 * @returns 验证结果，包含详细信息
 */
export async function validateOrderForHealthCheck(
	orderRequest: OrderRequest,
): Promise<ValidationResult> {
	try {
		logger.info(orderRequest, "健康检查-验证订单中");

		// 检查交易时间
		const timeValidation = validateOrderTime(orderRequest.type);
		if (!timeValidation.valid) {
			return {
				valid: false,
				stage: "time_validation",
				details: {
					error: timeValidation.message || "Invalid order time",
					error_code: "ORDER_TIME_EXCEEDED",
				},
			};
		}

		// 获取平台配置
		const platformConfig = await ConfigService.getPlatformConfig();

		// 验证买入订单
		if (orderRequest.direction === TradeDirection.BUY) {
			const buyRequest = orderRequest as QuoteBuyRequest;

			// 验证询价
			try {
				// 如果是测试询价 (inquiry_id === 0)，则跳过 Redis 检查，因为测试询价不存入 Redis
				// 相关的询价逻辑有效性已在 testInquiryProcess 中验证
				if (buyRequest.inquiry_id !== 0) {
					const inquiryKey = `inquiry:${buyRequest.inquiry_id}`;
					const inquiryData = await appRedis.get(inquiryKey);

					if (!inquiryData) {
						return {
							valid: false,
							stage: "inquiry_validation",
							details: {
								error: "No valid quote found. Please request a new quote.",
								error_code: "QUOTE_EXPIRED",
							},
						};
					}

					const storedInquiry = JSON.parse(inquiryData);

					if (storedInquiry.status === InquiryStatus.REJECTED) {
						return {
							valid: false,
							stage: "inquiry_validation",
							details: {
								error: "Quote rejected. Please request a new quote.",
								error_code: "QUOTE_REJECTED",
							},
						};
					}

					// 验证询价信息匹配
					if (
						storedInquiry.scale !== buyRequest.scale ||
						storedInquiry.structure !== buyRequest.structure ||
						storedInquiry.term !== buyRequest.term ||
						storedInquiry.user_id !== buyRequest.user_id ||
						storedInquiry.ts_code !== buyRequest.ts_code
					) {
						return {
							valid: false,
							stage: "inquiry_validation",
							details: {
								error: "Quote mismatched. Invalid quote.",
								error_code: "INVALID_INQUIRY_INFO",
							},
						};
					}

					// 验证报价提供商
					if (buyRequest.quote_provider === "INK") {
						if (
							storedInquiry.quote +
								storedInquiry.quote_diffs?.[buyRequest.quote_provider] !==
							Number(buyRequest.quote)
						) {
							return {
								valid: false,
								stage: "quote_validation",
								details: {
									error: "Internal quote does not match the stored quote.",
									error_code: "QUOTE_MISMATCH",
								},
							};
						}
					} else if (storedInquiry.external_quotes) {
						const externalQuote =
							storedInquiry.external_quotes[buyRequest.quote_provider];
						if (
							Number(externalQuote) +
								Number(
									storedInquiry.quote_diffs?.[buyRequest.quote_provider],
								) !==
								Number(buyRequest.quote) ||
							(storedInquiry.quote_diff &&
								storedInquiry.quote_diff !== buyRequest.quote_diff)
						) {
							return {
								valid: false,
								stage: "quote_validation",
								details: {
									error: `Quote from provider '${buyRequest.quote_provider}' does not match or is not available.`,
									error_code: "QUOTE_MISMATCH",
								},
							};
						}
					} else {
						return {
							valid: false,
							stage: "quote_validation",
							details: {
								error: "No external quotes available for this inquiry.",
								error_code: "INVALID_INQUIRY_INFO",
							},
						};
					}
				} // 结束 if (buyRequest.inquiry_id !== 0)

				// 验证规模
				if (buyRequest.scale <= 0) {
					return {
						valid: false,
						stage: "scale_validation",
						details: {
							error: "Scale must be greater than 0.",
							error_code: "INVALID_SCALE",
						},
					};
				}

				if (buyRequest.scale % 100 !== 0) {
					return {
						valid: false,
						stage: "scale_validation",
						details: {
							error: "Scale must be a multiple of 100.",
							error_code: "INVALID_SCALE",
						},
					};
				}

				// 验证余额
				// 在测试用户的情况下，我们假设有足够的资金

				// 假定测试用户总是有足够的资金
				const hasEnoughBalance = orderRequest.user_id === 0; // 更新测试用户ID为0

				if (!hasEnoughBalance) {
					return {
						valid: false,
						stage: "balance_validation",
						details: {
							error: "Insufficient balance for this transaction.",
							error_code: "INSUFFICIENT_BALANCE",
							required_amount: calcBuyAmount(
								buyRequest.scale,
								buyRequest.quote,
							),
						},
					};
				}
			} catch (error) {
				return {
					valid: false,
					stage: "validation_error",
					details: {
						error: error instanceof Error ? error.message : String(error),
						error_code: "VALIDATION_ERROR",
					},
				};
			}
		} else {
			// 验证卖出订单
			const sellRequest = orderRequest as SellRequest;

			try {
				// 验证订单存在性
				const order = await OrderModel.findByTradeNo(sellRequest.trade_no);

				// 验证所有权
				if (order.user_id !== sellRequest.user_id) {
					return {
						valid: false,
						stage: "ownership_validation",
						details: {
							error: "Order not owned by user.",
							error_code: "ORDER_NOT_OWNED",
						},
					};
				}

				// 验证规模
				if (sellRequest.scale <= 0) {
					return {
						valid: false,
						stage: "scale_validation",
						details: {
							error: "Scale must be greater than 0.",
							error_code: "INVALID_SCALE",
						},
					};
				}

				if (sellRequest.scale % 100 !== 0) {
					return {
						valid: false,
						stage: "scale_validation",
						details: {
							error: "Scale must be a multiple of 100.",
							error_code: "INVALID_SCALE",
						},
					};
				}

				// 检查T+N交易限制
				if (order.quote_provider) {
					const restriction =
						ProviderTradingRestrictions[
							order.quote_provider as keyof typeof ProviderTradingRestrictions
						];

					if (restriction > 0) {
						// 获取最近N个交易日 (加上今天)
						const recentTradingDays = await getLastNTradingDays(
							restriction + 1,
						);
						const orderDate = new Date(order.created_at);

						// 修复：使用时间戳比较而非字符串比较，并确保orderDate是午夜时间，以便正确比较
						// 将订单日期设置为当天的开始时间（00:00:00）以便进行准确比较
						const orderDateStart = new Date(orderDate);

						// 找到最早的交易日（交易日是倒序排列的，所以是最后一个）
						if (recentTradingDays.length > 0) {
							const earliestTradingDay =
								recentTradingDays[recentTradingDays.length];

							// 如果订单日期在或早于最早允许的交易日结束时间，则不满足T+N限制
							const nthTradingDay = new Date(earliestTradingDay);
							nthTradingDay.setHours(0, 0, 0, 0);
							orderDateStart.setHours(0, 0, 0, 0);
							if (orderDateStart > nthTradingDay) {
								throw AppError.create(
									"TRADING_RESTRICTION_VIOLATION",
									`未满足T+${restriction}限制，订单起始日：${dateToISOString(orderDate)}`,
									{
										order_date: dateToISOString(orderDate),
										restriction,
									},
								);
							}
						}
					}
				}

				// 检查是否有待处理的挂单
				const pendingOrders = await PendingOrder.findByTradeNo(
					sellRequest.trade_no,
				);
				const pendingScale = pendingOrders.reduce(
					(sum, order) => sum + order.scale,
					0,
				);
				const availableScale = order.scale - pendingScale;

				if (sellRequest.scale > availableScale) {
					return {
						valid: false,
						stage: "pending_orders",
						details: {
							error: `Insufficient available scale. Required: ${sellRequest.scale}, Available: ${availableScale}`,
							error_code: "PENDING_ORDERS_INSUFFICIENT_SCALE",
						},
					};
				}

				// 验证限价
				if (sellRequest.type === OrderType.LIMIT) {
					const { limit_price } = sellRequest;
					const isCall = order.structure.endsWith("C");

					if (!limit_price) {
						return {
							valid: false,
							stage: "limit_price_validation",
							details: {
								error: "Limit price is required",
								error_code: "INVALID_LIMIT_PRICE",
							},
						};
					}

					if (isCall && limit_price <= order.exercise_price) {
						return {
							valid: false,
							stage: "limit_price_validation",
							details: {
								error:
									"Limit price must be greater than exercise price for call options",
								error_code: "INVALID_LIMIT_PRICE",
							},
						};
					}

					if (!isCall && limit_price >= order.exercise_price) {
						return {
							valid: false,
							stage: "limit_price_validation",
							details: {
								error:
									"Limit price must be less than exercise price for put options",
								error_code: "INVALID_LIMIT_PRICE",
							},
						};
					}
				}
			} catch (error) {
				return {
					valid: false,
					stage: "validation_error",
					details: {
						error: error instanceof Error ? error.message : String(error),
						error_code: "VALIDATION_ERROR",
					},
				};
			}
		}

		// 所有验证通过
		return {
			valid: true,
			stage: "validation_complete",
			details: {
				message: "Order validation successful",
				order_type: orderRequest.type,
				direction: orderRequest.direction,
			},
		};
	} catch (error) {
		// 处理任何未预期的错误
		logger.error(
			error,
			"Health check order validation failed with unexpected error",
		);
		return {
			valid: false,
			stage: "unexpected_error",
			details: {
				error: error instanceof Error ? error.message : String(error),
				error_code: "UNEXPECTED_ERROR",
			},
		};
	}
}
