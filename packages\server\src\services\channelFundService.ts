import { AppError } from "@/core/appError.js";
import * as ChannelFundModel from "@/models/channelFund.js";
import { isChannel } from "@/config/configManager.js";
import type {
	CreateChannelTransactionRequest,
	UpdateChannelTransactionRequest,
	ChannelTransactionData,
	ChannelBalance,
} from "@packages/shared";
import {
	ChannelTransactionType,
	Currency,
	ChannelTransactionStatus,
} from "@packages/shared";
import { exchangeRateService } from "@/utils/exchangeRate.js";
import { withTransaction } from "@/core/dbTxnManager.js";
import type { Prisma } from "@prisma/client";

/**
 * 创建通道资金交易，仅限通道发起
 */
export async function createChannelTransaction(
	data: CreateChannelTransactionRequest & {
		status: ChannelTransactionStatus;
	},
): Promise<ChannelTransactionData> {
	if (
		data.type !== ChannelTransactionType.DEPOSIT &&
		data.type !== ChannelTransactionType.WITHDRAW
	) {
		throw AppError.create("BAD_REQUEST", "仅限通道出入金交易");
	}

	return ChannelFundModel.createTransaction(data);
}

/**
 * 获取通道余额，自动使用环境变量中的通道ID，仅限通道发起
 */
export async function getChannelBalance(): Promise<ChannelBalance> {
	return ChannelFundModel.getChannelBalance();
}

/**
 * 获取通道交易记录，多可选参数，禁止省略以免混淆
 * 通道版本：自动使用环境变量中的通道ID
 * 交易台版本：如果channelId为空，则获取所有通道的交易记录
 */
export async function getChannelTransactions(
	channelId?: string,
	limit = 50,
	offset = 0,
	status?: ChannelTransactionStatus,
	usePlatformDb = false,
): Promise<{ transactions: ChannelTransactionData[]; total: number }> {
	// 通道版本自动使用环境变量中的通道ID
	if (isChannel() && !channelId) {
		const transactions = await ChannelFundModel.getChannelTransactions(
			limit,
			offset,
			status,
			usePlatformDb,
		);
		const total = await ChannelFundModel.getChannelTransactionsCount(
			status,
			usePlatformDb,
		);
		return { transactions, total };
	}

	// 对于交易台版本，如果channelId为空，获取所有通道的交易记录
	if (!channelId) {
		const transactions = await ChannelFundModel.getAllTransactions(
			limit,
			offset,
			status,
		);
		const total = await ChannelFundModel.getAllTransactionsCount(status);
		return { transactions, total };
	}

	// 如果有指定的channelId，使用该channelId查询
	const transactions = await ChannelFundModel.getChannelTransactions(
		limit,
		offset,
		status,
		usePlatformDb,
		channelId,
	);
	const total = await ChannelFundModel.getChannelTransactionsCount(
		status,
		usePlatformDb,
		channelId,
	);

	return { transactions, total };
}

/**
 * 更新交易状态
 * 仅交易台版本可以调用
 */
export async function updateTransactionStatus(
	data: UpdateChannelTransactionRequest,
): Promise<ChannelTransactionData> {
	// 获取交易详情
	const transaction = await ChannelFundModel.getTransactionById(
		data.transaction_id.toString(),
	);

	if (!transaction) {
		throw AppError.create("NOT_FOUND", "资金记录不存在");
	}

	// 验证交易类型，仅允许通道出入金类型
	if (
		transaction.type !== ChannelTransactionType.DEPOSIT &&
		transaction.type !== ChannelTransactionType.WITHDRAW
	) {
		throw AppError.create("BAD_REQUEST", "仅限通道出入金类型的交易可以审核");
	}

	return ChannelFundModel.updateTransactionStatus(data, false);
}

/**
 * 获取所有通道
 * 仅交易台版本可以调用
 */
export async function getAllChannels() {
	return ChannelFundModel.getAllChannels();
}

/**
 * 获取待审核交易
 * 仅交易台版本可以调用
 */
export async function getPendingTransactions(
	limit = 50,
	offset = 0,
): Promise<{ transactions: ChannelTransactionData[]; total: number }> {
	const result = await ChannelFundModel.getPendingTransactions(limit, offset);
	return result;
}

export async function getTransactionById(
	transactionId: string,
): Promise<ChannelTransactionData | null> {
	return ChannelFundModel.getTransactionById(transactionId);
}

export async function getAllChannelBalances(): Promise<ChannelBalance[]> {
	return ChannelFundModel.getAllChannelBalances();
}

/**
 * 获取货币兑换汇率
 */
export async function getExchangeRate(
	fromCurrency: Currency,
	toCurrency: Currency,
): Promise<number> {
	if (fromCurrency === toCurrency) {
		throw AppError.create(
			"INVALID_EXCHANGE_CURRENCIES",
			"Cannot exchange to same currency",
		);
	}

	const rates = await exchangeRateService.getCurrentRates();
	// CNY -> HKD,USD
	if (fromCurrency === Currency.CNY) {
		return toCurrency === Currency.HKD ? 1 / rates.HKD_CNY : 1 / rates.USD_CNY;
	}
	// HKD,USD -> CNY
	if (toCurrency === Currency.CNY) {
		return fromCurrency === Currency.HKD ? rates.HKD_CNY : rates.USD_CNY;
	}
	// HKD -> USD
	if (fromCurrency === Currency.HKD && toCurrency === Currency.USD) {
		return rates.HKD_CNY / rates.USD_CNY;
	}
	// USD -> HKD
	return rates.USD_CNY / rates.HKD_CNY;
}

/**
 * 执行通道货币兑换
 */
export async function exchangeChannelCurrency(
	fromCurrency: Currency,
	toCurrency: Currency,
	amount: number,
): Promise<ChannelTransactionData> {
	// 验证参数
	if (amount <= 0) {
		throw AppError.create(
			"EXCHANGE_AMOUNT_TOO_SMALL",
			"Exchange amount must be positive",
		);
	}

	// 获取通道余额
	const balance = await ChannelFundModel.getChannelBalance();

	// 检查余额是否充足
	let hasBalance = false;
	if (fromCurrency === Currency.CNY) {
		hasBalance = balance.balance_cny >= amount;
	} else if (fromCurrency === Currency.HKD) {
		hasBalance = balance.balance_hkd >= amount;
	} else if (fromCurrency === Currency.USD) {
		hasBalance = balance.balance_usd >= amount;
	}

	if (!hasBalance) {
		throw AppError.create("INSUFFICIENT_BALANCE", "Insufficient balance");
	}

	// 获取当前汇率
	const rate = await getExchangeRate(fromCurrency, toCurrency);

	// 计算最终兑换所得
	const exchangedAmount = amount * rate;

	// 在平台事务中创建交易记录并自动处理
	return withTransaction(async (tx: Prisma.TransactionClient) => {
		// 扣除原始货币 - 使用负数金额表示资金流出
		await ChannelFundModel.createTransaction(
			{
				amount: -amount, // 负数金额，系统会将其直接存储为负数
				type: ChannelTransactionType.EXCHANGE,
				currency: fromCurrency,
				remarks: `转出 ${amount} ${fromCurrency} 兑换为 ${exchangedAmount.toFixed(2)} ${toCurrency}`,
				status: ChannelTransactionStatus.AUTO_CONFIRMED,
			},
			tx,
		);

		// 增加目标货币 - 正数金额表示资金流入
		return ChannelFundModel.createTransaction(
			{
				amount: exchangedAmount, // 正数金额，系统会将其直接存储为正数
				type: ChannelTransactionType.EXCHANGE,
				currency: toCurrency,
				remarks: `收到 ${amount} ${fromCurrency} 兑换的 ${exchangedAmount.toFixed(2)} ${toCurrency}`,
				status: ChannelTransactionStatus.AUTO_CONFIRMED,
			},
			tx,
		);
	}, true);
}
