import { request } from "./request";
import type {
	AdminData,
	AdminPermission,
	ApiTokenData,
} from "@packages/shared";

// 权限相关 API
export const permissionApi = {
	getAllAdmins: () => request.get<AdminData[]>("/admin/permission/admins"),

	createAdmin: (
		username: string,
		password: string,
		name: string,
		permissions: AdminPermission[],
	) =>
		request.post<AdminData>("/admin/permission/admins", {
			username,
			password,
			name,
			permissions,
		}),

	toggleAdminStatus: (adminId: number) =>
		request.post<AdminData>(`/admin/permission/admins/${adminId}`),

	updateAdminPermissions: (adminId: number, permissions: AdminPermission[]) =>
		request.put<AdminData>(`/admin/permission/admins/${adminId}/permissions`, {
			permissions,
		}),

	getApiToken: () => request.post<ApiTokenData>("/admin/permission/api-token"),
};
