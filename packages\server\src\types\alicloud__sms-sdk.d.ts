// 阿里云 SMS SDK 类型定义，保留备用
declare module "@alicloud/sms-sdk" {
	export default class SMSClient {
		constructor(config: {
			accessKeyId: string;
			secretAccessKey: string;
			endpoint: string;
			apiVersion: string;
			timeout: number;
		});

		sendSMS(params: {
			PhoneNumbers: string;
			SignName: string;
			TemplateCode: string;
			TemplateParam: string;
		}): Promise<{
			Code: string;
			Message: string;
		}>;
	}
}
