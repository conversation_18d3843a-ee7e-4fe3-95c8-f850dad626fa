import { wrapUserRoute } from "@/utils/routeWrapper.js";
import { Router } from "express";
import * as auditService from "@/services/auditService.js";
import type {
	CreateFundAuditRequest,
	CreateQualificationAuditRequest,
} from "@packages/shared";
import {
	authenticateToken,
	authenticateTokenWithoutQualification,
} from "@/middlewares/jwtAuth.js";

const router = Router();

// Create fund audit request: POST api/audit/fund
router.post(
	"/fund",
	authenticateToken,
	wrapUserRoute<CreateFundAuditRequest>(async (req, res) => {
		const result = await auditService.createFundAudit({
			...req.body,
			user_id: req.jwt.user_id,
		});
		res.status(201).json(result);
	}),
);

// Create qualification audit request: POST api/audit/qualification
router.post(
	"/qualification",
	authenticateTokenWithoutQualification,
	wrapUserRoute<CreateQualificationAuditRequest>(async (req, res) => {
		const result = await auditService.createQualificationAudit({
			...req.body,
			user_id: req.jwt.user_id,
		});
		res.status(201).json(result);
	}),
);

// Get qualification status: GET api/audit/qualification
router.get(
	"/qualification",
	authenticateTokenWithoutQualification,
	wrapUserRoute(async (req, res) => {
		const result = await auditService.getQualificationStatus(req.jwt.user_id);
		res.status(200).json(result);
	}),
);

// Get pending audits: GET api/audit/pending
router.get(
	"/pending",
	authenticateTokenWithoutQualification,
	wrapUserRoute(async (req, res) => {
		const result = await auditService.getPendingAudits(req.jwt.user_id);
		res.status(200).json(result);
	}),
);

export default router;
