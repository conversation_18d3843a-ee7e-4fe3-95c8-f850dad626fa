import type { Request, Response, NextFunction } from "express";
import type { AuthRequest } from "@/middlewares/jwtAuth.js";
import type { ParsedQs } from "qs";
import type {
	JWTPayload,
	UserJWTPayload,
	AdminJWTPayload,
} from "@packages/shared";

// Base request handler type
type HandlerRequest<
	ReqBody = unknown,
	ReqQuery = ParsedQs,
	Base extends Request = Request,
> = Omit<Base, "body" | "query"> & {
	body: ReqBody;
	query: ReqQuery;
	jwt?: Base extends AuthRequest ? JWTPayload : never;
};

// User authenticated request type
interface UserAuthRequest extends AuthRequest {
	jwt: UserJWTPayload;
}

// Admin authenticated request type
interface AdminAuthRequest extends AuthRequest {
	jwt: AdminJWTPayload;
}

type Handler<
	ReqBody = unknown,
	ReqQuery = ParsedQs,
	Base extends Request = Request,
> = (
	req: HandlerRequest<ReqBody, ReqQuery, Base>,
	res: Response,
	next: NextFunction,
) => Promise<void>;

type AsyncRouteHandler = (
	req: Request,
	res: Response,
	next?: NextFunction,
) => Promise<unknown>;

type SyncRouteHandler = (
	req: Request,
	res: Response,
	next?: NextFunction,
) => unknown;

type RouteHandler = AsyncRouteHandler | SyncRouteHandler;

/**
 * Base route wrapper
 * @returns A function that wraps the error handler
 */
export const wrapRoute = <ReqBody = unknown, ReqQuery = ParsedQs>(
	handler: Handler<ReqBody, ReqQuery>,
) => {
	return (req: Request, res: Response, next: NextFunction): void => {
		Promise.resolve(
			handler(req as HandlerRequest<ReqBody, ReqQuery>, res, next),
		).catch(next);
	};
};

/**
 * User authenticated route wrapper.
 * Requires JWT auth middleware and client request headers with credentials.
 * @returns A function that wraps the error handler
 */
export const wrapUserRoute = <ReqBody = unknown, ReqQuery = ParsedQs>(
	handler: Handler<ReqBody, ReqQuery, UserAuthRequest>,
) => {
	return (req: Request, res: Response, next: NextFunction): void => {
		Promise.resolve(
			handler(
				req as HandlerRequest<ReqBody, ReqQuery, UserAuthRequest>,
				res,
				next,
			),
		).catch(next);
	};
};

/**
 * Admin authenticated route wrapper. Requires JWT auth middleware and client request headers with credentials.
 * @returns A function that wraps the error handler
 */
export const wrapAdminRoute = <ReqBody = unknown, ReqQuery = ParsedQs>(
	handler: Handler<ReqBody, ReqQuery, AdminAuthRequest>,
) => {
	return (req: Request, res: Response, next: NextFunction): void => {
		Promise.resolve(
			handler(
				req as HandlerRequest<ReqBody, ReqQuery, AdminAuthRequest>,
				res,
				next,
			),
		).catch(next);
	};
};
