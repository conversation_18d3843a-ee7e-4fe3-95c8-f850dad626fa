import type { StructureType } from "./inquiry";

// 手动录单状态枚举
export enum ManualOrderStatus {
	HOLDING = "holding",
	SOLD = "sold",
}

// 手动录单数据接口
export interface ManualOrderData {
	manual_order_id: number;
	user_id: number;
	ts_code: string;
	entry_price: number;
	exercise_price: number;
	scale: number;
	term: number;
	structure: StructureType;
	quote: number;
	status: ManualOrderStatus;
	quote_provider: string;
	entry_date?: string;
	expiry_date?: string;
	exit_date?: string;
	settle_price?: number;
	remarks?: string;
}

// 手动录单请求接口
export type ManualOrderRequest = Omit<
	ManualOrderData,
	"manual_order_id" | "user_id" | "status"
>;
