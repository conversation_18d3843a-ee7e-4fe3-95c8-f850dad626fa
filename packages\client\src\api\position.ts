import { request } from "./request";
import type { PositionData } from "@packages/shared";

// 持仓相关 API
export const positionApi = {
	getPositions: (
		page: number,
		pageSize: number,
		isDescending: boolean,
		filters?: { ts_codes?: string[] },
	) =>
		request.get<{ items: PositionData[]; total: number; ts_codes: string[] }>(
			"/position",
			{
				params: {
					page,
					pageSize,
					isDescending,
					ts_codes: filters?.ts_codes?.join(","),
				},
			},
		),
} as const;
