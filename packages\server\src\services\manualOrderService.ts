import * as ManualOrder from "@/models/manualOrder.js";
import { AppError } from "@/core/appError.js";
import type {
	ManualOrderData,
	ManualOrderRequest,
	ManualOrderStatus,
} from "@packages/shared";
import logger from "@/utils/logger.js";

/**
 * 创建手动录单
 */
export async function createManualOrder(
	data: ManualOrderRequest & { user_id: number },
): Promise<ManualOrderData> {
	try {
		// 日期处理
		const entryDate = data.entry_date ? new Date(data.entry_date) : new Date();

		// 计算到期日
		let expiryDate: Date | null = null;
		if (data.expiry_date) {
			expiryDate = new Date(data.expiry_date);
		} else {
			// 根据期限自动计算到期日
			expiryDate = new Date(entryDate);
			if (data.term === 14) {
				// 两周
				expiryDate.setDate(expiryDate.getDate() + 14);
			} else {
				// 按月计算
				expiryDate.setMonth(expiryDate.getMonth() + data.term);
			}
		}

		// 创建手动录单记录
		const manualOrderData: Omit<
			ManualOrderData,
			"manual_order_id" | "created_at"
		> = {
			...data,
			status: "holding" as ManualOrderStatus,
			entry_date: entryDate.toISOString(),
			expiry_date: expiryDate.toISOString(),
			settle_price: undefined,
			exit_date: undefined,
		};

		const result = await ManualOrder.create(manualOrderData);
		return result;
	} catch (error) {
		logger.error(error, "Failed to create manual order");
		throw AppError.create("MANUAL_ORDER_FAILED", "创建手动录单失败", {
			details: error instanceof Error ? error.message : "未知错误",
		});
	}
}

/**
 * 获取用户手动录单列表
 */
export async function getUserManualOrders(
	user_id: number,
	page: number,
	pageSize: number,
	isDescending: boolean,
	filters?: {
		ts_codes?: string[];
		status?: string[];
		startDate?: string;
		endDate?: string;
	},
): Promise<{
	items: ManualOrderData[];
	total: number;
	ts_codes: string[];
}> {
	try {
		const offset = (page - 1) * pageSize;

		// 获取总数和分页数据
		const [ordersResult, ts_codes] = await Promise.all([
			ManualOrder.getAllForUser(
				user_id,
				offset,
				pageSize,
				isDescending,
				filters,
			),
			ManualOrder.getAllTsCodes(user_id),
		]);

		return {
			total: ordersResult.total,
			items: ordersResult.items,
			ts_codes,
		};
	} catch (error) {
		logger.error(error, "Failed to get user manual orders");
		throw AppError.create("MANUAL_ORDER_FAILED", "获取手动录单列表失败", {
			details: error instanceof Error ? error.message : "未知错误",
		});
	}
}

/**
 * 更新手动录单
 */
export async function updateManualOrder(
	id: number,
	user_id: number,
	data: Partial<ManualOrderData>,
): Promise<ManualOrderData> {
	try {
		// 验证录单所有权
		const existingOrder = await ManualOrder.findById(id);
		if (!existingOrder) {
			throw AppError.create("NOT_FOUND", "手动录单不存在");
		}

		if (existingOrder.user_id !== user_id) {
			throw AppError.create("FORBIDDEN", "无权限操作此录单");
		}

		// 特殊处理状态变更为sold的情况
		if (data.status === "sold" && existingOrder.status !== "sold") {
			// 如果是变更为已售出状态，自动填充关闭时间
			const now = new Date();
			data.exit_date = now.toISOString();

			// 如果没有提供结算价，使用当前价格
			if (!data.settle_price) {
				data.settle_price = existingOrder.entry_price;
			}
		}

		// 更新录单
		const result = await ManualOrder.update(id, data);
		return result;
	} catch (error) {
		if (error instanceof AppError) {
			throw error;
		}

		logger.error(error, "Failed to update manual order");
		throw AppError.create("MANUAL_ORDER_FAILED", "更新手动录单失败", {
			details: error instanceof Error ? error.message : "未知错误",
		});
	}
}

/**
 * 删除手动录单
 */
export async function deleteManualOrder(
	id: number,
	user_id: number,
): Promise<void> {
	try {
		// 验证录单所有权
		const existingOrder = await ManualOrder.findById(id);
		if (!existingOrder) {
			throw AppError.create("NOT_FOUND", "手动录单不存在");
		}

		if (existingOrder.user_id !== user_id) {
			throw AppError.create("FORBIDDEN", "无权限操作此录单");
		}

		// 删除录单
		await ManualOrder.deleteById(id);
	} catch (error) {
		if (error instanceof AppError) {
			throw error;
		}

		logger.error(error, "Failed to delete manual order");
		throw AppError.create("MANUAL_ORDER_FAILED", "删除手动录单失败", {
			details: error instanceof Error ? error.message : "未知错误",
		});
	}
}
